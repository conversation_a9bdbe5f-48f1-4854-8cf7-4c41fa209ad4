{"name": "woocommerce/woocommerce-shipping", "description": "WooCommerce Shipping", "authors": [{"name": "woocommerce"}], "autoload": {"psr-4": {"Automattic\\WCShipping\\": "src/"}}, "autoload-dev": {"psr-4": {"Automattic\\WCShipping\\Tests\\": "tests/"}}, "require": {"automattic/jetpack-connection": "^6.2.0", "automattic/jetpack-config": "^3.0.0", "automattic/jetpack-autoloader": "^5.0.0", "automattic/jetpack-status": "^5.0.1"}, "require-dev": {"phpunit/phpunit": "9.6.8", "squizlabs/php_codesniffer": "^3.8.1", "yoast/phpunit-polyfills": "^2.0", "wp-coding-standards/wpcs": "^3.0.1", "woocommerce/woocommerce-sniffs": "^1.0.0", "woocommerce/qit-cli": "^0.8.1", "wp-cli/wp-cli-bundle": "^2.10"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "automattic/jetpack-autoloader": true}, "platform": {"php": "7.4"}}, "scripts": {"check-security": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules,dist,docker,release,bin --standard=./.phpcs.security.xml  --report-full --report-summary"], "qit:security": ["pnpm run build && composer install && ./vendor/bin/qit run:security woocommerce-shipping --zip=woocommerce-shipping.zip"], "qit:plugin-check": ["pnpm run build && composer install && ./vendor/bin/qit run:plugin-check woocommerce-shipping --zip=woocommerce-shipping.zip"], "phpcs": ["phpcs -s -p"], "phpcbf": ["phpcbf -p"], "pre-commit-check": ["php ./tasks/check-classes-folder-change.php"]}}