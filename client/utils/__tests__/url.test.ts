import { urlParamHasValue, setUrlParamValue, deleteUrlParam } from '../url';

describe( 'URL Utilities', () => {
	beforeEach( () => {
		// Mock window.history.replaceState
		jest.spyOn( window.history, 'replaceState' ).mockImplementation(
			jest.fn()
		);
		// Set initial search params
		Object.defineProperty( window, 'location', {
			value: {
				pathname: '/test',
				search: '?foo=bar&baz=qux',
			},
			writable: true,
		} );
	} );

	afterEach( () => {
		jest.restoreAllMocks();
	} );

	test( 'urlParamHasValue returns true for matching parameter and value', () => {
		expect( urlParamHasValue( 'foo', 'bar' ) ).toBe( true );
	} );

	test( 'urlParamHasValue returns false for non-matching parameter or value', () => {
		expect( urlParamHasValue( 'foo', 'wrong' ) ).toBe( false );
		expect( urlParamHasValue( 'nonexistent', 'value' ) ).toBe( false );
	} );

	test( 'setUrlParamValue updates the parameter in the URL', () => {
		setUrlParamValue( 'foo', 'newvalue' );
		expect( window.history.replaceState ).toHaveBeenCalledWith(
			{},
			'',
			'/test?foo=newvalue&baz=qux'
		);
	} );

	test( 'setUrlParamValue adds a new parameter if it does not exist', () => {
		setUrlParamValue( 'newparam', 'value' );
		expect( window.history.replaceState ).toHaveBeenCalledWith(
			{},
			'',
			'/test?foo=bar&baz=qux&newparam=value'
		);
	} );

	test( 'deleteUrlParam removes the parameter from the URL', () => {
		deleteUrlParam( 'foo' );
		expect( window.history.replaceState ).toHaveBeenCalledWith(
			{},
			'',
			'/test?baz=qux'
		);
	} );

	test( 'deleteUrlParam does nothing if the parameter does not exist', () => {
		deleteUrlParam( 'nonexistent' );
		expect( window.history.replaceState ).toHaveBeenCalledWith(
			{},
			'',
			'/test?foo=bar&baz=qux'
		);
	} );
} );
