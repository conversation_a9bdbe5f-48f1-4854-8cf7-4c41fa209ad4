import {
	getCurrentOrderShipments,
	getNoneSelectedShipmentItems,
	normalizeSubItems,
	removeEmptyShipments,
} from 'utils';

const shipment2 = [
	{
		id: 2,
		quantity: 2,
		subItems: [
			{
				id: '2-sub-0',
				quantity: 1,
				parentId: 2,
				subItems: [],
			},
			{
				id: '2-sub-1',
				quantity: 1,
				parentId: 2,
				subItems: [],
			},
		],
	},
];

describe( 'Shipment utilities [normalizeSubItems]', () => {
	it( 'properly creates a parent for orphan subItems', () => {
		const shipments = {
			0: [
				{
					id: '1-sub-0',
					quantity: 1,
				},
				{
					id: '1-sub-1',
					quantity: 1,
				},
			],
			1: shipment2,
		};

		expect( normalizeSubItems( shipments ) ).toEqual( {
			0: [
				{
					id: 1,
					quantity: 2,
					subItems: [
						{
							id: '1-sub-0',
							quantity: 1,
						},
						{
							id: '1-sub-1',
							quantity: 1,
						},
					],
				},
			],
			1: shipment2,
		} );
	} );

	it( 'correctly assigns subItems to their parent', () => {
		const shipments = {
			0: [
				{
					id: 1,
					quantity: 2,
					subItems: [
						{
							id: '1-sub-0',
							quantity: 1,
							subItems: [],
						},
						{
							id: '1-sub-1',
							quantity: 1,
							subItems: [],
						},
					],
				},
				{
					id: '1-sub-2',
					quantity: 1,
					subItems: [],
				},
				{
					id: '1-sub-3',
					quantity: 1,
					subItems: [],
				},
			],
			1: shipment2,
		};

		expect( normalizeSubItems( shipments ) ).toEqual( {
			0: [
				{
					id: 1,
					quantity: 4,
					subItems: [
						{
							id: '1-sub-0',
							quantity: 1,
							subItems: [],
						},
						{
							id: '1-sub-1',
							quantity: 1,
							subItems: [],
						},
						{
							id: '1-sub-2',
							quantity: 1,
							subItems: [],
						},
						{
							id: '1-sub-3',
							quantity: 1,
							subItems: [],
						},
					],
				},
			],
			1: shipment2,
		} );
	} );

	it( 'removes the only subItem', () => {
		const shipments = {
			0: [
				{
					id: 1,
					quantity: 1,
					subItems: [
						{
							id: '1-sub-0',
							quantity: 1,
							subItems: [],
						},
					],
				},
			],
			1: shipment2,
		};

		expect( normalizeSubItems( shipments ) ).toEqual( {
			0: [
				{
					id: 1,
					quantity: 1,
					subItems: [],
				},
			],
			1: shipment2,
		} );
	} );

	it( 'creates subItems if more than one item with the same id exists in a shipment', () => {
		const shipments = {
			0: [
				{
					id: 1,
					quantity: 1,
					subItems: [],
				},
				{
					id: 1,
					quantity: 1,
					subItems: [],
				},
			],
			1: shipment2,
		};

		expect( normalizeSubItems( shipments ) ).toEqual( {
			0: [
				{
					id: 1,
					quantity: 2,
					subItems: [
						{
							id: '1-sub-0',
							quantity: 1,
							parentId: 1,
							subItems: [],
						},
						{
							id: '1-sub-1',
							quantity: 1,
							parentId: 1,
							subItems: [],
						},
					],
				},
			],
			1: shipment2,
		} );
	} );

	it( 'correctly merges items and subItems of the same id/parentId [Multiple]', () => {
		const shipments = {
			0: [
				{
					id: 1,
					quantity: 1,
					subItems: [],
				},
				{
					id: '1-sub-0',
					quantity: 1,
					parentId: 1,
					subItems: [],
				},
				{
					id: '1-sub-2',
					quantity: 1,
					parentId: 1,
					subItems: [],
				},
			],
			1: shipment2,
		};

		expect( normalizeSubItems( shipments ) ).toEqual( {
			0: [
				{
					id: 1,
					quantity: 3,
					subItems: [
						{
							id: '1-sub-0',
							quantity: 1,
							parentId: 1,
							subItems: [],
						},
						{
							id: '1-sub-1',
							quantity: 1,
							parentId: 1,
							subItems: [],
						},
						{
							id: '1-sub-2',
							quantity: 1,
							parentId: 1,
							subItems: [],
						},
					],
				},
			],
			1: shipment2,
		} );
	} );

	it( 'correctly merges items and subItems of the same id/parentId [Single]', () => {
		const shipments = {
			0: [
				{
					id: 4,
					quantity: 1,
					subItems: [],
				},
				{
					id: '4-sub-0',
					parentId: 4,
					quantity: 1,
					subItems: [],
				},
			],
			1: shipment2,
		};

		expect( normalizeSubItems( shipments ) ).toEqual( {
			0: [
				{
					id: 4,
					quantity: 2,
					subItems: [
						{
							id: '4-sub-0',
							parentId: 4,
							quantity: 1,
							subItems: [],
						},
						{
							id: '4-sub-1',
							parentId: 4,
							quantity: 1,
							subItems: [],
						},
					],
				},
			],
			1: shipment2,
		} );
	} );
} );

describe( 'Shipment utilities [getNoneSelectedShipmentItems]', () => {
	it( "doesn't throw for empty selections", () => {
		expect(
			getNoneSelectedShipmentItems(
				{
					0: shipment2,
				},
				{}
			)
		).toEqual( {
			0: shipment2,
		} );
	} );

	it( 'returns shipments not included in the selections collection', () => {
		expect(
			getNoneSelectedShipmentItems(
				{
					0: shipment2,
					1: [
						{
							id: 3,
							quantity: 1,
							subItems: [],
						},
					],
				},
				{
					0: [
						{
							id: '2-sub-0',
							quantity: 1,
							subItems: [],
						},
					],
				}
			)
		).toEqual( {
			0: [
				{
					id: 2,
					quantity: 1,
					subItems: [
						{
							id: '2-sub-1',
							quantity: 1,
							parentId: 2,
							subItems: [],
						},
					],
				},
			],
			1: [
				{
					id: 3,
					quantity: 1,
					subItems: [],
				},
			],
		} );
	} );

	it( 'removes shipment item with all subItems selected', () => {
		expect(
			getNoneSelectedShipmentItems(
				{
					0: shipment2,
					1: [
						{
							id: 3,
							quantity: 1,
							subItems: [],
						},
					],
				},
				{
					0: [
						{
							id: '2-sub-0',
							quantity: 1,
							subItems: [],
						},
						{
							id: '2-sub-1',
							quantity: 1,
							subItems: [],
						},
					],
				}
			)
		).toEqual( {
			0: [],
			1: [
				{
					id: 3,
					quantity: 1,
					subItems: [],
				},
			],
		} );
	} );
} );

describe( 'Shipment utilities [getCurrentOrderShipments]', () => {
	const config = {
		shipments: JSON.stringify( {} ),
		order: {
			line_items: [
				{
					id: 1,
					quantity: 1,
					name: '1',
				},
				{ id: 2, quantity: 2, name: '2' },
			],
		},
	};
	it( 'returns all order items if nothing is stored', () => {
		expect( getCurrentOrderShipments( config ) ).toEqual( {
			0: [
				{
					id: 1,
					name: '1',
					quantity: 1,
					subItems: [],
				},
				{
					id: 2,
					quantity: 2,
					name: '2',
					subItems: [
						{
							id: '2-sub-0',
							quantity: 1,
							name: '2',
							parentId: 2,
							subItems: [],
						},
						{
							id: '2-sub-1',
							quantity: 1,
							name: '2',
							parentId: 2,
							subItems: [],
						},
					],
				},
			],
		} );
	} );

	it( 'returns correctly if orderItems are updated and one item is removed', () => {
		const updatedConfig = {
			shipments: JSON.stringify( {
				0: [
					{
						id: 1,
						quantity: 1,
						subItems: [],
					},
				],
				1: [
					{
						id: 2,
						quantity: 2,
						subItems: [
							{
								id: '2-sub-0',
								quantity: 1,
								parentId: 2,
								subItems: [],
							},
							{
								id: '2-sub-1',
								quantity: 1,
								parentId: 2,
								subItems: [],
							},
						],
					},
				],
			} ),
			order: {
				line_items: [
					{
						id: 1,
						quantity: 1,
					},
				],
			},
		};

		expect( getCurrentOrderShipments( updatedConfig ) ).toEqual( {
			0: [
				{
					id: 1,
					quantity: 1,
					subItems: [],
				},
			],
		} );
	} );

	it( 'should add the orderItem added after the shipment was saved to the first shipment', () => {
		const updatedConfig = {
			shipments: JSON.stringify( {
				0: [
					{
						id: 1,
						quantity: 1,
						subItems: [],
					},
				],
				1: [
					{
						id: 2,
						quantity: 2,
						subItems: [ '2-sub-0', '2-sub-1' ],
					},
				],
			} ),
			order: {
				line_items: [
					{
						id: 1,
						quantity: 1,
						name: '1',
					},
					{
						id: 2,
						quantity: 2,
						name: '2',
					},
					{
						id: 3,
						quantity: 2,
						name: '3',
					},
				],
			},
		};
		expect( getCurrentOrderShipments( updatedConfig ) ).toEqual( {
			0: [
				{
					id: 1,
					name: '1',
					quantity: 1,
					subItems: [],
				},
				{
					id: 3,
					quantity: 2,
					name: '3',
					subItems: [
						{
							id: '3-sub-0',
							quantity: 1,
							name: '3',
							parentId: 3,
							subItems: [],
						},
						{
							id: '3-sub-1',
							quantity: 1,
							name: '3',
							parentId: 3,
							subItems: [],
						},
					],
				},
			],
			1: [
				{
					id: 2,
					quantity: 2,
					name: '2',
					subItems: [
						{
							id: '2-sub-0',
							quantity: 1,
							name: '2',
							parentId: 2,
							subItems: [],
						},
						{
							id: '2-sub-1',
							quantity: 1,
							name: '2',
							parentId: 2,
							subItems: [],
						},
					],
				},
			],
		} );
	} );
} );

describe( 'Shipment utilities [removeEmptyShipments]', () => {
	it( 'should properly remove empty shipments', () => {
		expect(
			removeEmptyShipments( {
				0: [],
				1: [
					{
						id: 1,
						quantity: 1,
						subItems: [],
					},
				],
				2: [],
			} )
		).toEqual( {
			0: [
				{
					id: 1,
					quantity: 1,
					subItems: [],
				},
			],
		} );
	} );
} );
