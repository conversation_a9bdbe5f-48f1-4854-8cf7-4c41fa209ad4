import { camelCaseKeysRecursive } from '../common';

describe( 'camelCaseKeysRecursive', () => {
	it( 'should convert simple object keys to camelCase', () => {
		const input = {
			first_name: '<PERSON>',
			last_name: '<PERSON>',
			phone_number: '************',
		};
		const expected = {
			firstName: '<PERSON>',
			lastName: '<PERSON>',
			phoneNumber: '************',
		};
		expect( camelCaseKeysRecursive( input ) ).toEqual( expected );
	} );

	it( 'should handle nested objects', () => {
		const input = {
			user_info: {
				first_name: '<PERSON>',
				address_details: {
					street_name: 'Main St',
					zip_code: '12345',
				},
			},
		};
		const expected = {
			userInfo: {
				firstName: '<PERSON>',
				addressDetails: {
					streetName: 'Main St',
					zipCode: '12345',
				},
			},
		};
		expect( camelCaseKeysRecursive( input ) ).toEqual( expected );
	} );

	it( 'should handle arrays of objects', () => {
		const input = {
			user_list: [
				{ first_name: '<PERSON>', last_name: '<PERSON><PERSON>' },
				{ first_name: '<PERSON>', last_name: '<PERSON>' },
			],
		};
		const expected = {
			userList: [
				{ firstName: 'John', lastName: 'Doe' },
				{ firstName: 'Jane', lastName: 'Smith' },
			],
		};
		expect( camelCaseKeysRecursive( input ) ).toEqual( expected );
	} );

	it( 'should preserve non-object values', () => {
		const input = {
			string_value: 'test',
			number_value: 42,
			boolean_value: true,
			null_value: null,
			undefined_value: undefined,
		};
		const expected = {
			stringValue: 'test',
			numberValue: 42,
			booleanValue: true,
			nullValue: null,
			undefinedValue: undefined,
		};
		expect( camelCaseKeysRecursive( input ) ).toEqual( expected );
	} );

	it( 'should handle empty objects', () => {
		const input = {};
		expect( camelCaseKeysRecursive( input ) ).toEqual( {} );
	} );

	it( 'should handle objects with array values', () => {
		const input = {
			favorite_numbers: [ 1, 2, 3 ],
			contact_info: {
				phone_numbers: [ '************', '************' ],
			},
		};
		const expected = {
			favoriteNumbers: [ 1, 2, 3 ],
			contactInfo: {
				phoneNumbers: [ '************', '************' ],
			},
		};
		expect( camelCaseKeysRecursive( input ) ).toEqual( expected );
	} );
} );
