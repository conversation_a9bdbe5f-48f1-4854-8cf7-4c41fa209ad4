import {
	addresAbbreviationMap,
	normaliseAddress,
	levenshteinDistance,
	areAddressesClose,
} from '../address';
import { Destination } from 'types';

describe( 'addresAbbreviationMap', () => {
	it( 'should have correct abbreviations', () => {
		expect( addresAbbreviationMap.STREET ).toBe( 'ST' );
		expect( addresAbbreviationMap.AVENUE ).toBe( 'AVE' );
		expect( addresAbbreviationMap.BOULEVARD ).toBe( 'BLVD' );
		expect( addresAbbreviationMap.ROAD ).toBe( 'RD' );
		expect( addresAbbreviationMap.DRIVE ).toBe( 'DR' );
		expect( addresAbbreviationMap.LANE ).toBe( 'LN' );
		expect( addresAbbreviationMap.COURT ).toBe( 'CT' );
		expect( addresAbbreviationMap.PARKWAY ).toBe( 'PKWY' );
		expect( addresAbbreviationMap.PLACE ).toBe( 'PL' );
		expect( addresAbbreviationMap.TERRACE ).toBe( 'TER' );
		expect( addresAbbreviationMap.CIRCLE ).toBe( 'CIR' );
		expect( addresAbbreviationMap.HIGHWAY ).toBe( 'HWY' );
		expect( addresAbbreviationMap.MOUNT ).toBe( 'MT' );
		expect( addresAbbreviationMap.MOUNTAIN ).toBe( 'MTN' );
		expect( addresAbbreviationMap.SQUARE ).toBe( 'SQ' );
		expect( addresAbbreviationMap.SUITE ).toBe( 'STE' );
		expect( addresAbbreviationMap.BUILDING ).toBe( 'BLDG' );
		expect( addresAbbreviationMap.FLOOR ).toBe( 'FL' );
		expect( addresAbbreviationMap.ROOM ).toBe( 'RM' );
		expect( addresAbbreviationMap.APARTMENT ).toBe( 'APT' );
		expect( addresAbbreviationMap.UNIT ).toBe( 'UNIT' );
		expect( addresAbbreviationMap.HARBOR ).toBe( 'HBR' );
		expect( addresAbbreviationMap.ISLAND ).toBe( 'IS' );
		expect( addresAbbreviationMap.CREEK ).toBe( 'CRK' );
		expect( addresAbbreviationMap.HEIGHTS ).toBe( 'HTS' );
		expect( addresAbbreviationMap.SPRING ).toBe( 'SPG' );
		expect( addresAbbreviationMap.VALLEY ).toBe( 'VLY' );
		expect( addresAbbreviationMap.CROSSING ).toBe( 'XING' );
		expect( addresAbbreviationMap.NORTH ).toBe( 'N' );
		expect( addresAbbreviationMap.SOUTH ).toBe( 'S' );
		expect( addresAbbreviationMap.EAST ).toBe( 'E' );
		expect( addresAbbreviationMap.WEST ).toBe( 'W' );
	} );
} );

describe( 'normaliseAddress', () => {
	it( 'should remove periods, commas, extra spaces, and convert to uppercase', () => {
		const address = '123, Main St. Apt. 4B';
		const expected = '123 MAIN ST APT 4B';
		expect( normaliseAddress( address ) ).toBe( expected );
	} );

	it( 'should handle empty strings', () => {
		const address = '';
		const expected = '';
		expect( normaliseAddress( address ) ).toBe( expected );
	} );

	it( 'should handle addresses with only spaces', () => {
		const address = '    ';
		const expected = ' ';
		expect( normaliseAddress( address ) ).toBe( expected );
	} );
} );

describe( 'levenshteinDistance', () => {
	it( 'should return 0 for identical strings', () => {
		const address1 = '123 Main St';
		const address2 = '123 Main St';
		expect( levenshteinDistance( address1, address2 ) ).toBe( 0 );
	} );

	it( 'should return the correct distance for different strings', () => {
		const address1 = '123 Main St';
		const address2 = '123 Main Street';
		expect( levenshteinDistance( address1, address2 ) ).toBe( 4 );
	} );

	it( 'should handle empty strings', () => {
		const address1 = '';
		const address2 = '123 Main St';
		expect( levenshteinDistance( address1, address2 ) ).toBe( 11 );
	} );
} );

describe( 'areAddressesClose', () => {
	const verifiedAddress: Destination = {
		address1: '123 MAIN ST',
		address2: 'APT 4B',
		city: 'SPRINGFIELD',
		state: 'IL',
		postcode: '62704-1234',
		country: 'US',
		email: '<EMAIL>',
		phone: '************',
		firstName: 'John',
		lastName: 'Doe',
	};

	const enteredAddress: Destination = {
		address1: '123 Main Street',
		address2: 'Apartment 4B',
		city: 'Springfield',
		state: 'IL',
		postcode: '62704',
		country: 'US',
		email: '<EMAIL>',
		phone: '************',
		firstName: 'John',
		lastName: 'Doe',
	};

	it( 'should return true for similar addresses', () => {
		expect( areAddressesClose( verifiedAddress, enteredAddress ) ).toBe(
			true
		);
	} );

	it( 'should return false for different addresses', () => {
		const differentAddress: Destination = {
			...enteredAddress,
			address1: '456 Elm Street', // Different street
		};
		expect( areAddressesClose( verifiedAddress, differentAddress ) ).toBe(
			false
		);
	} );

	it( 'should handle addresses with missing fields', () => {
		const partialAddress: Destination = {
			...enteredAddress,
			address2: '', // Missing apartment
		};
		expect( areAddressesClose( verifiedAddress, partialAddress ) ).toBe(
			false
		);
	} );

	it( 'should handle ZIP+4 codes', () => {
		const withtouZip4Address: Destination = {
			...enteredAddress,
			postcode: '62704', // Regular ZIP code to match verified ZIP+4 code
		};
		expect( areAddressesClose( verifiedAddress, withtouZip4Address ) ).toBe(
			true
		);
		const withZip4Address: Destination = {
			...enteredAddress,
			postcode: '62704-1234', // ZIP+4 code
		};
		expect( areAddressesClose( verifiedAddress, withZip4Address ) ).toBe(
			true
		);
	} );

	it( 'should handle postcode differences', () => {
		const wrongZipAddres: Destination = {
			...enteredAddress,
			postcode: '62706', // Different ZIP code
		};
		expect( areAddressesClose( verifiedAddress, wrongZipAddres ) ).toBe(
			false
		);
	} );

	it( 'should handle compass direction street differences', () => {
		const compassDirectionAddressVerfied: Destination = {
			...verifiedAddress,
			address1: '33 N 1ST ST',
		};
		const compassDirectionAddressEntered: Destination = {
			...compassDirectionAddressVerfied,
			address1: '33 North First Street',
			address2: 'Apartment 4B',
			city: 'Springfield',
		};

		expect(
			areAddressesClose(
				compassDirectionAddressVerfied,
				compassDirectionAddressEntered
			)
		).toBe( true );
	} );
} );
