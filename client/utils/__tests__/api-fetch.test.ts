import { apiFetch } from '@wordpress/data-controls';
import { abortableApiFetch, isAbortError } from '../api-fetch';

// Mock the @wordpress/data-controls module
jest.mock( '@wordpress/data-controls', () => ( {
	apiFetch: jest.fn(),
} ) );

// Mock DOMException for Node environment
class MockDOMException extends Error {
	constructor( message: string, name: string ) {
		super( message );
		this.name = name;
	}
}

// Replace global DOMException with our mock in Node environment
if ( typeof window === 'undefined' ) {
	global.DOMException = MockDOMException as unknown as typeof DOMException;
}

describe( 'api-fetch utilities', () => {
	beforeEach( () => {
		// Clear all mocks before each test
		jest.clearAllMocks();
	} );

	describe( 'isAbortError', () => {
		it( 'should return true for AbortError', () => {
			const error = new Error();
			error.name = 'AbortError';
			expect( isAbortError( error ) ).toBe( true );
		} );

		it( 'should return false for other errors', () => {
			expect( isAbortError( new Error( 'test' ) ) ).toBe( false );
			expect( isAbortError( null ) ).toBe( false );
			expect( isAbortError( undefined ) ).toBe( false );
			expect( isAbortError( {} ) ).toBe( false );
		} );
	} );

	describe( 'abortableApiFetch', () => {
		const mockRequest = { path: '/test' };
		const mockId = 'test-id';

		it( 'should make successful API request', async () => {
			const mockResponse = { success: true };
			( apiFetch as jest.Mock ).mockResolvedValueOnce( mockResponse );

			const generator = abortableApiFetch( mockRequest, mockId );
			let result = generator.next();

			// First yield should be the apiFetch call
			expect( result.done ).toBe( false );
			expect( apiFetch ).toHaveBeenCalledWith(
				expect.objectContaining( {
					...mockRequest,
					signal: expect.any( AbortSignal ),
				} )
			);

			// Simulate successful response
			result = generator.next( mockResponse );
			expect( result.done ).toBe( true );
			expect( result.value ).toEqual( mockResponse );
		} );

		it( 'should abort previous requests when making a new one', async () => {
			const mockAbort = jest.fn();
			const originalAbortController = window.AbortController;

			// Mock AbortController
			// @ts-ignore
			( window as Window & typeof globalThis ).AbortController = class {
				signal = { aborted: false };
				abort = mockAbort;
			};

			// Make first request
			const generator1 = abortableApiFetch( mockRequest, mockId );
			generator1.next();

			// Make second request
			const generator2 = abortableApiFetch( mockRequest, mockId );
			generator2.next();

			expect( mockAbort ).toHaveBeenCalledTimes( 1 );

			// Restore original AbortController
			( window as Window & typeof globalThis ).AbortController =
				originalAbortController;
		} );

		it( 'should handle abort errors', async () => {
			// Create a proper DOMException for AbortError
			const abortError = new MockDOMException(
				'The operation was aborted',
				'AbortError'
			);
			const successResponse = { success: true };
			( apiFetch as jest.Mock )
				.mockImplementationOnce( () => {
					throw abortError;
				} )
				.mockImplementationOnce( () => {
					return successResponse;
				} );

			const generator = abortableApiFetch( mockRequest, mockId );

			// First yield is the apiFetch call
			let result = generator.next();
			expect( result.done ).toBe( true ); // When it returns the error it's done.
			expect( result.value ).toBe( abortError );

			// Second yield should return success response
			result = abortableApiFetch( mockRequest, mockId ).next();
			expect( result.value ).toEqual( successResponse );
		} );

		it( 'should throw non-abort errors', async () => {
			const nonAbortError = new Error( 'API Error' );
			( apiFetch as jest.Mock ).mockImplementationOnce( () => {
				throw nonAbortError;
			} );

			const generator = abortableApiFetch( mockRequest, mockId );

			// When we try to get the next value, it should throw the error
			await expect( async () => {
				await generator.next();
			} ).rejects.toThrow( nonAbortError );
		} );
	} );
} );
