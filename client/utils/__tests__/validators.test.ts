import {
	validateCountryAndState,
	validateRequiredFields,
	validateEmojiString,
} from '../validators';
import { OriginAddress } from 'types';
import { hasStates } from 'utils';

jest.mock( '../location', () => ( {
	hasStates: jest.fn( () => false ),
} ) );

describe( 'Validators', () => {
	beforeEach( () => {
		// @ts-ignore
		hasStates.mockClear();
	} );
	describe( 'validateRequiredFields', () => {
		it( 'should return errors for missing required fields', () => {
			const result = validateRequiredFields( false )( {
				values: {
					address: '',
					city: '',
					postcode: '',
					country: '',
					name: '',
					company: '',
				} as OriginAddress,
				errors: {},
			} );
			expect( Object.keys( result.errors ) ).toEqual( [
				'address',
				'city',
				'postcode',
				'country',
				'company',
				'name',
			] );
		} );

		it( 'should return errors for missing email and phone for cross border shipment', () => {
			const result = validateRequiredFields( true )( {
				values: {
					address: '123 Main St',
					city: 'Anytown',
					postcode: '12345',
					country: 'US',
					email: '',
					phone: '',
					company: 'some company',
				} as OriginAddress,
				errors: {},
			} );

			expect( Object.keys( result.errors ) ).toEqual( [
				'email',
				'phone',
			] );
		} );

		it( 'should not return errors for valid fields', () => {
			const result = validateRequiredFields( false )( {
				values: {
					address: '123 Main St',
					city: 'Anytown',
					postcode: '12345',
					country: 'US',
					name: 'Some name',
				} as OriginAddress,
				errors: {},
			} );

			expect( result.errors ).toEqual( {} );
		} );

		it( 'should return error when name nor company is defined', () => {
			const result = validateRequiredFields( false )( {
				values: {
					address: '123 Main St',
					city: 'Anytown',
					postcode: '12345',
					country: 'US',
				} as OriginAddress,
				errors: {},
			} );

			expect( Object.keys( result.errors ) ).toEqual( [
				'company',
				'name',
			] );
		} );
	} );

	describe( 'validateCountryAndState', () => {
		it( 'should return error when state is missing for a country with states', () => {
			// @ts-ignore
			hasStates.mockReturnValue( true );
			const result = validateCountryAndState( {
				values: {
					country: 'US',
					state: '',
				} as OriginAddress,
				errors: {},
			} );

			expect( Object.keys( result.errors ) ).toEqual( [ 'state' ] );
		} );

		it( 'should not return error when state is provided for a country with states', () => {
			const result = validateCountryAndState( {
				values: {
					country: 'US',
					state: 'CA',
				} as OriginAddress,
				errors: {},
			} );

			expect( result.errors ).toEqual( {} );
		} );

		it( 'should not return error when state is missing for a country without states', () => {
			// @ts-ignore
			hasStates.mockReturnValue( false );
			const result = validateCountryAndState( {
				values: {
					country: 'SG',
					state: '',
				} as OriginAddress,
				errors: {},
			} );

			expect( result.errors ).toEqual( {} );
		} );
	} );

	describe( 'validateEmojiString', () => {
		it( 'should return errors for emoji usage in address fields.', () => {
			const result = validateEmojiString( {
				values: {
					address: '😂',
					city: '😂',
					postcode: '😂',
					country: 'US',
					state: '😂',
					company: 'some company',
				} as OriginAddress,
				errors: {},
			} );

			expect( Object.keys( result.errors ) ).toEqual( [
				'address',
				'city',
				'postcode',
				'state',
			] );
		} );

		it( 'should not return errors for valid fields', () => {
			const result = validateEmojiString( {
				values: {
					address: '123 Main St',
					city: 'Anytown',
					postcode: '12345',
					country: 'US',
					state: 'CA',
					name: 'Some name',
				} as OriginAddress,
				errors: {},
			} );

			expect( result.errors ).toEqual( {} );
		} );
	} );
} );
