import { canRefundLabel, hasLabelExpired } from '../label/refund';
import { Label } from 'types';
import { LABEL_PURCHASE_STATUS } from '../../data/constants';
import * as refundModule from '../label/refund';

describe( 'hasLabelExpired', () => {
	it( 'returns true when label is undefined', () => {
		expect( hasLabelExpired( undefined ) ).toBe( true );
	} );

	it( 'returns true when label status is ANONYMIZED', () => {
		const label = {
			status: LABEL_PURCHASE_STATUS.ANONYMIZED,
		} as Label;
		expect( hasLabelExpired( label ) ).toBe( true );
	} );

	it( 'returns true when label has a usedDate', () => {
		const label = {
			status: null,
			usedDate: new Date().getTime(),
			expiryDate: new Date().getTime() + 30,
		} as Label;
		expect( hasLabelExpired( label ) ).toBe( true );
	} );

	it( 'returns true when label expiryDate is in the past', () => {
		const label = {
			expiryDate: new Date().getTime() - 1000,
		} as Label;
		expect( hasLabelExpired( label ) ).toBe( true );
	} );

	it( 'returns false when label status is not ANONYMIZED, has no usedDate and expiryDate is in the future', () => {
		const label = {
			status: 'NOT_ANONYMIZED',
			expiryDate: new Date().getTime() + 1000,
		} as Label;
		expect( hasLabelExpired( label ) ).toBe( false );
	} );
} );

describe( 'canRefundLabel', () => {
	it( 'returns false when label is undefined', () => {
		expect( canRefundLabel( undefined ) ).toBe( false );
	} );

	it( 'returns false when label createdDate is more than 30 days ago', () => {
		const label = {
			createdDate: new Date().setDate( new Date().getDate() - 31 ),
			carrierId: null,
			tracking: null,
		} as Label;
		expect( canRefundLabel( label ) ).toBe( false );
	} );

	it( 'returns false when label is expired', () => {
		const label = {
			createdDate: new Date().setDate( new Date().getDate() - 1 ),
			carrierId: null,
			tracking: null,
		} as Label;
		jest.spyOn( refundModule, 'hasLabelExpired' ).mockReturnValue( true );
		expect( canRefundLabel( label ) ).toBe( false );
		jest.restoreAllMocks();
	} );

	it( 'returns false when carrierId is usps and tracking is not available', () => {
		const label = {
			createdDate: new Date().setDate( new Date().getDate() - 1 ),
			carrierId: 'usps',
			tracking: null,
		} as Label;
		jest.spyOn( refundModule, 'hasLabelExpired' ).mockReturnValue( false );
		expect( canRefundLabel( label ) ).toBe( false );
		jest.restoreAllMocks();
	} );

	it( 'returns true when label is not expired, carrierId is not usps or tracking is available, and createdDate is within 30 days', () => {
		const label = {
			createdDate: new Date().setDate( new Date().getDate() - 1 ),
			carrierId: 'fedex',
			tracking: '123',
		} as Label;
		jest.spyOn( refundModule, 'hasLabelExpired' ).mockReturnValue( false );
		expect( canRefundLabel( label ) ).toBe( true );
		jest.restoreAllMocks();
	} );
} );
