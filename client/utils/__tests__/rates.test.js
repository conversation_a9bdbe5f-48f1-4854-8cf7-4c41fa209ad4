import { groupRatesByCarrier } from '../rates';
import { rates } from './fixtures/rates';

describe( 'rates', () => {
	it( 'should return an object with the rates grouped by carrier', () => {
		const results = groupRatesByCarrier( rates );
		const firstShipment = results[ '0' ];
		expect( Object.keys( firstShipment ) ).toHaveLength( 3 );
		expect( Object.keys( firstShipment ) ).toEqual(
			Object.keys( rates[ '0' ] )
		);

		expect( firstShipment.default.usps ).toHaveLength( 5 );
		expect( firstShipment.default.usps[ 0 ].rateId ).toEqual(
			'rate_f23e85e96a3640a88d787a0daf871143'
		);
		expect( firstShipment.default.fedex ).toHaveLength( 1 );
		expect( firstShipment.default.fedex[ 0 ].rateId ).toEqual(
			'rate_622b2d2a189445d09e9c3693f7993e44'
		);
	} );
} );
