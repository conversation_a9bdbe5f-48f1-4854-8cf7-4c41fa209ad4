import { mockWCShippingConfig } from './fixtures/mock-wcshipping-config';
import {
	getAccountSettings,
	getCurrencySymbol,
	getCustomsInformation,
	getDimensionsUnit,
	getLastOrderCompleted,
	getPluginRelativeDirectory,
	getSelectedHazmat,
	getSelectedRates,
	getWeightUnit,
} from '../config';

// Mock window object with WCShipping_Config mock data.
beforeAll( () => {
	global.window = Object.create( window );
	Object.defineProperty( window, 'WCShipping_Config', {
		value: mockWCShippingConfig,
		writable: true,
	} );
} );

describe( 'config utils', () => {
	it( 'should return weight unit', () => {
		expect( getWeightUnit() ).toBe(
			mockWCShippingConfig.shippingLabelData.storeOptions.weight_unit
		);
	} );

	it( 'should return currency symbol', () => {
		expect( getCurrencySymbol() ).toBe(
			mockWCShippingConfig.shippingLabelData.storeOptions.currency_symbol
		);
	} );

	it( 'should return dimensions unit', () => {
		expect( getDimensionsUnit() ).toBe(
			mockWCShippingConfig.shippingLabelData.storeOptions.dimension_unit
		);
	} );

	it( 'should return account settings', () => {
		expect( getAccountSettings() ).toBe(
			mockWCShippingConfig.accountSettings
		);
	} );

	it( 'should return last order completed', () => {
		expect( getLastOrderCompleted() ).toBe(
			mockWCShippingConfig.accountSettings.userMeta.last_order_completed
		);
	} );

	it( 'should return selected rates', () => {
		expect( getSelectedRates() ).toBe(
			mockWCShippingConfig.shippingLabelData.storedData.selected_rates
		);
	} );

	it( 'should return selected hazmat', () => {
		expect( getSelectedHazmat() ).toBe(
			mockWCShippingConfig.shippingLabelData.storedData.selected_hazmat
		);
	} );

	it( 'should return customs information', () => {
		expect( getCustomsInformation() ).toBe(
			mockWCShippingConfig.shippingLabelData.storedData
				.customs_information
		);
	} );

	it( 'should return plugin relative directory for WooCommerce', () => {
		expect( getPluginRelativeDirectory( true ) ).toBe(
			mockWCShippingConfig.constants.WC_PLUGIN_RELATIVE_DIR
		);
	} );

	it( 'should return plugin relative directory for WCShipping', () => {
		expect( getPluginRelativeDirectory( false ) ).toBe(
			mockWCShippingConfig.constants.WCSHIPPING_RELATIVE_PLUGIN_DIR
		);
	} );
} );
