import { createReducer } from '..';

describe( 'Data store utilities', () => {
	const appendHandler = ( s, a ) => {
		// A handler that appends whatever is sent in the action's "payload" field to the current state.
		return { ...s, ...a.payload };
	};

	it( 'runs handler if action is recognized', () => {
		const defaultState = { foo: 'bar' };
		const reducer = createReducer( defaultState )
			.on( 'APPEND', appendHandler )
			.bind();

		const currentState = { baz: 'quux' };
		const result = reducer( currentState, {
			type: 'APPEND',
			payload: {
				quuz: 'corge',
			},
		} );

		expect( result ).toEqual( { baz: 'quux', quuz: 'corge' } );
	} );

	it( 'returns current state (i.e. does nothing) if action is not recognized', () => {
		const defaultState = { foo: 'bar' };
		const reducer = createReducer( defaultState )
			.on( 'APPEND', appendHandler )
			.bind();

		const currentState = { baz: 'quux' };
		const result = reducer( currentState, {
			type: 'THIS IS NOT A REGISTERED ACTION',
			payload: {
				quuz: 'corge',
			},
		} );

		expect( result ).toEqual( currentState );
	} );

	it( 'acts on default state if current state is empty', () => {
		const defaultState = { foo: 'bar' };
		const reducer = createReducer( defaultState )
			.on( 'APPEND', appendHandler )
			.bind();

		const currentState = undefined;
		const result = reducer( currentState, {
			type: 'APPEND',
			payload: {
				quuz: 'corge',
			},
		} );

		expect( result ).toEqual( { foo: 'bar', quuz: 'corge' } );
	} );

	it( 'returns default state if current state is empty and action is unrecognized', () => {
		const defaultState = { foo: 'bar' };
		const reducer = createReducer( defaultState )
			.on( 'APPEND', appendHandler )
			.bind();

		const currentState = undefined;
		const result = reducer( currentState, {
			type: 'NOT A REGISTERED ACTION',
			payload: {
				quuz: 'corge',
			},
		} );

		expect( result ).toEqual( { foo: 'bar' } );
	} );
} );
