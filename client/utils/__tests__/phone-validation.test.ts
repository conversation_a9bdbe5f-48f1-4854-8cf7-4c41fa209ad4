import { validatePhone } from '../validators';
import { OriginAddress } from 'types';

describe( 'Phone validation', () => {
	const createInput = ( phone: string ) => ( {
		values: { phone } as OriginAddress,
		errors: {},
	} );

	describe( 'validatePhone', () => {
		it( 'should accept valid 10-digit phone numbers', () => {
			const testCases = [
				'1234567890',
				'************',
				'(*************',
				'************',
				'************',
				'+1 ************',
				'1-************',
				'12345678901', // 11 digits starting with 1 (valid US format with country code)
			];

			testCases.forEach( ( phone ) => {
				const result = validatePhone( createInput( phone ) );
				expect( result.errors.phone ).toBeUndefined();
			} );
		} );

		it( 'should reject invalid phone numbers', () => {
			const testCases = [
				'123456789', // 9 digits
				'21234567890', // 11 digits not starting with 1
				'123', // too short
				'', // empty
				'abcdefghij', // letters only (0 digits)
				'12345678901234', // too long (14 digits)
			];

			testCases.forEach( ( phone ) => {
				const result = validatePhone( createInput( phone ) );
				expect( result.errors.phone ).toBe(
					'Please provide a valid phone number.'
				);
			} );
		} );

		it( 'should handle alphanumeric phone numbers', () => {
			const testCases = [
				{ phone: '123ABC4567', valid: false }, // Contains letters - invalid format
				{ phone: '1-800-FLOWERS', valid: false }, // Contains letters - invalid format
				{ phone: '1800FLOWERS', valid: false }, // Contains letters - invalid format
				{ phone: '123-ABC-DEFG', valid: false }, // Contains letters - invalid format
				{ phone: '1234567890ABC', valid: false }, // Contains letters - invalid format
				{ phone: 'ABC1234567890', valid: false }, // Contains letters - invalid format
				{ phone: '123ABC456DEF7890', valid: false }, // Contains letters - invalid format
				{ phone: '1-800-GOT-JUNK', valid: false }, // Contains letters - invalid format
				{ phone: '800-BUY-MORE', valid: false }, // Contains letters - invalid format
			];

			testCases.forEach( ( { phone, valid } ) => {
				const result = validatePhone( createInput( phone ) );

				if ( valid ) {
					expect( result.errors.phone ).toBeUndefined();
				} else {
					expect( result.errors.phone ).toBe(
						'Please provide a valid phone number.'
					);
				}
			} );
		} );

		it( 'should accept valid formatted phone numbers', () => {
			const testCases = [
				'+****************', // Formatted with country code and parentheses
				'(*************', // Formatted with parentheses
				'************', // Formatted with dashes
				'+1-************', // Country code with dashes
				'************', // Formatted with dots
				'************', // Formatted with spaces
				'+1 ************', // Country code with spaces
			];

			testCases.forEach( ( phone ) => {
				const result = validatePhone( createInput( phone ) );
				expect( result.errors.phone ).toBeUndefined();
			} );
		} );

		it( 'should reject phone numbers with invalid characters', () => {
			const testCases = [
				'************#', // Hash symbol
				'*************', // Asterisk
				'************@', // At symbol
				'123/456/7890', // Slashes
				'123_456_7890', // Underscores
				'123|456|7890', // Pipes
				'123\\456\\7890', // Backslashes
				'123<456>7890', // Angle brackets
				'123[456]7890', // Square brackets
				'123{456}7890', // Curly braces
			];

			testCases.forEach( ( phone ) => {
				const result = validatePhone( createInput( phone ) );
				expect( result.errors.phone ).toBe(
					'Please provide a valid phone number.'
				);
			} );
		} );

		it( 'should reject excessively long phone numbers', () => {
			const longPhone = '1'.repeat( 51 ); // 51 characters
			const result = validatePhone( createInput( longPhone ) );
			expect( result.errors.phone ).toBe(
				'Please provide a valid phone number.'
			);
		} );

		it( 'should handle edge cases', () => {
			const testCases = [
				{ phone: '   ', valid: false }, // Only spaces
				{ phone: '************', valid: true }, // All zeros (technically valid format)
				{ phone: '************', valid: true }, // All ones (technically valid format)
				{ phone: '+1', valid: false }, // Just country code
				{ phone: '1-', valid: false }, // Incomplete format
				{ phone: '()', valid: false }, // Empty parentheses
				{ phone: '()-', valid: false }, // Empty parentheses with dash
			];

			testCases.forEach( ( { phone, valid } ) => {
				const result = validatePhone( createInput( phone ) );

				if ( valid ) {
					expect( result.errors.phone ).toBeUndefined();
				} else {
					expect( result.errors.phone ).toBe(
						'Please provide a valid phone number.'
					);
				}
			} );
		} );
	} );
} );
