export const accountSettings = {
	storeOptions: {
		currency_symbol: '$',
		dimension_unit: 'cm',
		weight_unit: 'kg',
		origin_country: 'US',
	},
	purchaseSettings: {
		selected_payment_method_id: ********,
		enabled: true,
		email_receipts: true,
		use_last_service: false,
		use_last_package: true,
		paper_size: 'letter',
	},
	purchaseMeta: {
		can_manage_payments: true,
		can_edit_settings: true,
		master_user_name: 'admin',
		master_user_login: 'admin',
		master_user_wpcom_login: 'gato',
		master_user_email: '<EMAIL>',
		payment_methods: [
			{
				payment_method_id: ********,
				name: 'Sam with 3DS',
				card_type: 'visa',
				card_digits: '3238',
				expiry: '2029-01-31',
			},
			{
				payment_method_id: ********,
				name: 'Second Visa',
				card_type: 'visa',
				card_digits: '5556',
				expiry: '2028-05-31',
			},
			{
				payment_method_id: ********,
				name: '<PERSON> <PERSON><PERSON>',
				card_type: 'visa',
				card_digits: '4242',
				expiry: '2029-10-31',
			},
		],
		add_payment_method_url:
			'https://wordpress.com/me/purchases/add-credit-card',
		warnings: {
			payment_methods: false,
		},
	},
	userMeta: {
		last_box_id: 'large_flat_box',
		last_service_id: 'Priority',
		last_carrier_id: 'usps',
		last_order_completed: false,
	},
	enabledServices: [],
};
