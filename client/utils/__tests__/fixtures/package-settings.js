export const packagesSettings = {
	schema: {
		custom: {
			type: 'array',
			title: 'Box Sizes',
			description:
				'Items will be packed into these boxes based on item dimensions and volume. Outer dimensions will be passed to the delivery service, whereas inner dimensions will be used for packing. Items not fitting into boxes will be packed individually.',
			default: [],
			items: {
				type: 'object',
				title: 'Box',
				required: [
					'name',
					'inner_dimensions',
					'box_weight',
					'max_weight',
				],
				properties: {
					name: {
						type: 'string',
						title: 'Name',
					},
					is_user_defined: {
						type: 'boolean',
					},
					inner_dimensions: {
						type: 'string',
						title: 'Inner Dimensions (L x W x H)',
						pattern:
							'^(\\d+|(?:\\d*\\.\\d+)) x (\\d+|(?:\\d*\\.\\d+)) x (\\d+|(?:\\d*\\.\\d+))$',
					},
					outer_dimensions: {
						type: 'string',
						title: 'Outer Dimensions (L x W x H)',
						pattern:
							'^(\\d+|(?:\\d*\\.\\d+)) x (\\d+|(?:\\d*\\.\\d+)) x (\\d+|(?:\\d*\\.\\d+))$',
					},
					box_weight: {
						type: 'number',
						title: 'Weight of Box (lbs)',
					},
					max_weight: {
						type: 'number',
						title: 'Max Weight (lbs)',
					},
					is_letter: {
						type: 'boolean',
						title: 'Letter',
					},
				},
			},
		},
		predefined: {
			usps: {
				pri_flat_boxes: {
					title: 'USPS Priority Mail Flat Rate Boxes',
					definitions: [
						{
							name: 'Small Flat Rate Box',
							dimensions: '21.91 x 13.65 x 4.13',
							max_weight: 31.75,
							is_letter: false,
							group_id: 'pri_flat_boxes',
							can_ship_international: true,
						},
						{
							inner_dimensions: '27.94 x 21.59 x 13.97',
							outer_dimensions: '28.57 x 22.22 x 15.24',
							box_weight: 0,
							is_flat_rate: true,
							id: 'medium_flat_box_top',
							name: 'Medium Flat Rate Box 1, Top Loading',
							dimensions: {
								inner: '27.94 x 21.59 x 13.97',
								outer: '28.57 x 22.22 x 15.24',
							},
							max_weight: 31.75,
							is_letter: false,
							group_id: 'pri_flat_boxes',
							can_ship_international: true,
						},
					],
				},
				pri_envelopes: {
					title: 'USPS Priority Mail Flat Rate Envelopes',
					definitions: [
						{
							inner_dimensions: '31.75 x 24.13 x 1.27',
							outer_dimensions: '31.75 x 24.13 x 1.27',
							box_weight: 0,
							is_flat_rate: true,
							id: 'flat_envelope',
							name: 'Flat Rate Envelope',
							dimensions: [ '31.75 x 24.13 x 1.27' ],
							max_weight: 31.75,
							is_letter: true,
							group_id: 'pri_envelopes',
							can_ship_international: true,
						},
						{
							inner_dimensions: '38.1 x 24.13 x 1.27',
							outer_dimensions: '38.1 x 24.13 x 1.27',
							box_weight: 0,
							is_flat_rate: true,
							id: 'legal_flat_envelope',
							name: 'Legal Flat Rate Envelope',
							dimensions: [ '38.1 x 24.13 x 1.27' ],
							max_weight: 31.75,
							is_letter: true,
							group_id: 'pri_envelopes',
							can_ship_international: true,
						},
					],
				},
				pri_boxes: {
					title: 'USPS Priority Mail Boxes',
					definitions: [
						{
							inner_dimensions: '95.72 x 15.56 x 12.86',
							outer_dimensions: '95.72 x 15.56 x 12.86',
							box_weight: 0,
							is_flat_rate: false,
							id: 'medium_tube',
							name: 'Priority Mail Medium Tube',
							dimensions: '95.72 x 15.56 x 12.86',
							max_weight: 31.75,
							is_letter: false,
							service_group_ids: [
								'priority',
								'priority_international',
							],
							group_id: 'pri_boxes',
							can_ship_international: true,
						},
						{
							inner_dimensions: '63.5 x 15.24 x 14.92',
							outer_dimensions: '63.5 x 15.24 x 14.92',
							box_weight: 0,
							is_flat_rate: false,
							id: 'small_tube',
							name: 'Priority Mail Small Tube',
							dimensions: '63.5 x 15.24 x 14.92',
							max_weight: 31.75,
							is_letter: false,
							service_group_ids: [
								'priority',
								'priority_international',
							],
							group_id: 'pri_boxes',
							can_ship_international: true,
						},
						{
							inner_dimensions: '19.05 x 13.02 x 36.51',
							outer_dimensions: '19.05 x 13.02 x 36.51',
							box_weight: 0,
							is_flat_rate: false,
							id: 'shoe_box',
							name: 'Priority Mail Shoe Box',
							dimensions: '19.05 x 13.02 x 36.51',
							max_weight: 31.75,
							is_letter: false,
							service_group_ids: [
								'priority',
								'priority_international',
							],
							group_id: 'pri_boxes',
							can_ship_international: true,
						},
					],
				},
				pri_express_envelopes: {
					title: 'USPS Priority Mail Express Flat Rate Envelopes',
					definitions: [
						{
							inner_dimensions: '31.75 x 24.13 x 1.27',
							outer_dimensions: '31.75 x 24.13 x 1.27',
							box_weight: 0,
							is_flat_rate: true,
							id: 'express_flat_envelope',
							name: 'Flat Rate Envelope',
							dimensions: [ '31.75 x 24.13 x 1.27' ],
							max_weight: 31.75,
							is_letter: true,
							group_id: 'pri_express_envelopes',
							can_ship_international: true,
						},
						{
							inner_dimensions: '38.1 x 24.13 x 1.27',
							outer_dimensions: '38.1 x 24.13 x 1.27',
							box_weight: 0,
							is_flat_rate: true,
							id: 'express_legal_flat_envelope',
							name: 'Legal Flat Rate Envelope',
							dimensions: [ '38.1 x 24.13 x 1.27' ],
							max_weight: 31.75,
							is_letter: true,
							group_id: 'pri_express_envelopes',
							can_ship_international: true,
						},
					],
				},
				pri_express_boxes: {
					title: 'USPS Priority Mail Express Boxes',
					definitions: [
						{
							inner_dimensions: '38.73 x 31.43 x 7.62',
							outer_dimensions: '39.69 x 31.59 x 7.94',
							box_weight: 0,
							is_flat_rate: false,
							id: 'express_box',
							name: 'Priority Mail Express Box',
							dimensions: {
								inner: '38.73 x 31.43 x 7.62',
								outer: '39.69 x 31.59 x 7.94',
							},
							max_weight: 31.75,
							is_letter: false,
							service_group_ids: [
								'priority_exp',
								'priority_express_international',
							],
							group_id: 'pri_express_boxes',
							can_ship_international: true,
						},
					],
				},
			},
			fedex: {
				// copy of USPS
				pri_flat_boxes: {
					title: 'USPS Priority Mail Flat Rate Boxes',
					definitions: [
						{
							name: 'Small Flat Rate Box',
							dimensions: '21.91 x 13.65 x 4.13',
							max_weight: 31.75,
							is_letter: false,
							group_id: 'pri_flat_boxes',
							can_ship_international: true,
						},
						{
							inner_dimensions: '27.94 x 21.59 x 13.97',
							outer_dimensions: '28.57 x 22.22 x 15.24',
							box_weight: 0,
							is_flat_rate: true,
							id: 'medium_flat_box_top',
							name: 'Medium Flat Rate Box 1, Top Loading',
							dimensions: {
								inner: '27.94 x 21.59 x 13.97',
								outer: '28.57 x 22.22 x 15.24',
							},
							max_weight: 31.75,
							is_letter: false,
							group_id: 'pri_flat_boxes',
							can_ship_international: true,
						},
					],
				},
				pri_envelopes: {
					title: 'USPS Priority Mail Flat Rate Envelopes',
					definitions: [
						{
							inner_dimensions: '31.75 x 24.13 x 1.27',
							outer_dimensions: '31.75 x 24.13 x 1.27',
							box_weight: 0,
							is_flat_rate: true,
							id: 'flat_envelope',
							name: 'Flat Rate Envelope',
							dimensions: [ '31.75 x 24.13 x 1.27' ],
							max_weight: 31.75,
							is_letter: true,
							group_id: 'pri_envelopes',
							can_ship_international: true,
						},
						{
							inner_dimensions: '38.1 x 24.13 x 1.27',
							outer_dimensions: '38.1 x 24.13 x 1.27',
							box_weight: 0,
							is_flat_rate: true,
							id: 'legal_flat_envelope',
							name: 'Legal Flat Rate Envelope',
							dimensions: [ '38.1 x 24.13 x 1.27' ],
							max_weight: 31.75,
							is_letter: true,
							group_id: 'pri_envelopes',
							can_ship_international: true,
						},
					],
				},
				pri_boxes: {
					title: 'USPS Priority Mail Boxes',
					definitions: [
						{
							inner_dimensions: '95.72 x 15.56 x 12.86',
							outer_dimensions: '95.72 x 15.56 x 12.86',
							box_weight: 0,
							is_flat_rate: false,
							id: 'medium_tube',
							name: 'Priority Mail Medium Tube',
							dimensions: '95.72 x 15.56 x 12.86',
							max_weight: 31.75,
							is_letter: false,
							service_group_ids: [
								'priority',
								'priority_international',
							],
							group_id: 'pri_boxes',
							can_ship_international: true,
						},
						{
							inner_dimensions: '63.5 x 15.24 x 14.92',
							outer_dimensions: '63.5 x 15.24 x 14.92',
							box_weight: 0,
							is_flat_rate: false,
							id: 'small_tube',
							name: 'Priority Mail Small Tube',
							dimensions: '63.5 x 15.24 x 14.92',
							max_weight: 31.75,
							is_letter: false,
							service_group_ids: [
								'priority',
								'priority_international',
							],
							group_id: 'pri_boxes',
							can_ship_international: true,
						},
						{
							inner_dimensions: '19.05 x 13.02 x 36.51',
							outer_dimensions: '19.05 x 13.02 x 36.51',
							box_weight: 0,
							is_flat_rate: false,
							id: 'shoe_box',
							name: 'Priority Mail Shoe Box',
							dimensions: '19.05 x 13.02 x 36.51',
							max_weight: 31.75,
							is_letter: false,
							service_group_ids: [
								'priority',
								'priority_international',
							],
							group_id: 'pri_boxes',
							can_ship_international: true,
						},
					],
				},
				pri_express_envelopes: {
					title: 'USPS Priority Mail Express Flat Rate Envelopes',
					definitions: [
						{
							inner_dimensions: '31.75 x 24.13 x 1.27',
							outer_dimensions: '31.75 x 24.13 x 1.27',
							box_weight: 0,
							is_flat_rate: true,
							id: 'express_flat_envelope',
							name: 'Flat Rate Envelope',
							dimensions: [ '31.75 x 24.13 x 1.27' ],
							max_weight: 31.75,
							is_letter: true,
							group_id: 'pri_express_envelopes',
							can_ship_international: true,
						},
						{
							inner_dimensions: '38.1 x 24.13 x 1.27',
							outer_dimensions: '38.1 x 24.13 x 1.27',
							box_weight: 0,
							is_flat_rate: true,
							id: 'express_legal_flat_envelope',
							name: 'Legal Flat Rate Envelope',
							dimensions: [ '38.1 x 24.13 x 1.27' ],
							max_weight: 31.75,
							is_letter: true,
							group_id: 'pri_express_envelopes',
							can_ship_international: true,
						},
					],
				},
				pri_express_boxes: {
					title: 'USPS Priority Mail Express Boxes',
					definitions: [
						{
							inner_dimensions: '38.73 x 31.43 x 7.62',
							outer_dimensions: '39.69 x 31.59 x 7.94',
							box_weight: 0,
							is_flat_rate: false,
							id: 'express_box',
							name: 'Priority Mail Express Box',
							dimensions: {
								inner: '38.73 x 31.43 x 7.62',
								outer: '39.69 x 31.59 x 7.94',
							},
							max_weight: 31.75,
							is_letter: false,
							service_group_ids: [
								'priority_exp',
								'priority_express_international',
							],
							group_id: 'pri_express_boxes',
							can_ship_international: true,
						},
					],
				},
			},
		},
	},
	packages: {
		custom: [
			{
				id: 'custom_box_1',
				name: 'Custom one!',
				inner_dimensions: '22 x 22 x 25',
				is_user_defined: true,
			},
			{
				id: 'custom_box_2',
				name: 'Some custom package!',
				inner_dimensions: '100 x 2 x 8',
				is_user_defined: true,
			},
			{
				id: 'custom_box_3',
				name: 'Uniq',
				inner_dimensions: '10 x 10 x 10',
				is_user_defined: true,
			},
		],
		predefined: {
			fedex: [
				null,
				'FedExEnvelope',
				'FedExPak',
				'FedExSmallBox1',
				'FedExSmallBox2',
				'FedExMediumBox1',
				'FedExMediumBox2',
				'FedExLargeBox1',
				'FedExLargeBox2',
				'FedExExtraLargeBox1',
				'FedExExtraLargeBox2',
				'FedExTube',
				'FedEx10kgBox',
				'FedEx25kgBox',
			],
			usps: [
				null,
				'flat_envelope',
				'legal_flat_envelope',
				'padded_flat_envelope',
				'window_flat_envelope',
				'small_flat_envelope',
				'medium_tube',
				'small_tube',
				'shoe_box',
				'priority_4',
				'priority_7',
				'priority_1095',
				'priority_1096L',
				'priority_1097',
				'priority_dvd',
				'priority_tyvek_envelope',
				'large_flat_box',
				'dvd_flat',
				'large_video_flat',
				'express_flat_envelope',
				'express_legal_flat_envelope',
				'express_padded_flat_envelope',
				'express_box',
				'express_box_1',
				'express_box_2',
				'express_medium_tube',
				'express_small_tube',
				'express_tyvek_envelope',
				'express_window_envelope',
			],
		},
	},
};
