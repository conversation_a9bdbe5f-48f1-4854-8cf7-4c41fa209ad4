export const mockWCShippingConfig = {
	shippingLabelData: {
		storeOptions: {
			weight_unit: 'kg',
			currency_symbol: '$',
			dimension_unit: 'cm',
		},
		storedData: {
			selected_rates: [ 'rate1', 'rate2' ],
			selected_hazmat: [ 'hazmat1', 'hazmat2' ],
			customs_information: { info: 'some info' },
		},
	},
	accountSettings: {
		userMeta: {
			last_order_completed: true,
		},
	},
	constants: {
		WC_PLUGIN_RELATIVE_DIR: 'wc-plugin-dir',
		WCSHIPPING_RELATIVE_PLUGIN_DIR: 'wc-shipping-plugin-dir',
	},
};

export default mockWCShippingConfig;
