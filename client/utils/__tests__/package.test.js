import { getCarrierPackages } from 'utils';
import { packagesSettings } from 'utils/__tests__/fixtures/package-settings';

describe( 'Package utilities', () => {
	it( 'Should return the correct carrier packages', () => {
		const config = {
			packagesSettings,
		};

		expect(
			Object.keys(
				getCarrierPackages(
					{
						usps: [ 'medium_flat_box_top' ],
					},
					config
				).usps
			)
		).toHaveLength( 1 );

		expect(
			getCarrierPackages(
				{
					usps: [ 'medium_flat_box_top', 'medium_tube' ],
				},
				config
			).usps
		).toHaveLength( 2 );

		expect(
			getCarrierPackages(
				{
					usps: [ 'medium_flat_box_top', 'medium_tube' ],
				},
				config
			).usps[ 1 ]
		).toStrictEqual( {
			boxWeight: 0,
			canShipInternational: true,
			carrierId: 'usps',
			dimensions: '95.72 x 15.56 x 12.86',
			groupId: 'pri_boxes',
			id: 'medium_tube',
			innerDimensions: '95.72 x 15.56 x 12.86',
			isFlatRate: false,
			isLetter: false,
			maxWeight: 31.75,
			name: 'Priority Mail Medium Tube',
			outerDimensions: '95.72 x 15.56 x 12.86',
			serviceGroupIds: [ 'priority', 'priority_international' ],
		} );
	} );
} );
