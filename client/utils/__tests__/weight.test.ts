import { convertWeightToUnit, WEIGHT_UNITS } from '../weight';

describe( 'convertWeightToUnit', () => {
	it( 'should convert oz to lb correctly', () => {
		expect(
			convertWeightToUnit( 16, WEIGHT_UNITS.OZ, WEIGHT_UNITS.LBS )
		).toBeCloseTo( 1 );
	} );

	it( 'should convert lb to oz correctly', () => {
		expect(
			convertWeightToUnit( 1, WEIGHT_UNITS.LBS, WEIGHT_UNITS.OZ )
		).toBeCloseTo( 16 );
	} );

	it( 'should convert kg to lb correctly', () => {
		expect(
			convertWeightToUnit( 1, WEIGHT_UNITS.KG, WEIGHT_UNITS.LBS )
		).toBeCloseTo( 2.20462 );
	} );

	it( 'should convert lb to kg correctly', () => {
		expect(
			convertWeightToUnit( 2.20462, WEIGHT_UNITS.LBS, WEIGHT_UNITS.KG )
		).toBeCloseTo( 1 );
	} );

	it( 'should convert g to oz correctly', () => {
		expect(
			convertWeightToUnit( 28.3495, WEIGHT_UNITS.G, WEIGHT_UNITS.OZ )
		).toBeCloseTo( 1 );
	} );

	it( 'should convert oz to g correctly', () => {
		expect(
			convertWeightToUnit( 1, WEIGHT_UNITS.OZ, WEIGHT_UNITS.G )
		).toBeCloseTo( 28.3495 );
	} );

	it( 'should handle same unit conversion', () => {
		expect(
			convertWeightToUnit( 5, WEIGHT_UNITS.KG, WEIGHT_UNITS.KG )
		).toBe( 5 );
		expect(
			convertWeightToUnit( 10, WEIGHT_UNITS.LBS, WEIGHT_UNITS.LBS )
		).toBe( 10 );
	} );

	it( 'should handle zero weight', () => {
		expect(
			convertWeightToUnit( 0, WEIGHT_UNITS.KG, WEIGHT_UNITS.LBS )
		).toBe( 0 );
		expect(
			convertWeightToUnit( 0, WEIGHT_UNITS.OZ, WEIGHT_UNITS.G )
		).toBe( 0 );
	} );

	it( 'should handle decimal weights', () => {
		expect(
			convertWeightToUnit( 0.5, WEIGHT_UNITS.KG, WEIGHT_UNITS.G )
		).toBeCloseTo( 500 );
		expect(
			convertWeightToUnit( 1.5, WEIGHT_UNITS.LBS, WEIGHT_UNITS.OZ )
		).toBeCloseTo( 24 );
	} );

	it( 'should handle large numbers', () => {
		expect(
			convertWeightToUnit( 1000, WEIGHT_UNITS.KG, WEIGHT_UNITS.LBS )
		).toBeCloseTo( 2204.62 );
		expect(
			convertWeightToUnit( 5000, WEIGHT_UNITS.G, WEIGHT_UNITS.KG )
		).toBeCloseTo( 5 );
	} );

	it( 'should handle very small numbers', () => {
		expect(
			convertWeightToUnit( 0.001, WEIGHT_UNITS.KG, WEIGHT_UNITS.G )
		).toBeCloseTo( 1 );
		expect(
			convertWeightToUnit( 0.0625, WEIGHT_UNITS.LBS, WEIGHT_UNITS.OZ )
		).toBeCloseTo( 1 );
	} );

	it( 'should handle negative numbers', () => {
		expect(
			convertWeightToUnit( -1, WEIGHT_UNITS.KG, WEIGHT_UNITS.LBS )
		).toBeCloseTo( -2.20462 );
		expect(
			convertWeightToUnit( -16, WEIGHT_UNITS.OZ, WEIGHT_UNITS.LBS )
		).toBeCloseTo( -1 );
	} );
} );
