import { addressToString, formatAddressFields } from 'utils';

describe( 'Order utilities', () => {
	describe( 'addressToString', () => {
		it( 'Should properly create address strings from address object', () => {
			expect(
				addressToString( {
					address1: '123 Main St',
					address2: 'Suite 101',
					city: 'San Francisco',
					state: 'CA',
					postcode: '94105',
					country: 'US',
				} )
			).toEqual( '123 Main St, Suite 101, San Francisco, CA 94105, US' );

			expect(
				addressToString( {
					address1: '123 Main St',
					address2: '',
					city: 'San Francisco',
					state: 'CA',
					postcode: '94105',
					country: 'US',
				} )
			).toEqual( '123 Main St, San Francisco, CA 94105, US' );
		} );

		it( 'should handle null address', () => {
			const result = addressToString( null );
			expect( result ).toBe( '' );
		} );

		it( 'should handle undefined address', () => {
			const result = addressToString( undefined );
			expect( result ).toBe( '' );
		} );

		it( 'should handle missing city gracefully', () => {
			const result = addressToString( {
				address: '123 Main St',
				city: '',
				state: 'CA',
				postcode: '94105',
				country: 'US',
			} as Parameters< typeof addressToString >[ 0 ] );
			expect( result ).toBe( '123 Main St, CA 94105, US' );
		} );

		it( 'should handle missing state gracefully', () => {
			const result = addressToString( {
				address: '123 Main St',
				city: 'San Francisco',
				state: '',
				postcode: '94105',
				country: 'US',
			} as Parameters< typeof addressToString >[ 0 ] );
			expect( result ).toBe( '123 Main St, San Francisco, 94105, US' );
		} );

		it( 'should handle missing postcode gracefully', () => {
			const result = addressToString( {
				address: '123 Main St',
				city: 'San Francisco',
				state: 'CA',
				postcode: '',
				country: 'US',
			} as Parameters< typeof addressToString >[ 0 ] );
			expect( result ).toBe( '123 Main St, San Francisco, CA, US' );
		} );

		it( 'should handle all missing fields gracefully', () => {
			const result = addressToString( {
				address: '',
				city: '',
				state: '',
				postcode: '',
				country: '',
			} as Parameters< typeof addressToString >[ 0 ] );
			expect( result ).toBe( '' );
		} );

		it( 'should handle address with only country', () => {
			const result = addressToString( {
				address: '',
				city: '',
				state: '',
				postcode: '',
				country: 'US',
			} as Parameters< typeof addressToString >[ 0 ] );
			expect( result ).toBe( 'US' );
		} );

		it( 'should handle empty string values', () => {
			const result = addressToString( {
				address: '',
				address1: '',
				address2: '',
				city: '',
				state: '',
				postcode: '',
				country: 'US',
			} );
			expect( result ).toBe( 'US' );
		} );
	} );

	it( 'Should proper transform address object to address form format', () => {
		expect(
			formatAddressFields( {
				id: 'someid',
				email: '<EMAIL>',
				company: 'WooCommerce',
				address1: '123 Main St',
				address2: 'Suite 101',
				city: 'San Francisco',
				state: 'CA',
				postcode: '94105',
				country: 'US',
				firstName: 'John',
				lastName: 'Doe',
				phone: '************',
				isVerified: true,
			} )
		).toEqual( {
			id: 'someid',
			company: 'WooCommerce',
			address: '123 Main St, Suite 101',
			city: 'San Francisco',
			state: 'CA',
			postcode: '94105',
			country: 'US',
			name: 'John Doe',
			phone: '************',
			email: '<EMAIL>',
			isVerified: true,
		} );
		expect(
			formatAddressFields( {
				id: 'someid',
				email: '<EMAIL>',
				company: 'WooCommerce',
				address: '123 Main St, Suite 101',
				address1: '123 Main St',
				address2: '',
				city: 'San Francisco',
				state: 'CA',
				postcode: '94105',
				country: 'US',
				firstName: 'John',
				lastName: 'Doe',
				phone: '************',
				isVerified: true,
			} )
		).toEqual( {
			id: 'someid',
			company: 'WooCommerce',
			address: '123 Main St, Suite 101',
			city: 'San Francisco',
			state: 'CA',
			postcode: '94105',
			country: 'US',
			name: 'John Doe',
			phone: '************',
			email: '<EMAIL>',
			isVerified: true,
		} );
	} );
} );
