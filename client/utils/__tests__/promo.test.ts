import {
	getPromoDiscount,
	applyPromo,
	maybeDecrementPromoRemaining,
} from '../promo';
import { getPromotion } from '../config';
import { CamelCaseType, ResponseLabel } from 'types';

const mockGetPromotion = jest.mocked( getPromotion );

jest.mock( '../config' );

describe( 'promo utils', () => {
	const mockPromo = {
		id: 'promo123',
		carrier: 'usps',
		endDate: new Date().toJSON(),
		remaining: 5,
		discountType: 'percentage' as const,
		discountAmount: 20,
	};

	beforeEach( () => {
		jest.clearAllMocks();
	} );

	describe( 'getPromoDiscount', () => {
		it( 'should return undefined when no promo is configured', () => {
			mockGetPromotion.mockReturnValue( undefined );

			const result = getPromoDiscount( 100, 'promo123' );

			expect( result ).toBeUndefined();
		} );

		it( 'should return undefined when promoId is not provided', () => {
			mockGetPromotion.mockReturnValue( mockPromo );

			const result = getPromoDiscount( 100 );

			expect( result ).toBeUndefined();
		} );

		it( 'should return undefined when promoId does not match configured promo', () => {
			mockGetPromotion.mockReturnValue( mockPromo );

			const result = getPromoDiscount( 100, 'different-promo' );

			expect( result ).toBeUndefined();
		} );

		it( 'should calculate percentage discount correctly', () => {
			mockGetPromotion.mockReturnValue( mockPromo );

			const result = getPromoDiscount( 100, 'promo123' );

			expect( result ).toBe( 20 ); // 20% of 100
		} );

		it( 'should calculate percentage discount for different rates', () => {
			mockGetPromotion.mockReturnValue( {
				...mockPromo,
				discountAmount: 15,
			} );

			const result = getPromoDiscount( 50, 'promo123' );

			expect( result ).toBe( 7.5 ); // 15% of 50
		} );

		it( 'should return fixed discount when rate is higher than discount amount', () => {
			mockGetPromotion.mockReturnValue( {
				...mockPromo,
				discountType: 'fixed',
				discountAmount: 10,
			} );

			const result = getPromoDiscount( 50, 'promo123' );

			expect( result ).toBe( 10 ); // Min of 50 and 10
		} );

		it( 'should return rate amount when fixed discount is higher than rate', () => {
			mockGetPromotion.mockReturnValue( {
				...mockPromo,
				discountType: 'fixed',
				discountAmount: 100,
			} );

			const result = getPromoDiscount( 50, 'promo123' );

			expect( result ).toBe( 50 ); // Min of 50 and 100
		} );

		it( 'should handle zero rate', () => {
			mockGetPromotion.mockReturnValue( mockPromo );

			const result = getPromoDiscount( 0, 'promo123' );

			expect( result ).toBe( 0 );
		} );
	} );

	describe( 'applyPromo', () => {
		it( 'should return original rate when no discount is available', () => {
			mockGetPromotion.mockReturnValue( undefined );

			const result = applyPromo( 100, 'promo123' );

			expect( result ).toBe( 100 );
		} );

		it( 'should apply percentage discount correctly', () => {
			mockGetPromotion.mockReturnValue( mockPromo );

			const result = applyPromo( 100, 'promo123' );

			expect( result ).toBe( 80 ); // 100 - 20
		} );

		it( 'should apply fixed discount correctly', () => {
			mockGetPromotion.mockReturnValue( {
				...mockPromo,
				discountType: 'fixed',
				discountAmount: 15,
			} );

			const result = applyPromo( 100, 'promo123' );

			expect( result ).toBe( 85 ); // 100 - 15
		} );

		it( 'should not make rate negative with large fixed discount', () => {
			mockGetPromotion.mockReturnValue( {
				...mockPromo,
				discountType: 'fixed',
				discountAmount: 150,
			} );

			const result = applyPromo( 100, 'promo123' );

			expect( result ).toBe( 0 ); // 100 - 100 (capped at rate amount)
		} );

		it( 'should return original rate when promoId does not match', () => {
			mockGetPromotion.mockReturnValue( mockPromo );

			const result = applyPromo( 100, 'different-promo' );

			expect( result ).toBe( 100 );
		} );

		it( 'should return original rate when no promoId is provided', () => {
			mockGetPromotion.mockReturnValue( mockPromo );

			const result = applyPromo( 100 );

			expect( result ).toBe( 100 );
		} );

		it( 'should handle decimal rates correctly', () => {
			mockGetPromotion.mockReturnValue( {
				...mockPromo,
				discountAmount: 25,
			} );

			const result = applyPromo( 15.99, 'promo123' );

			expect( result ).toBe( 11.9925 ); // 15.99 - (15.99 * 0.25)
		} );
	} );

	describe( 'maybeDecrementPromoRemaining', () => {
		const mockLabel = {} as CamelCaseType< ResponseLabel >;

		it( 'should do nothing if label.promoId is missing', () => {
			mockGetPromotion.mockReturnValue( mockPromo );

			maybeDecrementPromoRemaining( mockLabel );

			expect( mockGetPromotion()?.remaining ).toBe( 5 );
		} );

		it( 'should do nothing if promo.remaining <= 0', () => {
			mockGetPromotion.mockReturnValue( { ...mockPromo, remaining: 0 } );

			maybeDecrementPromoRemaining( {
				...mockLabel,
				promoId: 'promo123',
			} );

			expect( mockGetPromotion()?.remaining ).toBe( 0 );
		} );

		it( 'should decrement promo.remaining if all conditions are met', () => {
			mockGetPromotion.mockReturnValue( { ...mockPromo, remaining: 3 } );

			maybeDecrementPromoRemaining( {
				...mockLabel,
				promoId: 'promo123',
			} );

			expect( mockGetPromotion()?.remaining ).toBe( 2 );
		} );
	} );
} );
