import { getDisplayDate } from '../date';
import { isDateValid } from '../date';

// Only mock i18n functions, not the date functions
jest.mock( '@wordpress/i18n', () => ( {
	__: jest.fn( ( text ) => text ),
	sprintf: jest.fn( ( format, ...args ) =>
		format.replace( /%s/g, () => args.shift() )
	),
} ) );

describe( 'getDisplayDate', () => {
	// Store original Date implementation
	const RealDate = global.Date;

	// Setup fixed date for testing
	const FIXED_DATE = new Date( '2023-05-15T12:00:00Z' ); // Monday, May 15, 2023

	beforeEach( () => {
		// Reset mocks
		jest.clearAllMocks();

		// Mock Date to return fixed date
		global.Date = class extends RealDate {
			constructor( date?: string | number | Date ) {
				if ( date ) {
					super( date );
				} else {
					super( FIXED_DATE );
				}
			}

			// Ensure static methods work correctly
			static now() {
				return FIXED_DATE.getTime();
			}
		} as typeof global.Date;
	} );

	afterEach( () => {
		// Restore original Date
		global.Date = RealDate;
	} );

	it( 'should return "Today" when the date is today', () => {
		const today = new Date( FIXED_DATE );
		expect( getDisplayDate( today ) ).toContain( 'Today' );
	} );

	it( 'should return "Tomorrow" when the date is tomorrow', () => {
		const tomorrow = new Date( FIXED_DATE );
		tomorrow.setDate( tomorrow.getDate() + 1 );

		expect( getDisplayDate( tomorrow ) ).toContain( 'Tomorrow' );
	} );

	it( 'should return just the formatted date for other dates', () => {
		const futureDate = new Date( FIXED_DATE );
		futureDate.setDate( futureDate.getDate() + 5 ); // 5 days in the future

		const result = getDisplayDate( futureDate );

		expect( result ).not.toContain( 'Today' );
		expect( result ).not.toContain( 'Tomorrow' );
	} );

	it( 'should handle past dates', () => {
		const pastDate = new Date( FIXED_DATE );
		pastDate.setDate( pastDate.getDate() - 5 ); // 5 days in the past

		const result = getDisplayDate( pastDate );

		expect( result ).not.toContain( 'Today' );
		expect( result ).not.toContain( 'Tomorrow' );
	} );
} );

describe( 'isDateValid', () => {
	it( 'should return true for valid ISO date strings', () => {
		expect( isDateValid( '2023-05-15T12:00:00Z' ) ).toBe( true );
		expect( isDateValid( '2023-05-15' ) ).toBe( true );
	} );

	it( 'should return true for valid date strings in other formats', () => {
		expect( isDateValid( 'May 15, 2023' ) ).toBe( true );
		expect( isDateValid( '2023/05/15' ) ).toBe( true );
	} );

	it( 'should return false for invalid date strings', () => {
		expect( isDateValid( 'not a date' ) ).toBe( false );
		expect( isDateValid( '2023-13-45' ) ).toBe( false ); // invalid month and day
		expect( isDateValid( '' ) ).toBe( false );
	} );

	it( 'should return false for invalid date objects that result in NaN', () => {
		expect( isDateValid( 'Invalid Date' ) ).toBe( false );
	} );
} );
