import {
	getCurrentOrderShipments,
	getNoneSelectedShipmentItems,
	normalizeSubItems,
	removeEmptyShipments,
	normalizeShipments,
} from '../shipment';
import * as orderItemsUtils from '../../order-items';
import * as configUtils from '../../config';

// Mock the dependencies
jest.mock( '../../order-items' );
jest.mock( '../../config' );

describe( 'shipment utilities', () => {
	// Common test data
	const mockOrderItems = [
		{ id: 1, name: 'Product 1', quantity: 2, price: '10.00', subItems: [] },
		{ id: 2, name: 'Product 2', quantity: 1, price: '20.00', subItems: [] },
		{ id: 3, name: 'Product 3', quantity: 3, price: '15.00', subItems: [] },
	];

	const mockSubItems = [
		{ id: '1-sub-0', parentId: 1, quantity: 1, subItems: [] },
		{ id: '1-sub-1', parentId: 1, quantity: 1, subItems: [] },
	];

	const mockConfig = {
		shipments: '{}',
		order: {
			line_items: mockOrderItems,
		},
	};

	beforeEach( () => {
		jest.clearAllMocks();

		// Mock order-items utility functions
		orderItemsUtils.getSubItems.mockImplementation( ( item ) =>
			item.quantity > 1 ? mockSubItems : []
		);
		orderItemsUtils.isSubItem.mockImplementation(
			( item ) => typeof item.id === 'string' && item.id.includes( 'sub' )
		);
		orderItemsUtils.getParentIdFromSubItemId.mockImplementation( ( id ) =>
			typeof id === 'string' && id.includes( 'sub' )
				? parseInt( id.split( '-sub-' )[ 0 ], 10 )
				: parseInt( id.toString(), 10 )
		);
		orderItemsUtils.getSubItemId.mockImplementation(
			( item, index ) => `${ item.id }-sub-${ index }`
		);
		orderItemsUtils.createSubItemOfCount.mockImplementation(
			( count, item ) =>
				new Array( count ).fill( 1 ).map( ( _, index ) => ( {
					...item,
					id: `${ item.id }-sub-${ index }`,
					parentId: item.id,
					quantity: 1,
					subItems: [],
				} ) )
		);

		// Mock config utility
		configUtils.getConfig.mockReturnValue( mockConfig );
	} );

	describe( 'getCurrentOrderShipments', () => {
		it( 'should return default shipment with all order items when no shipments exist', () => {
			const result = getCurrentOrderShipments();

			expect( result ).toEqual( {
				0: [
					{
						id: 1,
						name: 'Product 1',
						quantity: 2,
						price: '10.00',
						subItems: orderItemsUtils.getSubItems( {
							id: 1,
							name: 'Product 1',
							quantity: 2,
							price: '10.00',
						} ),
					},
					{
						id: 2,
						name: 'Product 2',
						quantity: 1,
						price: '20.00',
						subItems: orderItemsUtils.getSubItems( {
							id: 2,
							name: 'Product 2',
							quantity: 1,
							price: '20.00',
						} ),
					},
					{
						id: 3,
						name: 'Product 3',
						quantity: 3,
						price: '15.00',
						subItems: orderItemsUtils.getSubItems( {
							id: 3,
							name: 'Product 3',
							quantity: 3,
							price: '15.00',
						} ),
					},
				],
			} );
		} );

		it( 'should handle empty shipments gracefully', () => {
			const configWithEmptyShipments = {
				...mockConfig,
				shipments: '{}',
			};
			configUtils.getConfig.mockReturnValue( configWithEmptyShipments );

			const result = getCurrentOrderShipments();

			expect( result[ 0 ] ).toHaveLength( 3 );
			expect(
				result[ 0 ].map( ( item ) => ( {
					...item,
					subItems: orderItemsUtils.getSubItems( item ),
				} ) )
			).toEqual( [
				{
					id: 1,
					name: 'Product 1',
					quantity: 2,
					price: '10.00',
					subItems: orderItemsUtils.getSubItems( {
						id: 1,
						name: 'Product 1',
						quantity: 2,
						price: '10.00',
					} ),
				},
				{
					id: 2,
					name: 'Product 2',
					quantity: 1,
					price: '20.00',
					subItems: orderItemsUtils.getSubItems( {
						id: 2,
						name: 'Product 2',
						quantity: 1,
						price: '20.00',
					} ),
				},
				{
					id: 3,
					name: 'Product 3',
					quantity: 3,
					price: '15.00',
					subItems: orderItemsUtils.getSubItems( {
						id: 3,
						name: 'Product 3',
						quantity: 3,
						price: '15.00',
					} ),
				},
			] );
		} );

		it( 'should handle malformed shipments gracefully', () => {
			const consoleWarnSpy = jest
				.spyOn( console, 'warn' )
				.mockImplementation( () => {
					// Do nothing
				} );

			const configWithInvalidJSON = {
				...mockConfig,
				shipments: '{ invalid json }',
			};
			configUtils.getConfig.mockReturnValue( configWithInvalidJSON );

			const result = getCurrentOrderShipments();

			// Should log the JSON parsing error
			expect( consoleWarnSpy ).toHaveBeenCalledWith(
				expect.any( SyntaxError )
			);

			// The function should handle malformed JSON gracefully
			expect( result[ 0 ] ).toHaveLength( 3 );
			expect( result[ 0 ] ).toEqual( [
				{
					id: 1,
					name: 'Product 1',
					quantity: 2,
					price: '10.00',
					subItems: orderItemsUtils.getSubItems( {
						id: 1,
						name: 'Product 1',
						quantity: 2,
						price: '10.00',
						subItems: [],
					} ),
				},
				{
					id: 2,
					name: 'Product 2',
					quantity: 1,
					price: '20.00',
					subItems: orderItemsUtils.getSubItems( {
						id: 2,
						name: 'Product 2',
						quantity: 1,
						price: '20.00',
						subItems: [],
					} ),
				},
				{
					id: 3,
					name: 'Product 3',
					quantity: 3,
					price: '15.00',
					subItems: orderItemsUtils.getSubItems( {
						id: 3,
						name: 'Product 3',
						quantity: 3,
						price: '15.00',
						subItems: [],
					} ),
				},
			] );

			consoleWarnSpy.mockRestore();
		} );

		it( 'should handle existing shipments with sub-items', () => {
			const configWithShipments = {
				...mockConfig,
				shipments: JSON.stringify( {
					1: [
						{
							id: '1-sub-0',
							parentId: 1,
							quantity: 1,
							subItems: [],
						},
					],
				} ),
			};
			orderItemsUtils.isSubItem.mockImplementation(
				( item ) => item.id === '1-sub-0'
			);
			orderItemsUtils.getParentIdFromSubItemId.mockImplementation(
				( id ) => ( id === '1-sub-0' ? 1 : parseInt( id, 10 ) )
			);
			orderItemsUtils.getSubItems.mockImplementation( ( item ) =>
				item.quantity > 1 ? mockSubItems : []
			);
			configUtils.getConfig.mockReturnValue( configWithShipments );

			const result = getCurrentOrderShipments();

			// Should process existing shipments and fill in missing items
			// Then adds any items not already assigned to shipment 0
			expect( result[ 0 ].length ).toBeGreaterThan( 0 );

			// Sub-items get converted to their parent item structure
			// The sub-item '1-sub-0' should be converted to its parent form
			const convertedItem = result[ 0 ].find(
				( item ) => item.id === 1 || item.id === '1-sub-0'
			);
			expect( convertedItem ).toBeDefined();

			// Items 2 and 3 should also be in shipment 0 since they weren't assigned elsewhere
			const item2 = result[ 0 ].find( ( item ) => item.id === 2 );
			const item3 = result[ 0 ].find( ( item ) => item.id === 3 );
			expect( item2 ).toBeDefined();
			expect( item3 ).toBeDefined();
		} );

		it( 'should add items not in any shipment to shipment 0', () => {
			const configWithShipments = {
				...mockConfig,
				shipments: JSON.stringify( {
					1: [ { id: 1, quantity: 2 } ],
				} ),
			};
			configUtils.getConfig.mockReturnValue( configWithShipments );

			const result = getCurrentOrderShipments();

			// Items 2 and 3 should be added to shipment 0
			expect( result[ 0 ] ).toHaveLength( 2 );
			expect( result[ 0 ].map( ( item ) => item.id ) ).toEqual( [
				2, 3,
			] );
		} );

		it( 'should accept custom config parameter', () => {
			const customConfig = {
				shipments: '{}',
				order: {
					line_items: [
						{ id: 100, name: 'Custom Product', quantity: 1 },
					],
				},
			};

			const result = getCurrentOrderShipments( customConfig );

			expect( result ).toEqual( {
				0: [
					{
						id: 100,
						name: 'Custom Product',
						quantity: 1,
						subItems: orderItemsUtils.getSubItems( {
							id: 100,
							name: 'Custom Product',
							quantity: 1,
						} ),
					},
				],
			} );
		} );
	} );

	describe( 'getNoneSelectedShipmentItems', () => {
		const shipments = {
			0: [
				{ id: 1, quantity: 2, subItems: [] },
				{ id: 2, quantity: 1, subItems: [] },
			],
			1: [ { id: 3, quantity: 3, subItems: [] } ],
		};

		it( 'should return all items when no selections are made', () => {
			const selections = {};

			const result = getNoneSelectedShipmentItems(
				shipments,
				selections
			);

			expect( result ).toEqual( {
				0: [
					{ id: 1, quantity: 1, subItems: [] },
					{ id: 2, quantity: 1, subItems: [] },
				],
				1: [ { id: 3, quantity: 1, subItems: [] } ],
			} );
		} );

		it( 'should filter out selected items from shipments', () => {
			const selections = {
				0: [ { id: 1, quantity: 2 } ],
			};

			const result = getNoneSelectedShipmentItems(
				shipments,
				selections
			);

			expect( result ).toEqual( {
				0: [ { id: 2, quantity: 1, subItems: [] } ],
				1: [ { id: 3, quantity: 1, subItems: [] } ],
			} );
		} );

		it( 'should filter out items when all sub-items are selected', () => {
			const selections = {
				0: [ { id: '1-sub-0' }, { id: '1-sub-1' } ],
			};
			orderItemsUtils.isSubItem.mockImplementation(
				( item ) =>
					typeof item.id === 'string' && item.id.includes( 'sub' )
			);
			orderItemsUtils.getParentIdFromSubItemId.mockImplementation(
				( id ) =>
					typeof id === 'string' && id.includes( 'sub' )
						? 1
						: parseInt( id, 10 )
			);

			const result = getNoneSelectedShipmentItems(
				shipments,
				selections
			);

			expect( result ).toEqual( {
				0: [ { id: 2, quantity: 1, subItems: [] } ],
				1: [ { id: 3, quantity: 1, subItems: [] } ],
			} );
		} );

		it( 'should keep items when only some sub-items are selected', () => {
			const selections = {
				0: [ { id: '1-sub-0' } ],
			};
			orderItemsUtils.isSubItem.mockImplementation(
				( item ) =>
					typeof item.id === 'string' && item.id.includes( 'sub' )
			);
			orderItemsUtils.getParentIdFromSubItemId.mockImplementation(
				( id ) =>
					typeof id === 'string' && id.includes( 'sub' )
						? 1
						: parseInt( id, 10 )
			);

			const result = getNoneSelectedShipmentItems(
				shipments,
				selections
			);

			expect( result ).toEqual( {
				0: [
					{ id: 1, quantity: 1, subItems: [] },
					{ id: 2, quantity: 1, subItems: [] },
				],
				1: [ { id: 3, quantity: 1, subItems: [] } ],
			} );
		} );

		it( 'should remove empty shipments after filtering', () => {
			const selections = {
				0: [
					{ id: 1, quantity: 2 },
					{ id: 2, quantity: 1 },
				],
			};

			const result = getNoneSelectedShipmentItems(
				shipments,
				selections
			);

			expect( result ).toEqual( {
				0: [],
				1: [ { id: 3, quantity: 1, subItems: [] } ],
			} );
		} );

		it( 'should handle empty shipments', () => {
			const emptyShipments = {};
			const selections = {};

			const result = getNoneSelectedShipmentItems(
				emptyShipments,
				selections
			);

			expect( result ).toEqual( {} );
		} );

		it( 'should handle empty selections', () => {
			const selections = {};

			const result = getNoneSelectedShipmentItems(
				shipments,
				selections
			);

			expect( result ).toEqual( {
				0: [
					{ id: 1, quantity: 1, subItems: [] },
					{ id: 2, quantity: 1, subItems: [] },
				],
				1: [ { id: 3, quantity: 1, subItems: [] } ],
			} );
		} );
	} );

	describe( 'normalizeSubItems', () => {
		it( 'should convert sub-items back to their parent item structure', () => {
			const shipments = {
				0: [
					{ id: '1-sub-0', parentId: 1, quantity: 1, subItems: [] },
					{ id: '1-sub-1', parentId: 1, quantity: 1, subItems: [] },
				],
			};

			const result = normalizeSubItems( shipments );

			expect( result ).toEqual( {
				0: [
					{
						id: 1,
						parentId: 1,
						quantity: 2,
						subItems: [
							{
								id: '1-sub-0',
								parentId: 1,
								quantity: 1,
								subItems: [],
							},
							{
								id: '1-sub-1',
								parentId: 1,
								quantity: 1,
								subItems: [],
							},
						],
					},
				],
			} );
		} );

		it( 'should handle mixed sub-items and regular items', () => {
			const shipments = {
				0: [
					{ id: '1-sub-0', parentId: 1, quantity: 1, subItems: [] },
					{ id: 2, quantity: 1, subItems: [] },
				],
			};

			const result = normalizeSubItems( shipments );

			expect( result ).toEqual( {
				0: [
					{
						id: 1,
						parentId: 1,
						quantity: 1,
						subItems: [
							{
								id: '1-sub-0',
								parentId: 1,
								quantity: 1,
								subItems: [],
							},
						],
					},
					{ id: 2, quantity: 1, subItems: [] },
				],
			} );
		} );

		it( 'should preserve regular items without sub-items', () => {
			const shipments = {
				0: [
					{ id: 1, quantity: 2, subItems: [] },
					{ id: 2, quantity: 1, subItems: [] },
				],
			};

			const result = normalizeSubItems( shipments );

			expect( result ).toEqual( {
				0: [
					{
						id: 1,
						quantity: 2,
						subItems: [
							{
								id: '1-sub-0',
								parentId: 1,
								quantity: 1,
								subItems: [],
							},
							{
								id: '1-sub-1',
								parentId: 1,
								quantity: 1,
								subItems: [],
							},
						],
					},
					{ id: 2, quantity: 1, subItems: [] },
				],
			} );
		} );

		it( 'should handle empty shipments', () => {
			const shipments = {};

			const result = normalizeSubItems( shipments );

			expect( result ).toEqual( {} );
		} );

		it( 'should handle shipments with empty arrays', () => {
			const shipments = {
				0: [],
				1: [ { id: 1, quantity: 2, subItems: [] } ],
			};

			const result = normalizeSubItems( shipments );

			expect( result ).toEqual( {
				0: [],
				1: [
					{
						id: 1,
						quantity: 2,
						subItems: [
							{
								id: '1-sub-0',
								parentId: 1,
								quantity: 1,
								subItems: [],
							},
							{
								id: '1-sub-1',
								parentId: 1,
								quantity: 1,
								subItems: [],
							},
						],
					},
				],
			} );
		} );

		it( 'should aggregate multiple sub-items correctly', () => {
			const shipments = {
				0: [
					{ id: '1-sub-0', parentId: 1, quantity: 1, subItems: [] },
					{ id: '1-sub-1', parentId: 1, quantity: 1, subItems: [] },
					{ id: '1-sub-2', parentId: 1, quantity: 1, subItems: [] },
				],
			};

			const result = normalizeSubItems( shipments );

			expect( result ).toEqual( {
				0: [
					{
						id: 1,
						parentId: 1,
						quantity: 3,
						subItems: [
							{
								id: '1-sub-0',
								parentId: 1,
								quantity: 1,
								subItems: [],
							},
							{
								id: '1-sub-1',
								parentId: 1,
								quantity: 1,
								subItems: [],
							},
							{
								id: '1-sub-2',
								parentId: 1,
								quantity: 1,
								subItems: [],
							},
						],
					},
				],
			} );
		} );
	} );

	describe( 'removeEmptyShipments', () => {
		it( 'should remove shipments with empty arrays', () => {
			const shipments = {
				0: [],
				1: [ { id: 1, quantity: 2 } ],
				2: [],
			};

			const result = removeEmptyShipments( shipments );

			expect( result ).toEqual( {
				0: [ { id: 1, quantity: 2 } ],
			} );
		} );

		it( 'should preserve shipments with items', () => {
			const shipments = {
				0: [ { id: 1, quantity: 2 } ],
				1: [ { id: 2, quantity: 1 } ],
			};

			const result = removeEmptyShipments( shipments );

			expect( result ).toEqual( shipments );
		} );

		it( 'should handle completely empty shipments object', () => {
			const shipments = {};

			const result = removeEmptyShipments( shipments );

			expect( result ).toEqual( {} );
		} );

		it( 'should handle all empty shipments', () => {
			const shipments = {
				0: [],
				1: [],
				2: [],
			};

			const result = removeEmptyShipments( shipments );

			expect( result ).toEqual( {} );
		} );
	} );

	describe( 'normalizeShipments', () => {
		it( 'should normalize sub-items and remove empty shipments', () => {
			const shipments = {
				0: [
					{ id: '1-sub-0', parentId: 1, quantity: 1, subItems: [] },
					{ id: '1-sub-1', parentId: 1, quantity: 1, subItems: [] },
				],
				1: [],
				2: [ { id: 2, quantity: 1, subItems: [] } ],
			};

			const result = normalizeShipments( shipments );

			expect( result ).toEqual( {
				0: [
					{
						id: 1,
						parentId: 1,
						quantity: 2,
						subItems: [
							{
								id: '1-sub-0',
								parentId: 1,
								quantity: 1,
								subItems: [],
							},
							{
								id: '1-sub-1',
								parentId: 1,
								quantity: 1,
								subItems: [],
							},
						],
					},
				],
				1: [ { id: 2, quantity: 1, subItems: [] } ],
			} );
		} );

		it( 'should handle complex normalization', () => {
			const shipments = {
				0: [
					{ id: '1-sub-0', parentId: 1, quantity: 1, subItems: [] },
					{ id: 2, quantity: 1, subItems: [] },
				],
				1: [],
				2: [
					{ id: '3-sub-0', parentId: 3, quantity: 1, subItems: [] },
					{ id: '3-sub-1', parentId: 3, quantity: 1, subItems: [] },
					{ id: '3-sub-2', parentId: 3, quantity: 1, subItems: [] },
				],
			};

			const result = normalizeShipments( shipments );

			expect( result ).toEqual( {
				0: [
					{
						id: 1,
						parentId: 1,
						quantity: 1,
						subItems: [
							{
								id: '1-sub-0',
								parentId: 1,
								quantity: 1,
								subItems: [],
							},
						],
					},
					{ id: 2, quantity: 1, subItems: [] },
				],
				1: [
					{
						id: 3,
						parentId: 3,
						quantity: 3,
						subItems: [
							{
								id: '3-sub-0',
								parentId: 3,
								quantity: 1,
								subItems: [],
							},
							{
								id: '3-sub-1',
								parentId: 3,
								quantity: 1,
								subItems: [],
							},
							{
								id: '3-sub-2',
								parentId: 3,
								quantity: 1,
								subItems: [],
							},
						],
					},
				],
			} );
		} );

		it( 'should handle empty input', () => {
			const shipments = {};

			const result = normalizeShipments( shipments );

			expect( result ).toEqual( {} );
		} );
	} );

	describe( 'removeShipmentsWithNoMatchingItems (private function behavior)', () => {
		it( 'should be called during getCurrentOrderShipments', () => {
			const configWithShipments = {
				...mockConfig,
				shipments: JSON.stringify( {
					1: [ { id: 999, quantity: 1 } ], // Item not in order
					2: [ { id: 1, quantity: 2 } ], // Valid item
				} ),
			};
			configUtils.getConfig.mockReturnValue( configWithShipments );

			const result = getCurrentOrderShipments();

			// Shipment 1 should be removed because item 999 is not in order
			// Shipment 2's item should be moved to shipment 0 along with unassigned items
			expect( result[ 1 ] ).toBeUndefined();
			expect( result[ 0 ] ).toBeDefined();
			expect( result[ 0 ].length ).toBeGreaterThan( 0 );
		} );
	} );
} );
