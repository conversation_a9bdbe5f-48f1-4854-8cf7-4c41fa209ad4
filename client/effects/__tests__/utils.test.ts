import { act, renderHook } from '@testing-library/react';
import { useThrottledStateChange } from '../utils';
import { useState } from 'react';

jest.useFakeTimers();

describe( 'useThrottledStateChange', () => {
	beforeEach( () => {
		jest.clearAllTimers();
		jest.clearAllMocks();
	} );

	test( 'should call callback when state changes', async () => {
		const callback = jest.fn();
		const { result } = renderHook( () => {
			const [ state, setState ] = useState( { value: 1 } );
			useThrottledStateChange( state, callback, {
				delay: 500,
			} );
			return { state, setState };
		} );

		// Initial render should not trigger callback
		expect( callback ).not.toHaveBeenCalled();

		// Change state
		await act( () => {
			result.current.setState( { value: 2 } );
		} );

		// Callback should not be called yet
		expect( callback ).not.toHaveBeenCalled();

		// Fast subsequent changes should be debounced
		await act( () => {
			result.current.setState( { value: 3 } );
		} );

		await act( () => {
			result.current.setState( { value: 4 } );
		} );

		// Fast forward to the end of the debounce period
		jest.advanceTimersByTime( 500 );

		// Still at one call due to throttling
		expect( callback ).toHaveBeenCalledTimes( 1 );

		// New change after throttle period should trigger immediately
		await act( () => {
			result.current.setState( { value: 5 } );
		} );

		// Still at one call due to throttling
		expect( callback ).toHaveBeenCalledTimes( 1 );

		// Fast forward to the end of the debounce period
		jest.advanceTimersByTime( 500 );

		// Now at three calls due to debouncing
		expect( callback ).toHaveBeenCalledTimes( 2 );
	} );

	test( 'should handle nested state', () => {
		const callback = jest.fn();
		const { result } = renderHook( () => {
			const [ state, setState ] = useState( {
				nested: { value: 1 },
				other: 'unchanged',
			} );
			useThrottledStateChange( state, callback, {
				hasChanged: ( prev, current ) =>
					! ( prev.nested.value === current.nested.value ),
				delay: 1000,
			} );
			return { state, setState };
		} );

		// Initial render should not trigger callback
		expect( callback ).not.toHaveBeenCalled();

		// Change nested value
		act( () => {
			result.current.setState( {
				nested: { value: 2 },
				other: 'unchanged',
			} );
		} );

		// Fast forward to the end of the debounce period
		jest.advanceTimersByTime( 1000 );

		expect( callback ).toHaveBeenCalledTimes( 1 );

		// Change unrelated value should not trigger callback
		act( () => {
			result.current.setState( {
				nested: { value: 2 },
				other: 'changed',
			} );
		} );
		expect( callback ).toHaveBeenCalledTimes( 1 );
	} );

	test( 'should respect throttle options', async () => {
		const callback = jest.fn();
		const { result } = renderHook( () => {
			const [ state, setState ] = useState( { value: 1 } );
			useThrottledStateChange( state, callback, {
				hasChanged: ( prev, current ) => prev.value !== current.value,
				delay: 1000,
				leading: true,
				trailing: false,
			} );
			return { state, setState };
		} );

		// Change state multiple times quickly
		await act( async () => {
			result.current.setState( { value: 2 } );
		} );
		await act( async () => {
			result.current.setState( { value: 3 } );
		} );
		await act( async () => {
			result.current.setState( { value: 4 } );
		} );

		// Should only be called once due to throttling with trailing: false
		expect( callback ).toHaveBeenCalledTimes( 1 );

		// // Advance time less than throttle time
		jest.advanceTimersByTime( 500 );
		await act( () => {
			result.current.setState( { value: 5 } );
		} );

		expect( callback ).toHaveBeenCalledTimes( 1 );

		// // Advance time past throttle time
		jest.advanceTimersByTime( 1000 );
		await act( async () => {
			result.current.setState( { value: 6 } );
		} );
		expect( callback ).toHaveBeenCalledTimes( 2 );
	} );

	test( 'should cleanup throttled callback on unmount', () => {
		const callback = jest.fn();
		const { unmount } = renderHook( () => {
			const [ state ] = useState( { value: 1 } );
			useThrottledStateChange( state, callback, {
				delay: 300,
			} );
			return state;
		} );

		unmount();

		// After unmount, no more callbacks should be triggered
		jest.advanceTimersByTime( 500 );
		expect( callback ).not.toHaveBeenCalled();
	} );
} );
