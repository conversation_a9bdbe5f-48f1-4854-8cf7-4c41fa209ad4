import React from 'react';
import { render } from '@testing-library/react';
import { LabelPurchaseContextProvider } from '../context-provider';
import { LabelPurchaseContext } from '../context';

jest.mock( 'utils', () => ( {
	getCurrentOrderItems: jest.fn().mockReturnValue( [] ),
} ) );

jest.mock( '@wordpress/data', () => ( {
	...jest.requireActual( '@wordpress/data' ),
	useSelect: jest.fn().mockImplementation( ( fn ) =>
		fn( () => ( {
			getOrderDestination: jest.fn().mockReturnValue( {
				city: 'San Francisco',
				country: 'US',
				state: 'CA',
				isVerified: true,
			} ),
			getSavedPackages: jest.fn().mockReturnValue( {} ),
			getCustomsInformation: jest.fn().mockReturnValue( {} ),
			getRatesForShipment: jest.fn().mockReturnValue( {} ),
			getPurchaseAPIError: jest.fn(),
			getLabelOrigins: jest.fn().mockReturnValue( {} ),
			getLabelDestinations: jest.fn().mockReturnValue( {} ),
		} ) )
	),
} ) );

jest.mock( 'components/label-purchase/hooks', () => ( {
	useEssentialDetails: jest.fn().mockReturnValue( {
		focusArea: jest.fn().mockReturnValue( {} ),
		resetFocusArea: jest.fn(),
		isShippingServiceCompleted: jest.fn().mockReturnValue( false ),
	} ),
	useAccountState: jest.fn().mockReturnValue( {
		accountSettings: jest.fn().mockReturnValue( {} ),
		canPurchase: jest.fn().mockReturnValue( true ),
		setAccountCompleteOrder: jest.fn(),
		getAccountCompleteOrder: jest.fn().mockReturnValue( false ),
	} ),
	useTotalWeight: jest.fn().mockReturnValue( {
		getShipmentTotalWeight: jest.fn().mockReturnValue( 0 ),
		setShipmentTotalWeight: jest.fn(),
	} ),
	useShipmentState: jest.fn().mockReturnValue( {
		shipments: {},
		setShipments: jest.fn(),
		getShipmentWeight: jest.fn(),
		resetSelections: jest.fn(),
		selections: {},
		setSelection: jest.fn(),
		currentShipmentId: '0',
		setCurrentShipmentId: jest.fn(),
		getShipmentItems: jest.fn(),
		getSelectionItems: jest.fn(),
		setShipmentOrigin: jest.fn(),
		getShipmentOrigin: jest.fn(),
		getShipmentDestination: jest.fn(),
		revertLabelShipmentIdsToUpdate: jest.fn(),
		labelShipmentIdsToUpdate: {},
		getShipmentPurchaseOrigin: jest.fn(),
		hasVariations: jest.fn(),
		hasMultipleShipments: jest.fn(),
		isExtraLabelPurchaseValid: jest.fn(),
		resetShipmentAndSelection: jest.fn(),
		isShipmentAutogeneratedFromLabel: jest.fn(),
	} ),
	useRatesState: jest.fn().mockReturnValue( {
		selectedRates: {},
		selectRates: jest.fn(),
		selectRate: jest.fn(),
		getSelectedRate: jest.fn(),
		removeSelectedRate: jest.fn(),
		isFetching: false,
		updateRates: jest.fn(),
		fetchRates: jest.fn(),
		sortRates: jest.fn(),
		errors: [],
		setErrors: jest.fn(),
		matchAndSelectRate: jest.fn(),
		availableRates: [],
		preselectRateBasedOnLastSelections: jest.fn(),
	} ),
	usePackageState: jest.fn().mockReturnValue( {
		isCustomPackageTab: jest.fn().mockReturnValue( false ),
		getSelectedPackage: jest.fn().mockReturnValue( null ),
		getCustomPackage: jest.fn().mockReturnValue( {} ),
		setSelectedPackage: jest.fn(),
		setCurrentPackageTab: jest.fn(),
		isPackageSpecified: jest.fn().mockReturnValue( false ),
		currentPackageTab: jest.fn().mockReturnValue( 'custom' ),
	} ),
	useHazmatState: jest.fn().mockReturnValue( {
		getShipmentHazmat: jest.fn().mockReturnValue( { isHazmat: false } ),
		setShipmentHazmat: jest.fn(),
		applyHazmatToPackage: jest.fn(),
		isHazmatSpecified: jest.fn(),
	} ),
	useCustomsState: jest.fn().mockReturnValue( {
		getCustomsState: jest.fn(),
		setCustomsState: jest.fn(),
		maybeApplyCustomsToPackage: jest.fn(),
		hasErrors: jest.fn(),
		setErrors: jest.fn(),
		isCustomsNeeded: jest.fn(),
		isHSTariffNumberRequired: jest.fn(),
		updateCustomsItems: jest.fn(),
	} ),
	useLabelsState: jest.fn().mockReturnValue( {
		hasMissingPurchase: jest.fn().mockReturnValue( false ),
		hasUnfinishedShipment: jest.fn().mockReturnValue( false ),
		purchasedLabelsProductIds: jest.fn().mockReturnValue( [] ),
		hasPurchasedLabel: jest.fn().mockReturnValue( false ),
		isPurchasing: false,
		isUpdatingStatus: false,
		getCurrentShipmentLabel: jest.fn(),
		hasRequestedRefund: jest.fn().mockReturnValue( false ),
		labelStatusUpdateErrors: [],
		selectedLabelSize: {},
		setLabelSize: jest.fn(),
		paperSizes: [],
		getShipmentsWithoutLabel: jest.fn().mockReturnValue( [] ),
		isCurrentTabPurchasingExtraLabel: jest.fn().mockReturnValue( false ),
	} ),
} ) );

describe( 'LabelPurchaseContextProvider', () => {
	const TestComponent = () => {
		const context = React.useContext( LabelPurchaseContext );
		return <div>{ JSON.stringify( context ) }</div>;
	};

	it( 'provides the expected context value', () => {
		const { container } = render(
			<LabelPurchaseContextProvider orderId={ 1 }>
				<TestComponent />
			</LabelPurchaseContextProvider>
		);

		const contextValue = JSON.parse( container.textContent ?? '{}' );

		expect( contextValue ).toHaveProperty( 'orderItems' );
		expect( contextValue ).toHaveProperty( 'shipment' );
		expect( contextValue ).toHaveProperty( 'hazmat' );
		expect( contextValue ).toHaveProperty( 'packages' );
		expect( contextValue ).toHaveProperty( 'storeCurrency' );
		expect( contextValue ).toHaveProperty( 'rates' );
		expect( contextValue ).toHaveProperty( 'weight' );
		expect( contextValue ).toHaveProperty( 'customs' );
		expect( contextValue ).toHaveProperty( 'labels' );
		expect( contextValue ).toHaveProperty( 'account' );
		expect( contextValue ).toHaveProperty( 'essentialDetails' );
	} );
} );
