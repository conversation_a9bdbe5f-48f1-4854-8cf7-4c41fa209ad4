import {
	CustomPackageResponse,
	LocationResponse,
	ResponseLabel,
} from './connect-server';
import { StoreOptions } from './store-options';
import { Order } from './order';
import { HazmatState } from './hazmat-state';
import { Continent } from './continent';
import { SelectedRates } from './selected-rates';
import { SelectedOrigin } from './selected-origin';
import { SelectedDestination } from './selected-destination';
import { CustomsState } from './customs-state';
import { Carrier } from './carrier';
import { Constants } from './constants';
import { ShipmentRecord } from './helpers';
import { UserMeta } from './user-meta';
import { PurchaseSettings } from './purchase-settings';
import { PurchaseMeta } from './purchase-meta';
import { ShipmentDate } from './shipment-date';
import { Promotion } from './promotion';

export interface WCShippingConfigAccountSettings {
	purchaseSettings: PurchaseSettings;
	purchaseMeta: PurchaseMeta;
	userMeta: UserMeta;
	storeOptions: StoreOptions;
}

// Todo: Gradually improve this type definition.
export interface WCShippingConfig {
	order: Order;
	accountSettings: WCShippingConfigAccountSettings;
	context: string;
	continents: Continent[];
	is_destination_verified: boolean;
	is_origin_verified: boolean;
	items: number;
	packagesSettings: Record< string, unknown > & {
		packages: {
			custom: CustomPackageResponse[];
			predefined: Record< string, string[] >;
		};
		schema: Record< string, unknown >;
	};
	shipments: Record< string, unknown >[];
	shipments_autogenerated_from_labels: int[];
	shippingLabelData: Record< string, unknown > & {
		storeOptions: StoreOptions;
		currentOrderLabels: ResponseLabel[];
		storedData: {
			destination: LocationResponse;
			selected_rates: SelectedRates | '';
			selected_hazmat: HazmatState | '';
			selected_origin: SelectedOrigin | '';
			selected_destination: SelectedDestination | '';
			customs_information: ShipmentRecord< CustomsState > | '';
			shipment_dates: ShipmentRecord< ShipmentDate< string > > | '';
		};
	};
	origin_addresses: LocationResponse[];
	eu_countries: string[];
	constants: Constants;
	carrier_strategies: {
		upsdap: {
			origin_address: Record<
				string,
				{
					has_agreed_to_tos: boolean;
				}
			>;
		};
	};
	custom_fulfillment_summary: string;
	promotion?: Promotion;
	should_use_fulfillment_api: boolean;
}
