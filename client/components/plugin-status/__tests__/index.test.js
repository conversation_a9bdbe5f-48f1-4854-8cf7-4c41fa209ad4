import { pluginStatusConfig } from 'data/plugin-status';
import { printDocument } from 'utils/label/print-document';
import { createReduxStore, register } from '@wordpress/data';
import { act, fireEvent, render, screen } from '@testing-library/react';
import { STORE_NAME } from 'data/constants';
import apiFetch from '@wordpress/api-fetch';
import flushPromises from 'tests/utilities/flush-promises';
import PluginStatus from '../';
import { getPaperSizes } from 'components/label-purchase/label';

jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
	return mockUtils();
} );

jest.mock( '@wordpress/api-fetch', () => {
	const originalModule = jest.requireActual( '@wordpress/api-fetch' );
	return {
		__esModule: true,
		createNonceMiddleware: jest.fn( originalModule.createNonceMiddleware ),
		use: jest.fn( originalModule.use ),
		default: jest.fn(),
	};
} );

/**
 * Partial mocking the printDocument to always return done. In <PluginStatus>,
 * we don't care about the response, so the return does not matter.
 */
jest.mock( 'utils/label/print-document', () => {
	return {
		printDocument: jest.fn().mockResolvedValue( 'done' ),
	};
} );

/**
 * Initialize the redux store by mocking only the selectors. This way,
 * it's easier to test the <PluginStatus> state while retaining the reducer logic.
 */
const mockStoreForTest = () => {
	const testStore = {
		...pluginStatusConfig,
		selectors: {
			getWoocommerceHealthItem: jest
				.fn()
				.mockReturnValue( { state: 'failed' } ),
			getWPComHealthItem: jest
				.fn()
				.mockReturnValue( { state: 'failed' } ),
			getWCShippingHealthItem: jest.fn(),
			getLogs: jest.fn().mockReturnValue( {
				shipping: { count: 0 },
				other: { count: 0 },
			} ),
			getLoggingEnabled: jest.fn().mockReturnValue( false ),
			getDebugEnabled: jest.fn().mockReturnValue( false ),
		},
	};
	register( createReduxStore( STORE_NAME, testStore ) );
};
mockStoreForTest();

describe( 'Plugin status', () => {
	beforeEach( () => {
		// @ts-ignore
		apiFetch.mockClear();
	} );

	it( 'should make an API call to retrieve PDF', async () => {
		// Mock the actual data returned by the API, but truncated the b64 content.
		const expectedPDFData = {
			mimeType: 'application/pdf',
			b64Content: 'JVBERi0xLjMKJf////8KNyAwIG...',
			success: true,
		};

		// @ts-ignore
		apiFetch.mockResolvedValueOnce( expectedPDFData );

		render(
			<PluginStatus
				healthItems={ {} }
				services={ {} }
				loggingEnabled={ false }
				debugEnabled={ false }
				logs={ {} }
				storeOptions={ {
					origin_country: 'US',
				} }
			/>
		);
		await act( () => flushPromises() );

		// Click "Print" button to print test label
		fireEvent.click( screen.getByText( 'Print' ) );

		// This should trigger the label/preview endpoint.
		await act( () => flushPromises() );

		expect( apiFetch ).toHaveBeenCalledWith( {
			method: 'GET',
			path: `/wcshipping/v1/label/preview?paper_size=${
				getPaperSizes( 'US' )[ 0 ].key
			}&label_id_csv=test_1234&json=true`,
		} );
		expect( printDocument ).toHaveBeenCalledWith(
			expectedPDFData,
			'order-#test_1234-label.pdf'
		);
	} );
} );
