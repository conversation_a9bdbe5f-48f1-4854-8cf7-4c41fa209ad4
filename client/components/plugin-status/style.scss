.wcshipping-radio-control {

	.components-radio-control__option {
		// Align the RadioControl component circle to our component.
		display: flex;
		align-items: center;

		// Style around the boxes based on WCS&T style.
		padding: 16px 24px;
		margin-bottom: 14px;
		box-shadow: 0 0 0 1px #e1e2e2;
	}

	label {
		// Fill <PaymentMethod> to the whole width.
		flex-grow: 1;
	}
}

.health-status-card__icon--error {
	color: red;
}

.health-status-card__icon--success {
	color: green;
}
