@import "@wordpress/base-styles/variables";
@import "@wordpress/base-styles/breakpoints";
@import "@wordpress/base-styles/mixins";

.wcshipping-onboarding-container {
	max-width: $wide-content-width;
	margin-left: auto;
	margin-right: auto;

	&__row {
		align-items: stretch;
	}

	&__content {
		width: 100%;
	}

	&__media {
		align-items: center;
		box-sizing: border-box;
		padding: $grid-unit-30;

		@include break-large() {
			width: 40%;
		}
	}

	&__image {
		// Make sure the image behaves in a responsive matter.
		max-width: 100%;
		width: auto;
		height: auto;
		// Make sure the image doesn't take up too much vertical space on
		// phones and other small screens.
		max-height: 300px;

		// Center image.
		margin-left: auto;
		margin-right: auto;

		@include break-large() {
			// Reset max height since we're no longer showing the content and our image
			// as stacked on top of each other.
			max-height: 100%;
		}
	}
}
