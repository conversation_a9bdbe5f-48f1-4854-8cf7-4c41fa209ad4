@import "@wordpress/base-styles/colors";
@import "@wordpress/base-styles/variables";

$badge-colors: (
	"info": #3858e9,
	"warning": $alert-yellow,
	"error": $alert-red,
	"success": $alert-green,
);

.components-badge {
	background-color: color-mix(in srgb, $white 90%, var(--base-color));
	color: color-mix(in srgb, $black 50%, var(--base-color));
	padding: 0 $grid-unit-10;
	min-height: $grid-unit-30;
	max-width: 100%;
	border-radius: $radius-small;
	font-size: $font-size-small;
	font-weight: 400;
	line-height: $font-line-height-small;
	display: inline-flex;
	align-items: center;
	gap: 2px;

	&:where(.is-default) {
		background-color: $gray-100;
		color: $gray-800;
	}

	&.has-icon {
		padding-inline-start: $grid-unit-05;
	}

	// Generate color variants
	@each $type, $color in $badge-colors {
		&.is-#{$type} {
			--base-color: #{$color};
		}
	}
}

.components-badge__icon {
	flex-shrink: 0;
}

.components-badge__content {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
