# Forked components from @wordpress/components

To meet the design requirements of our projects, we sometimes need to use private components from @wordpress/components. As we are bundling them within the plugin, our only option is to copy them.

**This is just a temporary solution; any private components that become public should be removed here.**

The components should be forked as they are, without any customization.
