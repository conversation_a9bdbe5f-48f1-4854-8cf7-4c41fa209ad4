import React from 'react';
import { ShipmentTrackingLink } from '../link';
import { render } from '@testing-library/react';

describe( 'ShipmentTrackingLink', () => {
	it( 'should render USPS tracking link when tracking is available', () => {
		const label = {
			carrierId: 'usps',
			tracking: '9405500106025011240155',
		};

		const { getByText } = render(
			<ShipmentTrackingLink label={ label } />
		);

		const trackingLink = getByText( '9405500106025011240155' );
		expect( trackingLink ).toBeInTheDocument();
		expect( trackingLink ).toHaveAttribute(
			'href',
			'https://tools.usps.com/go/TrackConfirmAction.action?tLabels=9405500106025011240155'
		);
	} );

	it( 'should render UPS tracking link when tracking is available', () => {
		const label = {
			carrierId: 'ups',
			tracking: '9405500106025011240155',
		};

		const { getByText } = render(
			<ShipmentTrackingLink label={ label } />
		);

		const trackingLink = getByText( '9405500106025011240155' );
		expect( trackingLink ).toBeInTheDocument();
		expect( trackingLink ).toHaveAttribute(
			'href',
			'https://www.ups.com/track?loc=en_US&tracknum=9405500106025011240155'
		);
	} );

	it( 'should render FedEx tracking link when tracking is available', () => {
		const label = {
			carrierId: 'fedex',
			tracking: '9405500106025011240155',
		};

		const { getByText } = render(
			<ShipmentTrackingLink label={ label } />
		);

		const trackingLink = getByText( '9405500106025011240155' );
		expect( trackingLink ).toBeInTheDocument();
		expect( trackingLink ).toHaveAttribute(
			'href',
			'https://www.fedex.com/apps/fedextrack/?action=track&tracknumbers=9405500106025011240155'
		);
	} );

	it( 'should render DHLExpress tracking link when tracking is available', () => {
		const label = {
			carrierId: 'dhlexpress',
			tracking: '9405500106025011240155',
		};

		const { getByText } = render(
			<ShipmentTrackingLink label={ label } />
		);

		const trackingLink = getByText( '9405500106025011240155' );
		expect( trackingLink ).toBeInTheDocument();
		expect( trackingLink ).toHaveAttribute(
			'href',
			'https://www.dhl.com/en/express/tracking.html?AWB=9405500106025011240155&brand=DHL'
		);
	} );

	it( 'should render tracking number when tracking URL is empty', () => {
		const label = {
			carrierId: '123',
			tracking: 'ABC123',
		};

		const { getByText, queryByRole } = render(
			<ShipmentTrackingLink label={ label } />
		);

		const trackingLink = queryByRole( 'link' );
		expect( trackingLink ).not.toBeInTheDocument();

		const trackingNumber = getByText( 'ABC123' );
		expect( trackingNumber ).toBeInTheDocument();
	} );

	it( 'should render refund status', () => {
		const label = {
			carrierId: '123',
			tracking: 'ABC123',
			refund: { status: 'pending' },
		};

		const { getByText } = render(
			<ShipmentTrackingLink label={ label } />
		);

		const refundIndicator = getByText( '[ Refund - pending ]' );
		expect( refundIndicator ).toBeInTheDocument();
	} );

	it( 'should not render refund status if its not part of the label', () => {
		const label = {
			carrierId: '123',
			tracking: 'ABC123',
		};

		const { queryByText } = render(
			<ShipmentTrackingLink label={ label } />
		);

		// We're looking for a partial match to ensure we do not render unexpected
		// behaviour like "[Refund - undefined]".
		const refundIndicator = queryByText( '[ Refund - ', { exact: false } );
		expect( refundIndicator ).not.toBeInTheDocument();
	} );

	it( 'should not render anything when tracking is not available', () => {
		const label = {
			carrierId: '123',
			tracking: '',
		};

		const { container } = render(
			<ShipmentTrackingLink label={ label } />
		);

		expect( container.firstChild ).toBeNull();
	} );
} );
