import React from 'react';
import { render } from '@testing-library/react';
import { ShipmentTrackingEvent } from '../event';

describe( 'ShipmentTrackingEvent', () => {
	const mockLabel1 = {
		id: 2909,
		tracking: '9405500106025011240155',
		carrierId: 'usps',
		serviceName: 'USPS - Priority Mail',
		status: 'PURCHASED',
		created: 1705937218000,
	};

	const mockLabel2 = {
		id: 2909,
		tracking: 'ABCD1234',
		carrierId: 'abc',
		serviceName: 'Unknown Carrier - Unknown Service',
		status: 'PURCHASED',
		created: 1705937214291,
	};

	it( 'should render the tracking number and link correctly', () => {
		const { getByText, queryByRole } = render(
			<ShipmentTrackingEvent label={ mockLabel1 } />
		);

		const linkElement = queryByRole( 'link', {
			name: '9405500106025011240155',
		} );
		const labelElement = getByText( '( USPS - Priority Mail )' );

		expect( linkElement ).toBeInTheDocument();
		expect( labelElement ).toBeInTheDocument();
	} );

	it( 'should render the tracking number correctly without link', () => {
		const { getByText, queryByRole } = render(
			<ShipmentTrackingEvent label={ mockLabel2 } />
		);

		const linkElement = queryByRole( 'link', { name: 'ABCD1234' } );
		const labelElement = getByText(
			'( Unknown Carrier - Unknown Service )'
		);

		expect( linkElement ).toBeNull();
		expect( labelElement ).toBeInTheDocument();
	} );
} );
