import React from 'react';
import { render, screen } from '@testing-library/react';
import { ShipmentTracking } from '../shipment-tracking';

describe( 'ShipmentTracking', () => {
	const mockLabel1 = {
		id: 2909,
		tracking: '9405500106025011240155',
		carrierId: 'usps',
		serviceName: 'USPS - Priority Mail',
		status: 'PURCHASED',
		created: 1705937218000,
	};

	const mockLabel2 = {
		id: 2911,
		tracking: 'ABCD1234',
		carrierId: 'abc',
		serviceName: 'Unknown Carrier - Unknown Service',
		status: 'PURCHASE_ERROR',
		created: 1705937214291,
	};

	it( 'should render purchased label only', () => {
		render( <ShipmentTracking labels={ [ mockLabel1, mockLabel2 ] } /> );
		const label = screen.getByText( '9405500106025011240155' );
		expect( label ).toBeTruthy();
	} );

	it( 'should not render failed purchases', () => {
		render( <ShipmentTracking labels={ [ mockLabel2 ] } /> );
		const label = screen.getByText(
			'No shipping labels have been created for this order yet.'
		);
		expect( label ).toBeTruthy();
	} );
} );
