import React from 'react';
import { render } from '@testing-library/react';
import { ShipmentTrackingDay } from '../day';

describe( 'ShipmentTrackingDay', () => {
	const mockLabel1 = [
		{
			id: 2909,
			tracking: '9405500106025011240155',
			carrierId: 'usps',
			serviceName: 'USPS - Priority Mail',
			status: 'PURCHASED',
			created: 1705937218000,
		},
	];

	const mockLabel2 = [
		{
			id: 2910,
			tracking: 'ABCD1234',
			carrierId: 'abc',
			serviceName: 'Unkow Carrier - Unknown Service',
			status: 'PURCHASED',
			created: 1705937214291,
		},
	];

	it( 'should render the date and tracking number with link', () => {
		const { getByText, getByRole } = render(
			<ShipmentTrackingDay date="Jan 22, 2024" labels={ mockLabel1 } />
		);
		const trackingLink = getByRole( 'link', {
			name: '9405500106025011240155',
		} );
		expect( trackingLink ).toBeInTheDocument();
		expect( getByText( 'Jan 22, 2024' ) ).toBeInTheDocument();
		expect( getByText( '9405500106025011240155' ) ).toBeInTheDocument();
	} );

	it( 'should render the date and tracking number without link', () => {
		const { getByText, queryByRole } = render(
			<ShipmentTrackingDay date="Jan 22, 2024" labels={ mockLabel2 } />
		);
		expect( queryByRole( 'link', { name: 'ABCD1234' } ) ).toBeNull();
		expect( getByText( 'Jan 22, 2024' ) ).toBeInTheDocument();
		expect( getByText( 'ABCD1234' ) ).toBeInTheDocument();
	} );
} );
