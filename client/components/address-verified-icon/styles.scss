span.verification {
	color: var(--wp-green-green-50, #008a20);
	display: flex;
	gap: 4px;
	line-height: 15px;
	margin-top: 4px;

	svg {
		border: 1px solid var(--wp-green-green-50, #008a20);
		border-radius: 20px;
		fill: var(--wp-green-green-50, #008a20);
	}

	&.in-progress {
		color: var(--wp-yello-yellow-40, #c08c00);

		svg {
			border: 1px solid var(--wp-yello-yellow-40, #c08c00);
			border-radius: 20px;
			fill: var(--wp-yello-yellow-40, #c08c00);
		}
	}

	&.not-verified {
		color: var(--gutenberg-alert-red, #cc1818);
		line-height: 22px;
		margin-left: -4px;

		button {
			color: var(--gutenberg-alert-red, #cc1818);
			text-decoration: none;
			padding: 0 !important;
			min-height: 0 !important;
			height: auto;

			&:focus {
				outline: none;
				border: none;
				box-shadow: none;
			}
		}

		svg {
			border: none;
			fill: var(--gutenberg-alert-red, #cc1818);
		}
	}
}
