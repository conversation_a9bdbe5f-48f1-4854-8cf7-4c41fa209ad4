import { render, screen } from '@testing-library/react';
import React from 'react';
import PaymentMethodIcon from '..';

describe( 'Payment method icon', () => {
	describe( 'VISA payment method', () => {
		it( 'renders the VISA payment method icon', () => {
			render( <PaymentMethodIcon type="visa" /> );

			const icon = screen.getByLabelText( 'Visa' );

			expect( icon ).toHaveClass( 'is-visa' );
			expect( icon ).toHaveStyle(
				'background-image: url(test-file-stub);'
			);
		} );
	} );

	describe( 'Unknown payment method', () => {
		it( 'renders the asdfgh payment method icon', () => {
			render( <PaymentMethodIcon type="asdfgh" /> );

			const icon = screen.getByLabelText( 'unknown payment' );

			expect( icon ).toHaveClass( 'is-asdfgh' );
			expect( icon ).not.toHaveStyle(
				'background-image: url(test-file-stub);'
			);
		} );
	} );
} );
