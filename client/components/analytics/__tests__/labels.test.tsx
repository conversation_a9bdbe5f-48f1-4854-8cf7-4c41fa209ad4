import { apiFetch } from '@wordpress/data-controls';
import { act, render } from '@testing-library/react';
import { Labels } from 'components/analytics/labels';
import CurrencyFactory from '@woocommerce/currency';
import { dispatch } from '@wordpress/data';
import { registerAnalyticsStore, AnalyticsStore } from 'data/analytics';
import { createReportQueryPath } from 'utils';
import { LabelReportProps } from '../types';

registerAnalyticsStore();
const storeCurrency = CurrencyFactory();

jest.mock( '@wordpress/data-controls', () => ( {
	apiFetch: jest.fn(),
} ) );

jest.mock( 'utils', () => ( {
	...jest.requireActual( 'utils' ),
	getAnalyticsConfig: jest.fn().mockReturnValue( {
		cacheExpirationInSeconds: 3600,
	} ),
} ) );

describe( 'Labels Component', () => {
	it( 'should send the correct request structure to apiFetch on initial render', async () => {
		const props: LabelReportProps = {
			query: {
				perPage: '25',
				paged: '1',
				page: 'wc-admin',
				path: '/analytics/shipping',
			},
			path: '/analytics/shipping',
			currency: storeCurrency,
		};

		// Invalidate the resolution for the getLabelsReport selector so that it uses the resolver
		await act( async () => {
			// @ts-ignore
			dispatch( AnalyticsStore ).invalidateResolution(
				'getLabelsReport'
			);
		} );

		// Arrange
		const expectedRequest = {
			path: createReportQueryPath( {
				...props.query,
				offset: '0', // starting page
			} ),
			method: 'GET',
		};

		// Act
		await act( async () => {
			render( <Labels { ...props } /> );
		} );

		// Assert
		expect( apiFetch ).toHaveBeenCalledWith( expectedRequest );
	} );
} );
