@import "selectable-items";
@import "shipment-tabs";

.split-shipment-modal-overlay {
	z-index: 100001; // default + 1 to show it on top of the main modal
}

.split-shipment-modal {
	width: 70%;
	max-height: 85%;
	--modal-padding: 32px;

	.components-modal__header {
		height: 92px;
		align-items: flex-start;
	}

	.components-modal__content {
		padding: var(--modal-padding);
		padding-top: 0;
		padding-bottom: 0;
	}

	footer {
		margin-top: var(--modal-padding);
		border-top: 1px solid var(--gutenberg-gray-200, #e0e0e0);
		box-sizing: border-box;
		padding: 18px var(--modal-padding);
		margin-left: calc(var(--modal-padding) * -1);
		width: calc(100% + 2 * var(--modal-padding));
		margin-bottom: calc(var(--modal-padding) * -1);
		bottom: 0;
		position: sticky;
		z-index: 99;
		background: #fff;

		button {
			margin-left: 12px;
		}
	}


	.components-notice {
		margin: 0;
		border: none;
		height: 48px;
		box-sizing: border-box;
		padding: 12px 12px 12px 16px;

		&.is-info {
			background: var(--wp-blue-blue-0, #f0f6fc);
		}

		button {
			margin: 0 0 0 12px;
			padding: 0;
			height: 24px;
			width: 24px;
			min-width: unset;

		}

		svg {
			width: 16px;
		}
	}

	.split-shipment-actions {
		background: #fff;
		box-sizing: border-box;

		.components-dropdown button {
			padding: 8px 8px 8px 12px;
		}

		.components-popover__content {
			width: 200px;

			button {
				width: 100%;
			}
		}

		svg {
			margin-right: -4px;
		}
	}


}

.label-purchase-list-items {

	> .components-flex {
		height: 68px;
		max-height: 68px;


		&.selection-header-wrapper {
			height: 32px;

			span {
				margin-right: 8px;
			}

			button {
				height: 32px;
				max-height: 32px;

			}
		}
	}

	&.is-selectable {
		margin-top: 0;
	}

	.components-checkbox-control {
		margin: 0 24px 0 0;
		align-self: center;
		padding-bottom: 8px;

		.components-checkbox-control__input-container,
		.components-base-control__field {
			margin: 0;
		}
	}

	.sub-item {

		.components-checkbox-control {
			margin-right: 4px;
		}
	}


	.selectable-items__split-header {
		height: 32px;
		padding: 8px 8px 8px 0;
		background: rgba(240, 240, 240, 0.5);

		section {
			flex: 1;
		}

		.components-checkbox-control {
			padding: 0;
		}

		button:not(:disabled, [aria-disabled="true"]):hover,
		button:not(:disabled, [aria-disabled="true"]):focus {
			background: transparent;
			outline: none;
			box-shadow: none;
		}
	}
}

