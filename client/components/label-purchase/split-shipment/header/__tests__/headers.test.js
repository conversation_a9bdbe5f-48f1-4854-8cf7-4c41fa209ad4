import { render, screen } from '@testing-library/react';
import { <PERSON>Header, SplitHeader, StaticHeader } from '../index';
import { LabelPurchaseContext } from 'context/label-purchase';

jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
	return mockUtils();
} );

describe( 'Split shipment headers', () => {
	it( 'should render the SplitHeader component', () => {
		const context = {
			shipment: {
				shipments: {
					0: [
						{
							id: 1,
							quantity: 1,
							subItems: [],
						},
					],
				},
			},
			labels: {
				getShipmentsWithoutLabel: jest.fn( () => [ 0 ] ),
			},
		};
		const { asFragment } = render(
			<LabelPurchaseContext.Provider value={ context }>
				<SplitHeader
					selectAll={ jest.fn() }
					shipmentIndex={ 0 }
					selectablesCount={ 2 }
					selections={ [] }
					isDisabled={ false }
				/>
			</LabelPurchaseContext.Provider>
		);
		expect( asFragment() ).toMatchSnapshot();
		expect( screen.queryAllByTitle( 'Remove shipment' ) ).toHaveLength( 0 );
	} );

	it( 'should render the SelectionHeader component', () => {
		const { asFragment } = render(
			<SelectionHeader
				selectAll={ jest.fn() }
				selectionsCount={ 2 }
				selectablesCount={ 5 }
			/>
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'SplitHeader should disable delete button for a shipment with label', () => {
		const context = {
			shipment: {
				shipments: {
					0: [
						{
							id: 1,
							quantity: 1,
							subItems: [],
						},
					],
					1: [
						{
							id: 2,
							quantity: 2,
							subItems: [],
						},
					],
				},
			},
			labels: {
				getShipmentsWithoutLabel: jest.fn( () => [ 0, 1 ] ),
			},
		};
		const { asFragment } = render(
			<LabelPurchaseContext.Provider value={ context }>
				<SplitHeader
					selectAll={ jest.fn() }
					shipmentIndex={ 0 }
					selectablesCount={ 2 }
					selections={ [] }
					isDisabled={ false }
				/>
			</LabelPurchaseContext.Provider>
		);
		expect( asFragment() ).toMatchSnapshot();
		expect( screen.getByTitle( 'Remove shipment' ) ).toBeVisible();
	} );

	describe( 'StaticHeader', () => {
		const defaultProps = {
			hasVariations: false,
			selectAll: jest.fn(),
			hasMultipleShipments: false,
		};

		it( 'render the component successfully without variations', () => {
			const selectAll = jest.fn();
			const { asFragment } = render(
				<StaticHeader hasVariations={ false } selectAll={ selectAll } />
			);
			expect( asFragment() ).toMatchSnapshot( 'without variations' );

			const { asFragment: withVariationsFragment } = render(
				<StaticHeader hasVariations={ true } selectAll={ selectAll } />
			);
			expect( withVariationsFragment() ).toMatchSnapshot(
				'with variations'
			);
		} );

		it( 'checkbox should be checked when all items are selected', () => {
			render(
				<StaticHeader
					{ ...defaultProps }
					selections={ [ 1, 2, 3 ] }
					selectablesCount={ 3 }
				/>
			);

			const checkbox = screen.getByRole( 'checkbox' );
			expect( checkbox ).toBeChecked();
			expect( checkbox.indeterminate ).toBe( false );
		} );

		it( 'checkbox should be indeterminate when some items are selected', () => {
			render(
				<StaticHeader
					{ ...defaultProps }
					selections={ [ 1 ] }
					selectablesCount={ 3 }
				/>
			);

			const checkbox = screen.getByRole( 'checkbox' );
			expect( checkbox ).not.toBeChecked();
			expect( checkbox.indeterminate ).toBe( true );
		} );

		it( 'checkbox should be unchecked when no items are selected', () => {
			render(
				<StaticHeader
					{ ...defaultProps }
					selections={ [] }
					selectablesCount={ 3 }
				/>
			);

			const checkbox = screen.getByRole( 'checkbox' );
			expect( checkbox ).not.toBeChecked();
			expect( checkbox.indeterminate ).toBe( false );
		} );
	} );
} );
