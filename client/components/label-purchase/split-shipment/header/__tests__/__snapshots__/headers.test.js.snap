// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Split shipment headers SplitHeader should disable delete button for a shipment with label 1`] = `
<DocumentFragment>
  <div
    class="components-flex selectable-items__split-header css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <div
      class="components-base-control components-checkbox-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <div
          class="components-flex components-h-stack css-1m5h1bg-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="HStack"
        >
          <span
            class="components-checkbox-control__input-container"
          >
            <input
              aria-disabled="false"
              aria-label="Select all items in this shipment"
              class="components-checkbox-control__input"
              id="inspector-checkbox-control-1"
              title="Select all items in this shipment"
              type="checkbox"
              value="1"
            />
          </span>
        </div>
      </div>
    </div>
    <section>
      <button
        aria-disabled="false"
        class="components-button is-tertiary"
        type="button"
      >
        Select all (2)
      </button>
    </section>
    <span
      class="components-truncate components-text css-1uj2gl-PolymorphicDiv-Text-sx-Base-sx-upperCase e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Text"
    >
      Shipment 1/2
    </span>
    <button
      aria-label="Remove shipment"
      class="components-button has-icon"
      title="Remove shipment"
      type="button"
    >
      <svg
        aria-hidden="true"
        focusable="false"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"
        />
      </svg>
    </button>
  </div>
</DocumentFragment>
`;

exports[`Split shipment headers StaticHeader render the component successfully without variations: with variations 1`] = `
<DocumentFragment>
  <dl
    class="components-flex css-gc8ukt-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <div
      class="components-base-control components-checkbox-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <div
          class="components-flex components-h-stack css-1m5h1bg-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="HStack"
        >
          <span
            class="components-checkbox-control__input-container"
          >
            <input
              checked=""
              class="components-checkbox-control__input"
              id="inspector-checkbox-control-3"
              style="visibility: visible;"
              type="checkbox"
              value="1"
            />
            <svg
              aria-hidden="true"
              class="components-checkbox-control__checked"
              focusable="false"
              height="24"
              role="presentation"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"
              />
            </svg>
          </span>
        </div>
      </div>
    </div>
    <dt
      class="item-name"
    >
      Product
    </dt>
    <dt
      class="item-quantity"
    >
      Qty
    </dt>
    <dt
      class="item-variation"
    >
      Variation
    </dt>
    <dt
      class="item-dimensions"
    />
    <dt
      class="item-weight"
    >
      Weight
    </dt>
    <dt
      class="item-price"
    >
      Price
    </dt>
  </dl>
</DocumentFragment>
`;

exports[`Split shipment headers StaticHeader render the component successfully without variations: without variations 1`] = `
<DocumentFragment>
  <dl
    class="components-flex css-gc8ukt-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <div
      class="components-base-control components-checkbox-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <div
          class="components-flex components-h-stack css-1m5h1bg-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="HStack"
        >
          <span
            class="components-checkbox-control__input-container"
          >
            <input
              checked=""
              class="components-checkbox-control__input"
              id="inspector-checkbox-control-2"
              style="visibility: visible;"
              type="checkbox"
              value="1"
            />
            <svg
              aria-hidden="true"
              class="components-checkbox-control__checked"
              focusable="false"
              height="24"
              role="presentation"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"
              />
            </svg>
          </span>
        </div>
      </div>
    </div>
    <dt
      class="item-name"
    >
      Product
    </dt>
    <dt
      class="item-quantity"
    >
      Qty
    </dt>
    <dt
      class="item-dimensions"
    />
    <dt
      class="item-weight"
    >
      Weight
    </dt>
    <dt
      class="item-price"
    >
      Price
    </dt>
  </dl>
</DocumentFragment>
`;

exports[`Split shipment headers should render the SelectionHeader component 1`] = `
<DocumentFragment>
  <section>
    <span
      class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Text"
    >
      2 selected
    </span>
    <button
      class="components-button is-tertiary"
      type="button"
    >
      Select all (5)
    </button>
    <button
      class="components-button is-tertiary"
      type="button"
    >
      Clear selection
    </button>
  </section>
</DocumentFragment>
`;

exports[`Split shipment headers should render the SplitHeader component 1`] = `
<DocumentFragment>
  <div
    class="components-flex selectable-items__split-header css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <div
      class="components-base-control components-checkbox-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <div
          class="components-flex components-h-stack css-1m5h1bg-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="HStack"
        >
          <span
            class="components-checkbox-control__input-container"
          >
            <input
              aria-disabled="false"
              aria-label="Select all items in this shipment"
              class="components-checkbox-control__input"
              id="inspector-checkbox-control-0"
              title="Select all items in this shipment"
              type="checkbox"
              value="1"
            />
          </span>
        </div>
      </div>
    </div>
    <section>
      <button
        aria-disabled="false"
        class="components-button is-tertiary"
        type="button"
      >
        Select all (2)
      </button>
    </section>
    <span
      class="components-truncate components-text css-1uj2gl-PolymorphicDiv-Text-sx-Base-sx-upperCase e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Text"
    >
      Shipment 1/1
    </span>
  </div>
</DocumentFragment>
`;
