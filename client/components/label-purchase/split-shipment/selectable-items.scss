.selectable-items__header {
	justify-content: flex-start;
	background: #fff;

	margin-left: calc(var(--modal-padding) * -1);
	width: calc(100% + var(--modal-padding) * 2);
	padding: 0 var(--modal-padding);
	box-sizing: border-box;
	padding-top: 24px;

	.components-button.is-secondary:disabled,
	.components-button.is-secondary[aria-disabled="true"],
	.components-button.is-secondary[aria-disabled="true"]:hover,
	.components-button.is-tertiary:disabled,
	.components-button.is-tertiary[aria-disabled="true"],
	.components-button.is-tertiary[aria-disabled="true"]:hover {
		border-radius: 2px;
		background: var(--gutenberg-gray-100, #f0f0f0);
	}

	dl {
		text-transform: uppercase;
		color: var(--gutenberg-gray-700, #757575);
		height: 44px;
		align-items: center;
		box-sizing: border-box;
		margin-bottom: 0;

		dt {
			flex: 1;
			text-align: right;
		}

		.item-name {
			text-align: left;
			margin-left: 46px;
			padding-right: 30px;
		}

		.item-quantity {
			width: 76px;
			max-width: 76px;
		}

		.components-checkbox-control,
		.components-base-control__field {
			padding: 0;
			margin: 0;
		}
	}
}

.split-shipment-modal {

	.selectable-items__header {
		position: sticky;
		top: 0;
		z-index: 99;
	}

	.has-scrolled-content {

		.selectable-items__header {
			border-bottom: 1px solid #e0e0e0;
			box-shadow: 0 5px 5px #e0e0e0;
			transition:
				transform ease 0.5s,
				box-shadow ease 0.5s;
		}
	}
}
