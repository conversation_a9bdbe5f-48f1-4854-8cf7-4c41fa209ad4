import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SplitShipmentModal } from '../split-shipment-modal';
import { useLabelPurchaseContext } from 'context/label-purchase';
import { registerLabelPurchaseStore } from 'data/label-purchase';
import { registerAddressStore } from 'data/address';
import { SHOW_SPLIT_SHIPMENT_NOTICE } from '../constants';
import { dispatch } from '@wordpress/data';
import { usePrevious } from '@wordpress/compose';

registerLabelPurchaseStore();
registerAddressStore( true );

jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
	return mockUtils( {
		getConfig: jest.fn().mockReturnValue( {
			packagesSettings: {
				packages: {},
			},
			shippingLabelData: {
				storedData: {
					selected_destination: {},
				},
				currentOrderLabels: [],
			},
		} ),
		getCurrentOrder: jest.fn().mockReturnValue( { id: 1 } ),
		getPaymentSettings: jest.fn().mockReturnValue( {} ),
		getLastOrderCompleted: jest.fn().mockReturnValue( false ),
		getSelectedRates: jest.fn().mockReturnValue( {} ),
		getSelectedHazmat: jest.fn().mockReturnValue( {} ),
		getCustomsInformation: jest.fn().mockReturnValue( {} ),
		getWeightUnit: jest.fn().mockReturnValue( 'lb' ),
		getDimensionsUnit: jest.fn().mockReturnValue( 'in' ),
		getCurrencySymbol: jest.fn().mockReturnValue( '$' ),
		getSelectedRateOptions: jest.fn().mockReturnValue( {} ),
	} );
} );

jest.mock( 'context/label-purchase', () => ( {
	useLabelPurchaseContext: jest.fn(),
} ) );

jest.mock( '@wordpress/data', () => {
	const original = jest.requireActual( '@wordpress/data' );
	return {
		...original,
		dispatch: jest.fn( () => ( {
			updateShipments: jest.fn(),
		} ) ),
	};
} );

jest.mock( '@wordpress/compose', () => {
	const original = jest.requireActual( '@wordpress/compose' );
	return {
		...original,
		usePrevious: jest.fn(),
	};
} );

jest.mock( 'utils/config', () => {
	return {
		getConfig: jest.fn().mockReturnValue( {
			shippingLabelData: {
				currentOrderLabels: [],
			},
			origin_addresses: [],
			order: {
				shipping_address: {},
			},
		} ),
	};
} );

describe( 'SplitShipmentModal', () => {
	const setStartSplitShipment = jest.fn();
	const defaultProps = { setStartSplitShipment };

	const mockContext = {
		shipment: {
			shipments: {
				0: [
					{
						id: 1,
						subItems: [],
						quantity: 1,
					},
					{
						id: 2,
						subItems: [],
						quantity: 1,
					},
				],
			},
			setShipments: jest.fn(),
			selections: { 0: [ { id: 1, subItems: [], quantity: 1 } ] },
			setSelection: jest.fn(),
			resetSelections: jest.fn(),
			currentShipmentId: '0',
			revertLabelShipmentIdsToUpdate: jest.fn(),
			labelShipmentIdsToUpdate: [],
		},
		labels: {
			hasPurchasedLabel: jest.fn(),
		},
		customs: {
			updateCustomsItems: jest.fn(),
		},
	};

	beforeEach( () => {
		useLabelPurchaseContext.mockReturnValue( mockContext );
	} );

	afterEach( () => {
		jest.clearAllMocks();
	} );

	it( 'renders modal with correct title', () => {
		render( <SplitShipmentModal { ...defaultProps } /> );
		expect( screen.getByText( 'Split Shipment' ) ).toBeInTheDocument();
	} );

	it( 'shows creation notice by default', () => {
		localStorage.setItem( SHOW_SPLIT_SHIPMENT_NOTICE, true );
		render( <SplitShipmentModal { ...defaultProps } /> );

		screen.getAllByText( /To create a split shipment/ ).forEach( ( el ) => {
			expect( el ).toBeInTheDocument();
		} );
	} );

	it( 'handles create new shipment button click', () => {
		render( <SplitShipmentModal { ...defaultProps } /> );
		const createButton = screen.getByText( 'Create new shipment' );
		fireEvent.click( createButton );
		expect( mockContext.shipment.setShipments ).toHaveBeenCalled();
	} );

	it( 'handles cancel button click', () => {
		render( <SplitShipmentModal { ...defaultProps } /> );
		const cancelButton = screen.getByText( 'Cancel' );
		fireEvent.click( cancelButton );
		expect( setStartSplitShipment ).toHaveBeenCalledWith( false );
	} );

	it( 'disables save button when no changes made', () => {
		render( <SplitShipmentModal { ...defaultProps } /> );
		const saveButton = screen.getByText( 'Save' );
		expect( saveButton ).toBeDisabled();
	} );

	it( 'shows error notice when update fails', async () => {
		const errorMessage = 'Update failed';
		dispatch.mockReturnValue( {
			updateShipments: jest
				.fn()
				.mockResolvedValue( { error: { message: errorMessage } } ),
		} );
		// Mock previousShipmentsState to enable save button
		usePrevious.mockReturnValue( mockContext.shipment.shipments );

		render( <SplitShipmentModal { ...defaultProps } /> );

		const saveButton = screen.getByText( 'Save' );
		fireEvent.click( saveButton );

		await waitFor( () => {
			screen
				.getAllByText( new RegExp( errorMessage ) )
				.forEach( ( el ) => {
					expect( el ).toBeInTheDocument();
				} );
		} );
	} );
} );
