import { render } from '@testing-library/react';
import { MoveTo } from '../move-to';
import { LabelPurchaseContext } from 'context/label-purchase';

jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
	return mockUtils();
} );

describe( 'MoveTo', () => {
	it( 'should render the MoveTo component', () => {
		const context = {
			shipment: {
				shipments: {
					0: [
						{
							id: 1,
							quantity: 1,
							subItems: [],
						},
					],
					1: [
						{
							id: 2,
							quantity: 1,
							subItems: [],
						},
					],
				},
			},
			labels: {
				hasPurchasedLabel: () => jest.fn( () => false ),
			},
		};
		const { asFragment } = render(
			<LabelPurchaseContext.Provider value={ context }>
				<MoveTo isDisabled={ () => true } />
			</LabelPurchaseContext.Provider>
		);
		expect( asFragment() ).toMatchSnapshot( 'disabled' );

		const { asFragment: enabledAsFragment } = render(
			<LabelPurchaseContext.Provider value={ context }>
				<MoveTo isDisabled={ () => false } />
			</LabelPurchaseContext.Provider>
		);
		expect( enabledAsFragment() ).toMatchSnapshot( 'enabled' );
	} );
} );
