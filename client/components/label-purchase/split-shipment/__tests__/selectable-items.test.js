import { render, screen } from '@testing-library/react';
import { LabelPurchaseContext } from 'context/label-purchase';
import { SelectableItems } from '../selectable-items';

jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
	return mockUtils();
} );

describe( 'SelectableItems', () => {
	it( 'should render the SelectableItems component', () => {
		const contextInitialValue = {
			labels: {
				isCurrentTabPurchasingExtraLabel: jest
					.fn()
					.mockReturnValue( false ),
			},
			shipment: {
				shipments: {
					0: [
						{
							id: 1,
							quantity: 1,
							subItems: [],
						},
					],
					1: [
						{
							id: 2,
							quantity: 1,
							subItems: [],
						},
					],
				},
			},
		};
		const { asFragment } = render(
			<LabelPurchaseContext.Provider value={ contextInitialValue }>
				<SelectableItems
					selectAll={ jest.fn() }
					shipmentIndex={ 0 }
					orderItems={ [
						{
							id: 1,
							quantity: 1,
							subItems: [],
						},
					] }
					selectablesCount={ 1 }
					selections={ [] }
				/>
			</LabelPurchaseContext.Provider>
		);
		expect( asFragment() ).toMatchSnapshot( 'index 0' );

		const { asFragment: index1AsFragment } = render(
			<LabelPurchaseContext.Provider value={ contextInitialValue }>
				<SelectableItems
					selectAll={ jest.fn() }
					shipmentIndex={ 1 }
					orderItems={ [
						{
							id: 1,
							quantity: 1,
							subItems: [],
						},
					] }
					selectablesCount={ 1 }
					selections={ [] }
				/>
			</LabelPurchaseContext.Provider>
		);
		expect( index1AsFragment() ).toMatchSnapshot( 'index 1' );
	} );

	it( 'Should disable or remove action buttons on a shipment having label', () => {
		const contextInitialValue = {
			shipment: {
				shipments: {
					0: [
						{
							id: 1,
							quantity: 1,
							subItems: [],
						},
					],
					1: [
						{
							id: 3,
							quantity: 1,
							subItems: [],
						},
					],
					2: [
						{
							id: 3,
							quantity: 1,
							subItems: [],
						},
					],
				},
			},
			labels: {
				getShipmentsWithoutLabel: jest.fn( () => [ 0, 1 ] ),
				isCurrentTabPurchasingExtraLabel: jest
					.fn()
					.mockReturnValue( false ),
			},
		};
		const { asFragment, container } = render(
			<LabelPurchaseContext.Provider value={ contextInitialValue }>
				<SelectableItems
					selectAll={ jest.fn() }
					shipmentIndex={ 0 }
					orderItems={ [
						{
							id: 1,
							quantity: 1,
							subItems: [],
						},
					] }
					selectablesCount={ 1 }
					selections={ [] }
					isSplit={ true }
				/>
			</LabelPurchaseContext.Provider>
		);
		expect( asFragment() ).toMatchSnapshot(
			'one shipment already has a label'
		);

		expect( screen.queryAllByTitle( 'Remove shipment' ) ).toHaveLength( 1 );
		expect(
			container.querySelectorAll( '.selectable-items__split-header' )
		).toHaveLength( 1 );

		expect(
			screen.queryAllByTitle( 'Select all items in this shipment' )
		).toHaveLength( 1 );
	} );
} );
