// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MoveTo should render the MoveTo component: disabled 1`] = `
<DocumentFragment>
  <div
    class="components-dropdown"
    tabindex="-1"
  >
    <button
      aria-expanded="false"
      class="components-button is-secondary"
      disabled=""
      type="button"
    >
      Move To 
      <svg
        aria-hidden="true"
        focusable="false"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
        />
      </svg>
    </button>
  </div>
</DocumentFragment>
`;

exports[`MoveTo should render the MoveTo component: enabled 1`] = `
<DocumentFragment>
  <div
    class="components-dropdown"
    tabindex="-1"
  >
    <button
      aria-expanded="false"
      class="components-button is-secondary"
      type="button"
    >
      Move To 
      <svg
        aria-hidden="true"
        focusable="false"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
        />
      </svg>
    </button>
  </div>
</DocumentFragment>
`;
