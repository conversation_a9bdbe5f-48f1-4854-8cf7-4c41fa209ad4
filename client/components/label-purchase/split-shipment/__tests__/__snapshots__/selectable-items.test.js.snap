// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SelectableItems Should disable or remove action buttons on a shipment having label: one shipment already has a label 1`] = `
<DocumentFragment>
  <div
    class="components-flex selectable-items__split-header css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <div
      class="components-base-control components-checkbox-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <div
          class="components-flex components-h-stack css-1m5h1bg-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="HStack"
        >
          <span
            class="components-checkbox-control__input-container"
          >
            <input
              aria-label="Select all items in this shipment"
              class="components-checkbox-control__input"
              id="inspector-checkbox-control-2"
              title="Select all items in this shipment"
              type="checkbox"
              value="1"
            />
          </span>
        </div>
      </div>
    </div>
    <section>
      <button
        class="components-button is-tertiary"
        type="button"
      >
        Select all (1)
      </button>
    </section>
    <span
      class="components-truncate components-text css-1uj2gl-PolymorphicDiv-Text-sx-Base-sx-upperCase e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Text"
    >
      Shipment 1/3
    </span>
    <button
      aria-label="Remove shipment"
      class="components-button has-icon"
      title="Remove shipment"
      type="button"
    >
      <svg
        aria-hidden="true"
        focusable="false"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"
        />
      </svg>
    </button>
  </div>
  <div
    class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <div
      class="components-base-control components-checkbox-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <div
          class="components-flex components-h-stack css-1m5h1bg-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="HStack"
        >
          <span
            class="components-checkbox-control__input-container"
          >
            <input
              class="components-checkbox-control__input"
              id="inspector-checkbox-control-3"
              type="checkbox"
              value="1"
            />
          </span>
        </div>
      </div>
    </div>
    <button
      class="components-button expand-row has-icon"
      type="button"
    >
      <svg
        aria-hidden="true"
        focusable="false"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"
        />
      </svg>
    </button>
    <img
      width="32"
    />
    <dl>
      <dt
        class="item-name"
      >
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        />
        <small />
      </dt>
      <dt
        class="item-quantity"
      >
        <small>
          x
        </small>
        1
      </dt>
      <dt
        class="item-dimensions"
      >
        -
      </dt>
      <dt
        class="item-weight"
      >
        -
      </dt>
      <dt
        class="item-price"
      >
        $
      </dt>
    </dl>
  </div>
</DocumentFragment>
`;

exports[`SelectableItems should render the SelectableItems component: index 0 1`] = `
<DocumentFragment>
  <div
    class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <div
      class="components-base-control components-checkbox-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <div
          class="components-flex components-h-stack css-1m5h1bg-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="HStack"
        >
          <span
            class="components-checkbox-control__input-container"
          >
            <input
              class="components-checkbox-control__input"
              id="inspector-checkbox-control-0"
              type="checkbox"
              value="1"
            />
          </span>
        </div>
      </div>
    </div>
    <button
      class="components-button expand-row has-icon"
      type="button"
    >
      <svg
        aria-hidden="true"
        focusable="false"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"
        />
      </svg>
    </button>
    <img
      width="32"
    />
    <dl>
      <dt
        class="item-name"
      >
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        />
        <small />
      </dt>
      <dt
        class="item-quantity"
      >
        <small>
          x
        </small>
        1
      </dt>
      <dt
        class="item-dimensions"
      >
        -
      </dt>
      <dt
        class="item-weight"
      >
        -
      </dt>
      <dt
        class="item-price"
      >
        $
      </dt>
    </dl>
  </div>
</DocumentFragment>
`;

exports[`SelectableItems should render the SelectableItems component: index 1 1`] = `
<DocumentFragment>
  <div
    class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <div
      class="components-base-control components-checkbox-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <div
          class="components-flex components-h-stack css-1m5h1bg-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="HStack"
        >
          <span
            class="components-checkbox-control__input-container"
          >
            <input
              class="components-checkbox-control__input"
              id="inspector-checkbox-control-1"
              type="checkbox"
              value="1"
            />
          </span>
        </div>
      </div>
    </div>
    <button
      class="components-button expand-row has-icon"
      type="button"
    >
      <svg
        aria-hidden="true"
        focusable="false"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"
        />
      </svg>
    </button>
    <img
      width="32"
    />
    <dl>
      <dt
        class="item-name"
      >
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        />
        <small />
      </dt>
      <dt
        class="item-quantity"
      >
        <small>
          x
        </small>
        1
      </dt>
      <dt
        class="item-dimensions"
      >
        -
      </dt>
      <dt
        class="item-weight"
      >
        -
      </dt>
      <dt
        class="item-price"
      >
        $
      </dt>
    </dl>
  </div>
</DocumentFragment>
`;
