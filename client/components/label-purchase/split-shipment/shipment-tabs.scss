@import "@wordpress/base-styles/variables";
@import "@wordpress/base-styles/colors";

 .shipment-tabs {
	max-width: 100%;
	--max-width: 1400px;
	width: 100%;

	> .components-tab-panel__tabs {
		background: #fff;
		box-sizing: border-box;
		margin: 0 auto;
		max-width: var(--max-width, 1400px);
		overflow: auto;
		padding: 0 68px;
		position: sticky;
		top: var(--wcs-shipment-tabs-top);
		z-index: 10;
		display: flex;
		gap: $grid-unit-30;

		button.components-tab-panel__tabs-item.components-button {
			padding: 0;
			margin: 1px 0;
			padding-bottom: 14px;

			/**
			 Apply the following styles to `Edit shipments` button in the Shipment tabs
			  which has id ending with "-edit"
			 */
			&[id$="-edit"] {
				color: #007cba;

				&::after {
					background: transparent;
				}
			}
		}

		svg {
			background: var(--wp-green-green-50, #008a20);
			border-radius: 50px;
			color: #fff;
			height: 16px;
			margin-left: 8px;
			width: 16px;
		}
	}

	> .components-tab-panel__tab-content {
		margin: 0 auto;
		max-width: var(--max-width, 1400px);
	}
}
