.label-purchase-customs {

	.components-base-control {
		flex: 1;
		margin-bottom: 4px;
	}

	.label-purchase-form-tooltip .components-popover__content {
		font-size: 13px;
		padding: 16px;
		text-transform: none;
		white-space: normal;
		width: 280px;
	}

	label.components-base-control__label,
	label.components-input-control__label.components-truncate {
		line-height: 22px;
		max-width: unset;
		min-width: 100%;
		white-space: unset;
		width: 100%;

		.dashicon {
			margin-left: 4px;
		}
	}

	// some input components wrap their labels in .components-flex-item
	.customs-items .components-flex-item {
		position: absolute;
	}

	.components-base-control__field,
	.components-base-control__field .components-flex {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		margin-bottom: 0;

		.components-flex-item {
			min-width: 100%;
		}
	}

	.customs-items {

		.components-base-control__field,
		.components-base-control__field .components-flex {
			position: relative;
		}

		label.components-base-control__label,
		label.components-input-control__label.components-truncate {
			height: 44px;
			margin-top: -48px;
			overflow: unset;
			position: absolute;
			width: 100%;
		}
	}

	.components-checkbox-control {

		.components-base-control__field {

			div {
				flex-direction: row;
				justify-content: flex-start;
			}
		}
	}

	.components-input-control__suffix,
	.components-input-control__prefix {
		color: #007cba;
	}

	.components-input-control__suffix {
		margin-right: 12px;
	}

	.components-input-control__prefix {
		margin-left: 12px;
	}

	.components-base-control__help {
		font-size: 12px;
		margin-top: 0;
		width: 100%;
	}

	.has-error {

		label {
			color: #dc3232;

			.components-popover {
				color: #1e1e1eff;
			}
		}

		.components-input-control__prefix,
		.components-input-control__suffix {
			color: #dc3232;
		}

		.components-text-control__input,
		.components-input-control__backdrop {
			border-color: #dc3232;
		}
	}

	.components-base-control:has([required]) {

		label::after {
			content: " *";
		}
	}
}
