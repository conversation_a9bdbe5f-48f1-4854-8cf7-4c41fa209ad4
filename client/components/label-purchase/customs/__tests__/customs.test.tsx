/* eslint @typescript-eslint/no-var-requires: "off" */
import React from 'react';
import { render, screen } from '@testing-library/react';
import CurrencyFactory from '@woocommerce/currency';

import { registerLabelPurchaseStore } from 'data/label-purchase';
import { Label } from 'types';
import { LabelPurchaseContext } from 'context/label-purchase';
import { Customs } from '../customs';
import { registerAddressStore } from 'data/address';

jest.mock( 'utils', () => {
	const originalUtils = jest.requireActual( 'utils' );
	return {
		...originalUtils,
		composeAddress: jest.fn().mockReturnValue( {} ),
		getSelectedRateOptions: jest.fn().mockReturnValue( {} ),
		composeName: jest.fn().mockReturnValue( {} ),
	};
} );

jest.mock( 'utils/config', () => {
	const {
		packagesSettings,
	} = require( 'utils/__tests__/fixtures/package-settings' );

	const utils = jest.requireActual( 'utils' );
	const address = {
		name: '<PERSON>',
		email: '<EMAIL>',
		address: '123 Main St',
		address_2: 'Apt 1',
		city: 'San Francisco',
		state: 'CA',
		postcode: '94105',
		phone: '******-422-5555',
		country: 'US',
	};
	return {
		...utils,
		getConfig: () => ( {
			shippingLabelData: {
				storedData: {
					destination: address,
					origin: address,
				},
				storeOptions: {
					weight_unit: 'cm',
				},
			},
			order: {
				id: 123,
				shipping_address: address,
				line_items: [
					{
						description: 'P1',
						name: 'P1',
					},
				],
			},
			is_destination_verified: false,
			is_origin_verified: false,
			continents: [
				{
					code: 'NA',
					name: 'North America',
					countries: [
						{
							code: 'US',
							currency_code: 'USD',
							name: 'United States (US)',
							states: [
								{
									code: 'CA',
									name: 'California',
								},
							],
						},
					],
				},
			],
			packagesSettings,
			accountSettings: {
				storeOptions: {
					origin_country: 'US',
				},
			},
			origin_addresses: [ address ],
			eu_countries: [ 'DE' ],
		} ),
		getSelectedRates: () => null,
		getSelectedHazmat: () => null,
		getOriginAddresses: () => [],
		getCustomsInformation: () => null,
		getWeightUnit: () => 'cm',
		getCarrierStrategies: () => ( {
			upsdap: {
				originAddress: {
					1: {
						has_agreed_to_tos: true,
					},
				},
			},
		} ),
	};
} );
registerAddressStore( true );
registerLabelPurchaseStore();
describe( 'Customs section', () => {
	it( 'should render the customs section for the correct number of shipment items', () => {
		const items = [
			{
				id: 1,
				description: 'P1',
				hsTariffNumber: '123456',
				originCountry: 'US',
				price: '10',
				quantity: '1',
			},
			{
				id: 2,
				description: 'P2',
				hsTariffNumber: '123456',
				originCountry: 'US',
				price: '11',
				quantity: '2',
			},
		];
		const getCustomsState = jest.fn( () => ( {
			items,
			contentsType: 'string',
			contentsExplanation: 'string',
			restrictionType: 'string',
			restrictionComments: 'string',
			itn: 'string',
			isReturnToSender: true,
		} ) );
		const storeCurrency = CurrencyFactory();
		const getCurrentShipmentLabel = jest.fn(
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			( shipmentId?: string ) =>
				( {
					isLegacy: false,
				} as Label )
		);
		const ComponentToRender = ( props: { getCustomsState: jest.Mock } ) => (
			<LabelPurchaseContext.Provider
				value={ {
					// @ts-ignore
					rates: {
						updateRates: jest.fn(),
					}, // @ts-ignore
					customs: {
						// @ts-ignore
						getCustomsState: props.getCustomsState,
						setCustomsState: jest.fn(),
						setErrors: jest.fn(),
						isHSTariffNumberRequired: () => false,
					}, // @ts-ignore
					essentialDetails: { setCustomsCompleted: jest.fn() },
					storeCurrency, // @ts-ignore
					shipment: {
						// @ts-ignore
						getShipmentOrigin: () => ( {
							country: 'US',
						} ),
					}, // @ts-ignore
					labels: {
						hasPurchasedLabel: () => false,
						getCurrentShipmentLabel,
					},
				} }
			>
				<Customs />
			</LabelPurchaseContext.Provider>
		);
		const { asFragment, rerender } = render(
			<ComponentToRender getCustomsState={ getCustomsState } />
		);
		expect( asFragment() ).toMatchSnapshot( 'Shipment has 2 items' );
		expect( screen.queryByDisplayValue( 'P1' ) ).toBeTruthy();
		expect( screen.queryByDisplayValue( 'P2' ) ).toBeTruthy();

		const getCustomsState2 = jest.fn( () => ( {
			items: [ items[ 0 ] ],
			contentsType: 'string',
			contentsExplanation: 'string',
			restrictionType: 'string',
			restrictionComments: 'string',
			itn: 'string',
			isReturnToSender: true,
		} ) );

		rerender( <ComponentToRender getCustomsState={ getCustomsState2 } /> );

		expect( asFragment() ).toMatchSnapshot( 'Shipment has 1 item' );
		expect( screen.queryByDisplayValue( 'P1' ) ).toBeTruthy();
		expect( screen.queryByDisplayValue( 'P2' ) ).toBeFalsy();
	} );
} );
