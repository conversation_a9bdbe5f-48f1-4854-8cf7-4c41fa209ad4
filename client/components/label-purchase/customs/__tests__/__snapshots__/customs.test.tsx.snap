// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Customs section should render the customs section for the correct number of shipment items: Shipment has 1 item 1`] = `
<DocumentFragment>
  <div
    class="components-flex label-purchase-customs css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <h3
      class="components-truncate components-text components-heading css-4bfvya-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Heading"
    >
      Customs
    </h3>
    <div
      class="components-flex css-15o8liu-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
    >
      <div
        class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexBlock"
      >
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <div
              class="components-flex components-input-base components-select-control e1mv6sxx3 em5sgkm5 css-16rj6q3-PolymorphicDiv-Flex-base-ItemsColumn-Root-StyledInputBase e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="InputBase"
            >
              <div
                class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="FlexItem"
              >
                <label
                  class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="Text"
                  for="inspector-select-control-4"
                >
                  Content type
                </label>
              </div>
              <div
                class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
              >
                <select
                  class="components-select-control__input css-1i1tjy6-Select-fontSizeStyles-sizeStyles-rtl e1mv6sxx2"
                  id="inspector-select-control-4"
                  required=""
                >
                  <option
                    value="merchandise"
                  >
                    Merchandise
                  </option>
                  <option
                    value="gift"
                  >
                    Gift
                  </option>
                  <option
                    value="returned_goods"
                  >
                    Returned Goods
                  </option>
                  <option
                    value="sample"
                  >
                    Sample
                  </option>
                  <option
                    value="documents"
                  >
                    Documents
                  </option>
                  <option
                    value="other"
                  >
                    Other
                  </option>
                </select>
                <span
                  class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                >
                  <div
                    class="components-input-control-suffix-wrapper e1mv6sxx0 css-1hy8q7r-PrefixSuffixWrapper-prefixSuffixWrapperStyles-InputControlSuffixWrapperWithClickThrough-rtl em5sgkm0"
                    data-wp-c16t="true"
                    data-wp-component="InputControlSuffixWrapper"
                  >
                    <div
                      class="css-di1hl1-DownArrowWrapper e1mv6sxx1"
                    >
                      <svg
                        aria-hidden="true"
                        focusable="false"
                        height="18"
                        viewBox="0 0 24 24"
                        width="18"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                        />
                      </svg>
                    </div>
                  </div>
                </span>
                <div
                  aria-hidden="true"
                  class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexBlock"
      >
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <div
              class="components-flex components-input-base components-select-control e1mv6sxx3 em5sgkm5 css-16rj6q3-PolymorphicDiv-Flex-base-ItemsColumn-Root-StyledInputBase e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="InputBase"
            >
              <div
                class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="FlexItem"
              >
                <label
                  class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="Text"
                  for="inspector-select-control-5"
                >
                  Restriction type
                </label>
              </div>
              <div
                class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
              >
                <select
                  class="components-select-control__input css-1i1tjy6-Select-fontSizeStyles-sizeStyles-rtl e1mv6sxx2"
                  id="inspector-select-control-5"
                  required=""
                >
                  <option
                    value="none"
                  >
                    None
                  </option>
                  <option
                    value="quarantine"
                  >
                    Quarantine
                  </option>
                  <option
                    value="sanitary_phytosanitary_inspection"
                  >
                    Sanitary/Phytosanitary Inspection
                  </option>
                  <option
                    value="other"
                  >
                    Other…
                  </option>
                </select>
                <span
                  class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                >
                  <div
                    class="components-input-control-suffix-wrapper e1mv6sxx0 css-1hy8q7r-PrefixSuffixWrapper-prefixSuffixWrapperStyles-InputControlSuffixWrapperWithClickThrough-rtl em5sgkm0"
                    data-wp-c16t="true"
                    data-wp-component="InputControlSuffixWrapper"
                  >
                    <div
                      class="css-di1hl1-DownArrowWrapper e1mv6sxx1"
                    >
                      <svg
                        aria-hidden="true"
                        focusable="false"
                        height="18"
                        viewBox="0 0 24 24"
                        width="18"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                        />
                      </svg>
                    </div>
                  </div>
                </span>
                <div
                  aria-hidden="true"
                  class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
    >
      <div
        class="components-base-control has-error css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="inspector-text-control-5"
          >
            International transaction number (
            <a
              data-link-type="external"
              href="https://pe.usps.com/text/imm/immc5_010.htm"
              rel="noopener noreferrer"
              target="_blank"
            >
              more info about ITN
            </a>
            )
          </label>
          <input
            aria-describedby="inspector-text-control-5__help"
            checked=""
            class="components-text-control__input is-next-40px-default-size"
            id="inspector-text-control-5"
            type="text"
            value="string"
          />
        </div>
        <p
          class="components-base-control__help css-shdw93-StyledHelp ej5x27r1"
          id="inspector-text-control-5__help"
        >
          Please enter a valid ITN in one of these formats: X12345678901234, AES X12345678901234, or NOEEI 30.37(a)
        </p>
      </div>
    </div>
    <div
      class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
    >
      <div
        class="components-base-control components-checkbox-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <div
            class="components-flex components-h-stack css-1m5h1bg-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="HStack"
          >
            <span
              class="components-checkbox-control__input-container"
            >
              <input
                checked=""
                class="components-checkbox-control__input"
                id="inspector-checkbox-control-1"
                type="checkbox"
                value="1"
              />
              <svg
                aria-hidden="true"
                class="components-checkbox-control__checked"
                focusable="false"
                height="24"
                role="presentation"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"
                />
              </svg>
            </span>
            <label
              class="components-checkbox-control__label"
              for="inspector-checkbox-control-1"
            >
              Return package to sender if undeliverable
            </label>
          </div>
        </div>
      </div>
    </div>
    <div
      class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
    >
      <h4
        class="components-truncate components-text components-heading css-83s3ru-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Heading"
      >
        Product details
      </h4>
    </div>
    <div
      class="components-spacer css-q92uc9-PolymorphicDiv-classes-classes e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Spacer"
    />
    <div
      class="components-flex css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
    >
      <div
        class="components-flex css-1lv5pi3-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Flex"
      >
        <div
          class="components-flex-item components-flex-block css-16fennl-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="FlexBlock"
        >
          <div
            class="components-flex customs-items css-1w696dx-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <div
              class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <label
                  class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
                  for="inspector-text-control-6"
                >
                  Description 
                  <span
                    aria-expanded="false"
                    aria-haspopup="true"
                    class="dashicon dashicons dashicons-info-outline"
                    style="cursor: pointer;"
                  />
                </label>
                <input
                  checked=""
                  class="components-text-control__input is-next-40px-default-size"
                  id="inspector-text-control-6"
                  required=""
                  type="text"
                  value="P1"
                />
              </div>
            </div>
            <div
              class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <label
                  class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
                  for="inspector-text-control-7"
                >
                  HS tariff number (
                  <a
                    data-link-type="external"
                    href="https://woocommerce.com/document/woocommerce-shipping-and-tax/woocommerce-shipping/#section-30"
                    rel="noopener noreferrer"
                    target="_blank"
                  >
                    more…
                  </a>
                  )
                </label>
                <input
                  checked=""
                  class="components-text-control__input is-next-40px-default-size"
                  id="inspector-text-control-7"
                  placeholder="Optional"
                  type="text"
                  value="123456"
                />
              </div>
            </div>
            <div
              class="components-base-control components-input-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-input-base em5sgkm5 css-o61hjq-PolymorphicDiv-Flex-base-ItemsColumn-Root e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="InputBase"
                >
                  <div
                    class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="FlexItem"
                  >
                    <label
                      class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                      data-wp-c16t="true"
                      data-wp-component="Text"
                      for="inspector-input-control-4"
                    >
                      Value per unit
                    </label>
                  </div>
                  <div
                    class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                  >
                    <span
                      class="components-input-control__prefix css-zk8zu-Prefix em5sgkm8"
                    >
                      $
                    </span>
                    <input
                      checked=""
                      class="components-input-control__input css-16i4du4-Input-dragStyles-fontSizeStyles-sizeStyles-customPaddings em5sgkm3"
                      id="inspector-input-control-4"
                      min="0"
                      step="0.01"
                      type="number"
                      value="10"
                    />
                    <div
                      aria-hidden="true"
                      class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div
              class="components-base-control components-input-control has-error css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-input-base em5sgkm5 css-o61hjq-PolymorphicDiv-Flex-base-ItemsColumn-Root e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="InputBase"
                >
                  <div
                    class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="FlexItem"
                  >
                    <label
                      class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                      data-wp-c16t="true"
                      data-wp-component="Text"
                      for="inspector-input-control-5"
                    >
                      Weight per unit
                    </label>
                  </div>
                  <div
                    class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                  >
                    <input
                      aria-describedby="inspector-input-control-5__help"
                      class="components-input-control__input css-fy2ds2-Input-dragStyles-fontSizeStyles-sizeStyles-customPaddings em5sgkm3"
                      id="inspector-input-control-5"
                      min="0"
                      required=""
                      step="0.01"
                      type="number"
                      value=""
                    />
                    <span
                      class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                    >
                      cm
                    </span>
                    <div
                      aria-hidden="true"
                      class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                    />
                  </div>
                </div>
              </div>
              <p
                class="components-base-control__help css-shdw93-StyledHelp ej5x27r1"
                id="inspector-input-control-5__help"
              >
                This field is required
              </p>
            </div>
            <div
              class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-input-base components-select-control e1mv6sxx3 em5sgkm5 css-16rj6q3-PolymorphicDiv-Flex-base-ItemsColumn-Root-StyledInputBase e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="InputBase"
                >
                  <div
                    class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="FlexItem"
                  >
                    <label
                      class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                      data-wp-c16t="true"
                      data-wp-component="Text"
                      for="inspector-select-control-6"
                    >
                      Origin country
                      <span
                        aria-expanded="false"
                        aria-haspopup="true"
                        class="dashicon dashicons dashicons-info-outline"
                        style="cursor: pointer;"
                      />
                       
                    </label>
                  </div>
                  <div
                    class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                  >
                    <select
                      class="components-select-control__input css-1i1tjy6-Select-fontSizeStyles-sizeStyles-rtl e1mv6sxx2"
                      id="inspector-select-control-6"
                      required=""
                    >
                      <option
                        value="US"
                      >
                        United States (US)
                      </option>
                    </select>
                    <span
                      class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                    >
                      <div
                        class="components-input-control-suffix-wrapper e1mv6sxx0 css-1hy8q7r-PrefixSuffixWrapper-prefixSuffixWrapperStyles-InputControlSuffixWrapperWithClickThrough-rtl em5sgkm0"
                        data-wp-c16t="true"
                        data-wp-component="InputControlSuffixWrapper"
                      >
                        <div
                          class="css-di1hl1-DownArrowWrapper e1mv6sxx1"
                        >
                          <svg
                            aria-hidden="true"
                            focusable="false"
                            height="18"
                            viewBox="0 0 24 24"
                            width="18"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                            />
                          </svg>
                        </div>
                      </div>
                    </span>
                    <div
                      aria-hidden="true"
                      class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`Customs section should render the customs section for the correct number of shipment items: Shipment has 2 items 1`] = `
<DocumentFragment>
  <div
    class="components-flex label-purchase-customs css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <h3
      class="components-truncate components-text components-heading css-4bfvya-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Heading"
    >
      Customs
    </h3>
    <div
      class="components-flex css-15o8liu-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
    >
      <div
        class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexBlock"
      >
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <div
              class="components-flex components-input-base components-select-control e1mv6sxx3 em5sgkm5 css-16rj6q3-PolymorphicDiv-Flex-base-ItemsColumn-Root-StyledInputBase e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="InputBase"
            >
              <div
                class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="FlexItem"
              >
                <label
                  class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="Text"
                  for="inspector-select-control-0"
                >
                  Content type
                </label>
              </div>
              <div
                class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
              >
                <select
                  class="components-select-control__input css-1i1tjy6-Select-fontSizeStyles-sizeStyles-rtl e1mv6sxx2"
                  id="inspector-select-control-0"
                  required=""
                >
                  <option
                    value="merchandise"
                  >
                    Merchandise
                  </option>
                  <option
                    value="gift"
                  >
                    Gift
                  </option>
                  <option
                    value="returned_goods"
                  >
                    Returned Goods
                  </option>
                  <option
                    value="sample"
                  >
                    Sample
                  </option>
                  <option
                    value="documents"
                  >
                    Documents
                  </option>
                  <option
                    value="other"
                  >
                    Other
                  </option>
                </select>
                <span
                  class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                >
                  <div
                    class="components-input-control-suffix-wrapper e1mv6sxx0 css-1hy8q7r-PrefixSuffixWrapper-prefixSuffixWrapperStyles-InputControlSuffixWrapperWithClickThrough-rtl em5sgkm0"
                    data-wp-c16t="true"
                    data-wp-component="InputControlSuffixWrapper"
                  >
                    <div
                      class="css-di1hl1-DownArrowWrapper e1mv6sxx1"
                    >
                      <svg
                        aria-hidden="true"
                        focusable="false"
                        height="18"
                        viewBox="0 0 24 24"
                        width="18"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                        />
                      </svg>
                    </div>
                  </div>
                </span>
                <div
                  aria-hidden="true"
                  class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexBlock"
      >
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <div
              class="components-flex components-input-base components-select-control e1mv6sxx3 em5sgkm5 css-16rj6q3-PolymorphicDiv-Flex-base-ItemsColumn-Root-StyledInputBase e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="InputBase"
            >
              <div
                class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="FlexItem"
              >
                <label
                  class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="Text"
                  for="inspector-select-control-1"
                >
                  Restriction type
                </label>
              </div>
              <div
                class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
              >
                <select
                  class="components-select-control__input css-1i1tjy6-Select-fontSizeStyles-sizeStyles-rtl e1mv6sxx2"
                  id="inspector-select-control-1"
                  required=""
                >
                  <option
                    value="none"
                  >
                    None
                  </option>
                  <option
                    value="quarantine"
                  >
                    Quarantine
                  </option>
                  <option
                    value="sanitary_phytosanitary_inspection"
                  >
                    Sanitary/Phytosanitary Inspection
                  </option>
                  <option
                    value="other"
                  >
                    Other…
                  </option>
                </select>
                <span
                  class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                >
                  <div
                    class="components-input-control-suffix-wrapper e1mv6sxx0 css-1hy8q7r-PrefixSuffixWrapper-prefixSuffixWrapperStyles-InputControlSuffixWrapperWithClickThrough-rtl em5sgkm0"
                    data-wp-c16t="true"
                    data-wp-component="InputControlSuffixWrapper"
                  >
                    <div
                      class="css-di1hl1-DownArrowWrapper e1mv6sxx1"
                    >
                      <svg
                        aria-hidden="true"
                        focusable="false"
                        height="18"
                        viewBox="0 0 24 24"
                        width="18"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                        />
                      </svg>
                    </div>
                  </div>
                </span>
                <div
                  aria-hidden="true"
                  class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
    >
      <div
        class="components-base-control has-error css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="inspector-text-control-0"
          >
            International transaction number (
            <a
              data-link-type="external"
              href="https://pe.usps.com/text/imm/immc5_010.htm"
              rel="noopener noreferrer"
              target="_blank"
            >
              more info about ITN
            </a>
            )
          </label>
          <input
            aria-describedby="inspector-text-control-0__help"
            checked=""
            class="components-text-control__input is-next-40px-default-size"
            id="inspector-text-control-0"
            type="text"
            value="string"
          />
        </div>
        <p
          class="components-base-control__help css-shdw93-StyledHelp ej5x27r1"
          id="inspector-text-control-0__help"
        >
          Please enter a valid ITN in one of these formats: X12345678901234, AES X12345678901234, or NOEEI 30.37(a)
        </p>
      </div>
    </div>
    <div
      class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
    >
      <div
        class="components-base-control components-checkbox-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <div
            class="components-flex components-h-stack css-1m5h1bg-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="HStack"
          >
            <span
              class="components-checkbox-control__input-container"
            >
              <input
                checked=""
                class="components-checkbox-control__input"
                id="inspector-checkbox-control-0"
                type="checkbox"
                value="1"
              />
              <svg
                aria-hidden="true"
                class="components-checkbox-control__checked"
                focusable="false"
                height="24"
                role="presentation"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"
                />
              </svg>
            </span>
            <label
              class="components-checkbox-control__label"
              for="inspector-checkbox-control-0"
            >
              Return package to sender if undeliverable
            </label>
          </div>
        </div>
      </div>
    </div>
    <div
      class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
    >
      <h4
        class="components-truncate components-text components-heading css-83s3ru-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Heading"
      >
        Product details
      </h4>
    </div>
    <div
      class="components-spacer css-q92uc9-PolymorphicDiv-classes-classes e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Spacer"
    />
    <div
      class="components-flex css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
    >
      <div
        class="components-flex css-1lv5pi3-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Flex"
      >
        <div
          class="components-flex-item components-flex-block css-16fennl-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="FlexBlock"
        >
          <div
            class="components-flex customs-items css-1w696dx-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <div
              class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <label
                  class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
                  for="inspector-text-control-1"
                >
                  Description 
                  <span
                    aria-expanded="false"
                    aria-haspopup="true"
                    class="dashicon dashicons dashicons-info-outline"
                    style="cursor: pointer;"
                  />
                </label>
                <input
                  checked=""
                  class="components-text-control__input is-next-40px-default-size"
                  id="inspector-text-control-1"
                  required=""
                  type="text"
                  value="P1"
                />
              </div>
            </div>
            <div
              class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <label
                  class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
                  for="inspector-text-control-2"
                >
                  HS tariff number (
                  <a
                    data-link-type="external"
                    href="https://woocommerce.com/document/woocommerce-shipping-and-tax/woocommerce-shipping/#section-30"
                    rel="noopener noreferrer"
                    target="_blank"
                  >
                    more…
                  </a>
                  )
                </label>
                <input
                  checked=""
                  class="components-text-control__input is-next-40px-default-size"
                  id="inspector-text-control-2"
                  placeholder="Optional"
                  type="text"
                  value="123456"
                />
              </div>
            </div>
            <div
              class="components-base-control components-input-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-input-base em5sgkm5 css-o61hjq-PolymorphicDiv-Flex-base-ItemsColumn-Root e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="InputBase"
                >
                  <div
                    class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="FlexItem"
                  >
                    <label
                      class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                      data-wp-c16t="true"
                      data-wp-component="Text"
                      for="inspector-input-control-0"
                    >
                      Value per unit
                    </label>
                  </div>
                  <div
                    class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                  >
                    <span
                      class="components-input-control__prefix css-zk8zu-Prefix em5sgkm8"
                    >
                      $
                    </span>
                    <input
                      checked=""
                      class="components-input-control__input css-16i4du4-Input-dragStyles-fontSizeStyles-sizeStyles-customPaddings em5sgkm3"
                      id="inspector-input-control-0"
                      min="0"
                      step="0.01"
                      type="number"
                      value="10"
                    />
                    <div
                      aria-hidden="true"
                      class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div
              class="components-base-control components-input-control has-error css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-input-base em5sgkm5 css-o61hjq-PolymorphicDiv-Flex-base-ItemsColumn-Root e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="InputBase"
                >
                  <div
                    class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="FlexItem"
                  >
                    <label
                      class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                      data-wp-c16t="true"
                      data-wp-component="Text"
                      for="inspector-input-control-1"
                    >
                      Weight per unit
                    </label>
                  </div>
                  <div
                    class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                  >
                    <input
                      aria-describedby="inspector-input-control-1__help"
                      class="components-input-control__input css-fy2ds2-Input-dragStyles-fontSizeStyles-sizeStyles-customPaddings em5sgkm3"
                      id="inspector-input-control-1"
                      min="0"
                      required=""
                      step="0.01"
                      type="number"
                      value=""
                    />
                    <span
                      class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                    >
                      cm
                    </span>
                    <div
                      aria-hidden="true"
                      class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                    />
                  </div>
                </div>
              </div>
              <p
                class="components-base-control__help css-shdw93-StyledHelp ej5x27r1"
                id="inspector-input-control-1__help"
              >
                This field is required
              </p>
            </div>
            <div
              class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-input-base components-select-control e1mv6sxx3 em5sgkm5 css-16rj6q3-PolymorphicDiv-Flex-base-ItemsColumn-Root-StyledInputBase e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="InputBase"
                >
                  <div
                    class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="FlexItem"
                  >
                    <label
                      class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                      data-wp-c16t="true"
                      data-wp-component="Text"
                      for="inspector-select-control-2"
                    >
                      Origin country
                      <span
                        aria-expanded="false"
                        aria-haspopup="true"
                        class="dashicon dashicons dashicons-info-outline"
                        style="cursor: pointer;"
                      />
                       
                    </label>
                  </div>
                  <div
                    class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                  >
                    <select
                      class="components-select-control__input css-1i1tjy6-Select-fontSizeStyles-sizeStyles-rtl e1mv6sxx2"
                      id="inspector-select-control-2"
                      required=""
                    >
                      <option
                        value="US"
                      >
                        United States (US)
                      </option>
                    </select>
                    <span
                      class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                    >
                      <div
                        class="components-input-control-suffix-wrapper e1mv6sxx0 css-1hy8q7r-PrefixSuffixWrapper-prefixSuffixWrapperStyles-InputControlSuffixWrapperWithClickThrough-rtl em5sgkm0"
                        data-wp-c16t="true"
                        data-wp-component="InputControlSuffixWrapper"
                      >
                        <div
                          class="css-di1hl1-DownArrowWrapper e1mv6sxx1"
                        >
                          <svg
                            aria-hidden="true"
                            focusable="false"
                            height="18"
                            viewBox="0 0 24 24"
                            width="18"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                            />
                          </svg>
                        </div>
                      </div>
                    </span>
                    <div
                      aria-hidden="true"
                      class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="components-flex css-1lv5pi3-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Flex"
      >
        <div
          class="components-flex-item components-flex-block css-16fennl-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="FlexBlock"
        >
          <div
            class="components-flex customs-items css-1w696dx-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <div
              class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <label
                  class="components-visually-hidden css-1qfl78b-PolymorphicDiv e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="VisuallyHidden"
                  for="inspector-text-control-3"
                  style="border: 0px; clip: rect(1px, 1px, 1px, 1px); clip-path: inset( 50% ); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; word-wrap: normal;"
                >
                  Description 
                  <span
                    aria-expanded="false"
                    aria-haspopup="true"
                    class="dashicon dashicons dashicons-info-outline"
                    style="cursor: pointer;"
                  />
                </label>
                <input
                  checked=""
                  class="components-text-control__input is-next-40px-default-size"
                  id="inspector-text-control-3"
                  required=""
                  type="text"
                  value="P2"
                />
              </div>
            </div>
            <div
              class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <label
                  class="components-visually-hidden css-1qfl78b-PolymorphicDiv e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="VisuallyHidden"
                  for="inspector-text-control-4"
                  style="border: 0px; clip: rect(1px, 1px, 1px, 1px); clip-path: inset( 50% ); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; word-wrap: normal;"
                >
                  HS tariff number (
                  <a
                    data-link-type="external"
                    href="https://woocommerce.com/document/woocommerce-shipping-and-tax/woocommerce-shipping/#section-30"
                    rel="noopener noreferrer"
                    target="_blank"
                  >
                    more…
                  </a>
                  )
                </label>
                <input
                  checked=""
                  class="components-text-control__input is-next-40px-default-size"
                  id="inspector-text-control-4"
                  placeholder="Optional"
                  type="text"
                  value="123456"
                />
              </div>
            </div>
            <div
              class="components-base-control components-input-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-input-base em5sgkm5 css-o61hjq-PolymorphicDiv-Flex-base-ItemsColumn-Root e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="InputBase"
                >
                  <label
                    class="components-visually-hidden css-1qfl78b-PolymorphicDiv e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="VisuallyHidden"
                    for="inspector-input-control-2"
                    style="border: 0px; clip: rect(1px, 1px, 1px, 1px); clip-path: inset( 50% ); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; word-wrap: normal;"
                  >
                    Value per unit
                  </label>
                  <div
                    class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                  >
                    <span
                      class="components-input-control__prefix css-zk8zu-Prefix em5sgkm8"
                    >
                      $
                    </span>
                    <input
                      checked=""
                      class="components-input-control__input css-16i4du4-Input-dragStyles-fontSizeStyles-sizeStyles-customPaddings em5sgkm3"
                      id="inspector-input-control-2"
                      min="0"
                      step="0.01"
                      type="number"
                      value="11"
                    />
                    <div
                      aria-hidden="true"
                      class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div
              class="components-base-control components-input-control has-error css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-input-base em5sgkm5 css-o61hjq-PolymorphicDiv-Flex-base-ItemsColumn-Root e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="InputBase"
                >
                  <label
                    class="components-visually-hidden css-1qfl78b-PolymorphicDiv e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="VisuallyHidden"
                    for="inspector-input-control-3"
                    style="border: 0px; clip: rect(1px, 1px, 1px, 1px); clip-path: inset( 50% ); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; word-wrap: normal;"
                  >
                    Weight per unit
                  </label>
                  <div
                    class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                  >
                    <input
                      aria-describedby="inspector-input-control-3__help"
                      class="components-input-control__input css-fy2ds2-Input-dragStyles-fontSizeStyles-sizeStyles-customPaddings em5sgkm3"
                      id="inspector-input-control-3"
                      min="0"
                      required=""
                      step="0.01"
                      type="number"
                      value=""
                    />
                    <span
                      class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                    >
                      cm
                    </span>
                    <div
                      aria-hidden="true"
                      class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                    />
                  </div>
                </div>
              </div>
              <p
                class="components-base-control__help css-shdw93-StyledHelp ej5x27r1"
                id="inspector-input-control-3__help"
              >
                This field is required
              </p>
            </div>
            <div
              class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-input-base components-select-control e1mv6sxx3 em5sgkm5 css-16rj6q3-PolymorphicDiv-Flex-base-ItemsColumn-Root-StyledInputBase e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="InputBase"
                >
                  <label
                    class="components-visually-hidden css-1qfl78b-PolymorphicDiv e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="VisuallyHidden"
                    for="inspector-select-control-3"
                    style="border: 0px; clip: rect(1px, 1px, 1px, 1px); clip-path: inset( 50% ); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; word-wrap: normal;"
                  >
                    Origin country
                    <span
                      aria-expanded="false"
                      aria-haspopup="true"
                      class="dashicon dashicons dashicons-info-outline"
                      style="cursor: pointer;"
                    />
                     
                  </label>
                  <div
                    class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                  >
                    <select
                      class="components-select-control__input css-1i1tjy6-Select-fontSizeStyles-sizeStyles-rtl e1mv6sxx2"
                      id="inspector-select-control-3"
                      required=""
                    >
                      <option
                        value="US"
                      >
                        United States (US)
                      </option>
                    </select>
                    <span
                      class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                    >
                      <div
                        class="components-input-control-suffix-wrapper e1mv6sxx0 css-1hy8q7r-PrefixSuffixWrapper-prefixSuffixWrapperStyles-InputControlSuffixWrapperWithClickThrough-rtl em5sgkm0"
                        data-wp-c16t="true"
                        data-wp-component="InputControlSuffixWrapper"
                      >
                        <div
                          class="css-di1hl1-DownArrowWrapper e1mv6sxx1"
                        >
                          <svg
                            aria-hidden="true"
                            focusable="false"
                            height="18"
                            viewBox="0 0 24 24"
                            width="18"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                            />
                          </svg>
                        </div>
                      </div>
                    </span>
                    <div
                      aria-hidden="true"
                      class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
