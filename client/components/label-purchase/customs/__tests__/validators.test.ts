import { normalizeITN, validateITN } from '../validators';

jest.mock( 'utils/order', () => ( {
	getCurrentOrderItems: jest.fn( () => [
		{ total: '3000' },
		{ total: '1500' },
	] ),
} ) );

describe( 'normalizeITN', () => {
	it.each( [
		// [input, expected]
		[ 'X12345678901234', 'AES X12345678901234' ],
		[ '12345678901234', 'AES X12345678901234' ],
		[ 'AES X12345678901234', 'AES X12345678901234' ],
		[ 'AES ITN: X12345678901234', 'AES X12345678901234' ],
		[ 'aes itn:X12345678901234', 'AES X12345678901234' ],
		[ 'NOEEI 30.3', 'NOEEI 30.3' ],
		[ 'NOEEI 30.37', 'NOEEI 30.37' ],
		[ 'NOEEI 30.37(a)', 'NOEEI 30.37(a)' ],
		[ 'NOEEI  30.37(a)', 'NOEEI 30.37(a)' ],
		[ 'NOEEI 30.37(A)', 'NOEEI 30.37(a)' ],
		[ 'NOEEI 30.37(a)(1)', 'NOEEI 30.37(a)(1)' ],
		[ '  AES  X12345678901234  ', 'AES X12345678901234' ],
		[ 'AES 12345678901234', 'AES X12345678901234' ],
		[ 'AES ITN 12345678901234', 'AES X12345678901234' ],
		[ 'AES ITN X12345678901234', 'AES X12345678901234' ],
	] )( 'normalizes ITN %s to %s', ( input, expected ) => {
		expect( normalizeITN( input ) ).toBe( expected );
	} );
} );

describe( 'validateITN', () => {
	const mockItems = [
		{
			product_id: 1,
			price: '1000',
			quantity: 3,
			description: 'Item 1',
			hsTariffNumber: '123456',
			originCountry: 'US',
			id: 1,
			weight: '1',
			meta: {},
			tax_class: '',
			image: '',
			sku: '',
			subtotal: '1000',
			subtotal_tax: '0',
			total: '1000',
			total_tax: '0',
		},
		{
			product_id: 2,
			price: '600',
			quantity: 2,
			description: 'Item 2',
			hsTariffNumber: '654321',
			originCountry: 'US',
			id: 2,
			weight: '1',
			meta: {},
			tax_class: '',
			image: '',
			sku: '',
			subtotal: '600',
			subtotal_tax: '0',
			total: '600',
			total_tax: '0',
		},
	];

	const mockErrors = {
		items: mockItems.map( () => ( {} ) ),
	};

	it( 'should validate ITN format', () => {
		const validate = validateITN( {
			country: 'US',
			countryName: 'United States',
		} );
		const result = validate( {
			values: {
				itn: 'invalid-itn',
				items: mockItems,
				contentsType: '',
				restrictionType: '',
				isReturnToSender: false,
			},
			errors: mockErrors,
		} );
		expect( result.errors.itn ).toBe(
			'Please enter a valid ITN in one of these formats: X12345678901234, AES X12345678901234, or NOEEI 30.37(a)'
		);
	} );

	it( 'should require ITN for specific countries', () => {
		const validate = validateITN( { country: 'CN', countryName: 'China' } );
		const result = validate( {
			values: {
				itn: '',
				items: mockItems,
				contentsType: '',
				restrictionType: '',
				isReturnToSender: false,
			},
			errors: mockErrors,
		} );
		expect( result.errors.itn ).toBe(
			'International Transaction Number is required for shipping items valued over $2,500 per tariff number. Products with tariff number 123456 add up to more than $2,500.'
		);
	} );
} );
