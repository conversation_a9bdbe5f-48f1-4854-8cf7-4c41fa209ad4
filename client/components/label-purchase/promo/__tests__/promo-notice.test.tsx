import { render, screen, fireEvent } from '@testing-library/react';
import { PromoNotice } from '../promo-notice';
import { useLabelPurchaseContext } from 'context/label-purchase';
import { getPromotion, dismissPromo } from 'utils';
import { Promotion } from 'types';

jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
	return { ...mockUtils(), dismissPromo: jest.fn() };
} );

jest.mock( 'context/label-purchase', () => ( {
	useLabelPurchaseContext: jest.fn(),
} ) );

const mockGetPromotion = jest.mocked( getPromotion );
const mockDismissPromo = jest.mocked( dismissPromo );
const mockSetIsOpen = jest.fn();
const mockSetCurrentPackageTab = jest.fn();
const mockSetInitialCarrierTab = jest.fn();
const mockedUseLabelPurchaseContext = jest.mocked( useLabelPurchaseContext );

mockedUseLabelPurchaseContext.mockReturnValue( {
	packages: {
		setCurrentPackageTab: mockSetCurrentPackageTab,
		setInitialCarrierTab: mockSetInitialCarrierTab,
	},
} as unknown as ReturnType< typeof useLabelPurchaseContext > );

describe( 'PromoNotice', () => {
	const testPromo: Promotion = {
		id: 'promo-1',
		carrier: 'upsdap',
		endDate: new Date( '2025-12-31' ).toJSON(),
		remaining: 5,
		discountType: 'fixed',
		discountAmount: 10,
	};

	it( 'renders null when no promotion is available', () => {
		mockGetPromotion.mockReturnValue( undefined );

		const { container } = render(
			<PromoNotice setIsOpen={ mockSetIsOpen } />
		);
		expect( container.firstChild ).toBeNull();
	} );

	it( 'renders null when promotion exists but has no notice', () => {
		mockGetPromotion.mockReturnValue( testPromo );

		const { container } = render(
			<PromoNotice setIsOpen={ mockSetIsOpen } />
		);
		expect( container.firstChild ).toBeNull();
	} );

	it( 'renders notice with actions when promotion has notice', () => {
		mockGetPromotion.mockReturnValue( {
			...testPromo,
			notice: '<strong>Special promo!</strong>',
		} );

		render( <PromoNotice setIsOpen={ mockSetIsOpen } /> );

		expect(
			screen.getByText( 'Special promo!', { selector: 'strong' } )
		).toBeInTheDocument();
		expect( screen.getByText( 'Ship with UPS®' ) ).toBeInTheDocument();
	} );

	it( 'calls correct functions when "Ship with carrier" button is clicked', () => {
		mockGetPromotion.mockReturnValue( {
			...testPromo,
			notice: 'Promotion available!',
		} );

		render( <PromoNotice setIsOpen={ mockSetIsOpen } /> );

		const shipButton = screen.getByText( 'Ship with UPS®' );
		fireEvent.click( shipButton );

		expect( mockSetCurrentPackageTab ).toHaveBeenCalledWith(
			'carrier-package'
		);
		expect( mockSetInitialCarrierTab ).toHaveBeenCalledWith( 'upsdap' );
		expect( mockSetIsOpen ).toHaveBeenCalledWith( true );
	} );

	it( 'calls dismissPromo when dismiss button is clicked', () => {
		mockGetPromotion.mockReturnValue( {
			...testPromo,
			notice: 'Promotion available!',
		} );

		render( <PromoNotice setIsOpen={ mockSetIsOpen } /> );

		const dismissButton = screen.getByRole( 'button', {
			name: 'Close',
		} );
		fireEvent.click( dismissButton );

		expect( mockDismissPromo ).toHaveBeenCalledWith(
			'notice',
			testPromo.id
		);
		expect( screen.queryAllByText( 'Promotion available!' ) ).toHaveLength(
			1
		);
	} );
} );
