// promo-tooltip.test.tsx

import { render, screen } from '@testing-library/react';
import { PromoTooltip } from '../promo-tooltip';
import { getPromotion } from 'utils';
import { Promotion } from 'types';

const mockGetPromotion = jest.mocked( getPromotion );

jest.mock( 'utils' );

describe( 'PromoTooltip', () => {
	const basePromo: Promotion = {
		id: 'promo-1',
		carrier: 'upsdap',
		endDate: new Date( '2025-12-31' ).toJSON(),
		remaining: 5,
		discountType: 'fixed',
		discountAmount: 10,
		tooltip: 'Promo details, <a>learn more.</a>',
	};

	afterEach( () => {
		jest.clearAllMocks();
	} );

	it( 'renders null when no promotion is available', () => {
		mockGetPromotion.mockReturnValue( undefined );

		const { container } = render( <PromoTooltip rate={ 5 } /> );
		expect( container.firstChild ).toBeNull();
	} );

	it( 'renders null when promotion has no tooltip', () => {
		mockGetPromotion.mockReturnValue( {
			...basePromo,
			tooltip: undefined,
		} );

		const { container } = render( <PromoTooltip rate={ 5 } /> );
		expect( container.firstChild ).toBeNull();
	} );

	it( 'renders null when discountType is not fixed', () => {
		mockGetPromotion.mockReturnValue( {
			...basePromo,
			discountType: 'percentage',
		} );

		const { container } = render( <PromoTooltip rate={ 5 } /> );
		expect( container.firstChild ).toBeNull();
	} );

	it( 'renders null when rate is greater than discountAmount', () => {
		mockGetPromotion.mockReturnValue( basePromo );

		const { container } = render( <PromoTooltip rate={ 20 } /> );
		expect( container.firstChild ).toBeNull();
	} );

	it( 'renders null when rate equals discountAmount', () => {
		mockGetPromotion.mockReturnValue( basePromo );

		const { container } = render( <PromoTooltip rate={ 10 } /> );
		expect( container.firstChild ).toBeNull();
	} );

	it( 'renders tooltip when promo is valid and rate is eligible', () => {
		mockGetPromotion.mockReturnValue( basePromo );

		render( <PromoTooltip rate={ 5 } /> );
		expect( screen.getByRole( 'button' ) ).toBeInTheDocument();
	} );
} );
