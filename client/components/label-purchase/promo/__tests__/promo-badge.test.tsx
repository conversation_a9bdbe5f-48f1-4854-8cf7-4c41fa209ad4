import { render, screen } from '@testing-library/react';
import { PromoBadge } from '../promo-badge';
import { getPromotion } from 'utils';
import { Promotion } from 'types';

const mockGetPromotion = jest.mocked( getPromotion );

jest.mock( 'utils' );

describe( 'PromoBadge', () => {
	const testPromo: Promotion = {
		id: 'promo-1',
		carrier: 'upsdap',
		endDate: new Date( '2025-12-31' ).toJSON(),
		remaining: 5,
		discountType: 'fixed',
		discountAmount: 10,
	};

	afterEach( () => {
		jest.clearAllMocks();
	} );

	it( 'renders null when no promotion is available', () => {
		mockGetPromotion.mockReturnValue( undefined );

		const { container } = render( <PromoBadge carrier="upsdap" /> );
		expect( container.firstChild ).toBeNull();
	} );

	it( 'renders null when promotion exists but has no badge', () => {
		mockGetPromotion.mockReturnValue( testPromo );

		const { container } = render( <PromoBadge carrier="upsdap" /> );
		expect( container.firstChild ).toBeNull();
	} );

	it( 'renders null when promotion has badge but carrier does not match', () => {
		mockGetPromotion.mockReturnValue( {
			...testPromo,
			badge: 'Special offer!',
		} );

		const { container } = render( <PromoBadge carrier="test" /> );
		expect( container.firstChild ).toBeNull();
	} );

	it( 'renders badge when promotion has badge and carrier matches', () => {
		mockGetPromotion.mockReturnValue( {
			...testPromo,
			badge: '$10 OFF!',
		} );

		render( <PromoBadge carrier="upsdap" /> );

		expect( screen.getByText( '$10 OFF!' ) ).toBeInTheDocument();
	} );
} );
