import { render, screen, fireEvent } from '@testing-library/react';
import { PromoBanner } from '../promo-banner';
import { getPromotion, dismissPromo } from 'utils';
import { Promotion } from 'types';

const mockGetPromotion = jest.mocked( getPromotion );
const mockDismissPromo = jest.mocked( dismissPromo );

jest.mock( 'components/carrier-icon', () => ( {
	CarrierIcon: ( { carrier }: { carrier: string } ) => (
		<div data-testid="carrier-icon">{ carrier }</div>
	),
} ) );

jest.mock( 'utils', () => ( {
	dismissPromo: jest.fn(),
	getPromotion: jest.fn(),
	recordEvent: jest.fn(),
} ) );

describe( 'PromoBanner', () => {
	const testPromo: Promotion = {
		id: 'promo-1',
		carrier: 'upsdap',
		endDate: new Date( '2025-12-31' ).toJSON(),
		remaining: 5,
		discountType: 'fixed',
		discountAmount: 10,
	};

	afterEach( () => {
		jest.clearAllMocks();
	} );

	it( 'renders null when no promotion is available', () => {
		mockGetPromotion.mockReturnValue( undefined );

		const { container } = render( <PromoBanner /> );
		expect( container.firstChild ).toBeNull();
	} );

	it( 'renders null when promotion exists but has no banner', () => {
		mockGetPromotion.mockReturnValue( testPromo );

		const { container } = render( <PromoBanner /> );
		expect( container.firstChild ).toBeNull();
	} );

	it( 'renders banner with carrier icon and HTML content when promotion has banner', () => {
		mockGetPromotion.mockReturnValue( {
			...testPromo,
			banner: '<strong>Special promotion!</strong> Get 20% off.',
		} );

		render( <PromoBanner /> );

		expect( screen.getByTestId( 'carrier-icon' ) ).toHaveTextContent(
			'upsdap'
		);
		expect( screen.getByText( 'Special promotion!' ) ).toBeInTheDocument();
		expect( screen.getByText( 'Get 20% off.' ) ).toBeInTheDocument();
	} );

	it( 'calls dismissPromo and updates style when dismiss button is clicked', () => {
		mockGetPromotion.mockReturnValue( {
			...testPromo,
			banner: '<strong>Special promotion!</strong> Get 20% off.',
		} );

		render( <PromoBanner /> );

		const dismissButton = screen.getByRole( 'button', {
			name: 'Close',
		} );
		fireEvent.click( dismissButton );

		expect( mockDismissPromo ).toHaveBeenCalledWith(
			'banner',
			testPromo.id
		);
		expect(
			screen.queryByText( 'Special promotion!' )
		).not.toBeInTheDocument();
		expect(
			document.documentElement.style.getPropertyValue(
				'--wcs-promo-banner-height'
			)
		).toBe( '0px' );
	} );
} );
