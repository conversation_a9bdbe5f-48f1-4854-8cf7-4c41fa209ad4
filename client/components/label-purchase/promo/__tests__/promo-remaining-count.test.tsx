import { render, screen } from '@testing-library/react';
import { PromoRemainingCount } from '../promo-remaining-count';
import { getPromotion } from 'utils';
import { Promotion } from 'types';

const mockGetPromotion = jest.mocked( getPromotion );

jest.mock( 'utils' );

jest.mock( '../../packages', () => ( {
	CARRIER_ID_TO_NAME: {
		upsdap: 'UPS',
	},
} ) );

describe( 'PromoRemainingCount', () => {
	const basePromo: Promotion = {
		id: 'promo-1',
		carrier: 'upsdap',
		endDate: new Date( '2025-12-31' ).toJSON(),
		remaining: 5,
		discountType: 'fixed',
		discountAmount: 10,
	};

	afterEach( () => {
		jest.clearAllMocks();
	} );

	it( 'renders null when no promotion is available', () => {
		mockGetPromotion.mockReturnValue( undefined );

		const { container } = render( <PromoRemainingCount /> );
		expect( container.firstChild ).toBeNull();
	} );

	it( 'renders "ending soon" badge when promo ends within 7 days', () => {
		const soonDate = new Date();
		soonDate.setDate( soonDate.getDate() + 2 );
		mockGetPromotion.mockReturnValue( {
			...basePromo,
			endDate: soonDate.toJSON(),
			remaining: 3,
		} );

		render( <PromoRemainingCount /> );
		expect(
			screen.getByText( 'Promo ending soon · 3 left' )
		).toBeInTheDocument();
	} );

	it( 'renders carrier promo badge when promo does not end soon', () => {
		mockGetPromotion.mockReturnValue( {
			...basePromo,
			endDate: new Date( '2025-12-31' ).toJSON(),
			remaining: 7,
		} );

		render( <PromoRemainingCount /> );
		expect(
			screen.getByText( 'UPS Promo: 7 remaining' )
		).toBeInTheDocument();
	} );
} );
