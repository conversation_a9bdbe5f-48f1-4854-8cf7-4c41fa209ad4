import { render } from '@testing-library/react';
import { Items } from '../items';

jest.mock( 'utils', () => ( {
	getWeightUnit: () => 'lb',
	getCurrencySymbol: () => '$',
	getDimensionsUnit: () => 'in',
} ) );

describe( 'LabelPurchaseItems', () => {
	it( 'should render correctly', () => {
		const orderItems = [
			{
				id: 1,
				name: 'Test Product',
				quantity: 1,
				sku: '123',
				weight: 10,
				dimensions: {
					height: 1,
					width: 2,
					length: 3,
				},
				price: '10.05',
			},
		];
		const { asFragment } = render( <Items orderItems={ orderItems } /> );
		expect( asFragment() ).toMatchSnapshot();
	} );
} );
