// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`LabelPurchaseItems should render correctly 1`] = `
<DocumentFragment>
  <div
    class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <img
      alt="Test Product"
      width="32"
    />
    <dl>
      <dt
        class="item-name"
      >
        <span
          class="components-truncate components-text css-14afdk-PolymorphicDiv-Truncate-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          Test Product
        </span>
        <small>
          123
        </small>
      </dt>
      <dt
        class="item-quantity"
      >
        <small>
          x
        </small>
        1
      </dt>
      <dt
        class="item-dimensions"
      >
        3 x 2 x 1 (in)
      </dt>
      <dt
        class="item-weight"
      >
        10 lb
      </dt>
      <dt
        class="item-price"
      >
        $10.05
      </dt>
    </dl>
  </div>
</DocumentFragment>
`;
