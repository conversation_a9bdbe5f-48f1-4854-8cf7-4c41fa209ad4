.label-purchase-list-items {
	margin-top: 24px;

	> .components-flex {
		height: 52px;
		max-height: 52px;
		box-sizing: border-box;
		padding: 16px 0 16px 0;
		border: none;
		border-bottom: 1px solid var(--gutenberg-gray-200, #e0e0e0);

		&:last-child {
			border-bottom: none;
		}

		img {
			border-radius: 2px;
			max-width: 32px;
			border: 1px solid var(--gutenberg-gray-300, #ddd);
		}

		dl {
			color: var(--gutenberg-gray-900, #1e1e1e);
			font-size: 13px;
			font-weight: 400;
			display: flex;
			margin-left: 12px;
			width: 100%;
			justify-content: space-between;
			position: relative;
			box-sizing: border-box;
			align-items: center;

			dt {
				flex: 1;
				justify-content: right;
				text-align: right;
				box-sizing: border-box;
				line-height: 16px;
			}

			small {
				color: var(--gutenberg-gray-700, #757575);
				font-size: 11px;
				font-style: normal;
				font-weight: 400;
				line-height: 16px;
				left: 0;
				top: 16px;
			}

			.item-name {
				display: flex;
				flex-direction: column;
				text-align: left;
				overflow: hidden;

				small {
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			.item-quantity {
				display: flex;
				height: 24px;
				padding: 0 8px;
				align-items: center;
				justify-content: center;
				gap: 2px;
				border-radius: 2px;
				background: var(--gutenberg-gray-100, #f0f0f0);
				max-width: 30px;
				font-size: 12px;
				margin-left: 4px;
			}

			.item-variation {
				padding: 8px 0 8px 10px;
				text-transform: uppercase;
			}

			.item-dimensions,
			.item-weight {
				padding: 8px 0 8px 10px;
			}

			.item-price {
				padding: 8px 0;
			}

			@media only screen and (max-width: 960px) {


				.item-dimensions {
					white-space: nowrap;
				}

				.item-weight {
					max-width: 60px;
				}

				.item-price {
					max-width: 120px;
				}
			}

		}

		.selectable-items__header dl {

			dt.item-name {
				margin-left: 35px;
			}

			dt.item-quantity {
				background: none;
				padding-left: 37px;
			}

			dt.item-variation {
				padding-left: 40px;
			}

			dt.item-dimensions {
				padding-left: 5px;
			}
		}

		&.label-purchase__additional-label {
			height: auto;
			max-height: initial;
			padding: 0;

			.selectable-items__header {
				box-shadow: none;
				padding: 0;

				dl {
					margin-left: 0;

					dt.item-name {
						margin-left: 35px;
					}

					dt.item-quantity {
						background: none;
						padding-left: 37px;
					}

					dt.item-variation {
						padding-left: 40px;
					}

					dt.item-dimensions {
						padding-left: 5px;
					}
				}
			}

			.selection-header-wrapper {
				height: 32px;
				border-bottom: 1px solid var(--gutenberg-gray-200, #e0e0e0);
			}

		}
	}

	.components-button.expand-row {
		min-width: 20px;
		width: 20px;
		padding: 0;
		visibility: hidden;

		svg {
			width: 16px;
		}

		&.is-visible {
			visibility: visible;
		}

		&:focus,
		&:hover {
			outline: none;
			box-shadow: none;
		}
	}

	.sub-item {
		padding-left: 46px;
	}
}

.label-purchase-modal {

	.label-purchase-list-items {

		.components-button.expand-row {
			min-width: 20px;
			width: 20px;
			padding: 0;
		}
	}
}
