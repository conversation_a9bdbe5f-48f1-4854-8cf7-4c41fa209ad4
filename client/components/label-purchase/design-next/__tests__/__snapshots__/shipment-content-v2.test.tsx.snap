// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ShipmentContentV2 Snapshots should render complex state with multiple notices 1`] = `
<DocumentFragment>
  <div
    data-columns="1"
    data-row-gap="24px"
    data-testid="grid"
  >
    <div
      data-testid="purchase-notice"
    >
      Purchase Notice
    </div>
    <div
      data-margin="12"
      data-testid="divider"
    >
      Divider
    </div>
    <div
      data-testid="purchase-error-notice"
    >
      Purchase Error Notice - PURCHASED
    </div>
    <div
      data-testid="items-card"
    >
      Items Card - 2 items
    </div>
    <p
      class="upsdap-trademark-notice upsdap-trademark-notice--desktop"
    >
      UPS, the UPS brandmark, UPS Ready®, and the color brown are trademarks of United Parcel Service of America, Inc. All Rights Reserved.
    </p>
    <div
      data-testid="shipment-details"
    >
      Shipment Details - Order: 123
    </div>
    <div
      data-testid="payment-buttons"
    >
      Payment Buttons - Order: 123
    </div>
    <p
      class="upsdap-trademark-notice upsdap-trademark-notice--mobile"
    >
      UPS, the UPS brandmark, UPS Ready®, and the color brown are trademarks of United Parcel Service of America, Inc. All Rights Reserved.
    </p>
  </div>
</DocumentFragment>
`;

exports[`ShipmentContentV2 Snapshots should render default state without purchased label 1`] = `
<DocumentFragment>
  <div
    data-columns="1"
    data-row-gap="24px"
    data-testid="grid"
  >
    <div
      data-testid="purchase-error-notice"
    >
      Purchase Error Notice - no label
    </div>
    <div
      data-testid="items-card"
    >
      Items Card - 2 items
    </div>
    <div
      data-testid="packages-card"
    >
      Packages Card
    </div>
    <div
      class=""
      data-dismissible="false"
      data-status="info"
      data-testid="notice"
    >
      <p>
        No shipping rates were found based on the combination of the selected package and the total shipment weight.
      </p>
      <p>
        We couldn't find a shipping service for the combination of the selected package and the total shipment weight. Please adjust your input and try again.
      </p>
    </div>
    <div
      data-testid="shipment-details"
    >
      Shipment Details - Order: 123
    </div>
    <div
      data-testid="payment-buttons"
    >
      Payment Buttons - Order: 123
    </div>
  </div>
</DocumentFragment>
`;

exports[`ShipmentContentV2 Snapshots should render extra label purchase mode 1`] = `
<DocumentFragment>
  <div
    data-columns="1"
    data-row-gap="24px"
    data-testid="grid"
  >
    <div
      data-testid="purchase-error-notice"
    >
      Purchase Error Notice - no label
    </div>
    <div
      data-testid="items-card"
    >
      Items Card - 2 items
    </div>
    <div
      data-testid="packages-card"
    >
      Packages Card
    </div>
    <div
      data-testid="shipping-rates"
    >
      Shipping Rates - 1 rates, fetching: false
    </div>
    <p
      class="upsdap-trademark-notice upsdap-trademark-notice--desktop"
    >
      UPS, the UPS brandmark, UPS Ready®, and the color brown are trademarks of United Parcel Service of America, Inc. All Rights Reserved.
    </p>
    <div
      data-testid="shipment-details"
    >
      Shipment Details - Order: 123
    </div>
    <div
      data-testid="payment-buttons"
    >
      Payment Buttons - Order: 123
    </div>
    <p
      class="upsdap-trademark-notice upsdap-trademark-notice--mobile"
    >
      UPS, the UPS brandmark, UPS Ready®, and the color brown are trademarks of United Parcel Service of America, Inc. All Rights Reserved.
    </p>
  </div>
</DocumentFragment>
`;

exports[`ShipmentContentV2 Snapshots should render with UPS packages 1`] = `
<DocumentFragment>
  <div
    data-columns="1"
    data-row-gap="24px"
    data-testid="grid"
  >
    <div
      data-testid="purchase-error-notice"
    >
      Purchase Error Notice - no label
    </div>
    <div
      data-testid="items-card"
    >
      Items Card - 2 items
    </div>
    <div
      data-testid="packages-card"
    >
      Packages Card
    </div>
    <div
      data-testid="shipping-rates"
    >
      Shipping Rates - 1 rates, fetching: false
    </div>
    <p
      class="upsdap-trademark-notice upsdap-trademark-notice--desktop"
    >
      UPS, the UPS brandmark, UPS Ready®, and the color brown are trademarks of United Parcel Service of America, Inc. All Rights Reserved.
    </p>
    <div
      data-testid="shipment-details"
    >
      Shipment Details - Order: 123
    </div>
    <div
      data-testid="payment-buttons"
    >
      Payment Buttons - Order: 123
    </div>
    <p
      class="upsdap-trademark-notice upsdap-trademark-notice--mobile"
    >
      UPS, the UPS brandmark, UPS Ready®, and the color brown are trademarks of United Parcel Service of America, Inc. All Rights Reserved.
    </p>
  </div>
</DocumentFragment>
`;

exports[`ShipmentContentV2 Snapshots should render with available rates 1`] = `
<DocumentFragment>
  <div
    data-columns="1"
    data-row-gap="24px"
    data-testid="grid"
  >
    <div
      data-testid="purchase-error-notice"
    >
      Purchase Error Notice - no label
    </div>
    <div
      data-testid="items-card"
    >
      Items Card - 2 items
    </div>
    <div
      data-testid="packages-card"
    >
      Packages Card
    </div>
    <div
      data-testid="shipping-rates"
    >
      Shipping Rates - 2 rates, fetching: false
    </div>
    <div
      data-testid="shipment-details"
    >
      Shipment Details - Order: 123
    </div>
    <div
      data-testid="payment-buttons"
    >
      Payment Buttons - Order: 123
    </div>
  </div>
</DocumentFragment>
`;

exports[`ShipmentContentV2 Snapshots should render with custom package and HAZMAT 1`] = `
<DocumentFragment>
  <div
    data-columns="1"
    data-row-gap="24px"
    data-testid="grid"
  >
    <div
      data-testid="purchase-error-notice"
    >
      Purchase Error Notice - no label
    </div>
    <div
      data-testid="items-card"
    >
      Items Card - 2 items
    </div>
    <div
      data-testid="packages-card"
    >
      Packages Card
    </div>
    <div
      class=""
      data-dismissible="false"
      data-status="info"
      data-testid="notice"
    >
      <p>
        No shipping rates were found based on the combination of the selected HAZMAT category, the package type, package dimensions and the total shipment weight.
      </p>
      <p>
        We couldn't find a shipping service for the combination of the selected HAZMAT category, selected package type, package dimensions and the total shipment weight. Please adjust your input and try again.
      </p>
    </div>
    <p
      class="upsdap-trademark-notice upsdap-trademark-notice--desktop"
    >
      UPS, the UPS brandmark, UPS Ready®, and the color brown are trademarks of United Parcel Service of America, Inc. All Rights Reserved.
    </p>
    <div
      data-testid="shipment-details"
    >
      Shipment Details - Order: 123
    </div>
    <div
      data-testid="payment-buttons"
    >
      Payment Buttons - Order: 123
    </div>
    <p
      class="upsdap-trademark-notice upsdap-trademark-notice--mobile"
    >
      UPS, the UPS brandmark, UPS Ready®, and the color brown are trademarks of United Parcel Service of America, Inc. All Rights Reserved.
    </p>
  </div>
</DocumentFragment>
`;

exports[`ShipmentContentV2 Snapshots should render with customs needed 1`] = `
<DocumentFragment>
  <div
    data-columns="1"
    data-row-gap="24px"
    data-testid="grid"
  >
    <div
      data-testid="purchase-error-notice"
    >
      Purchase Error Notice - no label
    </div>
    <div
      data-testid="items-card"
    >
      Items Card - 2 items
    </div>
    <div
      data-testid="packages-card"
    >
      Packages Card
    </div>
    <div
      class=""
      data-dismissible="false"
      data-status="info"
      data-testid="notice"
    >
      <p>
        No shipping rates were found based on the combination of the selected package and the total shipment weight.
      </p>
      <p>
        We couldn't find a shipping service for the combination of the selected package and the total shipment weight. Please adjust your input and try again.
      </p>
    </div>
    <div
      data-testid="shipment-details"
    >
      Shipment Details - Order: 123
    </div>
    <div
      data-testid="payment-buttons"
    >
      Payment Buttons - Order: 123
    </div>
  </div>
</DocumentFragment>
`;

exports[`ShipmentContentV2 Snapshots should render with empty items array 1`] = `
<DocumentFragment>
  <div
    data-columns="1"
    data-row-gap="24px"
    data-testid="grid"
  >
    <div
      data-testid="purchase-error-notice"
    >
      Purchase Error Notice - no label
    </div>
    <div
      data-testid="items-card"
    >
      Items Card - 0 items
    </div>
    <div
      data-testid="packages-card"
    >
      Packages Card
    </div>
    <div
      class=""
      data-dismissible="false"
      data-status="info"
      data-testid="notice"
    >
      <p>
        No shipping rates were found based on the combination of the selected package and the total shipment weight.
      </p>
      <p>
        We couldn't find a shipping service for the combination of the selected package and the total shipment weight. Please adjust your input and try again.
      </p>
    </div>
    <p
      class="upsdap-trademark-notice upsdap-trademark-notice--desktop"
    >
      UPS, the UPS brandmark, UPS Ready®, and the color brown are trademarks of United Parcel Service of America, Inc. All Rights Reserved.
    </p>
    <div
      data-testid="shipment-details"
    >
      Shipment Details - Order: 123
    </div>
    <div
      data-testid="payment-buttons"
    >
      Payment Buttons - Order: 123
    </div>
    <p
      class="upsdap-trademark-notice upsdap-trademark-notice--mobile"
    >
      UPS, the UPS brandmark, UPS Ready®, and the color brown are trademarks of United Parcel Service of America, Inc. All Rights Reserved.
    </p>
  </div>
</DocumentFragment>
`;

exports[`ShipmentContentV2 Snapshots should render with empty rates array 1`] = `
<DocumentFragment>
  <div
    data-columns="1"
    data-row-gap="24px"
    data-testid="grid"
  >
    <div
      data-testid="purchase-error-notice"
    >
      Purchase Error Notice - no label
    </div>
    <div
      data-testid="items-card"
    >
      Items Card - 2 items
    </div>
    <div
      data-testid="packages-card"
    >
      Packages Card
    </div>
    <div
      class=""
      data-dismissible="false"
      data-status="info"
      data-testid="notice"
    >
      <p>
        No shipping rates were found based on the combination of the selected HAZMAT category, the package type, package dimensions and the total shipment weight.
      </p>
      <p>
        We couldn't find a shipping service for the combination of the selected HAZMAT category, selected package type, package dimensions and the total shipment weight. Please adjust your input and try again.
      </p>
    </div>
    <div
      data-testid="shipment-details"
    >
      Shipment Details - Order: 123
    </div>
    <div
      data-testid="payment-buttons"
    >
      Payment Buttons - Order: 123
    </div>
  </div>
</DocumentFragment>
`;

exports[`ShipmentContentV2 Snapshots should render with fetching rates 1`] = `
<DocumentFragment>
  <div
    data-columns="1"
    data-row-gap="24px"
    data-testid="grid"
  >
    <div
      data-testid="purchase-error-notice"
    >
      Purchase Error Notice - no label
    </div>
    <div
      data-testid="items-card"
    >
      Items Card - 2 items
    </div>
    <div
      data-testid="packages-card"
    >
      Packages Card
    </div>
    <div
      class="animate-loading"
      data-testid="shipping-rates"
    >
      Shipping Rates - 1 rates, fetching: true
    </div>
    <div
      data-testid="shipment-details"
    >
      Shipment Details - Order: 123
    </div>
    <div
      data-testid="payment-buttons"
    >
      Payment Buttons - Order: 123
    </div>
  </div>
</DocumentFragment>
`;

exports[`ShipmentContentV2 Snapshots should render with no rates available 1`] = `
<DocumentFragment>
  <div
    data-columns="1"
    data-row-gap="24px"
    data-testid="grid"
  >
    <div
      data-testid="purchase-error-notice"
    >
      Purchase Error Notice - no label
    </div>
    <div
      data-testid="items-card"
    >
      Items Card - 2 items
    </div>
    <div
      data-testid="packages-card"
    >
      Packages Card
    </div>
    <div
      class=""
      data-testid="no-rates-available"
    >
      No Rates Available
    </div>
    <div
      data-testid="shipment-details"
    >
      Shipment Details - Order: 123
    </div>
    <div
      data-testid="payment-buttons"
    >
      Payment Buttons - Order: 123
    </div>
  </div>
</DocumentFragment>
`;

exports[`ShipmentContentV2 Snapshots should render with purchase error 1`] = `
<DocumentFragment>
  <div
    data-columns="1"
    data-row-gap="24px"
    data-testid="grid"
  >
    <div
      data-testid="purchase-error-notice"
    >
      Purchase Error Notice - PURCHASE_ERROR
    </div>
    <div
      data-testid="items-card"
    >
      Items Card - 2 items
    </div>
    <div
      data-testid="packages-card"
    >
      Packages Card
    </div>
    <div
      class=""
      data-dismissible="false"
      data-status="info"
      data-testid="notice"
    >
      <p>
        No shipping rates were found based on the combination of the selected package and the total shipment weight.
      </p>
      <p>
        We couldn't find a shipping service for the combination of the selected package and the total shipment weight. Please adjust your input and try again.
      </p>
    </div>
    <div
      data-testid="shipment-details"
    >
      Shipment Details - Order: 123
    </div>
    <div
      data-testid="payment-buttons"
    >
      Payment Buttons - Order: 123
    </div>
  </div>
</DocumentFragment>
`;

exports[`ShipmentContentV2 Snapshots should render with purchased label 1`] = `
<DocumentFragment>
  <div
    data-columns="1"
    data-row-gap="24px"
    data-testid="grid"
  >
    <div
      data-testid="purchase-notice"
    >
      Purchase Notice
    </div>
    <div
      data-margin="12"
      data-testid="divider"
    >
      Divider
    </div>
    <div
      data-testid="purchase-error-notice"
    >
      Purchase Error Notice - PURCHASED
    </div>
    <div
      data-testid="items-card"
    >
      Items Card - 2 items
    </div>
    <div
      data-testid="shipment-details"
    >
      Shipment Details - Order: 123
    </div>
    <div
      data-testid="payment-buttons"
    >
      Payment Buttons - Order: 123
    </div>
  </div>
</DocumentFragment>
`;

exports[`ShipmentContentV2 Snapshots should render with refunded notice 1`] = `
<DocumentFragment>
  <div
    data-columns="1"
    data-row-gap="24px"
    data-testid="grid"
  >
    <div
      data-testid="purchase-error-notice"
    >
      Purchase Error Notice - no label
    </div>
    <div
      data-testid="refunded-notice"
    >
      Refunded Notice
    </div>
    <div
      data-margin-bottom="12"
      data-testid="spacer"
    />
    <div
      data-testid="items-card"
    >
      Items Card - 2 items
    </div>
    <div
      data-testid="packages-card"
    >
      Packages Card
    </div>
    <div
      class=""
      data-dismissible="false"
      data-status="info"
      data-testid="notice"
    >
      <p>
        No shipping rates were found based on the combination of the selected package and the total shipment weight.
      </p>
      <p>
        We couldn't find a shipping service for the combination of the selected package and the total shipment weight. Please adjust your input and try again.
      </p>
    </div>
    <div
      data-testid="shipment-details"
    >
      Shipment Details - Order: 123
    </div>
    <div
      data-testid="payment-buttons"
    >
      Payment Buttons - Order: 123
    </div>
  </div>
</DocumentFragment>
`;
