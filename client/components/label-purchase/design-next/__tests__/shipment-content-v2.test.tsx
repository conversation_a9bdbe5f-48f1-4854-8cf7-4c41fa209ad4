import React from 'react';
import { render } from '@testing-library/react';
import { merge } from 'lodash';
import { ShipmentContentV2 } from '../shipment-content-v2';
import {
	useLabelPurchaseContext,
	LabelPurchaseContextType,
} from 'context/label-purchase';
import { LABEL_PURCHASE_STATUS } from 'data/constants';
import { DeepPartial, ShipmentItem } from 'types';

// Mock dependencies
jest.mock( '@wordpress/components', () => ( {
	__experimentalDivider: jest.fn( ( { margin } ) => (
		<div data-testid="divider" data-margin={ margin }>
			Divider
		</div>
	) ),
	__experimentalGrid: jest.fn( ( { children, columns, rowGap } ) => (
		<div
			data-testid="grid"
			data-columns={ columns }
			data-row-gap={ rowGap }
		>
			{ children }
		</div>
	) ),
	__experimentalSpacer: jest.fn(
		( { children, marginBottom, paddingX, paddingY } ) => (
			<div
				data-testid="spacer"
				data-margin-bottom={ marginBottom }
				data-padding-x={ paddingX }
				data-padding-y={ paddingY }
			>
				{ children }
			</div>
		)
	),
	Animate: jest.fn( ( { type, children } ) => {
		const className = type === 'loading' ? 'animate-loading' : '';
		return children( { className } );
	} ),
	Notice: jest.fn( ( { children, status, isDismissible, className } ) => (
		<div
			data-testid="notice"
			data-status={ status }
			data-dismissible={ isDismissible }
			className={ className }
		>
			{ children }
		</div>
	) ),
} ) );

jest.mock( '@wordpress/i18n', () => ( {
	__: jest.fn( ( text ) => text ),
	sprintf: jest.fn( ( format, ...args ) => {
		// Simple sprintf implementation for testing
		let result = format;
		args.forEach( ( arg, index ) => {
			result = result.replace( `%${ index + 1 }$s`, arg );
		} );
		return result;
	} ),
} ) );

jest.mock( 'utils', () => ( {
	getCurrentOrder: jest.fn().mockReturnValue( {
		id: 123,
		order_number: '123',
		status: 'processing',
		currency: 'USD',
		total: '25.00',
		shipping_address: {
			first_name: 'John',
			last_name: 'Doe',
			address_1: '123 Test St',
			city: 'Test City',
			state: 'TS',
			postcode: '12345',
			country: 'US',
		},
	} ),
	hasUPSPackages: jest.fn().mockReturnValue( false ),
} ) );

jest.mock( '@wordpress/data', () => ( {
	useSelect: jest.fn().mockReturnValue( [] ),
	combineReducers: jest.fn(),
	createStore: jest.fn(),
	register: jest.fn(),
} ) );

jest.mock( 'data/label-purchase', () => ( {
	labelPurchaseStore: 'label-purchase-store',
} ) );

jest.mock( 'utils/config', () => ( {
	getConfig: jest.fn().mockReturnValue( {
		packagesSettings: {
			packages: {
				predefined: {},
				custom: [],
			},
		},
		accountSettings: {},
		shippingLabelData: {},
	} ),
} ) );

jest.mock( 'context/label-purchase', () => ( {
	useLabelPurchaseContext: jest.fn(),
} ) );

// Mock child components
jest.mock( '../cards/items-card', () => {
	return jest.fn( ( { items } ) => (
		<div data-testid="items-card">Items Card - { items.length } items</div>
	) );
} );

jest.mock( '../cards/packages-card', () => ( {
	PackagesCard: jest.fn( () => (
		<div data-testid="packages-card">Packages Card</div>
	) ),
} ) );

jest.mock( 'components/label-purchase/details', () => ( {
	ShipmentDetails: jest.fn( ( { order } ) => (
		<div data-testid="shipment-details">
			Shipment Details - Order: { order.id }
		</div>
	) ),
} ) );

jest.mock( '../../shipping-service', () => ( {
	ShippingRates: jest.fn( ( { availableRates, isFetching, className } ) => (
		<div data-testid="shipping-rates" className={ className }>
			Shipping Rates - { availableRates.length } rates, fetching:{ ' ' }
			{ String( isFetching ) }
		</div>
	) ),
} ) );

jest.mock( '../../label', () => ( {
	PurchaseNotice: jest.fn( () => (
		<div data-testid="purchase-notice">Purchase Notice</div>
	) ),
} ) );

jest.mock( '../../purchase', () => ( {
	PaymentButtons: jest.fn( ( { order } ) => (
		<div data-testid="payment-buttons">
			Payment Buttons - Order: { order.id }
		</div>
	) ),
} ) );

jest.mock( '../../label/refunded-notice', () => ( {
	RefundedNotice: jest.fn( () => (
		<div data-testid="refunded-notice">Refunded Notice</div>
	) ),
} ) );

jest.mock( '../../shipping-service/no-rates-available', () => ( {
	NoRatesAvailable: jest.fn( ( { className } ) => (
		<div data-testid="no-rates-available" className={ className }>
			No Rates Available
		</div>
	) ),
} ) );

jest.mock( '../../purchase/purchase-error-notice', () => ( {
	PurchaseErrorNotice: jest.fn( ( { label } ) => (
		<div data-testid="purchase-error-notice">
			Purchase Error Notice - { label?.status || 'no label' }
		</div>
	) ),
} ) );

describe( 'ShipmentContentV2 Snapshots', () => {
	const mockItems: ShipmentItem[] = [
		{
			id: 1,
			name: 'Test Product 1',
			quantity: 2,
			weight: '1.5',
			total: '10.00',
			price: '5.00',
			sku: 'TEST-SKU-1',
			product_id: 1,
			meta: {
				customs_info: {
					description: 'Test Description',
					hs_tariff_number: '123456',
					origin_country: 'US',
				},
			},
			image: 'https://example.com/image1.jpg',
			variation: [],
			subItems: [],
			tax_class: '',
			subtotal: '10.00',
			subtotal_tax: '0.00',
			total_tax: '0.00',
		},
		{
			id: 2,
			name: 'Test Product 2',
			quantity: 1,
			weight: '2.0',
			total: '15.00',
			price: '15.00',
			sku: 'TEST-SKU-2',
			product_id: 2,
			meta: {
				customs_info: {
					description: 'Test Description',
					hs_tariff_number: '654321',
					origin_country: 'US',
				},
			},
			image: '',
			variation: [],
			subItems: [],
			tax_class: '',
			subtotal: '15.00',
			subtotal_tax: '0.00',
			total_tax: '0.00',
		},
	];

	const mockContext = (
		overrides: DeepPartial< LabelPurchaseContextType > = {}
	) => {
		( useLabelPurchaseContext as jest.Mock ).mockReturnValue(
			merge(
				{
					labels: {
						getCurrentShipmentLabel: jest
							.fn()
							.mockReturnValue( null ),
						hasPurchasedLabel: jest.fn().mockReturnValue( false ),
						showRefundedNotice: false,
						hasRequestedRefund: jest.fn().mockReturnValue( false ),
						isCurrentTabPurchasingExtraLabel: jest
							.fn()
							.mockReturnValue( false ),
					},
					customs: {
						isCustomsNeeded: jest.fn().mockReturnValue( false ),
					},
					shipment: {
						getSelectionItems: jest.fn().mockReturnValue( [] ),
						currentShipmentId: '1',
						getShipmentDestination: jest.fn().mockReturnValue( {
							address: '456 Destination St',
							city: 'Dest City',
							state: 'DS',
							postcode: '54321',
							country: 'US',
						} ),
					},
					rates: {
						isFetching: false,
					},
					packages: {
						isCustomPackageTab: jest.fn().mockReturnValue( false ),
					},
					hazmat: {
						getShipmentHazmat: jest
							.fn()
							.mockReturnValue( { isHazmat: false } ),
					},
					essentialDetails: {
						setExtraLabelPurchaseCompleted: jest.fn(),
					},
				},
				overrides
			)
		);
	};

	beforeEach( () => {
		jest.clearAllMocks();
	} );

	it( 'should render default state without purchased label', () => {
		mockContext();
		const { asFragment } = render(
			<ShipmentContentV2 items={ mockItems } />
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render with purchased label', () => {
		mockContext( {
			labels: {
				hasPurchasedLabel: jest.fn().mockReturnValue( true ),
				getCurrentShipmentLabel: jest.fn().mockReturnValue( {
					status: LABEL_PURCHASE_STATUS.PURCHASED,
					id: 'label_123',
				} ),
			},
		} );

		const { asFragment } = render(
			<ShipmentContentV2 items={ mockItems } />
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render with purchase error', () => {
		mockContext( {
			labels: {
				getCurrentShipmentLabel: jest.fn().mockReturnValue( {
					status: LABEL_PURCHASE_STATUS.PURCHASE_ERROR,
					error: 'Purchase failed',
				} ),
			},
		} );

		const { asFragment } = render(
			<ShipmentContentV2 items={ mockItems } />
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render with refunded notice', () => {
		mockContext( {
			labels: {
				hasRequestedRefund: jest.fn().mockReturnValue( true ),
				showRefundedNotice: true,
				hasPurchasedLabel: jest.fn().mockReturnValue( false ),
			},
		} );

		const { asFragment } = render(
			<ShipmentContentV2 items={ mockItems } />
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render with customs needed', () => {
		mockContext( {
			customs: {
				isCustomsNeeded: jest.fn().mockReturnValue( true ),
			},
		} );

		const { asFragment } = render(
			<ShipmentContentV2 items={ mockItems } />
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render with no rates available', () => {
		const { useSelect } = require( '@wordpress/data' );
		useSelect.mockReturnValue( null );

		mockContext( {
			rates: {
				isFetching: false,
			},
		} );

		const { asFragment } = render(
			<ShipmentContentV2 items={ mockItems } />
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render with empty rates array', () => {
		const { useSelect } = require( '@wordpress/data' );
		useSelect.mockReturnValue( [] );

		mockContext( {
			packages: {
				isCustomPackageTab: jest.fn().mockReturnValue( true ),
			},
			hazmat: {
				getShipmentHazmat: jest
					.fn()
					.mockReturnValue( { isHazmat: true } ),
			},
		} );

		const { asFragment } = render(
			<ShipmentContentV2 items={ mockItems } />
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render with available rates', () => {
		const { useSelect } = require( '@wordpress/data' );
		useSelect.mockReturnValue( [
			{
				id: 'rate_1',
				service_name: 'Express',
				rate: 10.99,
				carrierId: 'ups',
			},
			{
				id: 'rate_2',
				service_name: 'Ground',
				rate: 7.99,
				carrierId: 'usps',
			},
		] );

		mockContext();

		const { asFragment } = render(
			<ShipmentContentV2 items={ mockItems } />
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render with fetching rates', () => {
		const { useSelect } = require( '@wordpress/data' );
		useSelect.mockReturnValue( [
			{
				id: 'rate_1',
				service_name: 'Express',
				rate: 10.99,
				carrierId: 'ups',
			},
		] );

		mockContext( {
			rates: {
				isFetching: true,
			},
		} );

		const { asFragment } = render(
			<ShipmentContentV2 items={ mockItems } />
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render with UPS packages', () => {
		const { hasUPSPackages } = require( 'utils' );
		hasUPSPackages.mockReturnValue( true );

		mockContext();

		const { asFragment } = render(
			<ShipmentContentV2 items={ mockItems } />
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render extra label purchase mode', () => {
		mockContext( {
			labels: {
				isCurrentTabPurchasingExtraLabel: jest
					.fn()
					.mockReturnValue( true ),
			},
			shipment: {
				getSelectionItems: jest
					.fn()
					.mockReturnValue( [ mockItems[ 0 ] ] ),
			},
		} );

		const { asFragment } = render(
			<ShipmentContentV2 items={ mockItems } />
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render with custom package and HAZMAT', () => {
		const { useSelect } = require( '@wordpress/data' );
		useSelect.mockReturnValue( [] );

		mockContext( {
			packages: {
				isCustomPackageTab: jest.fn().mockReturnValue( true ),
			},
			hazmat: {
				getShipmentHazmat: jest
					.fn()
					.mockReturnValue( { isHazmat: true } ),
			},
		} );

		const { asFragment } = render(
			<ShipmentContentV2 items={ mockItems } />
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render with empty items array', () => {
		mockContext();

		const { asFragment } = render( <ShipmentContentV2 items={ [] } /> );
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render complex state with multiple notices', () => {
		mockContext( {
			labels: {
				hasPurchasedLabel: jest.fn().mockReturnValue( true ),
				hasRequestedRefund: jest.fn().mockReturnValue( true ),
				showRefundedNotice: true,
				getCurrentShipmentLabel: jest.fn().mockReturnValue( {
					status: LABEL_PURCHASE_STATUS.PURCHASED,
					id: 'label_123',
				} ),
			},
			customs: {
				isCustomsNeeded: jest.fn().mockReturnValue( true ),
			},
		} );

		const { hasUPSPackages } = require( 'utils' );
		hasUPSPackages.mockReturnValue( true );

		const { asFragment } = render(
			<ShipmentContentV2 items={ mockItems } />
		);
		expect( asFragment() ).toMatchSnapshot();
	} );
} );
