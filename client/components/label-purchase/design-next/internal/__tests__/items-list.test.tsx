import React from 'react';
import { render, screen } from '@testing-library/react';
import { ItemsList } from '../items-list';
import { ShipmentItem, OrderItem } from 'types';

// Mock dependencies
jest.mock( '@wordpress/dataviews/wp', () => {
	const MockDataViews: any = // eslint-disable-line @typescript-eslint/no-explicit-any
		jest.fn( ( { fields, data, children } ) => (
			<div data-testid="dataviews">
				<div
					data-testid="dataviews-fields"
					data-fields-count={ fields.length }
				>
					{ data.map(
						(
							item: any, // eslint-disable-line @typescript-eslint/no-explicit-any
							index: number
						) => (
							<div
								key={ item.id || index }
								data-testid={ `item-${ item.id || index }` }
							>
								{ fields.map(
									(
										field: any // eslint-disable-line @typescript-eslint/no-explicit-any
									) => {
										const fieldLabel = field.getValue
											? field.getValue( { item } )
											: field.label;
										return (
											<div
												key={ field.id }
												data-testid={ `field-${ field.id }` }
											>
												{ field.render
													? field.render( { item } )
													: fieldLabel }
											</div>
										);
									}
								) }
							</div>
						)
					) }
				</div>
				{ children }
			</div>
		) );

	MockDataViews.Layout = jest.fn( () => (
		<div data-testid="dataviews-layout" />
	) );

	return {
		DataViews: MockDataViews,
	};
} );

jest.mock( '../../utils', () => ( {
	formatCurrency: jest
		.fn()
		.mockImplementation( ( amount ) => `$${ amount.toFixed( 2 ) }` ),
	getCurrencyObject: jest.fn().mockReturnValue( { code: 'USD' } ),
} ) );

jest.mock( 'utils', () => ( {
	getWeightUnit: jest.fn().mockReturnValue( 'lb' ),
} ) );

jest.mock( '@woocommerce/components', () => ( {
	Badge: jest.fn( ( { children, title, count } ) => (
		<span data-testid="badge" title={ title } data-count={ count }>
			{ children }
		</span>
	) ),
} ) );

describe( 'ItemsList', () => {
	const mockShipmentItems: ShipmentItem[] = [
		{
			id: 1,
			name: 'Test Product 1',
			quantity: 2,
			weight: '1.5',
			total: '10.00',
			price: '5.00',
			sku: 'TEST-SKU-1',
			product_id: 1,
			meta: {
				customs_info: {
					description: 'Test Description',
					hs_tariff_number: '123456',
					origin_country: 'US',
				},
			},
			image: 'https://example.com/image1.jpg',
			variation: [],
			subItems: [],
			tax_class: '',
			subtotal: '10.00',
			subtotal_tax: '0.00',
			total_tax: '0.00',
		},
		{
			id: 2,
			name: 'Test Product 2',
			quantity: 1,
			weight: '2.0',
			total: '15.00',
			price: '15.00',
			sku: 'TEST-SKU-2',
			product_id: 2,
			meta: {
				customs_info: {
					description: 'Test Description',
					hs_tariff_number: '654321',
					origin_country: 'US',
				},
			},
			image: '',
			variation: [],
			subItems: [],
			tax_class: '',
			subtotal: '15.00',
			subtotal_tax: '0.00',
			total_tax: '0.00',
		},
	];

	const mockItemWithMetaData = {
		...mockShipmentItems[ 0 ],
		meta_data: [
			{ key: 'color', label: 'Color', value: 'Red' },
			{ key: 'size', label: 'Size', value: 'Large' },
			{ key: 'material', label: 'Material', value: 'Cotton' },
			{ key: 'brand', label: 'Brand', value: 'TestBrand' },
			{ key: 'style', label: 'Style', value: 'Modern' },
		],
	} as ShipmentItem;

	beforeEach( () => {
		jest.clearAllMocks();
	} );

	it( 'renders DataViews with correct structure', () => {
		render( <ItemsList items={ mockShipmentItems } /> );

		expect( screen.getByTestId( 'dataviews' ) ).toBeInTheDocument();
		expect( screen.getByTestId( 'dataviews-fields' ) ).toHaveAttribute(
			'data-fields-count',
			'4'
		);
	} );

	it( 'displays all items in the list', () => {
		render( <ItemsList items={ mockShipmentItems } /> );

		expect( screen.getByTestId( 'item-1' ) ).toBeInTheDocument();
		expect( screen.getByTestId( 'item-2' ) ).toBeInTheDocument();
	} );

	it( 'renders item summary with name and sku', () => {
		render( <ItemsList items={ mockShipmentItems } /> );

		expect( screen.getByText( 'Test Product 1' ) ).toBeInTheDocument();
		expect( screen.getByText( 'TEST-SKU-1' ) ).toBeInTheDocument();
		expect( screen.getByText( 'Test Product 2' ) ).toBeInTheDocument();
		expect( screen.getByText( 'TEST-SKU-2' ) ).toBeInTheDocument();
	} );

	it( 'displays item image when available', () => {
		render( <ItemsList items={ mockShipmentItems } /> );

		const images = screen.getAllByRole( 'img' );
		expect( images ).toHaveLength( 1 ); // Only first item has image
		expect( images[ 0 ] ).toHaveAttribute(
			'src',
			'https://example.com/image1.jpg'
		);
		expect( images[ 0 ] ).toHaveAttribute( 'alt', 'Test Product 1' );
	} );

	it( 'displays quantities correctly', () => {
		render( <ItemsList items={ mockShipmentItems } /> );

		// Check for quantity values
		expect( screen.getByText( '2' ) ).toBeInTheDocument(); // First item quantity
		expect( screen.getByText( '1' ) ).toBeInTheDocument(); // Second item quantity
	} );

	it( 'displays weights with unit', () => {
		render( <ItemsList items={ mockShipmentItems } /> );

		expect( screen.getByText( '1.5 lb' ) ).toBeInTheDocument();
		expect( screen.getByText( '2 lb' ) ).toBeInTheDocument();
	} );

	it( 'handles zero weight correctly', () => {
		const itemsWithZeroWeight = [
			{
				...mockShipmentItems[ 0 ],
				weight: '0',
			},
		];

		render( <ItemsList items={ itemsWithZeroWeight } /> );

		expect( screen.getByText( '0 lb' ) ).toBeInTheDocument();
	} );

	it( 'handles missing weight correctly', () => {
		const itemsWithoutWeight = [
			{
				...mockShipmentItems[ 0 ],
				weight: '',
			},
		];

		render( <ItemsList items={ itemsWithoutWeight } /> );

		expect( screen.getByText( '0 lb' ) ).toBeInTheDocument();
	} );

	it( 'displays formatted currency totals', () => {
		render( <ItemsList items={ mockShipmentItems } /> );

		expect( screen.getByText( '$10.00' ) ).toBeInTheDocument();
		expect( screen.getByText( '$15.00' ) ).toBeInTheDocument();
	} );

	it( 'renders metadata badges when meta_data exists', () => {
		const itemsWithMeta = [ mockItemWithMetaData ];

		render( <ItemsList items={ itemsWithMeta } /> );

		// Should show first 3 meta items
		expect( screen.getByText( 'Red' ) ).toBeInTheDocument();
		expect( screen.getByText( 'Large' ) ).toBeInTheDocument();
		expect( screen.getByText( 'Cotton' ) ).toBeInTheDocument();

		// Should show +2 for remaining items
		expect( screen.getByText( '+2' ) ).toBeInTheDocument();
	} );

	it( 'shows correct tooltip for remaining metadata', () => {
		const itemsWithMeta = [ mockItemWithMetaData ];

		render( <ItemsList items={ itemsWithMeta } /> );

		const remainingBadge = screen.getByText( '+2' );
		expect( remainingBadge ).toHaveAttribute(
			'title',
			'Brand: TestBrand, Style: Modern'
		);
	} );

	it( 'does not render metadata when meta_data is empty', () => {
		const itemsWithoutMeta = [
			{
				...mockShipmentItems[ 0 ],
				meta_data: [],
			} as ShipmentItem,
		];

		render( <ItemsList items={ itemsWithoutMeta } /> );

		// Should not have any badges (no meta data)
		expect( screen.queryByTestId( 'badge' ) ).not.toBeInTheDocument();
	} );

	it( 'does not render metadata when meta_data is undefined', () => {
		const itemsWithoutMeta = [
			{
				...mockShipmentItems[ 0 ],
				meta_data: undefined,
			} as ShipmentItem,
		];

		render( <ItemsList items={ itemsWithoutMeta } /> );

		// Should not have any badges (no meta data)
		expect( screen.queryByTestId( 'badge' ) ).not.toBeInTheDocument();
	} );

	it( 'renders all metadata when less than limit', () => {
		const itemWithLimitedMeta = {
			...mockShipmentItems[ 0 ],
			meta_data: [
				{ key: 'color', label: 'Color', value: 'Blue' },
				{ key: 'size', label: 'Size', value: 'Medium' },
			],
		} as ShipmentItem;

		render( <ItemsList items={ [ itemWithLimitedMeta ] } /> );

		expect( screen.getByText( 'Blue' ) ).toBeInTheDocument();
		expect( screen.getByText( 'Medium' ) ).toBeInTheDocument();
		// Should not show +X since we're under the limit
		expect( screen.queryByText( /^\+\d+$/ ) ).not.toBeInTheDocument();
	} );

	it( 'handles empty items array', () => {
		render( <ItemsList items={ [] } /> );

		expect( screen.getByTestId( 'dataviews' ) ).toBeInTheDocument();
		// Should not have any item rows
		expect( screen.queryByTestId( /^item-/ ) ).not.toBeInTheDocument();
	} );

	it( 'renders with OrderItem type', () => {
		const orderItems: OrderItem[] = [
			{
				id: 1,
				name: 'Order Item 1',
				quantity: 1,
				weight: '1.0',
				total: '5.00',
				price: '5.00',
				sku: 'ORDER-SKU-1',
				product_id: 1,
				meta: {
					customs_info: {
						description: 'Test Description',
						hs_tariff_number: '123456',
						origin_country: 'US',
					},
				},
				image: '',
				variation: [],
				tax_class: '',
				subtotal: '5.00',
				subtotal_tax: '0.00',
				total_tax: '0.00',
			},
		];

		render( <ItemsList items={ orderItems } /> );

		expect( screen.getByText( 'Order Item 1' ) ).toBeInTheDocument();
		expect( screen.getByText( 'ORDER-SKU-1' ) ).toBeInTheDocument();
	} );

	it( 'configures DataViews with correct field structure', () => {
		render( <ItemsList items={ mockShipmentItems } /> );

		// Verify that the correct field types exist (multiple instances for each item)
		expect(
			screen.getAllByTestId( 'field-order_line_item_summary' )
		).toHaveLength( 2 );
		expect(
			screen.getAllByTestId( 'field-order_line_item_qty' )
		).toHaveLength( 2 );
		expect(
			screen.getAllByTestId( 'field-order_line_item_weight' )
		).toHaveLength( 2 );
		expect(
			screen.getAllByTestId( 'field-order_line_item_total' )
		).toHaveLength( 2 );
	} );

	it( 'handles items without images gracefully', () => {
		const itemsWithoutImages = mockShipmentItems.map( ( item ) => ( {
			...item,
			image: '',
		} ) );

		render( <ItemsList items={ itemsWithoutImages } /> );

		// Should not render any images
		expect( screen.queryByRole( 'img' ) ).not.toBeInTheDocument();
		// But should still render names and skus
		expect( screen.getByText( 'Test Product 1' ) ).toBeInTheDocument();
		expect( screen.getByText( 'Test Product 2' ) ).toBeInTheDocument();
	} );

	it( 'handles decimal quantities correctly', () => {
		const itemsWithDecimalQty = [
			{
				...mockShipmentItems[ 0 ],
				quantity: 2.5,
			},
		];

		render( <ItemsList items={ itemsWithDecimalQty } /> );

		expect( screen.getByText( '2.5' ) ).toBeInTheDocument();
	} );
} );
