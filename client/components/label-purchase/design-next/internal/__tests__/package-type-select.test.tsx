import { render, screen, fireEvent } from '@testing-library/react';
import React from 'react';
import PackageTypeSelect from '../package-type-select';
import { TAB_NAMES } from 'components/label-purchase/packages';

// Mock dependencies
jest.mock( '@wordpress/components', () => ( {
	Button: jest.fn(
		( {
			children,
			onClick,
			icon,
			iconPosition,
			className,
			style,
			...props
		} ) => (
			<button
				data-testid="package-select-button"
				onClick={ onClick }
				className={ className }
				style={ style }
				data-icon={ icon }
				data-icon-position={ iconPosition }
				{ ...props }
			>
				{ children }
			</button>
		)
	),
	Dropdown: jest.fn(
		( { renderToggle, renderContent, style, popoverProps } ) => {
			const [ isOpen, setIsOpen ] = React.useState( false );
			const onToggle = () => setIsOpen( ! isOpen );
			const onClose = () => setIsOpen( false );

			return (
				<div
					data-testid="dropdown"
					style={ style }
					data-popover-props={ JSON.stringify( popoverProps ) }
				>
					{ renderToggle( { isOpen, onToggle } ) }
					{ isOpen && (
						<div data-testid="dropdown-content">
							{ renderContent( { onClose } ) }
						</div>
					) }
				</div>
			);
		}
	),
	Flex: jest.fn(
		( { children, direction, wrap, gap, align, justify, style } ) => (
			<div
				data-testid="flex"
				data-direction={ direction }
				data-wrap={ wrap }
				data-gap={ gap }
				data-align={ align }
				data-justify={ justify }
				style={ style }
			>
				{ children }
			</div>
		)
	),
	MenuGroup: jest.fn( ( { children, label } ) => (
		<div data-testid="menu-group" data-label={ label }>
			{ children }
		</div>
	) ),
	MenuItem: jest.fn(
		( { children, onClick, role, isSelected, icon, iconPosition } ) => {
			const buttonProps: Record< string, unknown > = {
				'data-testid': 'menu-item',
				onClick,
				role,
				'data-selected': isSelected,
			};

			// Only add data-icon if icon is not undefined
			if ( icon !== undefined ) {
				buttonProps[ 'data-icon' ] = icon;
			}

			// Only add data-icon-position if iconPosition is not undefined
			if ( iconPosition !== undefined ) {
				buttonProps[ 'data-icon-position' ] = iconPosition;
			}

			return <button { ...buttonProps }>{ children }</button>;
		}
	),
	__experimentalText: jest.fn( ( { children, size, variant, style } ) => (
		<span
			data-testid="text"
			data-size={ size }
			data-variant={ variant }
			style={ style }
		>
			{ children }
		</span>
	) ),
	__experimentalScrollable: jest.fn( ( { children, style } ) => (
		<div data-testid="scrollable" style={ style }>
			{ children }
		</div>
	) ),
} ) );

jest.mock( '@wordpress/i18n', () => ( {
	__: jest.fn( ( text ) => text ),
	sprintf: jest.fn( ( format, ...args ) => {
		// Simple sprintf implementation that handles %s placeholders
		return format.replace( /%s/g, () => args.shift() );
	} ),
} ) );

jest.mock( '@wordpress/icons', () => ( {
	check: 'check-icon',
	chevronDown: 'chevron-down-icon',
	chevronUp: 'chevron-up-icon',
} ) );

jest.mock( 'components/carrier-icon', () => ( {
	CarrierIcon: jest.fn( ( { carrier, size } ) => (
		<div
			data-testid="carrier-icon"
			data-carrier={ carrier }
			data-size={ size }
		>
			{ carrier } icon
		</div>
	) ),
} ) );

jest.mock( 'components/label-purchase/packages', () => ( {
	CARRIER_ID_TO_NAME: {
		ups: 'UPS',
		usps: 'USPS',
		fedex: 'FedEx',
	},
	TAB_NAMES: {
		CUSTOM_PACKAGE: 'custom',
		SAVED_TEMPLATES: 'saved',
		CARRIER_PACKAGE: 'carrier',
	},
} ) );

jest.mock( 'utils', () => ( {
	getAvailableCarrierPackages: jest.fn().mockReturnValue( {
		ups: {
			letter: {
				definitions: [
					{
						id: 'ups_letter_1',
						name: 'UPS Letter',
						type: 'letter',
						isLetter: true,
						length: '12.5',
						width: '9.5',
						height: '0.75',
						outerDimensions: '12.5" x 9.5" x 0.75"',
						innerDimensions: '12.25" x 9.25" x 0.5"',
						dimensions: '12.5" x 9.5" x 0.75"',
						boxWeight: 0,
						isUserDefined: undefined,
						carrierId: 'ups',
						maxWeight: 0.5,
					},
				],
			},
			small_express_box: {
				definitions: [
					{
						id: 'ups_small_express',
						name: 'UPS Small Express Box',
						type: 'box',
						isLetter: false,
						length: '13',
						width: '11',
						height: '2',
						outerDimensions: '13" x 11" x 2"',
						innerDimensions: '12.75" x 10.75" x 1.75"',
						dimensions: '13" x 11" x 2"',
						boxWeight: 0.2,
						isUserDefined: undefined,
						carrierId: 'ups',
						maxWeight: 30,
					},
				],
			},
		},
		usps: {
			priority_mail_express: {
				definitions: [
					{
						id: 'usps_priority_express',
						name: 'Priority Mail Express',
						type: 'box',
						isLetter: false,
						length: '12.5',
						width: '9.5',
						height: '1',
						outerDimensions: '12.5" x 9.5" x 1"',
						innerDimensions: '12.25" x 9.25" x 0.75"',
						dimensions: '12.5" x 9.5" x 1"',
						boxWeight: 0.1,
						isUserDefined: undefined,
						carrierId: 'usps',
						maxWeight: 70,
					},
				],
			},
		},
	} ),
	getSelectedCarrierIdFromPackage: jest
		.fn()
		.mockImplementation( ( packages, packageId ) => {
			if (
				packageId === 'ups_letter_1' ||
				packageId === 'ups_small_express'
			) {
				return 'ups';
			}
			if ( packageId === 'usps_priority_express' ) {
				return 'usps';
			}
			return null;
		} ),
} ) );

describe( 'PackageTypeSelect', () => {
	const mockSetCurrentPackageTab = jest.fn();
	const mockSetSelectedPackage = jest.fn();

	const defaultProps = {
		currentPackageTab: TAB_NAMES.CUSTOM_PACKAGE,
		setCurrentPackageTab: mockSetCurrentPackageTab,
		selectedPackage: null,
		setSelectedPackage: mockSetSelectedPackage,
	};

	beforeEach( () => {
		jest.clearAllMocks();
	} );

	describe( 'Rendering', () => {
		it( 'should render dropdown with correct structure', () => {
			render( <PackageTypeSelect { ...defaultProps } /> );

			expect( screen.getByTestId( 'dropdown' ) ).toBeInTheDocument();
			expect(
				screen.getByTestId( 'package-select-button' )
			).toBeInTheDocument();
		} );

		it( 'should configure dropdown with correct popover props', () => {
			render( <PackageTypeSelect { ...defaultProps } /> );

			const dropdown = screen.getByTestId( 'dropdown' );
			const popoverProps = JSON.parse(
				dropdown.getAttribute( 'data-popover-props' ) ?? '{}'
			);

			expect( popoverProps ).toEqual( {
				placement: 'bottom-start',
				resize: false,
				shift: false,
				inline: true,
				noArrow: true,
			} );
		} );

		it( 'should apply correct styles to dropdown', () => {
			render( <PackageTypeSelect { ...defaultProps } /> );

			const dropdown = screen.getByTestId( 'dropdown' );
			expect( dropdown ).toHaveStyle( { width: '100%' } );
		} );
	} );

	describe( 'Toggle Button', () => {
		it( 'should display "Custom package" when custom tab is selected', () => {
			render(
				<PackageTypeSelect
					{ ...defaultProps }
					currentPackageTab={ TAB_NAMES.CUSTOM_PACKAGE }
				/>
			);

			expect( screen.getByText( 'Custom package' ) ).toBeInTheDocument();
		} );

		it( 'should display "Saved templates" when saved templates tab is selected', () => {
			render(
				<PackageTypeSelect
					{ ...defaultProps }
					currentPackageTab={ TAB_NAMES.SAVED_TEMPLATES }
				/>
			);

			expect( screen.getByText( 'Saved templates' ) ).toBeInTheDocument();
		} );

		it( 'should display package line when carrier package is selected', () => {
			const selectedPackage = {
				id: 'ups_letter_1',
				name: 'UPS Letter',
				type: 'box',
				isLetter: false,
				length: '12.5',
				width: '9.5',
				height: '0.75',
				outerDimensions: '12.5" x 9.5" x 0.75"',
				innerDimensions: '12.25" x 9.25" x 0.5"',
				dimensions: '12.5" x 9.5" x 0.75"',
				boxWeight: 0,
				isUserDefined: undefined,
				carrierId: 'ups',
				maxWeight: 0.5,
			};

			render(
				<PackageTypeSelect
					{ ...defaultProps }
					currentPackageTab={ TAB_NAMES.CARRIER_PACKAGE }
					selectedPackage={ selectedPackage }
				/>
			);

			expect( screen.getByText( 'UPS Letter' ) ).toBeInTheDocument();
			expect(
				screen.getByText( '12.5" x 9.5" x 0.75"' )
			).toBeInTheDocument();
			expect( screen.getByText( '0.5lb' ) ).toBeInTheDocument();
		} );

		it( 'should show chevron down when dropdown is closed', () => {
			render( <PackageTypeSelect { ...defaultProps } /> );

			const button = screen.getByTestId( 'package-select-button' );
			expect( button ).toHaveAttribute(
				'data-icon',
				'chevron-down-icon'
			);
		} );

		it( 'should show chevron up when dropdown is open', () => {
			render( <PackageTypeSelect { ...defaultProps } /> );

			const button = screen.getByTestId( 'package-select-button' );
			fireEvent.click( button );

			expect( button ).toHaveAttribute( 'data-icon', 'chevron-up-icon' );
		} );

		it( 'should apply correct styles when closed', () => {
			render( <PackageTypeSelect { ...defaultProps } /> );

			const button = screen.getByTestId( 'package-select-button' );
			expect( button ).toHaveStyle( {
				width: '100%',
				justifyContent: 'space-between',
				boxShadow: '0 0 0 1px inset #949494',
				color: '#555',
				paddingRight: '4px',
			} );
		} );

		it( 'should apply correct styles when open', () => {
			render( <PackageTypeSelect { ...defaultProps } /> );

			const button = screen.getByTestId( 'package-select-button' );
			fireEvent.click( button );

			expect( button ).toHaveStyle( {
				width: '100%',
				justifyContent: 'space-between',
				boxShadow: '0 0 0 1px inset #000',
				color: '#000',
				paddingRight: '4px',
			} );
		} );

		it( 'should toggle dropdown when clicked', () => {
			render( <PackageTypeSelect { ...defaultProps } /> );

			const button = screen.getByTestId( 'package-select-button' );

			// Initially closed
			expect(
				screen.queryByTestId( 'dropdown-content' )
			).not.toBeInTheDocument();

			// Click to open
			fireEvent.click( button );
			expect(
				screen.getByTestId( 'dropdown-content' )
			).toBeInTheDocument();

			// Click to close
			fireEvent.click( button );
			expect(
				screen.queryByTestId( 'dropdown-content' )
			).not.toBeInTheDocument();
		} );
	} );

	describe( 'PackageLine Component', () => {
		it( 'should render carrier icon with correct props', () => {
			const selectedPackage = {
				id: 'ups_letter_1',
				name: 'UPS Letter',
				type: 'letter',
				isLetter: true,
				length: '12.5',
				width: '9.5',
				height: '0.75',
				outerDimensions: '12.5" x 9.5" x 0.75"',
				innerDimensions: '12.25" x 9.25" x 0.5"',
				dimensions: '12.5" x 9.5" x 0.75"',
				boxWeight: 0,
				isUserDefined: undefined,
				carrierId: 'ups',
				maxWeight: 0.5,
			};

			render(
				<PackageTypeSelect
					{ ...defaultProps }
					currentPackageTab={ TAB_NAMES.CARRIER_PACKAGE }
					selectedPackage={ selectedPackage }
				/>
			);

			const carrierIcon = screen.getByTestId( 'carrier-icon' );
			expect( carrierIcon ).toHaveAttribute( 'data-carrier', 'ups' );
			expect( carrierIcon ).toHaveAttribute( 'data-size', 'small' );
		} );

		it( 'should render package without max weight', () => {
			const selectedPackage = {
				id: 'test_package',
				name: 'Test Package',
				type: 'box',
				isLetter: false,
				length: '10',
				width: '10',
				height: '10',
				outerDimensions: '10" x 10" x 10"',
				innerDimensions: '9.5" x 9.5" x 9.5"',
				dimensions: '10" x 10" x 10"',
				boxWeight: 0,
				isUserDefined: undefined,
			};

			render(
				<PackageTypeSelect
					{ ...defaultProps }
					currentPackageTab={ TAB_NAMES.CARRIER_PACKAGE }
					selectedPackage={ selectedPackage }
				/>
			);

			expect( screen.getByText( 'Test Package' ) ).toBeInTheDocument();
			expect( screen.getByText( '10" x 10" x 10"' ) ).toBeInTheDocument();
			expect( screen.queryByText( '•' ) ).not.toBeInTheDocument();
		} );
	} );

	describe( 'Dropdown Content', () => {
		beforeEach( () => {
			render( <PackageTypeSelect { ...defaultProps } /> );
			const button = screen.getByTestId( 'package-select-button' );
			fireEvent.click( button );
		} );

		it( 'should render scrollable container with max height', () => {
			const scrollable = screen.getByTestId( 'scrollable' );
			expect( scrollable ).toHaveStyle( { maxHeight: '300px' } );
		} );

		it( 'should render custom package menu item', () => {
			const menuItems = screen.getAllByTestId( 'menu-item' );
			const customPackageItem = menuItems.find( ( item ) =>
				item.textContent?.includes( 'Custom package' )
			);

			expect( customPackageItem ).toBeInTheDocument();
			expect( customPackageItem ).toHaveAttribute(
				'role',
				'menuitemradio'
			);
		} );

		it( 'should mark custom package as selected when current tab is custom', () => {
			const menuItems = screen.getAllByTestId( 'menu-item' );
			const customPackageItem = menuItems.find( ( item ) =>
				item.textContent?.includes( 'Custom package' )
			);

			expect( customPackageItem ).toHaveAttribute(
				'data-selected',
				'true'
			);
		} );

		it( 'should render carrier package groups', () => {
			const menuGroups = screen.getAllByTestId( 'menu-group' );

			// Should have custom package group + carrier groups
			expect( menuGroups.length ).toBeGreaterThan( 1 );

			// Check for UPS group
			const upsGroup = menuGroups.find(
				( group ) => group.getAttribute( 'data-label' ) === 'UPS'
			);
			expect( upsGroup ).toBeInTheDocument();

			// Check for USPS group
			const uspsGroup = menuGroups.find(
				( group ) => group.getAttribute( 'data-label' ) === 'USPS'
			);
			expect( uspsGroup ).toBeInTheDocument();
		} );

		it( 'should render carrier package items with correct content', () => {
			expect( screen.getByText( 'UPS Letter' ) ).toBeInTheDocument();
			expect(
				screen.getByText( 'UPS Small Express Box' )
			).toBeInTheDocument();
			expect(
				screen.getByText( 'Priority Mail Express' )
			).toBeInTheDocument();

			// Check dimensions are displayed
			expect(
				screen.getByText( '12.5" x 9.5" x 0.75"' )
			).toBeInTheDocument();
			expect( screen.getByText( '13" x 11" x 2"' ) ).toBeInTheDocument();

			// Check weights are displayed
			expect( screen.getByText( '0.5lb' ) ).toBeInTheDocument();
			expect( screen.getByText( '30lb' ) ).toBeInTheDocument();
			expect( screen.getByText( '70lb' ) ).toBeInTheDocument();
		} );
	} );

	describe( 'Interactions', () => {
		it( 'should call setCurrentPackageTab when custom package is clicked', () => {
			render( <PackageTypeSelect { ...defaultProps } /> );

			// Open dropdown
			const button = screen.getByTestId( 'package-select-button' );
			fireEvent.click( button );

			// Click custom package
			const menuItems = screen.getAllByTestId( 'menu-item' );
			const customPackageItem = menuItems.find( ( item ) =>
				item.textContent?.includes( 'Custom package' )
			);

			fireEvent.click( customPackageItem! );

			expect( mockSetCurrentPackageTab ).toHaveBeenCalledWith(
				TAB_NAMES.CUSTOM_PACKAGE
			);
		} );

		it( 'should call setSelectedPackage and setCurrentPackageTab when carrier package is clicked', () => {
			render( <PackageTypeSelect { ...defaultProps } /> );

			// Open dropdown
			const button = screen.getByTestId( 'package-select-button' );
			fireEvent.click( button );

			// Click UPS Letter package
			const menuItems = screen.getAllByTestId( 'menu-item' );
			const upsLetterItem = menuItems.find( ( item ) =>
				item.textContent?.includes( 'UPS Letter' )
			);

			fireEvent.click( upsLetterItem! );

			expect( mockSetSelectedPackage ).toHaveBeenCalledWith( {
				id: 'ups_letter_1',
				name: 'UPS Letter',
				type: 'letter',
				isLetter: true,
				length: '12.5',
				width: '9.5',
				height: '0.75',
				outerDimensions: '12.5" x 9.5" x 0.75"',
				innerDimensions: '12.25" x 9.25" x 0.5"',
				dimensions: '12.5" x 9.5" x 0.75"',
				boxWeight: 0,
				isUserDefined: undefined,
				carrierId: 'ups',
				maxWeight: 0.5,
			} );
			expect( mockSetCurrentPackageTab ).toHaveBeenCalledWith(
				TAB_NAMES.CARRIER_PACKAGE
			);
		} );

		it( 'should close dropdown after selecting an item', () => {
			render( <PackageTypeSelect { ...defaultProps } /> );

			// Open dropdown
			const button = screen.getByTestId( 'package-select-button' );
			fireEvent.click( button );

			expect(
				screen.getByTestId( 'dropdown-content' )
			).toBeInTheDocument();

			// Click custom package
			const menuItems = screen.getAllByTestId( 'menu-item' );
			const customPackageItem = menuItems.find( ( item ) =>
				item.textContent?.includes( 'Custom package' )
			);

			fireEvent.click( customPackageItem! );

			// Dropdown should be closed
			expect(
				screen.queryByTestId( 'dropdown-content' )
			).not.toBeInTheDocument();
		} );

		it( 'should show check icon for selected carrier package', () => {
			const selectedPackage = {
				id: 'ups_letter_1',
				name: 'UPS Letter',
				type: 'box',
				isLetter: false,
				length: '12.5',
				width: '9.5',
				height: '0.75',
				outerDimensions: '12.5" x 9.5" x 0.75"',
				innerDimensions: '12.25" x 9.25" x 0.5"',
				dimensions: '12.5" x 9.5" x 0.75"',
				boxWeight: 0,
				isUserDefined: undefined,
				carrierId: 'ups',
				maxWeight: 0.5,
			};

			render(
				<PackageTypeSelect
					{ ...defaultProps }
					currentPackageTab={ TAB_NAMES.CARRIER_PACKAGE }
					selectedPackage={ selectedPackage }
				/>
			);

			// Open dropdown
			const button = screen.getByTestId( 'package-select-button' );
			fireEvent.click( button );

			// Find the UPS Letter menu item
			const menuItems = screen.getAllByTestId( 'menu-item' );
			const upsLetterItem = menuItems.find( ( item ) =>
				item.textContent?.includes( 'UPS Letter' )
			);

			expect( upsLetterItem ).toHaveAttribute(
				'data-icon',
				'check-icon'
			);
			expect( upsLetterItem ).toHaveAttribute( 'data-selected', 'true' );
		} );

		it( 'should not show check icon for unselected packages', () => {
			render( <PackageTypeSelect { ...defaultProps } /> );

			// Open dropdown
			const button = screen.getByTestId( 'package-select-button' );
			fireEvent.click( button );

			// Find a carrier package menu item
			const menuItems = screen.getAllByTestId( 'menu-item' );
			const carrierPackageItem = menuItems.find( ( item ) =>
				item.textContent?.includes( 'UPS Letter' )
			);

			expect( carrierPackageItem ).not.toHaveAttribute( 'data-icon' );
			expect( carrierPackageItem ).toHaveAttribute(
				'data-selected',
				'false'
			);
		} );
	} );

	describe( 'Edge Cases', () => {
		it( 'should handle missing setSelectedPackage prop', () => {
			const { setSelectedPackage, ...propsWithoutSetter } = defaultProps;

			render( <PackageTypeSelect { ...propsWithoutSetter } /> );

			// Open dropdown
			const button = screen.getByTestId( 'package-select-button' );
			fireEvent.click( button );

			// Click carrier package (should not throw error)
			const menuItems = screen.getAllByTestId( 'menu-item' );
			const carrierPackageItem = menuItems.find( ( item ) =>
				item.textContent?.includes( 'UPS Letter' )
			);

			expect( () =>
				fireEvent.click( carrierPackageItem! )
			).not.toThrow();
		} );

		it( 'should handle empty available packages', () => {
			const { getAvailableCarrierPackages } = require( 'utils' );
			getAvailableCarrierPackages.mockReturnValue( {} );

			render( <PackageTypeSelect { ...defaultProps } /> );

			// Open dropdown
			const button = screen.getByTestId( 'package-select-button' );
			fireEvent.click( button );

			// Should only show custom package option
			const menuGroups = screen.getAllByTestId( 'menu-group' );
			expect( menuGroups ).toHaveLength( 1 ); // Only custom package group
		} );

		it( 'should handle missing selectedPackage when carrier tab is active', () => {
			render(
				<PackageTypeSelect
					{ ...defaultProps }
					currentPackageTab={ TAB_NAMES.CARRIER_PACKAGE }
					selectedPackage={ null }
				/>
			);

			// Should not render PackageLine
			expect(
				screen.queryByTestId( 'carrier-icon' )
			).not.toBeInTheDocument();
		} );
	} );
} );
