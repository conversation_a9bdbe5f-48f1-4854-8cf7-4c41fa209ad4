import { render, screen, fireEvent } from '@testing-library/react';
import { renderHook, act } from '@testing-library/react';
import React from 'react';
import { useCollapsibleCard } from '../useCollapsibleCard';

// Mock WordPress dependencies
jest.mock( '@wordpress/i18n', () => ( {
	__: jest.fn( ( text ) => text ),
} ) );

jest.mock( '@wordpress/icons', () => ( {
	chevronDown: 'chevron-down-icon',
	chevronUp: 'chevron-up-icon',
} ) );

jest.mock( '@wordpress/components', () => ( {
	CardHeader: jest.fn( ( { children, onClick, style, ...props } ) => (
		<div
			data-testid="card-header"
			onClick={ onClick }
			onKeyDown={ ( e ) => {
				if ( e.key === 'Enter' || e.key === ' ' ) {
					e.preventDefault();
					onClick?.( e );
				}
			} }
			style={ style }
			role="button"
			tabIndex={ 0 }
			{ ...props }
		>
			{ children }
		</div>
	) ),
	Button: jest.fn(
		( { icon, variant, className, style, size, ...props } ) => {
			const buttonProps: Record< string, unknown > = {
				'data-testid': 'toggle-button',
				className,
				style,
				'data-icon': icon,
				'data-variant': variant,
				...props,
			};

			// Only add data-size if size is not undefined
			if ( size !== undefined ) {
				buttonProps[ 'data-size' ] = size;
			}

			return <button { ...buttonProps }>Toggle</button>;
		}
	),
} ) );

describe( 'useCollapsibleCard', () => {
	// Test wrapper component to test the hook
	const TestComponent = ( {
		initialIsOpen,
		children = 'Test Content',
		...headerProps
	}: {
		initialIsOpen?: boolean;
		children?: React.ReactNode;
		[ key: string ]: unknown;
	} ) => {
		const { isOpen, toggle, CardHeader } =
			useCollapsibleCard( initialIsOpen );

		return (
			<div>
				<CardHeader { ...headerProps }>{ children }</CardHeader>
				<div data-testid="content" data-visible={ isOpen }>
					{ isOpen && <div>Card Content</div> }
				</div>
				<button data-testid="external-toggle" onClick={ toggle }>
					External Toggle
				</button>
			</div>
		);
	};

	beforeEach( () => {
		jest.clearAllMocks();
	} );

	describe( 'Initial State', () => {
		it( 'should default to open state when no initial value provided', () => {
			const { result } = renderHook( () => useCollapsibleCard() );

			expect( result.current.isOpen ).toBe( true );
		} );

		it( 'should use provided initial state', () => {
			const { result } = renderHook( () => useCollapsibleCard( false ) );

			expect( result.current.isOpen ).toBe( false );
		} );

		it( 'should accept true as initial state explicitly', () => {
			const { result } = renderHook( () => useCollapsibleCard( true ) );

			expect( result.current.isOpen ).toBe( true );
		} );
	} );

	describe( 'Toggle Functionality', () => {
		it( 'should toggle from open to closed', () => {
			const { result } = renderHook( () => useCollapsibleCard( true ) );

			expect( result.current.isOpen ).toBe( true );

			act( () => {
				result.current.toggle();
			} );

			expect( result.current.isOpen ).toBe( false );
		} );

		it( 'should toggle from closed to open', () => {
			const { result } = renderHook( () => useCollapsibleCard( false ) );

			expect( result.current.isOpen ).toBe( false );

			act( () => {
				result.current.toggle();
			} );

			expect( result.current.isOpen ).toBe( true );
		} );

		it( 'should toggle multiple times correctly', () => {
			const { result } = renderHook( () => useCollapsibleCard( true ) );

			// Start open
			expect( result.current.isOpen ).toBe( true );

			// Toggle to closed
			act( () => {
				result.current.toggle();
			} );
			expect( result.current.isOpen ).toBe( false );

			// Toggle back to open
			act( () => {
				result.current.toggle();
			} );
			expect( result.current.isOpen ).toBe( true );

			// Toggle to closed again
			act( () => {
				result.current.toggle();
			} );
			expect( result.current.isOpen ).toBe( false );
		} );

		it( 'should provide stable toggle function reference', () => {
			const { result, rerender } = renderHook( () =>
				useCollapsibleCard()
			);

			const firstToggle = result.current.toggle;

			// Force re-render
			rerender();

			const secondToggle = result.current.toggle;

			expect( firstToggle ).toBe( secondToggle );
		} );
	} );

	describe( 'CardHeader Component', () => {
		it( 'should render CardHeader with children', () => {
			render( <TestComponent>Test Header Content</TestComponent> );

			expect( screen.getByTestId( 'card-header' ) ).toBeInTheDocument();
			expect(
				screen.getByText( 'Test Header Content' )
			).toBeInTheDocument();
		} );

		it( 'should apply cursor pointer style to CardHeader', () => {
			render( <TestComponent /> );

			const cardHeader = screen.getByTestId( 'card-header' );
			expect( cardHeader ).toHaveStyle( { cursor: 'pointer' } );
		} );

		it( 'should render toggle button with correct initial icon', () => {
			render( <TestComponent initialIsOpen={ true } /> );

			const toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveAttribute(
				'data-icon',
				'chevron-up-icon'
			);
			expect( toggleButton ).toHaveAttribute( 'aria-expanded', 'true' );
		} );

		it( 'should render toggle button with correct icon when closed', () => {
			render( <TestComponent initialIsOpen={ false } /> );

			const toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveAttribute(
				'data-icon',
				'chevron-down-icon'
			);
			expect( toggleButton ).toHaveAttribute( 'aria-expanded', 'false' );
		} );

		it( 'should toggle state when CardHeader is clicked', () => {
			render( <TestComponent initialIsOpen={ true } /> );

			const content = screen.getByTestId( 'content' );
			expect( content ).toHaveAttribute( 'data-visible', 'true' );
			expect( screen.getByText( 'Card Content' ) ).toBeInTheDocument();

			// Click the header
			fireEvent.click( screen.getByTestId( 'card-header' ) );

			expect( content ).toHaveAttribute( 'data-visible', 'false' );
			expect(
				screen.queryByText( 'Card Content' )
			).not.toBeInTheDocument();
		} );

		it( 'should update button icon after toggle', () => {
			render( <TestComponent initialIsOpen={ true } /> );

			let toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveAttribute(
				'data-icon',
				'chevron-up-icon'
			);

			// Click to toggle
			fireEvent.click( screen.getByTestId( 'card-header' ) );

			// Re-query the button after the state change
			toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveAttribute(
				'data-icon',
				'chevron-down-icon'
			);
		} );

		it( 'should pass through additional props to CardHeader', () => {
			render(
				<TestComponent
					className="custom-class"
					data-custom="test-value"
				/>
			);

			const cardHeader = screen.getByTestId( 'card-header' );
			expect( cardHeader ).toHaveClass( 'custom-class' );
			expect( cardHeader ).toHaveAttribute( 'data-custom', 'test-value' );
		} );

		it( 'should merge custom styles with default styles', () => {
			render(
				<TestComponent
					style={ { backgroundColor: 'red', fontSize: '14px' } }
				/>
			);

			const cardHeader = screen.getByTestId( 'card-header' );
			expect( cardHeader ).toHaveStyle( {
				cursor: 'pointer',
				backgroundColor: 'red',
				fontSize: '14px',
			} );
		} );

		it( 'should set correct aria-label for expand state', () => {
			render( <TestComponent initialIsOpen={ false } /> );

			const toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveAttribute( 'aria-label', 'Expand' );
		} );

		it( 'should set correct aria-label for collapse state', () => {
			render( <TestComponent initialIsOpen={ true } /> );

			const toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveAttribute( 'aria-label', 'Collapse' );
		} );

		it( 'should apply iconSize prop to button', () => {
			render( <TestComponent iconSize="large" /> );

			const toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveAttribute( 'data-size', 'large' );
		} );

		it( 'should not pass iconSize when not provided', () => {
			render( <TestComponent /> );

			const toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).not.toHaveAttribute( 'data-size' );
		} );

		it( 'should have tertiary variant on toggle button', () => {
			render( <TestComponent /> );

			const toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveAttribute(
				'data-variant',
				'tertiary'
			);
		} );

		it( 'should have collapsible-card-toggle class on button', () => {
			render( <TestComponent /> );

			const toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveClass( 'collapsible-card-toggle' );
		} );

		it( 'should apply correct button styles', () => {
			render( <TestComponent /> );

			const toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveStyle( {
				color: '#1e1e1e',
				pointerEvents: 'none',
			} );
		} );
	} );

	describe( 'External Toggle Integration', () => {
		it( 'should work with external toggle function', () => {
			render( <TestComponent initialIsOpen={ true } /> );

			const content = screen.getByTestId( 'content' );
			expect( content ).toHaveAttribute( 'data-visible', 'true' );

			// Use external toggle button
			fireEvent.click( screen.getByTestId( 'external-toggle' ) );

			expect( content ).toHaveAttribute( 'data-visible', 'false' );
		} );

		it( 'should sync CardHeader and external toggle', () => {
			render( <TestComponent initialIsOpen={ true } /> );

			let toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveAttribute(
				'data-icon',
				'chevron-up-icon'
			);

			// Use external toggle
			fireEvent.click( screen.getByTestId( 'external-toggle' ) );

			// Re-query the button after the state change
			toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveAttribute(
				'data-icon',
				'chevron-down-icon'
			);
		} );
	} );

	describe( 'Component Re-rendering', () => {
		it( 'should maintain state across re-renders', () => {
			const { rerender } = render(
				<TestComponent initialIsOpen={ true } />
			);

			// Toggle to closed
			fireEvent.click( screen.getByTestId( 'card-header' ) );
			expect( screen.getByTestId( 'content' ) ).toHaveAttribute(
				'data-visible',
				'false'
			);

			// Re-render component
			rerender( <TestComponent initialIsOpen={ true } /> );

			// State should be maintained (still closed)
			expect( screen.getByTestId( 'content' ) ).toHaveAttribute(
				'data-visible',
				'false'
			);
		} );

		it( 'should provide stable CardHeader component reference when dependencies do not change', () => {
			const { result } = renderHook( () => useCollapsibleCard() );

			const firstCardHeader = result.current.CardHeader;

			// Different hook instances will have different CardHeader references
			// This tests that within the same hook instance, the CardHeader is memoized
			expect( result.current.CardHeader ).toBe( firstCardHeader );
		} );
	} );

	describe( 'Accessibility', () => {
		it( 'should have proper aria-expanded attribute', () => {
			render( <TestComponent initialIsOpen={ true } /> );

			let toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveAttribute( 'aria-expanded', 'true' );

			fireEvent.click( screen.getByTestId( 'card-header' ) );

			// Re-query the button after the state change
			toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveAttribute( 'aria-expanded', 'false' );
		} );

		it( 'should update aria-label when state changes', () => {
			render( <TestComponent initialIsOpen={ true } /> );

			let toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveAttribute( 'aria-label', 'Collapse' );

			fireEvent.click( screen.getByTestId( 'card-header' ) );

			// Re-query the button after the state change
			toggleButton = screen.getByTestId( 'toggle-button' );
			expect( toggleButton ).toHaveAttribute( 'aria-label', 'Expand' );
		} );
	} );
} );
