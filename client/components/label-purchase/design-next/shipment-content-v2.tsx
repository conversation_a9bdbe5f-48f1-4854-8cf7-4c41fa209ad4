import { JSX, useEffect } from 'react';
import { isEmpty } from 'lodash';
import { useSelect } from '@wordpress/data';
import {
	__experimentalDivider as Divider,
	__experimentalGrid as Grid,
	__experimentalSpacer as Spacer,
	Animate,
	Notice,
} from '@wordpress/components';
import { __, sprintf } from '@wordpress/i18n';
import { getCurrentOrder, hasUPSPackages } from 'utils';

import { labelPurchaseStore } from 'data/label-purchase';
import { ShipmentDetails } from 'components/label-purchase/details';
import { ShippingRates } from '../shipping-service';
import { useLabelPurchaseContext } from 'context/label-purchase';
import { PurchaseNotice } from '../label';
import { PaymentButtons } from '../purchase';
import { RefundedNotice } from '../label/refunded-notice';
import { NoRatesAvailable } from '../shipping-service/no-rates-available';
import { LABEL_PURCHASE_STATUS } from 'data/constants';
import { PurchaseErrorNotice } from '../purchase/purchase-error-notice';
import { ShipmentItem, ShipmentSubItem } from 'types';
import ItemsCard from './cards/items-card';
import { PackagesCard } from './cards/packages-card';

interface ShipmentContentProps {
	items: unknown[];
}

const LabelPurchaseStatusNotices = () => {
	const {
		labels: {
			getCurrentShipmentLabel,
			hasPurchasedLabel,
			showRefundedNotice,
			hasRequestedRefund,
		},
	} = useLabelPurchaseContext();

	return (
		<>
			{ hasPurchasedLabel( false ) &&
				getCurrentShipmentLabel()?.status !==
					LABEL_PURCHASE_STATUS.PURCHASE_ERROR && (
					<>
						<PurchaseNotice />
						<Divider margin="12" />
					</>
				) }
			<PurchaseErrorNotice label={ getCurrentShipmentLabel() } />
			{ hasRequestedRefund() &&
				showRefundedNotice &&
				! hasPurchasedLabel() && (
					<>
						<RefundedNotice />
						<Spacer marginBottom="12" />
					</>
				) }
		</>
	);
};

export const ShipmentContentV2 = ( {
	items,
}: ShipmentContentProps ): JSX.Element => {
	const order = getCurrentOrder();

	const {
		labels: { hasPurchasedLabel, isCurrentTabPurchasingExtraLabel },
		shipment: {
			getSelectionItems,
			currentShipmentId,
			getShipmentDestination,
		},
		rates: { isFetching },
		packages: { isCustomPackageTab },
		hazmat: { getShipmentHazmat },
		essentialDetails: { setExtraLabelPurchaseCompleted },
	} = useLabelPurchaseContext();
	const availableRates = useSelect(
		( select ) =>
			select( labelPurchaseStore ).getRatesForShipment(
				currentShipmentId
			),
		[ currentShipmentId ]
	);

	useEffect( () => {
		if ( isCurrentTabPurchasingExtraLabel() ) {
			setExtraLabelPurchaseCompleted( getSelectionItems()?.length > 0 );
		}
	}, [
		setExtraLabelPurchaseCompleted,
		isCurrentTabPurchasingExtraLabel,
		getSelectionItems,
	] );

	return (
		<Grid columns={ 1 } rowGap="24px">
			<LabelPurchaseStatusNotices />
			<ItemsCard
				items={ items as ( ShipmentItem | ShipmentSubItem )[] }
			/>
			{ ! hasPurchasedLabel( false ) && (
				<>
					<PackagesCard />
					{ ! Boolean( availableRates ) && (
						<Animate type={ isFetching ? 'loading' : undefined }>
							{ ( { className } ) => (
								<NoRatesAvailable className={ className } />
							) }
						</Animate>
					) }
					{ availableRates && isEmpty( availableRates ) && (
						<Animate type={ isFetching ? 'loading' : undefined }>
							{ ( { className } ) => (
								<Notice
									status="info"
									isDismissible={ false }
									className={ className }
								>
									<p>
										{ sprintf(
											// translators: %1$s: HAZMAT part, %2$s: package part
											__(
												'No shipping rates were found based on the combination of %1$s%2$s and the total shipment weight.',
												'woocommerce-shipping'
											),
											getShipmentHazmat().isHazmat
												? __(
														'the selected HAZMAT category, ',
														'woocommerce-shipping'
												  )
												: '',
											isCustomPackageTab()
												? __(
														'the package type, package dimensions',
														'woocommerce-shipping'
												  )
												: __(
														'the selected package',
														'woocommerce-shipping'
												  )
										) }
									</p>
									<p>
										{ sprintf(
											// translators: %1$s: HAZMAT part, %2$s: package part
											__(
												`We couldn't find a shipping service for the combination of %1$s%2$s and the total shipment weight. Please adjust your input and try again.`,
												'woocommerce-shipping'
											),
											getShipmentHazmat().isHazmat
												? __(
														'the selected HAZMAT category, ',
														'woocommerce-shipping'
												  )
												: '',
											isCustomPackageTab()
												? __(
														'selected package type, package dimensions',
														'woocommerce-shipping'
												  )
												: __(
														'the selected package',
														'woocommerce-shipping'
												  )
										) }
									</p>
								</Notice>
							) }
						</Animate>
					) }

					{ Boolean( availableRates ) &&
						! isEmpty( availableRates ) &&
						( isFetching ? (
							<Animate type="loading">
								{ ( { className } ) => (
									<ShippingRates
										availableRates={ availableRates }
										isFetching={ isFetching }
										className={ className }
									/>
								) }
							</Animate>
						) : (
							<ShippingRates
								availableRates={ availableRates }
								isFetching={ isFetching }
							/>
						) ) }
				</>
			) }
			{ hasUPSPackages() && (
				<>
					<p className="upsdap-trademark-notice upsdap-trademark-notice--desktop">
						{ __(
							'UPS, the UPS brandmark, UPS Ready®, and the color brown are trademarks of United Parcel Service of America, Inc. All Rights Reserved.',
							'woocommerce-shipping'
						) }
					</p>
				</>
			) }

			<ShipmentDetails
				order={ order }
				destinationAddress={ getShipmentDestination() }
			/>
			<PaymentButtons order={ order } />
			{ hasUPSPackages() && (
				<p className="upsdap-trademark-notice upsdap-trademark-notice--mobile">
					{ __(
						'UPS, the UPS brandmark, UPS Ready®, and the color brown are trademarks of United Parcel Service of America, Inc. All Rights Reserved.',
						'woocommerce-shipping'
					) }
				</p>
			) }
		</Grid>
	);
};
