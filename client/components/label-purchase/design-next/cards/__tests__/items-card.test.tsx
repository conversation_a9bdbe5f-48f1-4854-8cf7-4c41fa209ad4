import { render, screen } from '@testing-library/react';
import { merge } from 'lodash';
import ItemsCard from '../items-card';
import {
	useLabelPurchaseContext,
	LabelPurchaseContextType,
} from 'context/label-purchase';
import { ITEMS_SECTION } from 'components/label-purchase/essential-details/constants';
import { DeepPartial, ShipmentItem } from 'types';

// Mock dependencies
jest.mock( 'context/label-purchase', () => ( {
	useLabelPurchaseContext: jest.fn(),
} ) );

jest.mock( 'utils', () => ( {
	getSelectablesCount: jest.fn().mockReturnValue( 5 ),
	getSubItems: jest.fn().mockImplementation( ( item ) => [ item ] ),
	getWeightUnit: jest.fn().mockReturnValue( 'lb' ),
	hasSubItems: jest.fn().mockReturnValue( false ),
} ) );

jest.mock( '../../utils', () => ( {
	formatCurrency: jest
		.fn()
		.mockImplementation( ( amount ) => `$${ amount.toFixed( 2 ) }` ),
	getCurrencyObject: jest.fn().mockReturnValue( { code: 'USD' } ),
} ) );

jest.mock( '../../internal/items-list', () => ( {
	ItemsList: jest.fn( ( { items } ) => (
		<div data-testid="items-list">Items: { items.length }</div>
	) ),
} ) );

jest.mock( '../../internal/useCollapsibleCard', () => ( {
	useCollapsibleCard: jest.fn().mockImplementation( ( defaultOpen ) => ( {
		CardHeader: jest.fn( ( { children, iconSize, isBorderless } ) => (
			<div
				data-testid="card-header"
				data-icon-size={ iconSize }
				data-borderless={ isBorderless }
			>
				{ children }
			</div>
		) ),
		isOpen: defaultOpen,
	} ) ),
} ) );

jest.mock( 'components/label-purchase/split-shipment/header', () => ( {
	StaticHeader: jest.fn( ( props ) => (
		<div
			data-testid="static-header"
			data-testid-props={ JSON.stringify( props ) }
		>
			Static Header
		</div>
	) ),
} ) );

jest.mock(
	'components/label-purchase/split-shipment/selectable-items',
	() => ( {
		SelectableItems: jest.fn( ( props ) => (
			<div
				data-testid="selectable-items"
				data-testid-props={ JSON.stringify( props ) }
			>
				Selectable Items
			</div>
		) ),
	} )
);

describe( 'ItemsCard', () => {
	const mockItems: ShipmentItem[] = [
		{
			id: 1,
			name: 'Test Shipment 1',
			quantity: 2,
			weight: '1.5',
			total: '10.00',
			price: '5.00',
			sku: 'TEST-1',
			product_id: 1,
			meta: {
				customs_info: {
					description: 'Test Description',
					hs_tariff_number: '123456',
					origin_country: 'US',
				},
			},
			image: '',
			variation: [],
			subItems: [],
			tax_class: '',
			subtotal: '',
			subtotal_tax: '',
			total_tax: '',
		},
		{
			id: 2,
			name: 'Test Shipment 2',
			quantity: 1,
			weight: '2.0',
			total: '15.00',
			price: '15.00',
			sku: 'TEST-2',
			product_id: 2,
			meta: {
				customs_info: {
					description: 'Test Description',
					hs_tariff_number: '123456',
					origin_country: 'US',
				},
			},
			image: '',
			variation: [],
			subItems: [
				{
					id: 'subitem-3',
					parentId: 2,
					name: 'Test Item 3',
					quantity: 1,
					weight: '1.0',
					price: '10.00',
					total: '10.00',
					sku: 'TEST-1',
					product_id: 1,
					meta: {
						customs_info: {
							description: 'Test Description',
							hs_tariff_number: '123456',
							origin_country: 'US',
						},
					},
					image: '',
					variation: [],
					subItems: [],
					tax_class: '',
					subtotal: '',
					subtotal_tax: '',
					total_tax: '',
				},
				{
					id: 'subitem-4',
					parentId: 2,
					name: 'Test Item 4',
					quantity: 1,
					weight: '1.0',
					price: '10.00',
					total: '10.00',
					sku: 'TEST-2',
					product_id: 1,
					meta: {
						customs_info: {
							description: 'Test Description',
							hs_tariff_number: '123456',
							origin_country: 'US',
						},
					},
					image: '',
					variation: [],
					subItems: [],
					tax_class: '',
					subtotal: '',
					subtotal_tax: '',
					total_tax: '',
				},
			],
			tax_class: '',
			subtotal: '',
			subtotal_tax: '',
			total_tax: '',
		},
	];

	const mockContext = (
		overrides: DeepPartial< LabelPurchaseContextType > = {}
	) => {
		( useLabelPurchaseContext as jest.Mock ).mockReturnValue(
			merge(
				{
					shipment: {
						shipments: {
							'0': mockItems,
							'1': [],
						},
						selections: {},
						setSelection: jest.fn(),
						currentShipmentId: '0',
						hasVariations: false,
					},
					essentialDetails: {
						focusArea: undefined,
					},
					labels: {
						isCurrentTabPurchasingExtraLabel: jest
							.fn()
							.mockReturnValue( false ),
						hasPurchasedLabel: jest.fn().mockReturnValue( false ),
					},
				},
				overrides
			)
		);
	};

	beforeEach( () => {
		jest.clearAllMocks();
		// Mock scrollTo
		Object.defineProperty( window, 'scrollTo', {
			value: jest.fn(),
			writable: true,
		} );
		// Mock querySelector
		Object.defineProperty( document, 'querySelector', {
			value: jest.fn().mockReturnValue( {
				scrollTo: jest.fn(),
				querySelector: jest.fn().mockReturnValue( {
					getBoundingClientRect: jest
						.fn()
						.mockReturnValue( { height: 72 } ),
				} ),
			} ),
			writable: true,
		} );
	} );

	it( 'renders basic structure correctly', () => {
		mockContext();
		render( <ItemsCard items={ mockItems } /> );

		expect( screen.getByText( 'Items' ) ).toBeInTheDocument();
		expect( screen.getByTestId( 'card-header' ) ).toBeInTheDocument();
	} );

	it( 'displays items summary when collapsed', () => {
		const {
			useCollapsibleCard,
		} = require( '../../internal/useCollapsibleCard' );
		useCollapsibleCard.mockReturnValue( {
			CardHeader: jest.fn( ( { children } ) => (
				<div data-testid="card-header">{ children }</div>
			) ),
			isOpen: false,
		} );

		mockContext();
		render( <ItemsCard items={ mockItems } /> );

		// Should show summary when collapsed
		expect( screen.getByText( /3 items/ ) ).toBeInTheDocument(); // 2 + 1 = 3 total quantity
		expect( screen.getByText( /3.5 lb/ ) ).toBeInTheDocument(); // 1.5 + 2.0 = 3.5 total weight
	} );

	it( 'shows ItemsList when expanded and not purchasing extra label', () => {
		const {
			useCollapsibleCard,
		} = require( '../../internal/useCollapsibleCard' );
		useCollapsibleCard.mockReturnValue( {
			CardHeader: jest.fn( ( { children } ) => (
				<div data-testid="card-header">{ children }</div>
			) ),
			isOpen: true, // Force expanded state
		} );

		mockContext();
		render( <ItemsCard items={ mockItems } /> );

		expect( screen.getByTestId( 'items-list' ) ).toBeInTheDocument();
		expect( screen.getByText( 'Items: 2' ) ).toBeInTheDocument();
	} );

	it( 'displays order total correctly', () => {
		const {
			useCollapsibleCard,
		} = require( '../../internal/useCollapsibleCard' );
		useCollapsibleCard.mockReturnValue( {
			CardHeader: jest.fn( ( { children } ) => (
				<div data-testid="card-header">{ children }</div>
			) ),
			isOpen: true, // Force expanded state
		} );

		mockContext();
		render( <ItemsCard items={ mockItems } /> );

		expect( screen.getByText( 'Order Total' ) ).toBeInTheDocument();
		expect( screen.getAllByText( '$25.00' )[ 0 ] ).toBeInTheDocument(); // 10.00 + 15.00
	} );

	it( 'displays shipment summary correctly', () => {
		const {
			useCollapsibleCard,
		} = require( '../../internal/useCollapsibleCard' );
		useCollapsibleCard.mockReturnValue( {
			CardHeader: jest.fn( ( { children } ) => (
				<div data-testid="card-header">{ children }</div>
			) ),
			isOpen: true, // Force expanded state
		} );

		mockContext();
		render( <ItemsCard items={ mockItems } /> );

		expect( screen.getByText( 'In this shipment' ) ).toBeInTheDocument();
		expect( screen.getByText( '2 items' ) ).toBeInTheDocument(); // items.length
		expect( screen.getByText( '3.5 lb' ) ).toBeInTheDocument(); // total weight
	} );

	it( 'shows selectable items when purchasing extra label', () => {
		const {
			useCollapsibleCard,
		} = require( '../../internal/useCollapsibleCard' );
		useCollapsibleCard.mockReturnValue( {
			CardHeader: jest.fn( ( { children } ) => (
				<div data-testid="card-header">{ children }</div>
			) ),
			isOpen: true, // Force expanded state
		} );

		mockContext( {
			labels: {
				isCurrentTabPurchasingExtraLabel: jest
					.fn()
					.mockReturnValue( true ),
			},
		} );

		render( <ItemsCard items={ mockItems } /> );

		expect(
			screen.getByText(
				'Select the items you want to include in the new shipment.'
			)
		).toBeInTheDocument();
		expect( screen.getByTestId( 'static-header' ) ).toBeInTheDocument();
		expect( screen.getByTestId( 'selectable-items' ) ).toBeInTheDocument();
	} );

	it( 'handles selection updates correctly', () => {
		const {
			useCollapsibleCard,
		} = require( '../../internal/useCollapsibleCard' );
		useCollapsibleCard.mockReturnValue( {
			CardHeader: jest.fn( ( { children } ) => (
				<div data-testid="card-header">{ children }</div>
			) ),
			isOpen: true, // Force expanded state
		} );

		const setSelection = jest.fn();
		mockContext( {
			shipment: {
				setSelection,
				selections: { '0': [] },
			},
			labels: {
				isCurrentTabPurchasingExtraLabel: jest
					.fn()
					.mockReturnValue( true ),
			},
		} );

		render( <ItemsCard items={ mockItems } /> );

		// The addSelectionForShipment function should be passed to SelectableItems
		expect( screen.getByTestId( 'selectable-items' ) ).toBeInTheDocument();
	} );

	it( 'handles selectAll functionality', () => {
		const {
			useCollapsibleCard,
		} = require( '../../internal/useCollapsibleCard' );
		useCollapsibleCard.mockReturnValue( {
			CardHeader: jest.fn( ( { children } ) => (
				<div data-testid="card-header">{ children }</div>
			) ),
			isOpen: true, // Force expanded state
		} );

		const setSelection = jest.fn();
		mockContext( {
			shipment: {
				setSelection,
				selections: {},
				shipments: { '0': mockItems },
			},
			labels: {
				isCurrentTabPurchasingExtraLabel: jest
					.fn()
					.mockReturnValue( true ),
			},
		} );

		render( <ItemsCard items={ mockItems } /> );

		// This tests that the selectAll function is properly defined
		// The actual functionality would be tested through the SelectableItems component
		expect( screen.getByTestId( 'selectable-items' ) ).toBeInTheDocument();
	} );

	it( 'handles essential details focus area changes', () => {
		// Test that component renders without errors when focus area changes
		mockContext( {
			essentialDetails: {
				focusArea: undefined,
			},
		} );

		const { rerender } = render( <ItemsCard items={ mockItems } /> );

		// Change focus area to ITEMS_SECTION
		mockContext( {
			essentialDetails: {
				focusArea: ITEMS_SECTION,
			},
		} );

		rerender( <ItemsCard items={ mockItems } /> );

		// Verify component still renders correctly
		expect( screen.getByText( 'Items' ) ).toBeInTheDocument();
	} );

	it( 'calculates weight correctly for items without weight', () => {
		const {
			useCollapsibleCard,
		} = require( '../../internal/useCollapsibleCard' );
		useCollapsibleCard.mockReturnValue( {
			CardHeader: jest.fn( ( { children } ) => (
				<div data-testid="card-header">{ children }</div>
			) ),
			isOpen: false, // Collapsed to show summary
		} );

		const itemsWithoutWeight: ShipmentItem[] = [
			{
				...mockItems[ 0 ],
				weight: '', // Empty weight
			},
			{
				...mockItems[ 1 ],
				weight: '', // Undefined weight
			},
		];

		mockContext();
		render( <ItemsCard items={ itemsWithoutWeight } /> );

		// Should handle missing weights gracefully - check in summary
		expect( screen.getByText( /3 items/ ) ).toBeInTheDocument(); // Still shows item count
	} );

	it( 'renders with single item correctly', () => {
		const {
			useCollapsibleCard,
		} = require( '../../internal/useCollapsibleCard' );
		useCollapsibleCard.mockReturnValue( {
			CardHeader: jest.fn( ( { children } ) => (
				<div data-testid="card-header">{ children }</div>
			) ),
			isOpen: false, // Collapsed to show summary
		} );

		const singleItem = [ mockItems[ 0 ] ];
		mockContext();
		render( <ItemsCard items={ singleItem } /> );

		expect( screen.getByText( /2 items/ ) ).toBeInTheDocument(); // Shows quantity (2), not item count (1)
	} );

	it( 'handles empty items array', () => {
		const {
			useCollapsibleCard,
		} = require( '../../internal/useCollapsibleCard' );
		useCollapsibleCard.mockReturnValue( {
			CardHeader: jest.fn( ( { children } ) => (
				<div data-testid="card-header">{ children }</div>
			) ),
			isOpen: false, // Collapsed to show summary
		} );

		mockContext();
		render( <ItemsCard items={ [] } /> );

		expect( screen.getByText( 'Items' ) ).toBeInTheDocument();
		expect( screen.getByText( '0 items' ) ).toBeInTheDocument();
	} );
} );
