import { render, screen } from '@testing-library/react';
import React from 'react';
import { PackagesCard } from '../packages-card';
import { useLabelPurchaseContext } from 'context/label-purchase';
import { TAB_NAMES } from 'components/label-purchase/packages/constants';

// Mock dependencies
jest.mock( '@wordpress/components', () => ( {
	Card: jest.fn( ( { children } ) => (
		<div data-testid="card">{ children }</div>
	) ),
	CardBody: jest.fn( ( { children, style } ) => (
		<div data-testid="card-body" style={ style }>
			{ children }
		</div>
	) ),
	CardDivider: jest.fn( ( { style } ) => (
		<div data-testid="card-divider" style={ style }>
			Divider
		</div>
	) ),
	Flex: jest.fn( ( { children, direction, align } ) => (
		<div
			data-testid="flex"
			data-direction={ direction }
			data-align={ align }
		>
			{ children }
		</div>
	) ),
	__experimentalText: jest.fn( ( { children, as, weight, size, style } ) => (
		<span
			data-testid="text"
			data-as={ as }
			data-weight={ weight }
			data-size={ size }
			style={ style }
		>
			{ children }
		</span>
	) ),
	__experimentalSpacer: jest.fn( ( { children, marginBottom } ) => (
		<div data-testid="spacer" data-margin-bottom={ marginBottom }>
			{ children }
		</div>
	) ),
} ) );

jest.mock( '@wordpress/i18n', () => ( {
	__: jest.fn( ( text ) => text ),
	sprintf: jest.fn( ( format, ...args ) => {
		// Handle numbered placeholders like %1$d, %2$d, etc.
		return format.replace(
			/%(\d+)\$[sd]/g,
			( match: string, num: string ) => {
				const index = parseInt( num, 10 ) - 1;
				return args[ index ] || '';
			}
		);
	} ),
} ) );

jest.mock( '@wordpress/data', () => ( {
	select: jest.fn().mockReturnValue( {
		getRatesForShipment: jest.fn().mockReturnValue( [] ),
	} ),
	dispatch: jest.fn().mockReturnValue( {
		ratesReset: jest.fn(),
	} ),
} ) );

jest.mock( 'data/label-purchase', () => ( {
	labelPurchaseStore: 'label-purchase-store',
} ) );

jest.mock( 'components/label-purchase/packages/constants', () => ( {
	TAB_NAMES: {
		CUSTOM_PACKAGE: 'custom',
		CARRIER_PACKAGE: 'carrier',
		SAVED_TEMPLATES: 'saved',
	},
} ) );

jest.mock( 'components/label-purchase/packages/tab-views', () => ( {
	CarrierPackage: jest.fn( ( props ) => (
		<div
			data-testid="carrier-package"
			data-props={ JSON.stringify( props ) }
		>
			Carrier Package Component
		</div>
	) ),
	CustomPackage: jest.fn( ( props ) => (
		<div
			data-testid="custom-package"
			data-props={ JSON.stringify( props ) }
		>
			Custom Package Component
		</div>
	) ),
	SavedTemplates: jest.fn( ( props ) => (
		<div
			data-testid="saved-templates"
			data-props={ JSON.stringify( props ) }
		>
			Saved Templates Component
		</div>
	) ),
} ) );

jest.mock( 'context/label-purchase', () => ( {
	useLabelPurchaseContext: jest.fn(),
} ) );

jest.mock( 'utils/tracks', () => ( {
	recordEvent: jest.fn(),
} ) );

jest.mock( '../../internal/useCollapsibleCard', () => ( {
	useCollapsibleCard: jest.fn(),
} ) );

jest.mock( 'components/wp', () => ( {
	Badge: jest.fn( ( { children, intent } ) => (
		<span data-testid="badge" data-intent={ intent }>
			{ children }
		</span>
	) ),
} ) );

jest.mock( 'components/label-purchase/hazmat/usps-hazmat-categories', () => ( {
	uspsHazmatCategories: {
		'class-1': 'Explosives',
		'class-2': 'Gases',
		'class-3': 'Flammable Liquids',
	},
} ) );

jest.mock( 'components/label-purchase/hazmat', () => ( {
	Hazmat: jest.fn( () => <div data-testid="hazmat">Hazmat Component</div> ),
} ) );

jest.mock( '../../internal/package-type-select', () => {
	return jest.fn( ( props ) => (
		<div
			data-testid="package-type-select"
			data-props={ JSON.stringify( props ) }
		>
			Package Type Select
		</div>
	) );
} );

describe( 'PackagesCard', () => {
	const mockCardHeader = jest.fn(
		( { children, iconSize, isBorderless } ) => (
			<div
				data-testid="card-header"
				data-icon-size={ iconSize }
				data-borderless={ isBorderless }
			>
				{ children }
			</div>
		)
	);

	const mockContext = {
		packages: {
			getCustomPackage: jest.fn().mockReturnValue( {
				length: '10',
				width: '8',
				height: '6',
			} ),
			setCustomPackage: jest.fn(),
			getSelectedPackage: jest.fn().mockReturnValue( {
				id: 'ups_small_box',
				name: 'UPS Small Box',
				type: 'box',
				isLetter: false,
				length: '13',
				width: '11',
				height: '2',
				outerDimensions: '13" x 11" x 2"',
				innerDimensions: '12.75" x 10.75" x 1.75"',
				dimensions: '13" x 11" x 2"',
				boxWeight: 0.2,
				isUserDefined: undefined,
				carrierId: 'ups',
			} ),
			setSelectedPackage: jest.fn(),
			setCurrentPackageTab: jest.fn(),
			currentPackageTab: TAB_NAMES.CUSTOM_PACKAGE,
		},
		hazmat: {
			getShipmentHazmat: jest.fn().mockReturnValue( {
				isHazmat: false,
				category: null,
			} ),
		},
		shipment: {
			currentShipmentId: '1',
		},
		rates: {
			removeSelectedRate: jest.fn(),
		},
	};

	const {
		useCollapsibleCard,
	} = require( '../../internal/useCollapsibleCard' );

	beforeEach( () => {
		jest.clearAllMocks();

		// Reset mockContext to default state
		mockContext.packages.currentPackageTab = TAB_NAMES.CUSTOM_PACKAGE;
		mockContext.packages.getCustomPackage.mockReturnValue( {
			length: '10',
			width: '8',
			height: '6',
		} );
		mockContext.packages.getSelectedPackage.mockReturnValue( {
			id: 'ups_small_box',
			name: 'UPS Small Box',
			type: 'box',
			isLetter: false,
			length: '13',
			width: '11',
			height: '2',
			outerDimensions: '13" x 11" x 2"',
			innerDimensions: '12.75" x 10.75" x 1.75"',
			dimensions: '13" x 11" x 2"',
			boxWeight: 0.2,
			isUserDefined: undefined,
			carrierId: 'ups',
		} );
		mockContext.hazmat.getShipmentHazmat.mockReturnValue( {
			isHazmat: false,
			category: null,
		} );

		( useLabelPurchaseContext as jest.Mock ).mockReturnValue( mockContext );
		useCollapsibleCard.mockReturnValue( {
			CardHeader: mockCardHeader,
			isOpen: true,
		} );
	} );

	describe( 'Rendering', () => {
		it( 'should render card structure correctly', () => {
			render( <PackagesCard /> );

			expect( screen.getByTestId( 'card' ) ).toBeInTheDocument();
			expect( screen.getByTestId( 'card-header' ) ).toBeInTheDocument();
			expect( screen.getByTestId( 'card-body' ) ).toBeInTheDocument();
		} );

		it( 'should render card header with correct props', () => {
			render( <PackagesCard /> );

			const cardHeader = screen.getByTestId( 'card-header' );
			expect( cardHeader ).toHaveAttribute( 'data-icon-size', 'small' );
			expect( cardHeader ).toHaveAttribute( 'data-borderless', 'true' );
		} );

		it( 'should render package title', () => {
			render( <PackagesCard /> );

			expect( screen.getByText( 'Package' ) ).toBeInTheDocument();
		} );

		it( 'should use collapsible card hook with default open state', () => {
			render( <PackagesCard /> );

			expect( useCollapsibleCard ).toHaveBeenCalledWith( true );
		} );
	} );

	describe( 'Card Header Content', () => {
		it( 'should show package summary when card is closed', () => {
			useCollapsibleCard.mockReturnValue( {
				CardHeader: mockCardHeader,
				isOpen: false,
			} );

			render( <PackagesCard /> );

			// Should show summary text when closed - check for specific text that should be there
			expect( screen.getByText( /10.*8.*6/ ) ).toBeInTheDocument();
		} );

		it( 'should show package summary when package needs dimensions', () => {
			mockContext.packages.getCustomPackage.mockReturnValue( {
				length: '0',
				width: '8',
				height: '6',
			} );

			render( <PackagesCard /> );

			// Should show "Needs dimensions" badge
			expect( screen.getByTestId( 'badge' ) ).toBeInTheDocument();
			expect(
				screen.getByText( 'Needs dimensions' )
			).toBeInTheDocument();
		} );

		it( 'should not show summary when card is open and package has dimensions', () => {
			useCollapsibleCard.mockReturnValue( {
				CardHeader: mockCardHeader,
				isOpen: true,
			} );

			render( <PackagesCard /> );

			// Should not show summary when open and dimensions are valid
			expect(
				screen.queryByText( /10" x 8" x 6"/ )
			).not.toBeInTheDocument();
		} );
	} );

	describe( 'Package Summary Generation', () => {
		describe( 'Custom Package', () => {
			beforeEach( () => {
				mockContext.packages.currentPackageTab =
					TAB_NAMES.CUSTOM_PACKAGE;
			} );

			it( 'should show dimensions for valid custom package', () => {
				useCollapsibleCard.mockReturnValue( {
					CardHeader: mockCardHeader,
					isOpen: false,
				} );

				render( <PackagesCard /> );

				expect( screen.getByText( /10.*8.*6/ ) ).toBeInTheDocument();
			} );

			it( 'should show needs dimensions badge for missing length', () => {
				mockContext.packages.getCustomPackage.mockReturnValue( {
					length: '0',
					width: '8',
					height: '6',
				} );

				useCollapsibleCard.mockReturnValue( {
					CardHeader: mockCardHeader,
					isOpen: false,
				} );

				render( <PackagesCard /> );

				expect( screen.getByTestId( 'badge' ) ).toHaveAttribute(
					'data-intent',
					'warning'
				);
				expect(
					screen.getByText( 'Needs dimensions' )
				).toBeInTheDocument();
			} );

			it( 'should show needs dimensions badge for missing width', () => {
				mockContext.packages.getCustomPackage.mockReturnValue( {
					length: '10',
					width: '0',
					height: '6',
				} );

				useCollapsibleCard.mockReturnValue( {
					CardHeader: mockCardHeader,
					isOpen: false,
				} );

				render( <PackagesCard /> );

				expect(
					screen.getByText( 'Needs dimensions' )
				).toBeInTheDocument();
			} );

			it( 'should show needs dimensions badge for missing height', () => {
				mockContext.packages.getCustomPackage.mockReturnValue( {
					length: '10',
					width: '8',
					height: '0',
				} );

				useCollapsibleCard.mockReturnValue( {
					CardHeader: mockCardHeader,
					isOpen: false,
				} );

				render( <PackagesCard /> );

				expect(
					screen.getByText( 'Needs dimensions' )
				).toBeInTheDocument();
			} );

			it( 'should include hazmat information in custom package summary', () => {
				mockContext.hazmat.getShipmentHazmat.mockReturnValue( {
					isHazmat: true,
					category: 'class-1',
				} );

				useCollapsibleCard.mockReturnValue( {
					CardHeader: mockCardHeader,
					isOpen: false,
				} );

				render( <PackagesCard /> );

				expect(
					screen.getByText( /10.*8.*6.*Explosives/ )
				).toBeInTheDocument();
			} );
		} );

		describe( 'Carrier Package', () => {
			beforeEach( () => {
				mockContext.packages.currentPackageTab =
					TAB_NAMES.CARRIER_PACKAGE;
			} );

			it( 'should show carrier package name when selected', () => {
				useCollapsibleCard.mockReturnValue( {
					CardHeader: mockCardHeader,
					isOpen: false,
				} );

				render( <PackagesCard /> );

				expect(
					screen.getByText( 'UPS Small Box' )
				).toBeInTheDocument();
			} );

			it( 'should include hazmat information in carrier package summary', () => {
				mockContext.hazmat.getShipmentHazmat.mockReturnValue( {
					isHazmat: true,
					category: 'class-2',
				} );

				useCollapsibleCard.mockReturnValue( {
					CardHeader: mockCardHeader,
					isOpen: false,
				} );

				render( <PackagesCard /> );

				expect(
					screen.getByText( 'UPS Small Box · Gases' )
				).toBeInTheDocument();
			} );

			it( 'should show needs dimensions badge when no package selected', () => {
				mockContext.packages.getSelectedPackage.mockReturnValue( null );

				useCollapsibleCard.mockReturnValue( {
					CardHeader: mockCardHeader,
					isOpen: false,
				} );

				render( <PackagesCard /> );

				expect(
					screen.getByText( 'Needs dimensions' )
				).toBeInTheDocument();
			} );
		} );
	} );

	describe( 'Card Body Content', () => {
		it( 'should render card body when open', () => {
			render( <PackagesCard /> );

			expect( screen.getByTestId( 'card-body' ) ).toBeInTheDocument();
			expect( screen.getByTestId( 'card-body' ) ).toHaveStyle( {
				paddingTop: '0px',
			} );
		} );

		it( 'should not render card body when closed', () => {
			useCollapsibleCard.mockReturnValue( {
				CardHeader: mockCardHeader,
				isOpen: false,
			} );

			render( <PackagesCard /> );

			expect(
				screen.queryByTestId( 'card-body' )
			).not.toBeInTheDocument();
		} );

		it( 'should render PackageTypeSelect with correct props', () => {
			const PackageTypeSelect = require( '../../internal/package-type-select' );

			render( <PackagesCard /> );

			expect(
				screen.getByTestId( 'package-type-select' )
			).toBeInTheDocument();

			// Check that PackageTypeSelect was called with the correct props
			expect( PackageTypeSelect ).toHaveBeenCalledWith(
				expect.objectContaining( {
					currentPackageTab: TAB_NAMES.CUSTOM_PACKAGE,
					selectedPackage: mockContext.packages.getSelectedPackage(),
					setSelectedPackage: mockContext.packages.setSelectedPackage,
				} ),
				expect.anything()
			);
		} );

		it( 'should render Hazmat component', () => {
			render( <PackagesCard /> );

			expect( screen.getByTestId( 'hazmat' ) ).toBeInTheDocument();
		} );

		it( 'should render card divider with correct styles', () => {
			render( <PackagesCard /> );

			const divider = screen.getByTestId( 'card-divider' );
			expect( divider ).toHaveStyle( {
				marginTop: '16px',
				marginBottom: '16px',
			} );
		} );
	} );

	describe( 'Tab Rendering', () => {
		it( 'should render CustomPackage when custom tab is selected', () => {
			mockContext.packages.currentPackageTab = TAB_NAMES.CUSTOM_PACKAGE;

			render( <PackagesCard /> );

			expect(
				screen.getByTestId( 'custom-package' )
			).toBeInTheDocument();
			expect(
				screen.queryByTestId( 'carrier-package' )
			).not.toBeInTheDocument();
			expect(
				screen.queryByTestId( 'saved-templates' )
			).not.toBeInTheDocument();
		} );

		it( 'should render CarrierPackage when carrier tab is selected', () => {
			mockContext.packages.currentPackageTab = TAB_NAMES.CARRIER_PACKAGE;

			render( <PackagesCard /> );

			expect(
				screen.getByTestId( 'carrier-package' )
			).toBeInTheDocument();
			expect(
				screen.queryByTestId( 'custom-package' )
			).not.toBeInTheDocument();
			expect(
				screen.queryByTestId( 'saved-templates' )
			).not.toBeInTheDocument();
		} );

		it( 'should render SavedTemplates when saved templates tab is selected', () => {
			mockContext.packages.currentPackageTab = TAB_NAMES.SAVED_TEMPLATES;

			render( <PackagesCard /> );

			expect(
				screen.getByTestId( 'saved-templates' )
			).toBeInTheDocument();
			expect(
				screen.queryByTestId( 'custom-package' )
			).not.toBeInTheDocument();
			expect(
				screen.queryByTestId( 'carrier-package' )
			).not.toBeInTheDocument();
		} );

		it( 'should pass correct props to CustomPackage', () => {
			mockContext.packages.currentPackageTab = TAB_NAMES.CUSTOM_PACKAGE;

			const {
				CustomPackage,
			} = require( 'components/label-purchase/packages/tab-views' );

			render( <PackagesCard /> );

			expect(
				screen.getByTestId( 'custom-package' )
			).toBeInTheDocument();

			// Check that CustomPackage was called with the correct props
			expect( CustomPackage ).toHaveBeenCalledWith(
				expect.objectContaining( {
					rawPackageData: mockContext.packages.getCustomPackage(),
					setRawPackageData: mockContext.packages.setCustomPackage,
					setSelectedPackage: mockContext.packages.setSelectedPackage,
				} ),
				expect.anything()
			);
		} );

		it( 'should pass correct props to CarrierPackage', () => {
			mockContext.packages.currentPackageTab = TAB_NAMES.CARRIER_PACKAGE;

			const {
				CarrierPackage,
			} = require( 'components/label-purchase/packages/tab-views' );

			render( <PackagesCard /> );

			expect(
				screen.getByTestId( 'carrier-package' )
			).toBeInTheDocument();

			// Check that CarrierPackage was called with the correct props
			expect( CarrierPackage ).toHaveBeenCalledWith(
				expect.objectContaining( {
					selectedPackage: mockContext.packages.getSelectedPackage(),
					setSelectedPackage: mockContext.packages.setSelectedPackage,
				} ),
				expect.anything()
			);
		} );
	} );

	describe( 'Tab Selection Logic', () => {
		const { select, dispatch } = require( '@wordpress/data' );
		const { recordEvent } = require( 'utils/tracks' );

		beforeEach( () => {
			select.mockReturnValue( {
				getRatesForShipment: jest.fn().mockReturnValue( [] ),
			} );
			dispatch.mockReturnValue( {
				ratesReset: jest.fn(),
			} );
		} );

		it( 'should not trigger actions when selecting the same tab', () => {
			const PackageTypeSelect = require( '../../internal/package-type-select' );

			render( <PackagesCard /> );

			// Simulate selecting the same tab
			const tabSelectionClick =
				PackageTypeSelect.mock.calls[ 0 ][ 0 ].setCurrentPackageTab;
			tabSelectionClick( TAB_NAMES.CUSTOM_PACKAGE );

			// Should not call any actions when selecting the same tab
			expect(
				mockContext.packages.setCurrentPackageTab
			).not.toHaveBeenCalled();
			expect( recordEvent ).not.toHaveBeenCalled();
		} );

		it( 'should handle tab selection with rates reset when rates exist', () => {
			select.mockReturnValue( {
				getRatesForShipment: jest
					.fn()
					.mockReturnValue( [ 'rate1', 'rate2' ] ),
			} );

			const ratesReset = jest.fn();
			dispatch.mockReturnValue( {
				ratesReset,
			} );

			const PackageTypeSelect = require( '../../internal/package-type-select' );

			render( <PackagesCard /> );

			// Get the setCurrentPackageTab function passed to PackageTypeSelect
			const tabSelectionClick =
				PackageTypeSelect.mock.calls[ 0 ][ 0 ].setCurrentPackageTab;
			tabSelectionClick( TAB_NAMES.CARRIER_PACKAGE );

			expect( ratesReset ).toHaveBeenCalled();
			expect( mockContext.rates.removeSelectedRate ).toHaveBeenCalled();
			expect(
				mockContext.packages.setCurrentPackageTab
			).toHaveBeenCalledWith( TAB_NAMES.CARRIER_PACKAGE );
			expect( recordEvent ).toHaveBeenCalledWith(
				'label_purchase_package_tab_clicked',
				{
					tab_name: TAB_NAMES.CARRIER_PACKAGE,
				}
			);
		} );

		it( 'should handle tab selection without rates reset when no rates exist', () => {
			const ratesReset = jest.fn();
			dispatch.mockReturnValue( {
				ratesReset,
			} );

			const PackageTypeSelect = require( '../../internal/package-type-select' );

			render( <PackagesCard /> );

			// Get the setCurrentPackageTab function passed to PackageTypeSelect
			const tabSelectionClick =
				PackageTypeSelect.mock.calls[ 0 ][ 0 ].setCurrentPackageTab;
			tabSelectionClick( TAB_NAMES.CARRIER_PACKAGE );

			expect( ratesReset ).not.toHaveBeenCalled();
			expect( mockContext.rates.removeSelectedRate ).toHaveBeenCalled();
			expect(
				mockContext.packages.setCurrentPackageTab
			).toHaveBeenCalledWith( TAB_NAMES.CARRIER_PACKAGE );
			expect( recordEvent ).toHaveBeenCalledWith(
				'label_purchase_package_tab_clicked',
				{
					tab_name: TAB_NAMES.CARRIER_PACKAGE,
				}
			);
		} );
	} );

	describe( 'Package Dimensions Validation', () => {
		it( 'should identify package as needing dimensions when custom package has invalid length', () => {
			mockContext.packages.getCustomPackage.mockReturnValue( {
				length: '0',
				width: '8',
				height: '6',
			} );
			mockContext.packages.currentPackageTab = TAB_NAMES.CUSTOM_PACKAGE;

			render( <PackagesCard /> );

			// Should show "Needs dimensions" even when card is open
			expect(
				screen.getByText( 'Needs dimensions' )
			).toBeInTheDocument();
		} );

		it( 'should identify package as not needing dimensions when all custom package dimensions are valid', () => {
			mockContext.packages.getCustomPackage.mockReturnValue( {
				length: '10',
				width: '8',
				height: '6',
			} );
			mockContext.packages.currentPackageTab = TAB_NAMES.CUSTOM_PACKAGE;

			render( <PackagesCard /> );

			// Should not show "Needs dimensions" badge when dimensions are valid and card is open
			expect(
				screen.queryByText( 'Needs dimensions' )
			).not.toBeInTheDocument();
		} );

		it( 'should not check dimensions for non-custom package tabs', () => {
			mockContext.packages.getCustomPackage.mockReturnValue( {
				length: '0',
				width: '0',
				height: '0',
			} );
			mockContext.packages.currentPackageTab = TAB_NAMES.CARRIER_PACKAGE;

			render( <PackagesCard /> );

			// Should not show "Needs dimensions" for custom package when carrier tab is selected
			// (Only shows if no carrier package is selected)
			expect(
				screen.queryByText( 'Needs dimensions' )
			).not.toBeInTheDocument();
		} );
	} );

	describe( 'Edge Cases', () => {
		it( 'should handle missing custom package data', () => {
			mockContext.packages.getCustomPackage.mockReturnValue( null );

			useCollapsibleCard.mockReturnValue( {
				CardHeader: mockCardHeader,
				isOpen: false,
			} );

			render( <PackagesCard /> );

			expect(
				screen.getByText( 'Needs dimensions' )
			).toBeInTheDocument();
		} );

		it( 'should handle undefined custom package dimensions', () => {
			mockContext.packages.getCustomPackage.mockReturnValue( {
				length: undefined,
				width: undefined,
				height: undefined,
			} );

			useCollapsibleCard.mockReturnValue( {
				CardHeader: mockCardHeader,
				isOpen: false,
			} );

			render( <PackagesCard /> );

			expect(
				screen.getByText( 'Needs dimensions' )
			).toBeInTheDocument();
		} );

		it( 'should handle string dimensions that parse to zero', () => {
			mockContext.packages.getCustomPackage.mockReturnValue( {
				length: 'abc',
				width: '8',
				height: '6',
			} );

			useCollapsibleCard.mockReturnValue( {
				CardHeader: mockCardHeader,
				isOpen: false,
			} );

			render( <PackagesCard /> );

			expect(
				screen.getByText( 'Needs dimensions' )
			).toBeInTheDocument();
		} );
	} );
} );
