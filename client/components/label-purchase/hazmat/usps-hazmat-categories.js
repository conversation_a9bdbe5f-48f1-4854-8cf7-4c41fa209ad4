/**
 * External dependencies
 */
import { __ } from '@wordpress/i18n';

export const uspsHazmatCategories = {
	AIR_ELIGIBLE_ETHANOL: __(
		'Air Eligible Ethanol Package - (authorized fragrance and hand sanitizer shipments)',
		'woocommerce-shipping'
	),
	CLASS_1: __(
		'Class 1 – Toy Propellant/Safety Fuse Package',
		'woocommerce-shipping'
	),
	CLASS_3: __(
		'Class 3 - Package (Hand sanitizer, rubbing alcohol, ethanol base products, flammable liquids etc.)',
		'woocommerce-shipping'
	),
	CLASS_7: __(
		'Class 7 – Radioactive Materials Package (e.g., smoke detectors, minerals, gun sights, etc.)',
		'woocommerce-shipping'
	),
	CLASS_8_CORROSIVE: __(
		'Class 8 – Corrosive Materials Package - Air Eligible Corrosive Materials (certain cleaning or tree/weed killing compounds, etc.)',
		'woocommerce-shipping'
	),
	CLASS_8_WET_BATTERY: __(
		'Class 8 – Nonspillable Wet Battery Package - Sealed lead acid batteries',
		'woocommerce-shipping'
	),
	CLASS_9_NEW_LITHIUM_INDIVIDUAL: __(
		'Class 9 - Lithium Battery Marked – Ground Only Package - New Individual or spare lithium batteries (marked UN3480 or UN3090)',
		'woocommerce-shipping'
	),
	CLASS_9_USED_LITHIUM: __(
		'Class 9 - Lithium Battery – Returns Package - Used electronic devices containing or packaged with lithium batteries (markings required)',
		'woocommerce-shipping'
	),
	CLASS_9_NEW_LITHIUM_DEVICE: __(
		'Class 9 - Lithium batteries, marked package - New electronic devices packaged with lithium batteries (marked UN3481 or UN3091)',
		'woocommerce-shipping'
	),
	CLASS_9_DRY_ICE: __(
		'Class 9 – Dry Ice Package (limited to 5 lbs. if shipped via Air)',
		'woocommerce-shipping'
	),
	CLASS_9_UNMARKED_LITHIUM: __(
		'Class 9 – Lithium batteries, unmarked package - New electronic devices installed or packaged with lithium batteries (no marking)',
		'woocommerce-shipping'
	),
	CLASS_9_MAGNETIZED: __(
		'Class 9 – Magnetized Materials Package',
		'woocommerce-shipping'
	),
	DIVISION_4_1: __(
		'Division 4.1 – Mailable flammable solids and Safety Matches Package - Safety/strike on box matches, book matches, mailable flammable solids',
		'woocommerce-shipping'
	),
	// eslint-disable-next-line @wordpress/i18n-translator-comments
	DIVISION_5_1: __(
		'Division 5.1 – Oxidizers Package - Hydrogen peroxide (8 to 20% concentration)',
		'woocommerce-shipping'
	),
	DIVISION_5_2: __(
		'Division 5.2 – Organic Peroxides Package',
		'woocommerce-shipping'
	),
	DIVISION_6_1: __(
		'Division 6.1 – Toxic Materials Package (with an LD50 of 50 mg/kg or less) - (pesticides, herbicides, etc.)',
		'woocommerce-shipping'
	),
	DIVISION_6_2: __(
		'Division 6.2 - Hazardous Materials - Biological Materials (e.g., lab test kits, authorized COVID test kit returns)',
		'woocommerce-shipping'
	),
	EXCEPTED_QUANTITY_PROVISION: __(
		'Excepted Quantity Provision Package (e.g., small volumes of flammable liquids, corrosive, toxic or environmentally hazardous materials - marking required)',
		'woocommerce-shipping'
	),
	GROUND_ONLY: __(
		'Ground Only Hazardous Materials (For items that are not listed, but are restricted to surface only)',
		'woocommerce-shipping'
	),
	ID8000: __(
		'ID8000 Consumer Commodity Package - Air Eligible ID8000 Consumer Commodity (Non-flammableaerosols, Flammable combustible liquids, Toxic Substance, Miscellaneious hazardous materials)',
		'woocommerce-shipping'
	),
	LIGHTERS: __(
		'Lighters Package - Authorized Lighters',
		'woocommerce-shipping'
	),
	LIMITED_QUANTITY: __(
		'LTD QTY Ground Package - Aerosols, spray disinfectants, spray paint, hair spray, propane, butane, cleaning products, etc. - Fragrances, nail polish, nail polish remover, solvents, hand sanitizer, rubbing alcohol, ethanol base products, etc. - Other limited quantity surface materials (cosmetics, cleaning products, paints, etc.)',
		'woocommerce-shipping'
	),
	SMALL_QUANTITY_PROVISION: __(
		'Small Quantity Provision Package (markings required)',
		'woocommerce-shipping'
	),
};
