.label-purchase-hazmat {
	margin-top: 24px;

	label {
		font-size: 13px;
		font-weight: 400;
		text-transform: none;
	}

	.hazmat-radio-control > div {
		display: flex;

		.components-flex {
			display: inline-flex;
			flex-direction: row;
			align-items: flex-start;
			margin-left: 24px;
		}

		.components-radio-control__option {
			display: inline-flex;
			margin-right: 16px;

			&:last-of-type {
				margin-right: 0;
			}
		}
	}

	.hazmat-category-dropdown {
		position: relative;
		width: 100%;

		section {
			display: flex;
			flex-direction: row;
			width: 100%;

			span {
				color: var(--gutenberg-gray-700, #757575);
				font-size: 13px;
				font-style: normal;
				font-weight: 400;
				line-height: 16px;

				&:first-child {
					color: var(--gutenberg-gray-900, #1e1e1e);
					margin-right: 12px;
					opacity: 1;
				}
			}
		}

		.hazmat-category__toggle {
			background: var(--gutenberg-white, #fff);
			border: 1px solid var(--gutenberg-gray-600, #949494);
			border-radius: 2px;
			box-shadow: none;
			color: var(--gutenberg-gray-600, #949494);
			flex-direction: row-reverse;
			justify-content: space-between;
			min-height: 41px;
			padding: 12px;
			width: 100%;

			&:disabled {
				background: var(--wp-gray-0, #f6f7f7);
				border-color: var(--gutenberg-gray-400, #ccc);
				color: var(--gutenberg-gray-500, #bbb);
			}

			&.has-error {
				border-color: var(--gutenberg-red-500, #d94f4f);
			}
		}

		.components-popover__content {
			max-width: 100%;
			width: 100%;

			.components-button {
				border-bottom: 1px solid #efefef;
				height: unset;

				&:last-child {
					border-bottom: none;
				}

				.components-menu-item__item {
					max-width: 100%;
					overflow: hidden;
					text-align: left;
					white-space: break-spaces;
					word-wrap: anywhere;
				}
			}
		}
	}
}
