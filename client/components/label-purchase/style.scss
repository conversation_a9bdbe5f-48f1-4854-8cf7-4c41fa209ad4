$left-sidebar-width: 160px;
$left-sidebar-folded-width: 36px;
$top-nav-height: 32px;

@import "items/styles";
@import "details/styles";
@import "packages/styles";
@import "split-shipment/styles";
@import "hazmat/styles";
@import "shipping-service/styles";
@import "customs/styles";
@import "label/styles";
@import "../controlled-popover/styles";
@import "../confirm-modal/styles";
@import "../address-verified-icon/styles";
@import "../address-step/styles";
@import "../carrier/upsdap/styles";

:root {
	--wcs-button-height__default: 40px;
	--wcs-button-padding__default: 12px;
	--wcs-main-modal-z-index: 10000;
	--wcs-sentry-modal-z-index: 10001;

	// Sticky elements variables
	--wcs-label-purchase-modal-header-top: var(--wcs-promo-banner-height);
	--wcs-label-purchase-modal-header-height: 72px;
	--wcs-shipment-tabs-top: calc(var(--wcs-promo-banner-height) + var(--wcs-label-purchase-modal-header-height));
	--wcs-shipment-tabs-heigh: 52px;
	--wcs-shipment-details-top: calc(var(--wcs-promo-banner-height) + var(--wcs-label-purchase-modal-header-height) + var(--wcs-shipment-tabs-heigh) + 24px);
}

// TourKit is a modal that is rendered in the body, so we need to set a higher z-index
.woocommerce-tour-kit {
	z-index: calc(var(--wcs-main-modal-z-index) + 1);

	.tour-kit-frame__arrow {
		background-color: var(--gutenberg-white, #fff);
	}
}

.wcshipping-shipping-label-meta-box {

	&__content {
		align-items: center;
		display: flex;
		justify-content: space-between;

		svg {
			flex-shrink: 0;
		}
	}

	&__summary {
		display: block;
		font-size: 15px;
		font-weight: 700;
		margin-left: 10px;
	}

	#normal-sortables & {

		&__button-container {
			margin-left: auto;
		}
	}

	#side-sortables & {
		flex-direction: column;
		justify-content: space-around;

		&__content {
			flex-direction: column;
			margin-left: 0;
			text-align: center;
		}
	}
}

.label-purchase-overlay {
	background: transparent;
	margin-left: $left-sidebar-width;
	margin-top: var(--wp-admin--admin-bar--height, $top-nav-height);
	z-index: var(--wcs-main-modal-z-index);
}

.sentry-error-embed-wrapper {
	// We need to override the z-index of the Sentry error embed wrapper
	// so it shows above the label-purchase-overlay.
	z-index: var(--wcs-sentry-modal-z-index) !important;
}

.label-purchase-modal {
	background-color: #f6f7f7;
	border-radius: 0 !important;
	bottom: 0;
	box-shadow: none;
	height: 100%;
	max-height: 100%;
	min-width: calc(100vw - $left-sidebar-width);
	position: absolute;
	right: 0;
	width: calc(100vw - $left-sidebar-width);

	.components-text-control__input,
	.components-input-control__input,
	.components-select-control__input,
	button.components-button {
		font-size: 13px;
		line-height: normal;

		&:not(.is-small, .is-compact, .woocommerce-tooltip__button) {
			height: var(--wcs-button-height__default);
			padding: var(--wcs-button-padding__default);
		}
	}

	.components-modal__content {
		background: var(--gutenberg-white, #fff);
		padding: 0;
	}

	.items-header {

		button {
			margin-right: -12px; // To compensate for the padding right of the Button component
		}
	}

	.label-purchase-modal__header {
		align-items: center;
		background: var(--gutenberg-white, #fff);
		box-sizing: border-box;
		gap: var(--grid-unit-15, 12px);
		height: var(--wcs-label-purchase-modal-header-height);
		justify-content: stretch;
		left: 0;
		padding: 0 32px;
		position: sticky;
		top: var(--wcs-label-purchase-modal-header-top);
		width: 100%;
		z-index: 10;

		// The separator needs to be a pseudo-element to be placed after sticky elements with a full width.
		&::before {
			content: "";
			background-color: var(--gutenberg-gray-300, #ddd);
			display: block;
			height: 1px;
			left: 0;
			top: calc(var(--wcs-label-purchase-modal-header-height) + var(--wcs-shipment-tabs-heigh) - 2px);
			position: absolute;
			right: 0;
		}

		button.components-button {
			padding: 0;
			min-width: unset;
		}

		h3 {
			color: var(--Gutenberg-Gray-900, #1e1e1e);
			font-size: 20px;
			font-style: normal;
			font-weight: 500;
			line-height: normal;
			margin: 0;
		}

		span {
			background: var(--gutenberg-gray-100, #f0f0f0);
			border-radius: 2px;
			font-size: 11px;
			font-style: normal;
			font-weight: 400;
			line-height: 16px;
			padding: 5px 9px;
		}
	}

	hr.components-divider {
		color: var(--Gutenberg-Gray-300, #ddd);
		position: sticky;
		top: 12px;
	}

	.package-total-weight {

		&.has-error {
			position: relative;

			.components-input-control__input,
			.components-input-control__backdrop,
			.components-input-control__suffix,
			.components-base-control__help {
				border-color: #d94f4f;
				color: #d94f4f;
			}

			.components-base-control__help {
				position: absolute;
			}
		}

	}

	.package-total-weight-unit {
		width: 60px;

		label {
			visibility: hidden;
		}

		.components-select-control__input,
		svg {
			color: #007cba;
			fill: #007cba;
		}
	}

	input[disabled],
	button[disabled],
	.components-select-control__input[disabled],
	select[disabled] {
		background: var(--gutenberg-gray-100, #f0f0f0);
		border-color: var(--gutenberg-gray-400, #ccc);
		color: var(--Gutenberg-Gray-600, #949494);
		cursor: default;
	}

	button[disabled] {

		section span:first-child {
			opacity: 0.5;
		}
	}

	.get-rates-button-wrapper {
		padding-top: 23.4px; // Padded to match the height of the total weight input
	}

	.components-notice {
		border: 0;
		gap: 12px;
		margin: 0;
		padding: var(--grid-unit-15, 12px) var(--grid-unit-20, 16px);

		.components-notice__content {
			margin: 0;

			>p {
				margin: 0;
			}
		}

		&.is-info {
			background: var(--WP-Blue-Blue-0, #f0f6fc);
		}
	}
}

.label-purchase-modal__content {
	box-sizing: border-box;
	margin: 0 auto;
	max-width: var(--max-width, 1400px);
	padding: 48px 68px;

	>div:first-child {
		flex: 3;

		>div h3 {
			color: var(--gutenberg-gray-900, #1e1e1e);
			font-size: 20px;
			font-weight: 500;
			letter-spacing: -0.2px;
		}
	}

	input[type="checkbox"]:disabled {
		background-color: #ccc;
		border-color: #ccc;
	}
}

.label-purchase-note {
	color: var(--gutenberg-gray-700, #757575);
	font-size: 11px;
	font-style: normal;
	line-height: 16px;
	padding-left: var(--grid-unit-20, 16px);
}

@media only screen and (max-width: 960px) {

	.label-purchase-modal__content {
		padding: 24px 16px;
	}

	.folded,
	.auto-fold {

		.label-purchase-overlay {
			margin-left: 0;
		}

		.label-purchase-modal {
			min-width: 100%;
			width: 100%;
		}

		&.sticky-menu {

			.label-purchase-overlay {
				margin-left: $left-sidebar-folded-width;
			}

			.label-purchase-modal {
				min-width: calc(100vw - $left-sidebar-folded-width);
				width: calc(100vw - $left-sidebar-folded-width);
			}
		}
	}
}
