import { renderHook, act } from '@testing-library/react';
import { select, useSelect } from '@wordpress/data';
import { useShipmentState } from '../shipment';

jest.mock( 'data/label-purchase', () => ( {
	labelPurchaseStore: jest.fn(),
} ) );

jest.mock( '@wordpress/data', () => ( {
	dispatch: jest.fn(),
	select: jest.fn().mockReturnValue( {
		getPurchasedLabel: jest.fn(),
		getOriginAddresses: jest.fn().mockReturnValue( [] ),
	} ),
	useSelect: jest.fn(),
} ) );

jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
	return {
		...mockUtils(),
		getShipmentsAutogeneratedFromLabels: jest.fn(),
	};
} );

import { getShipmentsAutogeneratedFromLabels } from 'utils';
import { LABEL_PURCHASE_STATUS } from 'data/constants';

describe( 'useShipmentState', () => {
	it( 'provides getSelectionItems function', () => {
		const { result } = renderHook( () => useShipmentState() );

		act( () => {
			result.current.setSelection( {
				0: [ { id: 1, name: 'Test Item' } ],
			} );
		} );

		expect( result.current.getSelectionItems( '0' ) ).toEqual( [
			{ id: 1, name: 'Test Item' },
		] );
	} );

	it( 'should remove the shipment correctly', () => {
		const updateShipmentsMock = jest.fn();
		jest.spyOn( require( '@wordpress/data' ), 'dispatch' ).mockReturnValue(
			{
				updateShipments: updateShipmentsMock,
			}
		);

		jest.spyOn( require( 'utils/config' ), 'getConfig' ).mockReturnValue( {
			order: {
				line_items: [
					{ id: 1, name: 'Item 1' },
					{ id: 2, name: 'Item 2' },
				],
			},
		} );

		const { result } = renderHook( () => useShipmentState() );

		const initialShipments = {
			0: [ { id: 1, name: 'Item 1' } ],
			1: [ { id: 2, name: 'Item 2' } ],
		};

		act( () => {
			result.current.setShipments( initialShipments );
			result.current.setCurrentShipmentId( '1' );
		} );

		act( () => {
			result.current.resetShipmentAndSelection();
		} );

		expect( updateShipmentsMock ).toHaveBeenCalledWith( {
			orderId: expect.any( String ),
			shipments: {
				0: [ { id: 1, name: 'Item 1' } ],
			},
			shipmentIdsToUpdate: {},
		} );
	} );

	describe( 'isShipmentAutogeneratedFromLabel', () => {
		test( 'returns false when currentShipmentId is not in the fallback array', () => {
			// current shipment id is "0" by default; 0 is not in this array.
			getShipmentsAutogeneratedFromLabels.mockReturnValue( [ 5 ] );
			const { result } = renderHook( () => useShipmentState() );
			expect( result.current.isShipmentAutogeneratedFromLabel() ).toBe(
				false
			);
		} );

		test( 'returns true when currentShipmentId is in the fallback array', () => {
			// "0" converts to 0; we include 0 here.
			getShipmentsAutogeneratedFromLabels.mockReturnValue( [ 0, 2 ] );
			const { result } = renderHook( () => useShipmentState() );
			expect( result.current.isShipmentAutogeneratedFromLabel() ).toBe(
				true
			);
		} );

		test( 'updates the fallback value if currentShipmentId is updated', () => {
			// Setup so that fallback is signaled for shipment id 1.
			getShipmentsAutogeneratedFromLabels.mockReturnValue( [ 1 ] );
			const { result } = renderHook( () => useShipmentState() );
			// Initially, currentShipmentId is "0".
			expect( result.current.isShipmentAutogeneratedFromLabel() ).toBe(
				false
			);
			// Update currentShipmentId to '1'
			act( () => {
				result.current.setCurrentShipmentId( '1' );
			} );
			expect( result.current.isShipmentAutogeneratedFromLabel() ).toBe(
				true
			);
		} );
	} );

	describe( 'getShipmentDestination', () => {
		const orderDestination = { address: '123 Order St' };
		const labelDestination = { address: '456 Label St' };

		const setupMocks = ( label = null, destination = orderDestination ) => {
			useSelect.mockReturnValue( destination );
			select.mockReturnValue( { getPurchasedLabel: () => label } );
		};

		test( 'returns order destination when no purchased label exists', () => {
			setupMocks( null );

			const { result } = renderHook( () => useShipmentState() );
			expect( result.current.getShipmentDestination() ).toBe(
				orderDestination
			);
		} );

		test( 'returns order destination for non-purchased label status', () => {
			setupMocks( { status: LABEL_PURCHASE_STATUS.PURCHASE_ERROR } );

			const { result } = renderHook( () => useShipmentState() );
			expect( result.current.getShipmentDestination() ).toBe(
				orderDestination
			);
		} );

		test( 'returns label destination for purchased label', () => {
			setupMocks(
				{ status: LABEL_PURCHASE_STATUS.PURCHASED },
				labelDestination
			);

			const { result } = renderHook( () => useShipmentState() );
			expect( result.current.getShipmentDestination() ).toBe(
				labelDestination
			);
		} );
	} );
} );
