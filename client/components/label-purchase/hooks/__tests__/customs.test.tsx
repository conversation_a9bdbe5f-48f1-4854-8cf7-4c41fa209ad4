import { renderHook, act } from '@testing-library/react';
import { useCustomsState } from '../customs';
import { useSelect } from '@wordpress/data';
import { ShipmentItem } from 'types';

jest.mock( 'data/address', () => ( {
	addressStore: {},
} ) );

jest.mock( 'data/label-purchase', () => ( {
	labelPurchaseStore: {},
} ) );

// mock utils
jest.mock( 'utils', () => {
	const originalUtils = jest.requireActual( 'utils' );
	return {
		...originalUtils,
		getOriginAddresses: jest.fn().mockReturnValue( {} ),
	};
} );

const mockDestinationAddress = {
	id: 123,
	name: '<PERSON>',
	address: '123 Main St - destination',
	address_2: 'Apt 1',
	city: 'San Francisco - dest',
	state: 'CA',
	postcode: '94105',
	country: 'PR',
	phone: '1234567890',
	isVerified: true,
};

const mockCustomsItems = [
	{
		id: 1,
		product_id: 1,
		name: 'Product 1',
		meta: {
			customs_info: {
				description: 'Product 1 Description',
				hs_tariff_number: '12345',
			},
		},
	},
	{
		id: 2,
		product_id: 2,
		name: 'Product 2',
		meta: {
			customs_info: {
				description: 'Product 2 Description',
				hs_tariff_number: '',
			},
		},
	},
];

const mockCustomsItems2 = [
	{
		id: 5,
		product_id: 5,
		name: 'Product 1',
		meta: {
			customs_info: {
				description: 'Product 5 Description',
				hs_tariff_number: '55555',
			},
		},
	},
	{
		id: 6,
		product_id: 6,
		name: 'Product 6',
		meta: {
			customs_info: {
				description: 'Product 6 Description',
				hs_tariff_number: '66666',
			},
		},
	},
];

jest.mock( '@wordpress/data', () => ( {
	useSelect: jest.fn().mockImplementation( ( selector ) => {
		return selector( () => ( {
			getShipmentDestination: () => ( { country: 'DE' } ),
			getCustomsInformation: ( shipmentId: string ) =>
				shipmentId === '1'
					? {
							items: mockCustomsItems,
							contentsType: 'type',
							contentsExplanation: 'explanation',
							restrictionType: 'restrictionType',
							restrictionComments: 'restrictionComments',
							itn: 'itn sting',
							isReturnToSender: false,
					  }
					: {
							items: mockCustomsItems2,
							contentsType: 'type2',
							contentsExplanation: 'explanation2',
							restrictionType: 'restrictionType2',
							restrictionComments: 'restrictionComments2',
							itn: 'itn sting2',
							isReturnToSender: true,
					  },
		} ) );
	} ),
	select: jest.fn().mockReturnValue( {
		getStoreOrigin: jest.fn().mockReturnValue( { country: 'US' } ),
		getCustomsInformation: jest.fn().mockReturnValue( null ),
	} ),
} ) );

describe( 'useCustomsState - updateCustomsItems', () => {
	beforeEach( () => {
		jest.clearAllMocks();
	} );

	it( 'should update customs items when shipment items change', () => {
		const { result } = renderHook( () =>
			useCustomsState(
				'1',
				{},
				{},
				jest.fn().mockReturnValue( mockCustomsItems ),
				jest.fn().mockReturnValue( [] ),
				jest.fn().mockReturnValue( { country: 'US' } ),
				jest.fn().mockReturnValue( mockDestinationAddress )
			)
		);

		act( () => {
			result.current.updateCustomsItems();
		} );

		const expectedItems = mockCustomsItems.map( ( item ) => ( {
			...item,
			description: item.meta.customs_info.description,
			hsTariffNumber: item.meta.customs_info.hs_tariff_number,
			originCountry: 'US', // Country of origin from getShipmentOrigin
		} ) );

		expect( result.current.getCustomsState().items ).toEqual(
			expectedItems
		);
	} );

	it( 'should retain existing customs values when available', () => {
		const { result } = renderHook( () =>
			useCustomsState(
				'1',
				{},
				{},
				jest.fn().mockReturnValue( mockCustomsItems ),
				jest.fn().mockReturnValue( null ),
				jest.fn().mockReturnValue( { country: 'US' } ),
				jest.fn().mockReturnValue( mockDestinationAddress )
			)
		);

		act( () => {
			result.current.updateCustomsItems();
		} );

		const updatedItems = result.current.getCustomsState().items;
		expect( updatedItems[ 0 ].hsTariffNumber ).toBe( '12345' ); // Retain existing HS tariff number
		expect( updatedItems[ 1 ].hsTariffNumber ).toBe( '' ); // New item, no existing value
	} );

	it( 'should handle the case where customs is not needed', () => {
		const getShipmentItems = jest.fn().mockReturnValue( mockCustomsItems );
		// @ts-ignore
		useSelect.mockImplementation( ( selector ) => {
			return selector( () => ( {
				getOrderDestination: () => ( { country: 'US' } ), // Change to domestic shipment
				getCustomsInformation: ( shipmentId: string ) =>
					shipmentId === '1'
						? {
								items: mockCustomsItems,
								contentsType: 'type',
								contentsExplanation: 'explanation',
								restrictionType: 'restrictionType',
								restrictionComments: 'restrictionComments',
								itn: 'itn sting',
								isReturnToSender: false,
						  }
						: null,
			} ) );
		} );

		const { result } = renderHook( () =>
			// a nonexistent shipment ID
			useCustomsState(
				'0',
				{},
				{},
				getShipmentItems,
				jest.fn().mockReturnValue( null ),
				jest.fn().mockReturnValue( { country: 'US' } ),
				jest.fn().mockReturnValue( mockDestinationAddress )
			)
		);

		const destination = useSelect(
			// @ts-ignore
			( select ) => select( 'addressStore' ).getOrderDestination(),
			[]
		);
		expect( destination.country ).toBe( 'US' ); // Check if the mocked value is US

		const itemsBeforeUpdateCall = result.current.getCustomsState().items;
		act( () => {
			result.current.updateCustomsItems();
		} );

		// Expect no customs items update when customs are not needed
		expect( result.current.getCustomsState().items ).toEqual(
			itemsBeforeUpdateCall
		);
	} );

	it( 'when the second shipment gets removed, the items should get added to the first and the only shipment', () => {
		const getShipmentItems = jest.fn().mockReturnValue( mockCustomsItems );
		const getShipmentItems2 = jest
			.fn()
			.mockReturnValue( mockCustomsItems2 );
		// @ts-ignore
		useSelect.mockImplementation( ( selector ) => {
			return selector( () => ( {
				getShipmentDestination: () => ( { country: 'DE' } ),
				getCustomsInformation: ( shipmentId: string ) =>
					shipmentId === '0'
						? {
								items: mockCustomsItems,
								contentsType: 'type',
								contentsExplanation: 'explanation',
								restrictionType: 'restrictionType',
								restrictionComments: 'restrictionComments',
								itn: 'itn sting',
								isReturnToSender: false,
						  }
						: {
								items: mockCustomsItems2,
								contentsType: 'type2',
								contentsExplanation: 'explanation2',
								restrictionType: 'restrictionType2',
								restrictionComments: 'restrictionComments2',
								itn: 'itn sting 2',
								isReturnToSender: true,
						  },
			} ) );
		} );

		const getShipmentOrigin = jest
			.fn()
			.mockReturnValue( { country: 'US' } );
		const getShipmentDestination = jest
			.fn()
			.mockReturnValue( mockDestinationAddress );

		const { result, rerender } = renderHook(
			( {
				currentShipmentId: id,
				getShipmentItems: getItems,
				getShipmentOrigin: _getShipmentOrigin,
				getShipmentDestination: _getShipmentDestination,
			} ) =>
				useCustomsState(
					id,
					{},
					{},
					getItems,
					jest.fn().mockReturnValue( null ),
					_getShipmentOrigin,
					_getShipmentDestination
				),
			{
				initialProps: {
					currentShipmentId: '0',
					getShipmentItems,
					getShipmentOrigin,
					getShipmentDestination,
				},
			}
		);

		act( () => {
			result.current.updateCustomsItems();
		} );

		const updatedItems = result.current.getCustomsState().items;
		expect( updatedItems[ 0 ].hsTariffNumber ).toBe( '12345' );
		expect( updatedItems[ 1 ].hsTariffNumber ).toBe( '' );

		rerender( {
			currentShipmentId: '1',
			getShipmentItems: getShipmentItems2,
			getShipmentOrigin,
			getShipmentDestination,
		} );

		const secondShipmentUpdatedItems =
			result.current.getCustomsState().items;

		expect( secondShipmentUpdatedItems[ 0 ].hsTariffNumber ).toBe(
			'55555'
		);
		expect( secondShipmentUpdatedItems[ 1 ].hsTariffNumber ).toBe(
			'66666'
		);

		rerender( {
			currentShipmentId: '',
			getShipmentItems: jest.fn().mockReturnValue( [
				// Combine the items from both shipments
				...mockCustomsItems,
				...mockCustomsItems2,
			] ),
			getShipmentOrigin,
			getShipmentDestination,
		} );

		act( () => {
			result.current.updateCustomsItems();
		} );

		const singleShipmentUpdatedItems =
			result.current.getCustomsState().items;

		expect( singleShipmentUpdatedItems[ 0 ].hsTariffNumber ).toBe(
			'12345'
		);
		expect( singleShipmentUpdatedItems[ 1 ].hsTariffNumber ).toBe( '' );
		expect( singleShipmentUpdatedItems[ 2 ].hsTariffNumber ).toBe(
			'55555'
		);
		expect( singleShipmentUpdatedItems[ 3 ].hsTariffNumber ).toBe(
			'66666'
		);
	} );

	it( 'uses getSelectionItems when available', () => {
		const mockGetSelectionItems = jest.fn().mockReturnValue( [
			{
				id: 2,
				product_id: 2,
				name: 'Test Product',
				meta: {
					customs_info: {
						description: 'Test Product Description',
						hs_tariff_number: '23456',
					},
				},
			},
		] );
		const mockGetShipmentItems = jest.fn().mockReturnValue( [] );

		const { result } = renderHook( () =>
			useCustomsState(
				'0',
				{},
				{},
				mockGetShipmentItems,
				mockGetSelectionItems,
				jest.fn().mockReturnValue( { country: 'US' } ),
				jest.fn().mockReturnValue( { country: 'CA' } )
			)
		);

		act( () => {
			result.current.getCustomsState();
		} );

		expect( mockGetSelectionItems ).toHaveBeenCalled();
	} );

	it( 'should update customs items based on selections', () => {
		// @ts-ignore
		useSelect.mockImplementation( ( selector ) => {
			return selector( () => ( {
				getCustomsInformation: () => null,
			} ) );
		} );
		const mockShipmentItems = [
			{
				id: 1,
				product_id: 1,
				name: 'Product 1',
				weight: '1',
				quantity: 1,
				price: '10',
				subtotal: '10',
				meta: {},
				subItems: [],
				tax_class: '',
				image: '',
				sku: '',
				subtotal_tax: '',
				total: '',
				total_tax: '',
			},
			{
				id: 2,
				product_id: 2,
				name: 'Product 2',
				weight: '2',
				quantity: 1,
				price: '20',
				subtotal: '20',
				meta: {},
				subItems: [],
				tax_class: '',
				image: '',
				sku: '',
				subtotal_tax: '',
				total: '',
				total_tax: '',
			},
		] as ShipmentItem[];

		const selections = {
			'1': [
				{
					...mockShipmentItems[ 0 ],
					quantity: 2,
				},
			],
		};

		const getShipmentItems = jest.fn().mockReturnValue( mockShipmentItems );
		const getSelectionItems = jest.fn().mockReturnValue( selections );
		const getShipmentOrigin = jest
			.fn()
			.mockReturnValue( { country: 'US' } );
		const getShipmentDestination = jest
			.fn()
			.mockReturnValue( { country: 'CA' } );

		const { result } = renderHook( () =>
			useCustomsState(
				'1',
				{ '1': mockShipmentItems },
				selections,
				getShipmentItems,
				getSelectionItems,
				getShipmentOrigin,
				getShipmentDestination
			)
		);

		// Give time for effects to run
		act( () => {
			expect( result.current.getCustomsState().items ).toEqual( [
				expect.objectContaining( {
					product_id: 1,
					quantity: 2,
				} ),
			] );
		} );
	} );
} );
