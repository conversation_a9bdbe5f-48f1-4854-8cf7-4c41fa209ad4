import { renderHook, act } from '@testing-library/react';
import { RATES_FETCH_FAILED } from 'data/label-purchase/action-types';
import { Carrier, Package, Rate } from 'types';

const wordpressDataSelect = {
	getRatesForShipment: jest.fn(),
	getCustomsInformation: jest.fn(),
	getSelectedRates: jest.fn(),
	getSelectedRateOptions: jest.fn().mockReturnValue( {} ),
};

const setupMocks = () => {
	jest.mock( 'data/label-purchase', () => ( {
		labelPurchaseStore: jest.fn(),
	} ) );

	jest.mock( '@wordpress/data', () => {
		const original = jest.requireActual( '@wordpress/data' );
		return {
			...original,
			dispatch: jest.fn().mockReturnValue( {
				getRates: jest.fn().mockReturnValue( {
					type: 'RATES_FETCH_SUCCEEDED',
					payload: {
						rates: [],
						shipmentId: '1',
					},
				} ),
			} ),
			select: jest.fn().mockReturnValue( wordpressDataSelect ),
			useSelect: jest.fn().mockReturnValue( wordpressDataSelect ),
		};
	} );

	jest.mock( 'utils', () => {
		const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
		return mockUtils( {
			getConfig: jest.fn().mockReturnValue( {
				packagesSettings: {
					packages: {},
				},
			} ),
			getAccountSettings: jest.fn().mockReturnValue( {
				purchaseSettings: {
					use_last_service: false,
				},
				userMeta: {
					last_carrier_id: 'default_carrier',
					last_service_id: 'default_service',
				},
			} ),
			getCurrentOrder: jest.fn().mockReturnValue( { id: 1 } ),
			applyPromo: jest.fn( ( rate, promoId ) => {
				// Mock promo application: return discounted rate if promoId exists
				return promoId ? rate - 5 : rate;
			} ),
		} );
	} );
};

setupMocks();

import { useRatesState } from '../rates';
import { select } from '@wordpress/data';

describe( 'useRatesState', () => {
	beforeEach( () => {
		jest.clearAllMocks();
		setupMocks();
	} );

	const defaultProps = {
		currentShipmentId: '1',
		currentPackageTab: 'default',
		getPackageForRequest: jest.fn(),
		applyHazmatToPackage: jest.fn( ( pkg ) => pkg ),
		totalWeight: 1,
		customs: {
			getCustomsState: jest.fn(),
			setCustomsState: jest.fn(),
			hasErrors: jest.fn(),
			setErrors: jest.fn(),
			isCustomsNeeded: jest.fn(),
			isHSTariffNumberRequired: jest.fn(),
			updateCustomsItems: jest.fn(),
			maybeApplyCustomsToPackage: jest.fn( ( pkg ) => pkg ),
		},
		getShipmentOrigin: jest.fn(),
		getCurrentShipmentDate: jest.fn().mockReturnValue( {
			shippingDate: new Date( '2025-01-01' ),
		} ),
	};

	it( 'should initialize with empty selected rates', () => {
		const { result } = renderHook( () => useRatesState( defaultProps ) );

		expect( result.current.selectedRates ).toEqual( { 0: null } );
		expect( result.current.isFetching ).toBe( false );
		expect( result.current.errors ).toEqual( {} );
	} );

	describe( 'matchAndSelectRate', () => {
		it( 'should match and select a rate with its parent when both exist', () => {
			const mockRate = {
				serviceId: 'test_service',
				carrierId: 'usps' as Carrier,
				rate: 10.0,
				type: 'adultSignatureRequired' as const,
				freePickup: false,
				insurance: 0,
				isSelected: false,
				listRate: 10.0,
				rateId: 'test_rate_id',
				retailRate: 10.0,
				shipmentId: 'test_shipment_id',
				title: 'Test Service',
				caveats: [],
				tracking: false,
			};

			const mockParentRate = {
				serviceId: 'parent_service',
				carrierId: 'usps' as Carrier,
				rate: 15.0,
				freePickup: false,
				insurance: 0,
				isSelected: false,
				listRate: 10.0,
				rateId: 'test_rate_id',
				retailRate: 10.0,
				shipmentId: 'test_shipment_id',
				title: 'Test Service',
				caveats: [],
				tracking: false,
			};

			const mockGetRatesForShipment = jest.fn().mockReturnValue( {
				usps: [ mockRate, mockParentRate ],
			} );

			( select as jest.Mock ).mockReturnValue( {
				...wordpressDataSelect,
				getRatesForShipment: mockGetRatesForShipment,
			} );

			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			act( () => {
				const matched = result.current.matchAndSelectRate( {
					rate: mockRate,
					parent: mockParentRate,
				} );

				expect( matched ).toEqual( {
					rate: mockRate,
					parent: mockParentRate,
				} );
			} );
		} );

		it( 'should return false when parent rate is not found for non-default rate type', () => {
			const mockRate = {
				serviceId: 'test_service',
				carrierId: 'usps' as Carrier,
				rate: 10.0,
				type: 'adultSignatureRequired' as const,
				freePickup: false,
				insurance: 0,
				isSelected: false,
				listRate: 10.0,
				rateId: 'test_rate_id',
				retailRate: 10.0,
				shipmentId: 'test_shipment_id',
				title: 'Test Service',
				caveats: [],
				tracking: false,
			};

			const mockParentRate = {
				serviceId: 'parent_service',
				carrierId: 'usps' as Carrier,
				rate: 15.0,
				freePickup: false,
				insurance: 0,
				isSelected: false,
				listRate: 10.0,
				rateId: 'test_rate_id',
				retailRate: 10.0,
				shipmentId: 'test_shipment_id',
				title: 'Test Service',
				caveats: [],
				tracking: false,
			};

			// Mock getRatesForShipment to return specific rate but no parent rate
			const mockGetRatesForShipment = jest
				.fn()
				.mockReturnValue( { usps: [ mockRate ] } );

			( select as jest.Mock ).mockReturnValue( {
				...wordpressDataSelect,
				getRatesForShipment: mockGetRatesForShipment,
			} );

			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			act( () => {
				const matched = result.current.matchAndSelectRate( {
					rate: mockRate,
					parent: mockParentRate,
				} );

				expect( matched ).toBe( false );
			} );
		} );

		it( 'should match and select a rate without parent for default rate type', () => {
			const mockDefaultRate = {
				serviceId: 'test_service',
				carrierId: 'usps' as Carrier,
				rate: 10.0,
				freePickup: false,
				insurance: 0,
				isSelected: false,
				listRate: 10.0,
				rateId: 'test_rate_id',
				retailRate: 10.0,
				shipmentId: 'test_shipment_id',
				title: 'Test Service',
				caveats: [],
				tracking: false,
			};

			const mockGetRatesForShipment = jest.fn().mockReturnValue( {
				usps: [ mockDefaultRate ],
			} );

			( select as jest.Mock ).mockReturnValue( {
				...wordpressDataSelect,
				getRatesForShipment: mockGetRatesForShipment,
			} );

			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			act( () => {
				const matched = result.current.matchAndSelectRate( {
					rate: mockDefaultRate,
					parent: null,
				} );

				expect( matched ).toEqual( {
					rate: mockDefaultRate,
					parent: null,
				} );
			} );
		} );

		it( 'should return false when rate is not found', () => {
			const mockRate = {
				serviceId: 'nonexistent_service',
				carrierId: 'usps' as Carrier,
				rate: 10.0,
				freePickup: false,
				insurance: 0,
				isSelected: false,
				listRate: 10.0,
				rateId: 'test_rate_id',
				retailRate: 10.0,
				shipmentId: 'test_shipment_id',
				title: 'Test Service',
				caveats: [],
				tracking: false,
			};

			const mockGetRatesForShipment = jest.fn().mockReturnValue( {} );

			( select as jest.Mock ).mockReturnValue( {
				...wordpressDataSelect,
				getRatesForShipment: mockGetRatesForShipment,
			} );

			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			act( () => {
				const matched = result.current.matchAndSelectRate( {
					rate: mockRate,
					parent: null,
				} );

				expect( matched ).toBe( false );
			} );
		} );
	} );

	describe( 'sortRates', () => {
		it( 'should sort rates by the specified criteria', () => {
			const rates = [
				{ serviceId: 'Standard', rate: 10 } as Rate,
				{ serviceId: 'Express', rate: 20 } as Rate,
			];

			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			const sortedRates = result.current.sortRates( rates, 'rate' );
			expect( sortedRates[ 0 ].rate ).toBe( 10 );
			expect( sortedRates[ 1 ].rate ).toBe( 20 );
		} );

		it( 'should always put MediaMail at the bottom', () => {
			const rates = [
				{ serviceId: 'MediaMail', rate: 5 } as Rate,
				{ serviceId: 'Standard', rate: 10 } as Rate,
				{ serviceId: 'Express', rate: 20 } as Rate,
			];

			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			const sortedRates = result.current.sortRates( rates, 'rate' );
			expect( sortedRates[ sortedRates.length - 1 ].serviceId ).toBe(
				'MediaMail'
			);
		} );

		it( 'should prioritize last used service when use_last_service is enabled', () => {
			// Mock account settings with use_last_service enabled
			jest.requireMock( 'utils' ).getAccountSettings.mockReturnValue( {
				purchaseSettings: {
					use_last_service: true,
				},
				userMeta: {
					last_carrier_id: 'usps' as Carrier,
					last_service_id: 'Express',
				},
			} );

			const rates = [
				{ serviceId: 'Standard', rate: 10 } as Rate,
				{ serviceId: 'Express', rate: 20 } as Rate,
				{ serviceId: 'Ground', rate: 15 } as Rate,
			];

			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			const sortedRates = result.current.sortRates( rates, 'rate' );
			expect( sortedRates[ 0 ].serviceId ).toBe( 'Express' );
		} );

		it( 'should sort rates by promo price when sorting by cheapest', () => {
			const rates = [
				{
					serviceId: 'Standard',
					rate: 15,
				} as Rate,
				{
					serviceId: 'Express',
					rate: 17,
					promoId: 'promo123',
				} as Rate,
				{
					serviceId: 'Priority',
					rate: 19,
					promoId: 'promo456',
				} as Rate,
			];

			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			const sortedRates = result.current.sortRates( rates, 'cheapest' );

			expect( sortedRates[ 0 ].serviceId ).toBe( 'Express' ); // 12 (after promo)
			expect( sortedRates[ 1 ].serviceId ).toBe( 'Priority' ); // 14 (after promo)
			expect( sortedRates[ 2 ].serviceId ).toBe( 'Standard' ); // 15 (no promo)
		} );
	} );

	describe( 'updateRates', () => {
		it( 'should not fetch rates if previous request is still fetching', async () => {
			const { result } = renderHook( () =>
				useRatesState( {
					...defaultProps,
					getPackageForRequest: jest.fn().mockReturnValue( {
						length: '10',
						width: '10',
						height: '10',
						weight: '1',
					} ),
				} )
			);

			// Set isFetching to true
			await act( async () => {
				await result.current.fetchRates( {
					length: '10',
					width: '10',
					height: '10',
				} as Package );
			} );

			const initialFetchCount = (
				defaultProps.getPackageForRequest as jest.Mock
			 ).mock.calls.length;

			// Try to update rates while still fetching
			await act( async () => {
				await result.current.updateRates();
			} );

			expect(
				defaultProps.getPackageForRequest as jest.Mock
			).toHaveBeenCalledTimes( initialFetchCount );
		} );

		it( 'should not fetch rates if package data is incomplete', () => {
			const getPackageForRequestMock = jest.fn().mockReturnValue( {
				length: '', // Missing required field
				width: '10',
				height: '10',
				weight: '1',
			} );
			const { result } = renderHook( () =>
				useRatesState( {
					...defaultProps,
					getPackageForRequest: getPackageForRequestMock,
				} )
			);

			act( () => {
				result.current.updateRates();
			} );

			expect( getPackageForRequestMock ).toHaveBeenCalled();
			// Verify that fetchRates was not called due to incomplete data
			expect( result.current.isFetching ).toBe( false );
		} );

		it( 'should fetch rates with customs information when required', async () => {
			const mockPackage = {
				length: '10',
				width: '10',
				height: '10',
				id: 'test_package',
			};

			const { result } = renderHook( () =>
				useRatesState( {
					...defaultProps,
					getPackageForRequest: jest
						.fn()
						.mockReturnValue( mockPackage ),
				} )
			);

			// Mock successful rates fetch
			( select as jest.Mock ).mockReturnValue( {
				...wordpressDataSelect,
				getRatesForShipment: jest.fn().mockReturnValue( {
					usps: [ { serviceId: 'test_service', rate: 10 } ],
				} ),
			} );

			await act( async () => {
				await result.current.updateRates();
			} );

			expect(
				defaultProps.customs.maybeApplyCustomsToPackage
			).toHaveBeenCalled();
			expect( defaultProps.applyHazmatToPackage ).toHaveBeenCalled();
		} );

		it( 'should not fetch rates if totalWeight is 0', () => {
			const getPackageForRequestMock = jest.fn().mockReturnValue( {
				length: '10', // Missing required field
				width: '10',
				height: '10',
				weight: '1',
			} );
			const { result } = renderHook( () =>
				useRatesState( {
					...defaultProps,
					getPackageForRequest: getPackageForRequestMock,
					totalWeight: 0,
				} )
			);

			act( () => {
				result.current.updateRates();
			} );

			expect( getPackageForRequestMock ).not.toHaveBeenCalled();
			expect( result.current.isFetching ).toBe( false );
		} );
	} );

	describe( 'error handling', () => {
		it( 'should handle REST API errors appropriately', async () => {
			// Mock dispatch to simulate a REST API error
			jest.spyOn(
				require( '@wordpress/data' ), // eslint-disable-line @typescript-eslint/no-var-requires
				'dispatch'
			).mockReturnValue( {
				getRates: jest.fn().mockResolvedValue( {
					type: RATES_FETCH_FAILED,
					payload: {
						code: 'rest_invalid_param',
						data: {
							params: {
								'packages[0][weight]':
									'Package weight must be greater than 0',
							},
						},
					},
				} ),
			} );

			const { result } = renderHook( () =>
				useRatesState( {
					...defaultProps,
					getPackageForRequest: jest.fn().mockReturnValue( {
						length: '10',
						width: '10',
						height: '10',
						weight: '1',
					} ),
				} )
			);

			await act( async () => {
				await result.current.fetchRates( {
					length: '10',
					width: '10',
					height: '10',
				} as Package );
			} );

			expect( result.current.errors.endpoint ).toEqual( {
				rates: 'Package weight must be greater than 0.',
			} );
		} );

		it( 'should handle multiple error messages from endpoint', async () => {
			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			// Mock dispatch to simulate multiple endpoint errors
			jest.spyOn(
				require( '@wordpress/data' ), // eslint-disable-line @typescript-eslint/no-var-requires
				'dispatch'
			).mockReturnValue( {
				getRates: jest.fn().mockResolvedValue( {
					payload: {
						1: {
							default: {
								errors: [
									{ message: 'Error 1' },
									{ message: 'Error 2' },
								],
							},
						},
					},
				} ),
			} );

			await act( async () => {
				await result.current.fetchRates( {
					length: '10',
					width: '10',
					height: '10',
				} as Package );
			} );

			expect( result.current.errors.endpoint ).toEqual( {
				rates: [ 'Error 1', 'Error 2' ],
			} );
		} );
	} );

	describe( 'preselectRateBasedOnLastSelections', () => {
		it( 'should not preselect rate when use_last_service is disabled', () => {
			const mockRates = {
				usps: [
					{
						serviceId: 'last_used_service',
						carrierId: 'usps' as Carrier,
					},
				],
			};

			( select as jest.Mock ).mockReturnValue( {
				...wordpressDataSelect,
				getRatesForShipment: jest.fn().mockReturnValue( mockRates ),
			} );

			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			act( () => {
				result.current.preselectRateBasedOnLastSelections(
					defaultProps.currentShipmentId
				);
			} );

			expect( result.current.getSelectedRate() ).toBeUndefined();
		} );

		it( 'should preselect rate matching last carrier and service when available', () => {
			// Mock account settings with use_last_service enabled
			jest.requireMock( 'utils' ).getAccountSettings.mockReturnValue( {
				purchaseSettings: {
					use_last_service: true,
				},
				userMeta: {
					last_carrier_id: 'usps' as Carrier,
					last_service_id: 'test_service',
				},
			} );

			const mockRate = {
				serviceId: 'test_service',
				carrierId: 'usps' as Carrier,
				rate: 10,
			};

			const mockRates = {
				usps: [ mockRate ],
			};

			( select as jest.Mock ).mockReturnValue( {
				...wordpressDataSelect,
				getRatesForShipment: jest.fn().mockReturnValue( mockRates ),
			} );

			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			act( () => {
				const rates = result.current.preselectRateBasedOnLastSelections(
					defaultProps.currentShipmentId
				);
				expect( rates ).toBeDefined();
			} );
			expect( result.current.getSelectedRate()?.rate ).toEqual(
				mockRate
			);
		} );

		it( 'should reorder rates to put preselected rate first', () => {
			// Mock account settings with use_last_service enabled
			jest.requireMock( 'utils' ).getAccountSettings.mockReturnValue( {
				purchaseSettings: {
					use_last_service: true,
				},
				userMeta: {
					last_carrier_id: 'usps' as Carrier,
					last_service_id: 'preferred_service',
				},
			} );

			const mockPreferredRate = {
				serviceId: 'preferred_service',
				carrierId: 'usps' as Carrier,
				rate: 15,
			};

			const mockOtherRate = {
				serviceId: 'other_service',
				carrierId: 'usps' as Carrier,
				rate: 10,
			};

			const mockRates = {
				usps: [ mockOtherRate, mockPreferredRate ],
			};

			( select as jest.Mock ).mockReturnValue( {
				...wordpressDataSelect,
				getRatesForShipment: jest.fn().mockReturnValue( mockRates ),
			} );

			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			act( () => {
				const rates = result.current.preselectRateBasedOnLastSelections(
					defaultProps.currentShipmentId
				);
				expect( rates?.usps[ 0 ] ).toEqual( mockPreferredRate );
				expect( rates?.usps[ 1 ] ).toEqual( mockOtherRate );
			} );
		} );

		it( 'should not preselect rate when last used service is not available', () => {
			// Mock account settings with use_last_service enabled but unavailable service
			jest.requireMock( 'utils' ).getAccountSettings.mockReturnValue( {
				purchaseSettings: {
					use_last_service: true,
				},
				userMeta: {
					last_carrier_id: 'usps' as Carrier,
					last_service_id: 'unavailable_service',
				},
			} );

			const mockRates = {
				usps: [
					{
						serviceId: 'available_service',
						carrierId: 'usps' as Carrier,
					},
				],
			};

			( select as jest.Mock ).mockReturnValue( {
				...wordpressDataSelect,
				getRatesForShipment: jest.fn().mockReturnValue( mockRates ),
			} );

			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			act( () => {
				const rates = result.current.preselectRateBasedOnLastSelections(
					defaultProps.currentShipmentId
				);
				expect( rates ).toEqual( mockRates );
				expect( result.current.getSelectedRate() ).toBeUndefined();
			} );
		} );
	} );

	describe( 'removeSelectedRate', () => {
		it( 'should remove the selected rate for current shipment', () => {
			const mockRate = {
				rate: {
					serviceId: 'test_service',
					carrierId: 'usps' as Carrier,
					rate: 10,
				},
				parent: null,
			};

			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			// First select a rate
			act( () => {
				result.current.selectRate( mockRate.rate as Rate );
			} );

			// Verify rate was selected
			expect( result.current.getSelectedRate() ).toBeDefined();

			// Remove the selected rate
			act( () => {
				result.current.removeSelectedRate();
			} );

			// Verify rate was removed
			expect( result.current.getSelectedRate() ).toBeNull();
		} );

		it( 'should not modify state if no rate was selected', () => {
			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			const initialState = result.current.selectedRates;

			act( () => {
				result.current.removeSelectedRate();
			} );

			expect( result.current.selectedRates ).toEqual( initialState );
		} );
	} );

	describe( 'selectedRateOptions', () => {
		it( 'should initialize with empty rate options', () => {
			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			expect( result.current.selectedRateOptions ).toEqual( {
				[ defaultProps.currentShipmentId ]: {},
			} );
			expect( result.current.getSelectedRateOptions() ).toEqual( {} );
		} );

		it( 'should add a rate option when selectRateOption is called', () => {
			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			act( () => {
				result.current.selectRateOption( 'signature', 'yes', 5.0 );
			} );

			expect( result.current.getSelectedRateOptions() ).toEqual( {
				signature: {
					value: 'yes',
					surcharge: 5.0,
				},
			} );
		} );

		it( 'should remove a rate option when value is false or no', () => {
			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			// First add an option
			act( () => {
				result.current.selectRateOption( 'signature', 'yes', 5.0 );
			} );

			// Then remove it by setting value to false
			act( () => {
				result.current.selectRateOption( 'signature', false, 0 );
			} );

			expect( result.current.getSelectedRateOptions() ).toEqual( {} );

			// Add it again and remove with 'no'
			act( () => {
				result.current.selectRateOption( 'signature', 'yes', 5.0 );
				result.current.selectRateOption( 'signature', 'no', 0 );
			} );

			expect( result.current.getSelectedRateOptions() ).toEqual( {} );
		} );

		it( 'should maintain separate options for different shipments', () => {
			const { result } = renderHook( () =>
				useRatesState( {
					...defaultProps,
					currentShipmentId: '1',
				} )
			);

			act( () => {
				result.current.selectRateOption( 'signature', 'yes', 5.0 );
			} );

			// Verify options for shipment 1
			expect( result.current.selectedRateOptions[ '1' ] ).toEqual( {
				signature: {
					value: 'yes',
					surcharge: 5.0,
				},
			} );

			// Change shipment and add different option
			const { result: result2 } = renderHook( () =>
				useRatesState( {
					...defaultProps,
					currentShipmentId: '2',
				} )
			);

			act( () => {
				result2.current.selectRateOption( 'insurance', 'yes', 3.0 );
			} );

			// Verify options for shipment 2
			expect( result2.current.selectedRateOptions[ '2' ] ).toEqual( {
				insurance: {
					value: 'yes',
					surcharge: 3.0,
				},
			} );
		} );

		it( 'should update existing option when called with same option name', () => {
			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			act( () => {
				result.current.selectRateOption( 'signature', 'yes', 5.0 );
				result.current.selectRateOption( 'signature', 'yes', 7.0 );
			} );

			expect( result.current.getSelectedRateOptions() ).toEqual( {
				signature: {
					value: 'yes',
					surcharge: 7.0,
				},
			} );
		} );

		it( 'should reset rate options when selecting a new base rate', () => {
			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			// First add some rate options
			act( () => {
				result.current.selectRateOption( 'signature', 'yes', 5.0 );
				result.current.selectRateOption( 'insurance', 'yes', 3.0 );
			} );

			// Verify options were added
			expect( result.current.getSelectedRateOptions() ).toEqual( {
				signature: {
					value: 'yes',
					surcharge: 5.0,
				},
				insurance: {
					value: 'yes',
					surcharge: 3.0,
				},
			} );

			// Select a new base rate
			act( () => {
				result.current.selectRate( {
					serviceId: 'new_service',
					carrierId: 'usps' as Carrier,
					rate: 10,
				} as Rate );
			} );

			// Verify options were reset
			expect( result.current.getSelectedRateOptions() ).toEqual( {} );
		} );

		it( 'should not reset rate options when selecting a rate with same service ID', () => {
			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			// First select a rate and add options
			act( () => {
				result.current.selectRate( {
					serviceId: 'test_service',
					carrierId: 'usps' as Carrier,
					rate: 10,
				} as Rate );
				result.current.selectRateOption( 'signature', 'yes', 5.0 );
			} );

			// Select the same rate with different price
			act( () => {
				result.current.selectRate( {
					serviceId: 'test_service',
					carrierId: 'usps' as Carrier,
					rate: 15,
				} as Rate );
			} );

			// Verify options were preserved
			expect( result.current.getSelectedRateOptions() ).toEqual( {
				signature: {
					value: 'yes',
					surcharge: 5.0,
				},
			} );
		} );

		it( 'should not reset rate options when selecting a rate with parent', () => {
			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			// First add some rate options
			act( () => {
				result.current.selectRateOption( 'signature', 'yes', 5.0 );
			} );

			// Select a rate with parent
			act( () => {
				result.current.selectRate(
					{
						serviceId: 'child_service',
						carrierId: 'usps' as Carrier,
						rate: 15,
					} as Rate,
					{
						serviceId: 'parent_service',
						carrierId: 'usps' as Carrier,
						rate: 10,
					} as Rate
				);
			} );

			// Verify options were preserved
			expect( result.current.getSelectedRateOptions() ).toEqual( {
				signature: {
					value: 'yes',
					surcharge: 5.0,
				},
			} );
		} );

		it( 'should not maintain rate options when removing selected rate', () => {
			const { result } = renderHook( () =>
				useRatesState( defaultProps )
			);

			// Add rate options and select a rate
			act( () => {
				result.current.selectRate( {
					serviceId: 'test_service',
					carrierId: 'usps' as Carrier,
					rate: 10,
				} as Rate );
				result.current.selectRateOption( 'signature', 'yes', 5.0 );
			} );

			// Remove the selected rate
			act( () => {
				result.current.removeSelectedRate();
			} );

			// Verify options were preserved
			expect( result.current.getSelectedRateOptions() ).toEqual( {} );
		} );
	} );
} );
