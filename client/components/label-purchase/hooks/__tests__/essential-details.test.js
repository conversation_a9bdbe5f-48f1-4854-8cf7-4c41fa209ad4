import { renderHook, act } from '@testing-library/react';
import { useEssentialDetails } from '../essential-details';

describe( 'useEssentialDetails', () => {
	it( 'manages extra label purchase completion state', () => {
		const { result } = renderHook( () => useEssentialDetails() );

		expect( result.current.isExtraLabelPurchaseCompleted() ).toBe( false );

		act( () => {
			result.current.setExtraLabelPurchaseCompleted( true );
		} );

		expect( result.current.isExtraLabelPurchaseCompleted() ).toBe( true );
	} );
} );
