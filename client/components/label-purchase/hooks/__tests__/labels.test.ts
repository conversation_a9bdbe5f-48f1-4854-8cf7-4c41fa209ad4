import { renderHook, act } from '@testing-library/react';
import { LABEL_PURCHASE_STATUS, LABEL_RATE_OPTION } from 'data/constants';
const testLabel = {
	status: LABEL_PURCHASE_STATUS.PURCHASED,
	labelId: 123,
};

const setupMocks = () => {
	jest.mock( '@wordpress/api-fetch', () => {
		return {
			__esModule: true,
			default: jest.fn(),
		};
	} );

	jest.mock( 'data/label-purchase', () => ( {
		labelPurchaseStore: jest.fn(),
	} ) );
	jest.mock( 'data/address', () => ( {
		addressStore: jest.fn(),
	} ) );
	jest.mock( '../../label', () => ( {
		getPaperSizes: jest
			.fn()
			.mockReturnValue( [ { key: '4x6', name: '4x6' } ] ),
	} ) );

	jest.mock( 'utils', () => ( {
		__esModule: true,
		getConfig: jest.fn().mockReturnValue( {
			packagesSettings: {
				packages: {},
			},
			order: {
				shipping_address: {},
				line_items: [
					{ id: 1, name: 'Test Product' },
					{ id: 2, name: 'Test Product 2' },
				],
			},
		} ),
		getCurrentOrder: jest.fn().mockReturnValue( { id: 1 } ),
		getCurrentOrderItems: jest.fn().mockReturnValue( [] ),
		getPaymentSettings: jest.fn().mockReturnValue( {} ),
		getLastOrderCompleted: jest.fn().mockReturnValue( false ),
		shouldAutomaticallyOpenPrintDialog: jest.fn().mockReturnValue( false ),
		getPrintURL: jest.fn().mockReturnValue( 'testPath' ),
		getPDFFileName: jest.fn().mockReturnValue( 'mocked-file-name.pdf' ),
		printDocument: jest.fn(),
		maybeDecrementPromoRemaining: jest.fn(),
	} ) );

	jest.mock( '@wordpress/data', () => ( {
		dispatch: jest.fn().mockReturnValue( {
			fetchLabelStatus: jest.fn(),
			purchaseLabel: jest.fn(),
		} ),
		select: jest.fn().mockReturnValue( {
			getPurchasedLabel: jest.fn().mockReturnValue( testLabel ),
			getPurchasedLabels: jest.fn().mockReturnValue( [ testLabel ] ),
			getLabelOrigins: jest.fn().mockReturnValue( [] ),
			getLabelDestinations: jest.fn().mockReturnValue( [] ),
			getStoreOrigin: jest.fn().mockReturnValue( { country: 'CA' } ),
		} ),
	} ) );
};

setupMocks();

import { useLabelsState } from '../labels';
import { printDocument, shouldAutomaticallyOpenPrintDialog } from 'utils';
import apiFetch from '@wordpress/api-fetch';
import { RateWithParent, ShipmentItem } from 'types';

describe( 'useLabelsState', () => {
	beforeEach( () => {
		jest.restoreAllMocks();
		setupMocks();
	} );

	it( 'should initialize state correctly', () => {
		const { result } = renderHook( () =>
			useLabelsState( {
				currentShipmentId: '1',
				getPackageForRequest: jest.fn(),
				getShipmentItems: jest.fn(),
				totalWeight: 10,
				getShipmentHazmat: jest.fn(),
				updateRates: jest.fn(),
				getShipmentOrigin: jest.fn(),
				getSelectionItems: jest.fn(),
				customs: {
					getCustomsState: jest.fn(),
					setCustomsState: jest.fn(),
					maybeApplyCustomsToPackage: jest.fn(),
					hasErrors: jest.fn(),
					setErrors: jest.fn(),
					isCustomsNeeded: jest.fn(),
					isHSTariffNumberRequired: jest.fn(),
					updateCustomsItems: jest.fn(),
				},
				shipments: {},
				applyHazmatToPackage: jest.fn( ( v ) => v ),
				getSelectedRateOptions: jest.fn(),
				getCurrentShipmentDate: jest.fn().mockReturnValue( {
					shippingDate: new Date( '2025-02-26' ),
					estimatedDeliveryDate: new Date( '2025-02-30' ),
				} ),
			} )
		);

		expect( result.current.isPurchasing ).toBe( false );
		expect( result.current.isUpdatingStatus ).toBe( false );
		expect( result.current.isPrinting ).toBe( false );
		expect( result.current.isRefunding ).toBe( false );
	} );

	it( 'should handle purchase label errors correctly', async () => {
		const labelPurchaseError = new Error( 'This is a test error' );
		const mockPurchaseLabel = jest
			.fn()
			.mockRejectedValue( labelPurchaseError );

		// eslint-disable-next-line @typescript-eslint/no-var-requires
		jest.spyOn( require( '@wordpress/data' ), 'dispatch' ).mockReturnValue(
			{
				purchaseLabel: mockPurchaseLabel,
			}
		);

		const rate = {
			rate: {
				serviceId: '1',
				carrierId: '1',
				shipmentId: '1',
				title: 'test',
			},
			parent: null,
		};
		const { result } = renderHook( () =>
			useLabelsState( {
				currentShipmentId: '1',
				getPackageForRequest: jest.fn().mockReturnValue( {
					id: 1,
					type: 'envelope',
					length: 1,
					width: 1,
					height: 1,
				} ),
				getShipmentItems: jest
					.fn()
					.mockReturnValue( [ { product_id: 1 } ] ),
				totalWeight: 10,
				getShipmentHazmat: jest.fn(),
				updateRates: jest.fn(),
				getShipmentOrigin: jest.fn(),
				getSelectionItems: jest.fn(),
				customs: {
					getCustomsState: jest.fn(),
					setCustomsState: jest.fn(),
					maybeApplyCustomsToPackage: jest.fn(),
					hasErrors: jest.fn(),
					setErrors: jest.fn(),
					isCustomsNeeded: jest.fn(),
					isHSTariffNumberRequired: jest.fn(),
					updateCustomsItems: jest.fn(),
				},
				shipments: {},
				applyHazmatToPackage: jest.fn( ( v ) => v ),
				getSelectedRateOptions: jest.fn(),
				getCurrentShipmentDate: jest.fn().mockReturnValue( {
					shippingDate: new Date( '2025-02-26' ),
					estimatedDeliveryDate: new Date( '2025-02-30' ),
				} ),
			} )
		);

		await act( async () => {
			try {
				await result.current.requestLabelPurchase(
					1,
					rate as RateWithParent
				);
			} catch ( e ) {
				expect( e ).toEqual( {
					actions: [],
					cause: 'purchase_error',
					message: [
						'Error purchasing label.',
						'This is a test error',
					],
				} );
			}
		} );

		expect( result.current.isPurchasing ).toBe( false );
	} );

	it( 'should update the label status correctly', async () => {
		const mockFetchLabelStatus = jest.fn().mockResolvedValue( {} );
		const mockGetPurchasedLabel = jest
			.fn()
			.mockReturnValue( { status: LABEL_PURCHASE_STATUS.PURCHASED } );

		// eslint-disable-next-line @typescript-eslint/no-var-requires
		jest.spyOn( require( '@wordpress/data' ), 'dispatch' ).mockReturnValue(
			{
				fetchLabelStatus: mockFetchLabelStatus,
			}
		);

		// eslint-disable-next-line @typescript-eslint/no-var-requires
		jest.spyOn( require( '@wordpress/data' ), 'select' ).mockReturnValue( {
			getPurchasedLabel: mockGetPurchasedLabel,
			getPurchasedLabels: jest.fn().mockReturnValue( [ testLabel ] ),
			getLabelOrigins: jest.fn().mockReturnValue( [] ),
			getLabelDestinations: jest.fn().mockReturnValue( [] ),
			getStoreOrigin: jest.fn().mockReturnValue( { country: 'CA' } ),
		} );

		const { result } = renderHook( () =>
			useLabelsState( {
				currentShipmentId: '1',
				getPackageForRequest: jest.fn().mockReturnValue( {
					id: 1,
					type: 'envelope',
					length: 1,
					width: 1,
					height: 1,
				} ),
				getShipmentItems: jest
					.fn()
					.mockReturnValue( [ { product_id: 1 } ] ),
				totalWeight: 10,
				getShipmentHazmat: jest.fn(),
				updateRates: jest.fn(),
				getShipmentOrigin: jest.fn(),
				getSelectionItems: jest.fn(),
				customs: {
					getCustomsState: jest.fn(),
					setCustomsState: jest.fn(),
					maybeApplyCustomsToPackage: jest.fn(),
					hasErrors: jest.fn(),
					setErrors: jest.fn(),
					isCustomsNeeded: jest.fn(),
					isHSTariffNumberRequired: jest.fn(),
					updateCustomsItems: jest.fn(),
				},
				shipments: {},
				applyHazmatToPackage: jest.fn( ( v ) => v ),
				getSelectedRateOptions: jest.fn(),
				getCurrentShipmentDate: jest.fn().mockReturnValue( {
					shippingDate: new Date( '2025-02-26' ),
					estimatedDeliveryDate: new Date( '2025-02-30' ),
				} ),
			} )
		);

		await act( async () => {
			await result.current.updatePurchaseStatus( 123 );
		} );

		expect( mockFetchLabelStatus ).toHaveBeenCalled();
		expect( mockGetPurchasedLabel ).toHaveBeenCalled();
	} );

	it( "automatically prints a label once label's status is changed to purchased", async () => {
		const purchaseLabelMock = jest.fn().mockResolvedValue( {
			status: LABEL_PURCHASE_STATUS.PURCHASE_IN_PROGRESS,
		} );

		const mockFetchLabelStatus = jest
			.fn()
			.mockReturnValueOnce( {
				status: LABEL_PURCHASE_STATUS.PURCHASE_IN_PROGRESS,
			} )
			.mockReturnValue( { status: LABEL_PURCHASE_STATUS.PURCHASED } );

		const mockGetPurchasedLabel = jest
			.fn()
			.mockReturnValueOnce( {
				status: LABEL_PURCHASE_STATUS.PURCHASE_IN_PROGRESS,
			} )
			.mockReturnValue( { status: LABEL_PURCHASE_STATUS.PURCHASED } );

		// eslint-disable-next-line @typescript-eslint/no-var-requires
		jest.spyOn( require( '@wordpress/data' ), 'dispatch' ).mockReturnValue(
			{
				purchaseLabel: purchaseLabelMock,
				fetchLabelStatus: mockFetchLabelStatus,
			}
		);

		// eslint-disable-next-line @typescript-eslint/no-var-requires
		jest.spyOn( require( '@wordpress/data' ), 'select' ).mockReturnValue( {
			getPurchasedLabel: mockGetPurchasedLabel,
			getPurchasedLabels: jest.fn().mockReturnValue( [
				{
					status: LABEL_PURCHASE_STATUS.PURCHASE_IN_PROGRESS,
				},
			] ),
			getLabelOrigins: jest.fn().mockReturnValue( [] ),
			getLabelDestinations: jest.fn().mockReturnValue( [] ),
			getStoreOrigin: jest.fn().mockReturnValue( { country: 'CA' } ),
		} );

		( apiFetch as unknown as jest.Mock ).mockImplementation(
			( { path } ) => {
				if ( path === 'testPath' ) {
					return Promise.resolve( {
						success: true,
						fileContents: 'mocked-pdf-content',
						fileName: 'mocked-file-name.pdf',
					} );
				}
				// For other paths, you can either throw an error or return a default value
				return Promise.reject(
					new Error( 'Unexpected path in apiFetch' )
				);
			}
		);

		const rateWithParent = {
			rate: {
				serviceId: '1',
				carrierId: '1',
				shipmentId: '1',
				title: 'test',
			},
			parent: null,
		};
		const { result } = renderHook( () =>
			useLabelsState( {
				currentShipmentId: '1',
				getPackageForRequest: jest.fn().mockReturnValue( {
					id: 1,
					type: 'envelope',
					length: 1,
					width: 1,
					height: 1,
				} ),
				getShipmentItems: jest
					.fn()
					.mockReturnValue( [ { product_id: 1 } ] ),
				totalWeight: 10,
				getShipmentHazmat: jest.fn(),
				updateRates: jest.fn(),
				getShipmentOrigin: jest.fn(),
				getSelectionItems: jest.fn(),
				customs: {
					getCustomsState: jest.fn(),
					setCustomsState: jest.fn(),
					maybeApplyCustomsToPackage: jest.fn(),
					hasErrors: jest.fn(),
					setErrors: jest.fn(),
					isCustomsNeeded: jest.fn(),
					isHSTariffNumberRequired: jest.fn(),
					updateCustomsItems: jest.fn(),
				},
				shipments: {},
				applyHazmatToPackage: jest.fn( ( d ) => d ),
				getSelectedRateOptions: jest.fn(),
				getCurrentShipmentDate: jest.fn().mockReturnValue( {
					shippingDate: new Date( '2025-02-26' ),
					estimatedDeliveryDate: new Date( '2025-02-30' ),
				} ),
			} )
		);

		( shouldAutomaticallyOpenPrintDialog as jest.Mock ).mockReturnValue(
			true
		);

		await act( () =>
			result.current.requestLabelPurchase(
				1,
				rateWithParent as RateWithParent
			)
		);
		expect( printDocument ).toHaveBeenCalledTimes( 1 );
	} );

	it( 'should set is_letter to true if the package is an envelope when attempting to purchase a label', async () => {
		const mockPurchaseLabel = jest.fn().mockResolvedValue( {} );

		// eslint-disable-next-line @typescript-eslint/no-var-requires
		jest.spyOn( require( '@wordpress/data' ), 'dispatch' ).mockReturnValue(
			{
				purchaseLabel: mockPurchaseLabel,
			}
		);

		// Mock select to return needed values
		// eslint-disable-next-line @typescript-eslint/no-var-requires
		jest.spyOn( require( '@wordpress/data' ), 'select' ).mockReturnValue( {
			getPurchasedLabel: jest.fn(),
			getPurchasedLabels: jest.fn().mockReturnValue( [] ),
			getLabelOrigins: jest.fn().mockReturnValue( [] ),
			getLabelDestinations: jest.fn().mockReturnValue( [] ),
			getStoreOrigin: jest.fn().mockReturnValue( { country: 'CA' } ),
		} );

		const mockCustoms = {
			getCustomsState: jest.fn(),
			setCustomsState: jest.fn(),
			maybeApplyCustomsToPackage: jest
				.fn()
				.mockImplementation( ( pkg ) => pkg ),
			hasErrors: jest.fn(),
			setErrors: jest.fn(),
			isCustomsNeeded: jest.fn(),
			isHSTariffNumberRequired: jest.fn(),
			updateCustomsItems: jest.fn(),
		};

		const rateWithParent = {
			rate: {
				serviceId: '1',
				carrierId: '1',
				shipmentId: '1',
				title: 'test',
				rate: 9,
			},
			parent: null,
		};
		const { result } = renderHook( () =>
			useLabelsState( {
				currentShipmentId: '1',
				getPackageForRequest: jest.fn().mockReturnValue( {
					id: 1,
					type: 'envelope',
					length: 1,
					width: 1,
					height: 1,
					isUserDefined: true,
				} ),
				getShipmentItems: jest
					.fn()
					.mockReturnValue( [ { product_id: 1 } ] ),
				getSelectionItems: jest.fn(),
				totalWeight: 10,
				getShipmentHazmat: jest.fn(),
				updateRates: jest.fn(),
				getShipmentOrigin: jest.fn(),
				customs: mockCustoms,
				shipments: {},
				applyHazmatToPackage: jest.fn( ( v ) => v ),
				getSelectedRateOptions: jest.fn( () => ( {
					[ LABEL_RATE_OPTION.SIGNATURE ]: {
						value: 'yes',
						surcharge: 10,
					},
				} ) ),
				getCurrentShipmentDate: jest.fn().mockReturnValue( {
					shippingDate: new Date( '2025-02-26' ),
					estimatedDeliveryDate: new Date( '2025-02-30' ),
				} ),
			} )
		);

		await act( () =>
			result.current.requestLabelPurchase(
				1,
				rateWithParent as RateWithParent
			)
		);

		expect( mockPurchaseLabel ).toHaveBeenCalledWith(
			1,
			[
				expect.objectContaining( {
					is_letter: true,
				} ),
			],
			'1',
			rateWithParent,
			expect.objectContaining( {
				[ LABEL_RATE_OPTION.SIGNATURE ]: {
					value: 'yes',
					surcharge: 10,
				},
			} ),
			expect.any( Object ),
			undefined,
			expect.any( Object ),
			expect.any( Object ),
			{ label_date: '2025-02-26T00:00:00.000Z' }
		);
	} );

	it( 'calculates hasMissingPurchase correctly', async () => {
		const mockFetchLabelStatus = jest.fn().mockResolvedValue( {} );
		const mockGetPurchasedLabel = jest.fn().mockReturnValue( null );

		// eslint-disable-next-line @typescript-eslint/no-var-requires
		jest.spyOn( require( '@wordpress/data' ), 'dispatch' ).mockReturnValue(
			{
				fetchLabelStatus: mockFetchLabelStatus,
			}
		);

		// eslint-disable-next-line @typescript-eslint/no-var-requires
		jest.spyOn( require( '@wordpress/data' ), 'select' ).mockReturnValue( {
			getPurchasedLabel: mockGetPurchasedLabel,
			getPurchasedLabels: jest.fn().mockReturnValue( [ testLabel ] ),
			getLabelOrigins: jest.fn().mockReturnValue( [] ),
			getLabelDestinations: jest.fn().mockReturnValue( [] ),
			getStoreOrigin: jest.fn().mockReturnValue( { country: 'CA' } ),
		} );

		const utils = jest.requireMock( 'utils' );
		utils.getCurrentOrderItems.mockReturnValue( [ { id: 1 } ] );

		const { result } = renderHook( () =>
			useLabelsState( {
				currentShipmentId: '0',
				getPackageForRequest: jest.fn(),
				getShipmentItems: jest.fn(),
				getSelectionItems: jest.fn(),
				totalWeight: 1,
				getShipmentHazmat: jest.fn(),
				updateRates: jest.fn(),
				getShipmentOrigin: jest.fn(),
				customs: {
					getCustomsState: jest.fn(),
					setCustomsState: jest.fn(),
					maybeApplyCustomsToPackage: jest.fn(),
					hasErrors: jest.fn(),
					setErrors: jest.fn(),
					isCustomsNeeded: jest.fn(),
					isHSTariffNumberRequired: jest.fn(),
					updateCustomsItems: jest.fn(),
				},
				shipments: { '0': [ { id: 1 } as ShipmentItem ] },
				applyHazmatToPackage: jest.fn(),
				getSelectedRateOptions: jest.fn(),
				getCurrentShipmentDate: jest.fn().mockReturnValue( {
					shippingDate: new Date( '2025-02-26' ),
					estimatedDeliveryDate: new Date( '2025-02-30' ),
				} ),
			} )
		);

		expect( result.current.hasMissingPurchase() ).toBe( true );
	} );

	it( 'calculates isCurrentTabPurchasingExtraLabel correctly', () => {
		const mockFetchLabelStatus = jest.fn().mockResolvedValue( {} );
		const mockGetPurchasedLabel = jest.fn().mockImplementation( ( id ) => {
			if ( id === '0' ) {
				return testLabel;
			} else if ( id === '1' ) {
				return null;
			}
		} );

		// eslint-disable-next-line @typescript-eslint/no-var-requires
		jest.spyOn( require( '@wordpress/data' ), 'dispatch' ).mockReturnValue(
			{
				fetchLabelStatus: mockFetchLabelStatus,
			}
		);

		// eslint-disable-next-line @typescript-eslint/no-var-requires
		jest.spyOn( require( '@wordpress/data' ), 'select' ).mockReturnValue( {
			getPurchasedLabel: mockGetPurchasedLabel,
			getPurchasedLabels: jest.fn().mockReturnValue( [ testLabel ] ),
			getLabelOrigins: jest.fn().mockReturnValue( [] ),
			getLabelDestinations: jest.fn().mockReturnValue( [] ),
			getStoreOrigin: jest.fn().mockReturnValue( { country: 'CA' } ),
		} );

		const utils = jest.requireMock( 'utils' );
		utils.getCurrentOrderItems.mockReturnValue( [
			{ id: 1, quantity: 1 },
			{ id: 2, quantity: 1 },
		] );

		const { result } = renderHook( () =>
			useLabelsState( {
				currentShipmentId: '1',
				getPackageForRequest: jest.fn(),
				getShipmentItems: jest.fn(),
				getSelectionItems: jest.fn(),
				totalWeight: 1,
				getShipmentHazmat: jest.fn(),
				updateRates: jest.fn(),
				getShipmentOrigin: jest.fn(),
				customs: {
					getCustomsState: jest.fn(),
					setCustomsState: jest.fn(),
					maybeApplyCustomsToPackage: jest.fn(),
					hasErrors: jest.fn(),
					setErrors: jest.fn(),
					isCustomsNeeded: jest.fn(),
					isHSTariffNumberRequired: jest.fn(),
					updateCustomsItems: jest.fn(),
				},
				shipments: {
					'0': [
						{ id: 1 } as ShipmentItem,
						{ id: 2 } as ShipmentItem,
					],
					'1': [ { id: 2 } as ShipmentItem ],
				},
				applyHazmatToPackage: jest.fn(),
				getSelectedRateOptions: jest.fn(),
				getCurrentShipmentDate: jest.fn().mockReturnValue( {
					shippingDate: new Date( '2025-02-26' ),
					estimatedDeliveryDate: new Date( '2025-02-30' ),
				} ),
			} )
		);
		act( () => {
			result.current.isPurchasing = false;
			result.current.isUpdatingStatus = false;
		} );

		expect( result.current.isCurrentTabPurchasingExtraLabel() ).toBe(
			true
		);
	} );

	it( 'should set showRefundedNotice to true after refunding a label', async () => {
		const mockRefundLabel = jest.fn().mockResolvedValue( {} );

		// eslint-disable-next-line @typescript-eslint/no-var-requires
		jest.spyOn( require( '@wordpress/data' ), 'dispatch' ).mockReturnValue(
			{
				refundLabel: mockRefundLabel,
			}
		);

		// eslint-disable-next-line @typescript-eslint/no-var-requires
		jest.spyOn( require( '@wordpress/data' ), 'select' ).mockReturnValue( {
			getPurchasedLabels: jest.fn().mockReturnValue( [ testLabel ] ),
			getPurchasedLabel: jest.fn().mockReturnValue( testLabel ),
		} );

		const { result } = renderHook( () =>
			useLabelsState( {
				currentShipmentId: '1',
				getPackageForRequest: jest.fn(),
				getShipmentItems: jest.fn(),
				getSelectionItems: jest.fn(),
				totalWeight: 1,
				getShipmentHazmat: jest.fn(),
				updateRates: jest.fn(),
				getShipmentOrigin: jest.fn(),
				customs: {
					getCustomsState: jest.fn(),
					setCustomsState: jest.fn(),
					maybeApplyCustomsToPackage: jest.fn(),
					hasErrors: jest.fn(),
					setErrors: jest.fn(),
					isCustomsNeeded: jest.fn(),
					isHSTariffNumberRequired: jest.fn(),
					updateCustomsItems: jest.fn(),
				},
				shipments: {},
				applyHazmatToPackage: jest.fn(),
				getSelectedRateOptions: jest.fn(),
				getCurrentShipmentDate: jest.fn().mockReturnValue( {
					shippingDate: new Date( '2025-02-26' ),
					estimatedDeliveryDate: new Date( '2025-02-30' ),
				} ),
			} )
		);

		expect( result.current.showRefundedNotice ).toBe( false );

		await act( async () => {
			await result.current.refundLabel();
		} );

		expect( result.current.showRefundedNotice ).toBe( true );
	} );
} );
