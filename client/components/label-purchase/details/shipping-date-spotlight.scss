.shipping-date-spotlight {

    .tour-kit-spotlight {
        outline: none;// The outline that comes from the TourKit doesn't allow border radius to work, so it's replaced with a box shadow
        box-shadow: 0 0 0 99999px rgba(0,0,0,.5);
        border-radius: 4px;
    }

    .components-card {
        border-radius: 0;
    }

    .components-button.has-icon {
        padding: 0;
        min-width: 0;
    }

    .woocommerce-tour-kit-step__heading {
        color: var(--<PERSON><PERSON><PERSON>-Gray-900, #1E1E1E);
        font-weight: 600;
        line-height: 24px;
        margin-top: -16px; // It should get aligned with the close button
    }

    .woocommerce-tour-kit-step {
        padding-bottom: 16px;
    }

    .components-card-footer {
        display: none;
    }
}