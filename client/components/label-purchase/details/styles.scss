@import "ship-from";
@import "shipping-date-spotlight";

// This popover is appended to the body, so we need to style it here
.shipping-date-popover {

	.components-popover__content {
		padding: 16px;
	}
}



.label-purchase-modal__content>div:nth-child(2) {
	flex: 2;

	@media only screen and (min-width: 640px) {
		position: sticky;
		top: var(--wcs-shipment-details-top);
	}

	h3 {
		font-size: 16px;
		font-style: normal;
		font-weight: 600;
		line-height: 24px;
		margin-bottom: 24px;
	}

	.components-base-control {
		margin-bottom: 16px;

		&:last-child {
			margin-bottom: 0;
		}

		&:nth-last-child(2) {
			// Subtotal
			margin-bottom: 8px;
		}

		select {
			border: none;
			box-sizing: border-box;
			color: var(--gutenberg-blue, #007cba);
			flex: 1;
			font-size: 13px;
			font-style: normal;
			font-weight: 400;
			line-height: 13px;
			margin-left: 0;
			max-width: 100%;
			outline: none;
			overflow: hidden;
			padding-left: 8px;
			text-overflow: ellipsis;
			white-space: nowrap;
			width: 100%;

			&:focus {
				outline: none;
			}

			&:disabled {
				background-image: none;
				color: #1e1e1e;
				cursor: default;
				padding-right: 0;
			}
		}
	}

	.shipment-details {
		background: var(--gutenberg-white, #fff);
		border: 1px solid var(--gutenberg-gray-300, #ddd);
		border-radius: var(--grid-unit-05, 4px);
		box-sizing: border-box;
		padding: 32px;

		.components-base-control {
			overflow: hidden;
		}
	}


	.components-base-control__field {
		display: flex;
	}

	label.components-base-control__label {
		margin: 0;
		min-width: 160px;
		color: var(--Gutenberg-Gray-900, rgba(30, 30, 30, 0.7));

		text-transform: none;
		width: 160px;

		strong {
			font-weight: 600;
		}

		+* {
			overflow: hidden;
		}
	}


	.rate-discount {
		align-items: center;
		border: none;
		border-radius: 2px;
		box-sizing: border-box;
		max-height: 38px;
		text-align: center;

		.components-notice__content {
			align-items: center;
			display: flex;
			justify-content: center;
			margin: 0;
		}

		svg {
			cursor: pointer;
			margin-left: 4px;
		}

		&.is-success svg {
			fill: #005c12;
		}

		&:not(.is-success) {
			background: var(--gutenberg-gray-600, #f0f0f0);
		}
	}

	span.verification.not-verified {

		svg {
			margin-left: 1px;
		}
	}

	.components-text {
		line-height: 22px;
	}

	.components-base-control__field,
	.components-base-control__label {
		color: var(--gutenberg-gray-900, #1e1e1e);
		font-size: 13px;
		font-style: normal;
		font-weight: 400;
		line-height: 16px;
	}


	.components-base-control.shipping-date-control {
		width: 100%;
		--data-info-popover-width: 256px;

		&.wp-tour-kit-spotlit {
			padding: 0 16px;
			width: calc(100% + 32px);
			margin-left: -16px;
		}

		.components-base-control__label {
			display: flex;
			flex-direction: row;
			line-height: 20px; // Height of the info icon
		}

		.components-button {
			text-decoration: none;
			border: none;

			&:focus {
				box-shadow: none;
				outline: none;
				border: none;
			}

			color: var(--Gutenberg-Gray-900, #1E1E1E);
			line-height: 20px;
			margin: 0;
			padding: 0;
			display: flex;
			align-items: center;
			flex-direction: row-reverse;
			width: 100%;
			justify-content: flex-end; // it's reversed because the icon is on the left

			&:disabled {
				background-color: transparent;
			}

			&.has-icon {

				.dashicon {
					margin-left: auto; // push it to the right
					color: #3858E9;
				}
			}
		}

		.shipping-date-info-popover {
			// Setting the popover almost in the center of the label
			margin-left: calc(var(--data-info-popover-width) / -2 + 60px); // 60px is the approximate width of the label

			.components-popover__content {
				width: 256px;

				color: var(--gutenberg-gray-900, #1e1e1e);
				line-height: 20px;
				padding: 12px;
				max-width: var(--data-info-popover-width);
			}
		}
	}

	// Setting the mark as complete row as flex ruins it's checkbox styling
	.purchase-label-mark-order-complete {

		.components-base-control__field {
			display: inherit;
		}
	}

	.purchase-label__ship-to {
		position: relative;

		.components-text {
			flex-direction: column;
			padding-right: 24px;
		}

		.ship-to-edit-icon {
			padding: 0;
			position: absolute;
			right: -8px;
			top: -8px;

			&:disabled {
				display: none;
			}

			&:focus {
				box-shadow: none;
				outline: none;
				border: none;
			}
		}
	}

	@media only screen and (max-width: 960px) {
		flex-direction: column;

		.components-base-control__label {
			min-width: 80px;
			width: 80px;
		}
	}

	.purchase-label-buttons {
		margin-top: 24px;
		position: sticky;
		top: 24px;

		button {
			display: flex;
			flex: 1;
			flex-direction: row-reverse;
			justify-content: center;
			text-align: center;
		}

		button::first-letter {
			text-transform: uppercase;
		}
	}

	.components-notice {
		margin: 0;

		.components-notice__content {
			width: 100%;

			button {
				margin: 0;
			}
		}
	}

	.shipment-details__costs {

		.components-base-control {
			margin-bottom: 8px;
		}

		.components-base-control__field {
			align-items: center;
		}

		label {
			display: flex;
			gap: 8px;
			flex-direction: column;
		}

		&.has-rates label {
			flex: 1;
		}

		.cost-placeholder {
			align-items: center;
			background: var(--gutenberg-gray-200, #e0e0e0);
			border-radius: 20px;
			display: flex;
			height: 8px;
			width: 70px;
		}

		.subtotal-extra-bit {
			border-bottom: 1px solid var(--gutenberg-gray-300, #ddd);
			border-left: 1px solid var(--gutenberg-gray-300, #ddd);
			display: block;
			height: 8px;
			margin-left: 8px;
			margin-top: 4px;
			padding: 0;
			width: 8px;
		}

		[for="promo-discount"] {
			color: var(--wp-green-green-70, #005c12);

			+.components-badge {
				padding-inline-start: 8px;

				svg {
					display: none;
				}
			}
		}
	}
}
