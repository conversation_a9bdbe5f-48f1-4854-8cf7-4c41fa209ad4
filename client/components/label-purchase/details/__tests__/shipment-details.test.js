import { render, waitFor, screen, act } from '@testing-library/react';
import { registerLabelPurchaseStore } from 'data/label-purchase';
import { ShipmentDetails } from '../shipment-details';
import { LabelPurchaseContext } from 'context/label-purchase';
import CurrencyFactory from '@woocommerce/currency';
import { registerAddressStore } from 'data/address';
import { address, destinationAddress } from 'test-helpers/test-utils';
import { setupDateMock, teardownDateMock } from 'test-helpers/test-dates';

jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
	return mockUtils();
} );

registerAddressStore( true );
registerLabelPurchaseStore();

// Mock useSelect for ShippingDateSpotlight's isDismissed state
jest.mock( '@wordpress/data', () => {
	const originalModule = jest.requireActual( '@wordpress/data' );
	return {
		...originalModule,
		useSelect: jest.fn().mockImplementation( ( selector, deps ) => {
			// Check if this is the isDismissed call from ShippingDateSpotlight
			if ( typeof selector === 'function' ) {
				// Create a mock select function that returns an object with a get method
				const mockSelect = ( storeSelector ) => {
					if ( storeSelector.name === 'core/preferences' ) {
						return {
							get: ( scope, key ) => {
								// Return false specifically for the shipping date spotlight preference
								if (
									scope === 'woocommerce-shipping' &&
									key === 'shipping-date-spotlight-dismissed'
								) {
									return false;
								}

								// For other preferences, return undefined or a default value
								return "Can't find it check here";
							},
						};
					}
					// For other stores, use the original select implementation if possible
					return originalModule.select
						? originalModule.select( storeSelector, deps )
						: {};
				};

				// Call the selector with our mock select function
				return selector( mockSelect );
			}

			// For all other useSelect calls, use the original implementation
			return originalModule.useSelect( selector, deps );
		} ),
	};
} );

describe( 'ShipmentDetails', () => {
	beforeAll( () => {
		setupDateMock();
	} );

	afterAll( () => {
		teardownDateMock();
	} );

	it( 'should render the ShipmentDetails component', async () => {
		const props = {
			order: {
				total_shipping: '50.00',
				subtotal_shipping: '10.00',
				total: '100.00',
				line_items: [ {}, {} ],
				total_line_items_quantity: 4,
			},
			shippingType: 'Flat Rate',
			destinationAddress,
		};

		const storeCurrency = CurrencyFactory( 'USD' );
		const rates = {
			getSelectedRate: () => ( {
				rate: {
					rate: 50,
					title: 'UPS Ground',
					type: 'adultSignatureRequired',
				},
				parent: {
					rate: 10,
					title: 'UPS Ground',
				},
			} ),
			getSelectedRateOptions: jest.fn(),
			selectRateOption: jest.fn(),
			getCurrentRateOptions: jest.fn().mockReturnValue( [] ),
		};

		const getCurrentShipmentLabel = jest.fn( () => ( {
			isLegacy: false,
			status: 'PURCHASED',
		} ) );

		const labels = {
			hasPurchasedLabel: () => false,
			getCurrentShipmentLabel,
		};
		const shipment = {
			getShipmentOrigin: () => address,
			getShipmentPurchaseOrigin: () => ( {
				...address,
				address: '123 Main St - snapshot origin',
			} ),
			getShipmentDestination: () => destinationAddress,
			getCurrentShipmentDate: jest.fn().mockReturnValue( {
				shippingDate: null,
				estimatedDeliveryDate: null,
			} ),
		};

		const { asFragment } = render(
			<LabelPurchaseContext.Provider
				value={ { storeCurrency, rates, labels, shipment } }
			>
				<ShipmentDetails { ...props } />
			</LabelPurchaseContext.Provider>
		);
		// Wait for the component to settle after any potential async updates, this handles the auto address verify functionality.
		// eslint-disable-next-line testing-library/no-wait-for-empty-callback
		await waitFor( () => {
			// Wait for some DOM change or state to indicate updates are complete, if necessary
		} );

		expect( asFragment() ).toMatchSnapshot();

		const { asFragment: asValidatingFragment } = render(
			<LabelPurchaseContext.Provider
				value={ { storeCurrency, rates, labels, shipment } }
			>
				<ShipmentDetails { ...props } />
			</LabelPurchaseContext.Provider>
		);
		// We do not wait as we want to test the initial render state where it updates the message to show the address is being verified.
		expect( asValidatingFragment() ).toMatchSnapshot(
			'auto address verification'
		);

		const ratesWithDiscount = {
			getSelectedRate: () => ( {
				rate: {
					rate: 50,
					retailRate: 53.2,
					title: 'UPS Ground',
					type: 'signatureRequired',
				},
				parent: {
					rate: 10,
					title: 'UPS Ground',
				},
			} ),
			getSelectedRateOptions: jest.fn(),
			selectRateOption: jest.fn(),
		};

		const { asFragment: asFrag } = render(
			<LabelPurchaseContext.Provider
				value={ {
					storeCurrency,
					rates: ratesWithDiscount,
					labels,
					shipment,
				} }
			>
				<ShipmentDetails { ...props } />
			</LabelPurchaseContext.Provider>
		);
		// Wait for the component to settle after any potential async updates
		// eslint-disable-next-line testing-library/no-wait-for-empty-callback
		await waitFor( () => {
			// Wait for some DOM change or state to indicate updates are complete, if necessary
		} );
		expect( asFrag() ).toMatchSnapshot( 'with discount' );

		getCurrentShipmentLabel.mockReturnValue( {
			isLegacy: true,
			status: 'PURCHASED',
		} );
		labels.hasPurchasedLabel = () => true;

		const { asFragment: asLegacyFrag } = render(
			<LabelPurchaseContext.Provider
				value={ {
					storeCurrency,
					rates: ratesWithDiscount,
					labels,
					shipment,
				} }
			>
				<ShipmentDetails { ...props } />
			</LabelPurchaseContext.Provider>
		);
		// Wait for the component to settle after any potential async updates
		// eslint-disable-next-line testing-library/no-wait-for-empty-callback
		await waitFor( () => {
			// Wait for some DOM change or state to indicate updates are complete, if necessary
		} );
		expect( asLegacyFrag() ).toMatchSnapshot(
			'with legacy shipping label'
		);
	} );

	it( 'ship from address for a successful purchase is read from the snapshot', async () => {
		const props = {
			order: {
				total_shipping: '50.00',
				subtotal_shipping: '10.00',
				total: '100.00',
				line_items: [ {}, {} ],
				total_line_items_quantity: 4,
			},
			shippingType: 'Flat Rate',
			destinationAddress,
		};

		const storeCurrency = CurrencyFactory( 'USD' );
		const rates = {
			getSelectedRate: () => ( {
				rate: {
					rate: 50,
					title: 'UPS Ground',
					type: 'adultSignatureRequired',
				},
				parent: {
					rate: 10,
					title: 'UPS Ground',
				},
			} ),
			getSelectedRateOptions: jest.fn(),
			selectRateOption: jest.fn(),
		};

		const getCurrentShipmentLabel = jest.fn( () => null );
		const hasPurchasedLabel = jest.fn( () => false );
		const getShipmentPurchaseOrigin = jest.fn( () => address );

		const labels = {
			hasPurchasedLabel,
			getCurrentShipmentLabel,
		};
		const changeAddress = 'address at purchase time';
		const shipment = {
			getShipmentOrigin: () => address,
			getShipmentPurchaseOrigin,
			getShipmentDestination: () => destinationAddress,
			getCurrentShipmentDate: jest.fn().mockReturnValue( {
				shippingDate: new Date( '2025-02-25' ),
				estimatedDeliveryDate: new Date( '2025-02-28' ),
			} ),
		};

		let asFragment;
		await act( async () => {
			asFragment = render(
				<LabelPurchaseContext.Provider
					value={ { storeCurrency, rates, labels, shipment } }
				>
					<ShipmentDetails { ...props } />
				</LabelPurchaseContext.Provider>
			).asFragment;
		} );

		expect(
			screen.getByText( ( content ) =>
				content.includes( address.address )
			)
		).toBeInTheDocument();

		expect( asFragment() ).toMatchSnapshot( 'no purchased label' );

		getCurrentShipmentLabel.mockReturnValue( {
			status: 'PURCHASED',
		} );

		hasPurchasedLabel.mockReturnValue( true );
		getShipmentPurchaseOrigin.mockReturnValue( {
			...address,
			address: changeAddress,
		} );

		let withPurchasedLabelFragment;
		await act( async () => {
			withPurchasedLabelFragment = render(
				<LabelPurchaseContext.Provider
					value={ { storeCurrency, rates, labels, shipment } }
				>
					<ShipmentDetails { ...props } />
				</LabelPurchaseContext.Provider>
			).asFragment;
		} );

		expect(
			screen.getByText( ( content ) => content.includes( changeAddress ) )
		).toBeInTheDocument();

		expect( withPurchasedLabelFragment() ).toMatchSnapshot(
			'with purchased label'
		);
	} );

	it( 'should show cost placeholder when label has PURCHASE_ERROR status', async () => {
		const props = {
			order: {
				total_shipping: '50.00',
				subtotal_shipping: '10.00',
				total: '100.00',
				line_items: [ {}, {} ],
				total_line_items_quantity: 4,
			},
			shippingType: 'Flat Rate',
			destinationAddress,
		};

		const storeCurrency = CurrencyFactory( 'USD' );
		const rates = {
			getSelectedRate: () => null,
			getSelectedRateOptions: jest.fn(),
			selectRateOption: jest.fn(),
			getCurrentRateOptions: jest.fn().mockReturnValue( [] ),
		};

		const getCurrentShipmentLabel = jest.fn( () => ( {
			isLegacy: false,
			status: 'PURCHASE_ERROR',
		} ) );

		const labels = {
			hasPurchasedLabel: () => false,
			getCurrentShipmentLabel,
		};

		const shipment = {
			getShipmentOrigin: () => address,
			getShipmentPurchaseOrigin: () => address,
			getShipmentDestination: () => destinationAddress,
			getCurrentShipmentDate: jest.fn().mockReturnValue( {
				shippingDate: new Date( '2025-02-25' ),
				estimatedDeliveryDate: new Date( '2025-02-28' ),
			} ),
		};

		const { container } = render(
			<LabelPurchaseContext.Provider
				value={ { storeCurrency, rates, labels, shipment } }
			>
				<ShipmentDetails { ...props } />
			</LabelPurchaseContext.Provider>
		);

		await waitFor( () => {
			expect(
				container.querySelector( '.cost-placeholder' )
			).toBeInTheDocument();
		} );
	} );
} );
