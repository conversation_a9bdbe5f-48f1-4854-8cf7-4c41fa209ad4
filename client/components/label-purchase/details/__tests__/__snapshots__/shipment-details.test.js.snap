// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ShipmentDetails ship from address for a successful purchase is read from the snapshot: no purchased label 1`] = `
<DocumentFragment>
  <div
    class="shipment-details"
  >
    <h3
      class="components-truncate components-text components-heading css-4bfvya-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Heading"
    >
      Order details
    </h3>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="ship-from"
        >
          Ship from
        </label>
        <div
          class="components-flex css-6d15bh-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <div
            class="components-dropdown components-dropdown-menu origin-address-dropdown"
            tabindex="-1"
          >
            <button
              aria-expanded="false"
              aria-haspopup="true"
              aria-label="Choose a ship from address"
              class="components-button components-dropdown-menu__toggle has-icon"
              type="button"
            >
              <span>
                123 Main St, San Francisco, CA 94105, PR
              </span>
              <svg
                aria-hidden="true"
                focusable="false"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      class="components-base-control purchase-label__ship-to css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="ship-to"
        >
          Ship to
        </label>
        <span
          class="components-truncate components-text css-zqwd4y-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          <button
            class="components-button ship-to-edit-icon has-icon"
            title="Click to change address"
            type="button"
          >
            <svg
              aria-hidden="true"
              focusable="false"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="m19 7-3-3-8.5 8.5-1 4 4-1L19 7Zm-7 11.5H5V20h7v-1.5Z"
              />
            </svg>
          </button>
          124 Main St - destination, San Francisco - dest, CA 94105, PR
          <span
            class="verification not-verified"
          >
            <button
              class="components-button is-link has-text has-icon"
              title="Unverified address"
              type="button"
            >
              <svg
                aria-hidden="true"
                focusable="false"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M5.5 12a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0ZM12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16Zm.75 4v1.5h-1.5V8h1.5Zm0 8v-5h-1.5v5h1.5Z"
                  fill-rule="evenodd"
                />
              </svg>
              Unverified address
            </button>
          </span>
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="no-of-items"
        >
          Number of items
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          4
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="order-value"
        >
          Order value
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          $100.00
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="shipping-type"
        >
          Shipping type
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          Flat Rate
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="shipping-costs"
        >
          Shipping costs
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          $50.00
        </span>
      </div>
    </div>
    <section
      class="shipment-details__costs has-rates"
    >
      <hr
        aria-orientation="horizontal"
        class="components-divider css-udwiw9-DividerView-renderBorder-renderSize-rtl-renderMargin e19on6iw0"
        data-wp-c16t="true"
        data-wp-component="Divider"
        role="separator"
      />
      <h3
        class="components-truncate components-text components-heading css-4bfvya-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Heading"
      >
        Shipment details
      </h3>
      <div />
      <div
        class="components-base-control shipping-date-control css-fothu4-Wrapper-boxSizingReset ej5x27r4 wp-tour-kit-spotlit"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="shipping-date-control-4"
          >
            Ship date 
            <span
              aria-expanded="false"
              aria-haspopup="true"
              class="dashicon dashicons dashicons-info-outline"
              style="cursor: pointer;"
            />
          </label>
          <section
            class="components-flex shipping-date-field-wrapper css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <button
              aria-expanded="false"
              aria-haspopup="true"
              class="components-button shipping-date-display is-link has-text has-icon"
              style="pointer-events: auto;"
              type="button"
            >
              <span
                class="dashicon dashicons dashicons-calendar-alt"
              />
              February 25
            </button>
          </section>
        </div>
      </div>
      <hr
        aria-orientation="horizontal"
        class="components-divider css-udwiw9-DividerView-renderBorder-renderSize-rtl-renderMargin e19on6iw0"
        data-wp-c16t="true"
        data-wp-component="Divider"
        role="separator"
      />
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="sub-total"
          >
            UPS Ground (base fee)
          </label>
          <span
            class="components-truncate components-text css-1g5ovhl-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            $10.00
          </span>
        </div>
      </div>
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="sub-total-extra"
          >
            <section
              class="components-flex css-1ykk4a2-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="Flex"
            >
              <span
                class="subtotal-extra-bit"
              />
              Adult Signature Required
            </section>
          </label>
          <div
            class="components-flex css-c6x1q3-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <span
              class="components-truncate components-text css-1g5ovhl-PolymorphicDiv-Text-sx-Base e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="Text"
            >
              $40.00
            </span>
          </div>
        </div>
      </div>
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="total"
          >
            <strong>
              Total
            </strong>
          </label>
          <span
            class="components-truncate components-text css-1fgy6ww-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            $50.00
          </span>
        </div>
      </div>
    </section>
  </div>
</DocumentFragment>
`;

exports[`ShipmentDetails ship from address for a successful purchase is read from the snapshot: with purchased label 1`] = `
<DocumentFragment>
  <div
    class="shipment-details"
  >
    <h3
      class="components-truncate components-text components-heading css-4bfvya-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Heading"
    >
      Order details
    </h3>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="ship-from"
        >
          Ship from
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          address at purchase time, San Francisco, CA 94105, PR
        </span>
      </div>
    </div>
    <div
      class="components-base-control purchase-label__ship-to css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="ship-to"
        >
          Ship to
        </label>
        124 Main St - destination, San Francisco - dest, CA 94105, PR
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="no-of-items"
        >
          Number of items
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          4
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="order-value"
        >
          Order value
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          $100.00
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="shipping-type"
        >
          Shipping type
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          Flat Rate
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="shipping-costs"
        >
          Shipping costs
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          $50.00
        </span>
      </div>
    </div>
    <section
      class="shipment-details__costs has-rates"
    >
      <hr
        aria-orientation="horizontal"
        class="components-divider css-udwiw9-DividerView-renderBorder-renderSize-rtl-renderMargin e19on6iw0"
        data-wp-c16t="true"
        data-wp-component="Divider"
        role="separator"
      />
      <h3
        class="components-truncate components-text components-heading css-4bfvya-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Heading"
      >
        Shipment details
      </h3>
      <div
        class="components-base-control shipping-date-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="shipping-date-control-5"
          >
            Ship date 
            <span
              aria-expanded="false"
              aria-haspopup="true"
              class="dashicon dashicons dashicons-info-outline"
              style="cursor: pointer;"
            />
          </label>
          <section
            class="components-flex shipping-date-field-wrapper css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <button
              aria-expanded="false"
              aria-haspopup="true"
              class="components-button shipping-date-display is-link"
              disabled=""
              style="pointer-events: none;"
              type="button"
            >
              February 25
            </button>
          </section>
        </div>
      </div>
      <hr
        aria-orientation="horizontal"
        class="components-divider css-udwiw9-DividerView-renderBorder-renderSize-rtl-renderMargin e19on6iw0"
        data-wp-c16t="true"
        data-wp-component="Divider"
        role="separator"
      />
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="sub-total"
          >
            UPS Ground (base fee)
          </label>
          <span
            class="components-truncate components-text css-1g5ovhl-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            $10.00
          </span>
        </div>
      </div>
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="sub-total-extra"
          >
            <section
              class="components-flex css-1ykk4a2-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="Flex"
            >
              <span
                class="subtotal-extra-bit"
              />
              Adult Signature Required
            </section>
          </label>
          <div
            class="components-flex css-c6x1q3-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <span
              class="components-truncate components-text css-1g5ovhl-PolymorphicDiv-Text-sx-Base e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="Text"
            >
              $40.00
            </span>
          </div>
        </div>
      </div>
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="total"
          >
            <strong>
              Total
            </strong>
          </label>
          <span
            class="components-truncate components-text css-1fgy6ww-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            $50.00
          </span>
        </div>
      </div>
    </section>
  </div>
</DocumentFragment>
`;

exports[`ShipmentDetails should render the ShipmentDetails component 1`] = `
<DocumentFragment>
  <div
    class="shipment-details"
  >
    <h3
      class="components-truncate components-text components-heading css-4bfvya-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Heading"
    >
      Order details
    </h3>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="ship-from"
        >
          Ship from
        </label>
        <div
          class="components-flex css-6d15bh-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <div
            class="components-dropdown components-dropdown-menu origin-address-dropdown"
            tabindex="-1"
          >
            <button
              aria-expanded="false"
              aria-haspopup="true"
              aria-label="Choose a ship from address"
              class="components-button components-dropdown-menu__toggle has-icon"
              type="button"
            >
              <span>
                123 Main St, San Francisco, CA 94105, PR
              </span>
              <svg
                aria-hidden="true"
                focusable="false"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      class="components-base-control purchase-label__ship-to css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="ship-to"
        >
          Ship to
        </label>
        <span
          class="components-truncate components-text css-zqwd4y-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          <button
            class="components-button ship-to-edit-icon has-icon"
            title="Click to change address"
            type="button"
          >
            <svg
              aria-hidden="true"
              focusable="false"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="m19 7-3-3-8.5 8.5-1 4 4-1L19 7Zm-7 11.5H5V20h7v-1.5Z"
              />
            </svg>
          </button>
          124 Main St - destination, San Francisco - dest, CA 94105, PR
          <span
            class="verification not-verified"
          >
            <button
              class="components-button is-link has-text has-icon"
              title="Unverified address"
              type="button"
            >
              <svg
                aria-hidden="true"
                focusable="false"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M5.5 12a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0ZM12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16Zm.75 4v1.5h-1.5V8h1.5Zm0 8v-5h-1.5v5h1.5Z"
                  fill-rule="evenodd"
                />
              </svg>
              Unverified address
            </button>
          </span>
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="no-of-items"
        >
          Number of items
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          4
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="order-value"
        >
          Order value
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          $100.00
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="shipping-type"
        >
          Shipping type
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          Flat Rate
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="shipping-costs"
        >
          Shipping costs
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          $50.00
        </span>
      </div>
    </div>
    <section
      class="shipment-details__costs has-rates"
    >
      <hr
        aria-orientation="horizontal"
        class="components-divider css-udwiw9-DividerView-renderBorder-renderSize-rtl-renderMargin e19on6iw0"
        data-wp-c16t="true"
        data-wp-component="Divider"
        role="separator"
      />
      <h3
        class="components-truncate components-text components-heading css-4bfvya-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Heading"
      >
        Shipment details
      </h3>
      <div />
      <div
        class="components-base-control shipping-date-control css-fothu4-Wrapper-boxSizingReset ej5x27r4 wp-tour-kit-spotlit"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="shipping-date-control-1"
          >
            Ship date 
            <span
              aria-expanded="false"
              aria-haspopup="true"
              class="dashicon dashicons dashicons-info-outline"
              style="cursor: pointer;"
            />
          </label>
          <section
            class="components-flex shipping-date-field-wrapper css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <button
              aria-expanded="false"
              aria-haspopup="true"
              class="components-button shipping-date-display is-link has-text has-icon"
              style="pointer-events: auto;"
              type="button"
            >
              <span
                class="dashicon dashicons dashicons-calendar-alt"
              />
              Today (February 26)
            </button>
          </section>
        </div>
      </div>
      <hr
        aria-orientation="horizontal"
        class="components-divider css-udwiw9-DividerView-renderBorder-renderSize-rtl-renderMargin e19on6iw0"
        data-wp-c16t="true"
        data-wp-component="Divider"
        role="separator"
      />
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="sub-total"
          >
            UPS Ground (base fee)
          </label>
          <span
            class="components-truncate components-text css-1g5ovhl-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            $10.00
          </span>
        </div>
      </div>
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="sub-total-extra"
          >
            <section
              class="components-flex css-1ykk4a2-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="Flex"
            >
              <span
                class="subtotal-extra-bit"
              />
              Adult Signature Required
            </section>
          </label>
          <div
            class="components-flex css-c6x1q3-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <span
              class="components-truncate components-text css-1g5ovhl-PolymorphicDiv-Text-sx-Base e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="Text"
            >
              $40.00
            </span>
          </div>
        </div>
      </div>
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="total"
          >
            <strong>
              Total
            </strong>
          </label>
          <span
            class="components-truncate components-text css-1fgy6ww-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            $50.00
          </span>
        </div>
      </div>
    </section>
  </div>
</DocumentFragment>
`;

exports[`ShipmentDetails should render the ShipmentDetails component: auto address verification 1`] = `
<DocumentFragment>
  <div
    class="shipment-details"
  >
    <h3
      class="components-truncate components-text components-heading css-4bfvya-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Heading"
    >
      Order details
    </h3>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="ship-from"
        >
          Ship from
        </label>
        <div
          class="components-flex css-6d15bh-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <div
            class="components-dropdown components-dropdown-menu origin-address-dropdown"
            tabindex="-1"
          >
            <button
              aria-expanded="false"
              aria-haspopup="true"
              aria-label="Choose a ship from address"
              class="components-button components-dropdown-menu__toggle has-icon"
              type="button"
            >
              <span>
                123 Main St, San Francisco, CA 94105, PR
              </span>
              <svg
                aria-hidden="true"
                focusable="false"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      class="components-base-control purchase-label__ship-to css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="ship-to"
        >
          Ship to
        </label>
        <span
          class="components-truncate components-text css-zqwd4y-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          <button
            class="components-button ship-to-edit-icon has-icon"
            title="Click to change address"
            type="button"
          >
            <svg
              aria-hidden="true"
              focusable="false"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="m19 7-3-3-8.5 8.5-1 4 4-1L19 7Zm-7 11.5H5V20h7v-1.5Z"
              />
            </svg>
          </button>
          124 Main St - destination, San Francisco - dest, CA 94105, PR
          <span
            class="verification not-verified"
          >
            <button
              class="components-button is-link has-text has-icon"
              title="Unverified address"
              type="button"
            >
              <svg
                aria-hidden="true"
                focusable="false"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M5.5 12a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0ZM12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16Zm.75 4v1.5h-1.5V8h1.5Zm0 8v-5h-1.5v5h1.5Z"
                  fill-rule="evenodd"
                />
              </svg>
              Unverified address
            </button>
          </span>
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="no-of-items"
        >
          Number of items
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          4
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="order-value"
        >
          Order value
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          $100.00
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="shipping-type"
        >
          Shipping type
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          Flat Rate
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="shipping-costs"
        >
          Shipping costs
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          $50.00
        </span>
      </div>
    </div>
    <section
      class="shipment-details__costs has-rates"
    >
      <hr
        aria-orientation="horizontal"
        class="components-divider css-udwiw9-DividerView-renderBorder-renderSize-rtl-renderMargin e19on6iw0"
        data-wp-c16t="true"
        data-wp-component="Divider"
        role="separator"
      />
      <h3
        class="components-truncate components-text components-heading css-4bfvya-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Heading"
      >
        Shipment details
      </h3>
      <div />
      <div
        class="components-base-control shipping-date-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="shipping-date-control-2"
          >
            Ship date 
            <span
              aria-expanded="false"
              aria-haspopup="true"
              class="dashicon dashicons dashicons-info-outline"
              style="cursor: pointer;"
            />
          </label>
          <section
            class="components-flex shipping-date-field-wrapper css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <button
              aria-expanded="false"
              aria-haspopup="true"
              class="components-button shipping-date-display is-link has-text has-icon"
              style="pointer-events: auto;"
              type="button"
            >
              <span
                class="dashicon dashicons dashicons-calendar-alt"
              />
              Today (February 26)
            </button>
          </section>
        </div>
      </div>
      <hr
        aria-orientation="horizontal"
        class="components-divider css-udwiw9-DividerView-renderBorder-renderSize-rtl-renderMargin e19on6iw0"
        data-wp-c16t="true"
        data-wp-component="Divider"
        role="separator"
      />
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="sub-total"
          >
            UPS Ground (base fee)
          </label>
          <span
            class="components-truncate components-text css-1g5ovhl-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            $10.00
          </span>
        </div>
      </div>
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="sub-total-extra"
          >
            <section
              class="components-flex css-1ykk4a2-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="Flex"
            >
              <span
                class="subtotal-extra-bit"
              />
              Adult Signature Required
            </section>
          </label>
          <div
            class="components-flex css-c6x1q3-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <span
              class="components-truncate components-text css-1g5ovhl-PolymorphicDiv-Text-sx-Base e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="Text"
            >
              $40.00
            </span>
          </div>
        </div>
      </div>
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="total"
          >
            <strong>
              Total
            </strong>
          </label>
          <span
            class="components-truncate components-text css-1fgy6ww-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            $50.00
          </span>
        </div>
      </div>
    </section>
  </div>
</DocumentFragment>
`;

exports[`ShipmentDetails should render the ShipmentDetails component: with discount 1`] = `
<DocumentFragment>
  <div
    class="shipment-details"
  >
    <h3
      class="components-truncate components-text components-heading css-4bfvya-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Heading"
    >
      Order details
    </h3>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="ship-from"
        >
          Ship from
        </label>
        <div
          class="components-flex css-6d15bh-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <div
            class="components-dropdown components-dropdown-menu origin-address-dropdown"
            tabindex="-1"
          >
            <button
              aria-expanded="false"
              aria-haspopup="true"
              aria-label="Choose a ship from address"
              class="components-button components-dropdown-menu__toggle has-icon"
              type="button"
            >
              <span>
                123 Main St, San Francisco, CA 94105, PR
              </span>
              <svg
                aria-hidden="true"
                focusable="false"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      class="components-base-control purchase-label__ship-to css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="ship-to"
        >
          Ship to
        </label>
        <span
          class="components-truncate components-text css-zqwd4y-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          <button
            class="components-button ship-to-edit-icon has-icon"
            title="Click to change address"
            type="button"
          >
            <svg
              aria-hidden="true"
              focusable="false"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="m19 7-3-3-8.5 8.5-1 4 4-1L19 7Zm-7 11.5H5V20h7v-1.5Z"
              />
            </svg>
          </button>
          124 Main St - destination, San Francisco - dest, CA 94105, PR
          <span
            class="verification in-progress"
          >
            <svg
              aria-hidden="true"
              focusable="false"
              height="16"
              viewBox="0 0 24 24"
              width="16"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"
              />
            </svg>
            Verifying address…
          </span>
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="no-of-items"
        >
          Number of items
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          4
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="order-value"
        >
          Order value
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          $100.00
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="shipping-type"
        >
          Shipping type
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          Flat Rate
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="shipping-costs"
        >
          Shipping costs
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          $50.00
        </span>
      </div>
    </div>
    <section
      class="shipment-details__costs has-rates"
    >
      <hr
        aria-orientation="horizontal"
        class="components-divider css-udwiw9-DividerView-renderBorder-renderSize-rtl-renderMargin e19on6iw0"
        data-wp-c16t="true"
        data-wp-component="Divider"
        role="separator"
      />
      <h3
        class="components-truncate components-text components-heading css-4bfvya-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Heading"
      >
        Shipment details
      </h3>
      <div />
      <div
        class="components-base-control shipping-date-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="shipping-date-control-3"
          >
            Ship date 
            <span
              aria-expanded="false"
              aria-haspopup="true"
              class="dashicon dashicons dashicons-info-outline"
              style="cursor: pointer;"
            />
          </label>
          <section
            class="components-flex shipping-date-field-wrapper css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <button
              aria-expanded="false"
              aria-haspopup="true"
              class="components-button shipping-date-display is-link has-text has-icon"
              style="pointer-events: auto;"
              type="button"
            >
              <span
                class="dashicon dashicons dashicons-calendar-alt"
              />
              Today (February 26)
            </button>
          </section>
        </div>
      </div>
      <hr
        aria-orientation="horizontal"
        class="components-divider css-udwiw9-DividerView-renderBorder-renderSize-rtl-renderMargin e19on6iw0"
        data-wp-c16t="true"
        data-wp-component="Divider"
        role="separator"
      />
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="sub-total"
          >
            UPS Ground (base fee)
          </label>
          <span
            class="components-truncate components-text css-1g5ovhl-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            $10.00
          </span>
        </div>
      </div>
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="sub-total-extra"
          >
            <section
              class="components-flex css-1ykk4a2-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="Flex"
            >
              <span
                class="subtotal-extra-bit"
              />
              Signature Required
            </section>
          </label>
          <div
            class="components-flex css-c6x1q3-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <span
              class="components-truncate components-text css-1g5ovhl-PolymorphicDiv-Text-sx-Base e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="Text"
            >
              $40.00
            </span>
          </div>
        </div>
      </div>
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="total"
          >
            <strong>
              Total
            </strong>
          </label>
          <span
            class="components-truncate components-text css-1fgy6ww-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            $50.00
          </span>
        </div>
      </div>
      <div
        class="rate-discount components-notice is-null"
      >
        <div
          class="components-visually-hidden css-1qfl78b-PolymorphicDiv e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="VisuallyHidden"
          style="border: 0px; clip: rect(1px, 1px, 1px, 1px); clip-path: inset( 50% ); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; word-wrap: normal;"
        >
          Notice
        </div>
        <div
          class="components-notice__content"
        >
          You save $3.20 with WooCommerce Shipping. 
          <svg
            aria-expanded="false"
            aria-haspopup="true"
            aria-hidden="true"
            focusable="false"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 4.75a7.25 7.25 0 100 14.5 7.25 7.25 0 000-14.5zM3.25 12a8.75 8.75 0 1117.5 0 8.75 8.75 0 01-17.5 0zM12 8.75a1.5 1.5 0 01.167 2.99c-.465.052-.917.44-.917 1.01V14h1.5v-.845A3 3 0 109 10.25h1.5a1.5 1.5 0 011.5-1.5zM11.25 15v1.5h1.5V15h-1.5z"
            />
          </svg>
          <div
            class="components-notice__actions"
          />
        </div>
      </div>
    </section>
  </div>
</DocumentFragment>
`;

exports[`ShipmentDetails should render the ShipmentDetails component: with legacy shipping label 1`] = `
<DocumentFragment>
  <div
    class="shipment-details"
  >
    <h3
      class="components-truncate components-text components-heading css-4bfvya-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Heading"
    >
      Order details
    </h3>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="ship-from"
        >
          Ship from
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          123 Main St - snapshot origin, San Francisco, CA 94105, PR
        </span>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          **************************
        </span>
      </div>
    </div>
    <div
      class="components-base-control purchase-label__ship-to css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="ship-to"
        >
          Ship to
        </label>
        124 Main St - destination, San Francisco - dest, CA 94105, PR
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="no-of-items"
        >
          Number of items
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          4
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="order-value"
        >
          Order value
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          $100.00
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="shipping-type"
        >
          Shipping type
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          Flat Rate
        </span>
      </div>
    </div>
    <div
      class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
    >
      <div
        class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
      >
        <label
          class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
          for="shipping-costs"
        >
          Shipping costs
        </label>
        <span
          class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          $50.00
        </span>
      </div>
    </div>
    <section
      class="shipment-details__costs has-rates"
    >
      <hr
        aria-orientation="horizontal"
        class="components-divider css-udwiw9-DividerView-renderBorder-renderSize-rtl-renderMargin e19on6iw0"
        data-wp-c16t="true"
        data-wp-component="Divider"
        role="separator"
      />
      <h3
        class="components-truncate components-text components-heading css-4bfvya-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Heading"
      >
        Shipment details
      </h3>
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        />
      </div>
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="sub-total-extra"
          >
            <section
              class="components-flex css-1ykk4a2-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="Flex"
            >
              <span
                class="subtotal-extra-bit"
              />
              Signature Required
            </section>
          </label>
          <div
            class="components-flex css-c6x1q3-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <span
              class="components-truncate components-text css-1g5ovhl-PolymorphicDiv-Text-sx-Base e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="Text"
            >
              $40.00
            </span>
          </div>
        </div>
      </div>
      <div
        class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
      >
        <div
          class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
        >
          <label
            class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
            for="total"
          >
            <strong>
              Total
            </strong>
          </label>
          <span
            class="components-truncate components-text css-1fgy6ww-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            $50.00
          </span>
        </div>
      </div>
      <div
        class="rate-discount components-notice is-success"
      >
        <div
          class="components-visually-hidden css-1qfl78b-PolymorphicDiv e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="VisuallyHidden"
          style="border: 0px; clip: rect(1px, 1px, 1px, 1px); clip-path: inset( 50% ); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; word-wrap: normal;"
        >
          Notice
        </div>
        <div
          class="components-notice__content"
        >
          You saved $3.20 with WooCommerce Shipping. 
          <svg
            aria-expanded="false"
            aria-haspopup="true"
            aria-hidden="true"
            focusable="false"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 4.75a7.25 7.25 0 100 14.5 7.25 7.25 0 000-14.5zM3.25 12a8.75 8.75 0 1117.5 0 8.75 8.75 0 01-17.5 0zM12 8.75a1.5 1.5 0 01.167 2.99c-.465.052-.917.44-.917 1.01V14h1.5v-.845A3 3 0 109 10.25h1.5a1.5 1.5 0 011.5-1.5zM11.25 15v1.5h1.5V15h-1.5z"
            />
          </svg>
          <div
            class="components-notice__actions"
          />
        </div>
      </div>
    </section>
  </div>
</DocumentFragment>
`;
