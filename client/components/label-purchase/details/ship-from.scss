.origin-address-dropdown {
	overflow: hidden;
	width: 100%;

	.components-button.components-dropdown-menu__toggle.has-icon,
	.components-button.components-dropdown-menu__toggle.is-opened,
	.components-button.components-dropdown-menu__toggle:focus {
		box-shadow: none;
		color: var(--Upcoming-<PERSON><PERSON>, #3858e9);
		display: flex;
		font-size: 13px;
		font-weight: 400;
		justify-content: flex-start;
		outline: none;
		padding: 0;
		text-align: left;
		text-decoration: none;
		white-space: nowrap;
		width: 100%;
		height: unset;

		span {
			flex: 4;
			// 28px is the width of the icon + padding right
			max-width: calc(100% - 28px);
			overflow: hidden;
			text-align: left;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		svg {
			flex: 1;
			position: relative;
			right: -8px;
			width: 36px;
			max-width: 36px;
		}
	}
}

.components-popover.is-ship-from {
	z-index: calc(var(--wcs-main-modal-z-index) + 1);
}

.origin-address-options {

	.components-button.components-menu-item__button {

		&:not(:first-child) {
			margin-top: 4px;
		}

		&:focus,
		.components-button:focus {
			box-shadow:
				0 0 0 var(--wp-admin-border-width-focus)
				var(--Upcoming-Blueberry, #3858e9);
		}

		.verification {
			margin-top: 0;
		}
	}

	.components-menu-item__item {
		width: 300px;

		.components-text {
			white-space: break-spaces;
		}

		&:hover {
			color: var(--Upcoming-Blueberry, #3858e9);

			.components-text {
				color: var(--Upcoming-Blueberry, #3858e9);
			}
		}
	}

	.components-button.is-link,
	*[data-link-type="wp-admin"] {
		color: var(--Upcoming-Blueberry, #3858e9);
		text-decoration: none;
	}
}
