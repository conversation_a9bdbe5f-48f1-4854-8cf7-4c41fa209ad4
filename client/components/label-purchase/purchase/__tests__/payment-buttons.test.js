import { ProvideTestState, orderTestData } from 'test-helpers';

import { act, fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { getPurchasedLabels } from 'utils/order';

import { PaymentButtons } from '../payment-buttons';
import { apiFetch } from '@wordpress/data-controls';
import { registerLabelPurchaseStore } from 'data/label-purchase';
import { LabelPurchaseContext } from 'context/label-purchase';
import flushPromises from 'tests/utilities/flush-promises';
import { LABEL_PURCHASE_STATUS } from 'data/constants';

jest.mock( '@wordpress/data-controls', () => {
	return {
		__esModule: true,
		apiFetch: jest.fn(),
	};
} );

jest.mock( '@wordpress/data', () => {
	return {
		__esModule: true,
		...jest.requireActual( '@wordpress/data' ),
	};
} );

jest.mock( 'utils/order', () => {
	return {
		__esModule: true,
		getPurchasedLabels: jest.fn(),
	};
} );

const singleLabel = {
	labelId: 206,
	tracking: '9405500000005252525252',
	refundableAmount: 9.05,
	created: 1706916090177,
	carrierId: 'usps',
	serviceName: 'USPS - Priority Mail',
	status: LABEL_PURCHASE_STATUS.PURCHASED,
	commercialInvoiceUrl: '',
	isCommercialInvoiceSubmittedElectronically: false,
	packageName: 'Small Flat Rate Box',
	isLetter: false,
	productNames: [ 'Beanie with Logo' ],
	productIds: [ 32 ],
	id: '0',
	receiptItemId: 12345,
	createdDate: 1706916094000,
	mainReceiptId: 20180245,
	rate: 9.05,
	currency: 'USD',
	expiryDate: *************,
};
const shipmentsTestData = {
	0: [
		{
			id: 1,
			quantity: 2,
			subItems: [
				{
					id: '1-sub-0',
					quantity: 1,
					subItems: [],
				},
				{
					id: '1-sub-1',
					quantity: 1,
					subItems: [],
				},
			],
		},
		{
			id: '1-sub-2',
			quantity: 1,
			subItems: [],
		},
		{
			id: '1-sub-3',
			quantity: 1,
			subItems: [],
		},
	],
};

registerLabelPurchaseStore();

describe( 'Payment Buttons', () => {
	beforeEach( () => {
		jest.clearAllMocks();
	} );
	const {
		accountSettings, // eslint-disable-next-line @typescript-eslint/no-var-requires
	} = require( 'utils/__tests__/fixtures/account-settings' );

	const initialContext = {
		shipment: {
			shipments: shipmentsTestData,
			selections: {},
			currentShipmentId: 0,
			getShipmentOrigin: () => ( {
				country: 'US',
				state: 'CA',
				isVerified: true,
			} ),
			isExtraLabelPurchaseValid: jest.fn( () => true ),
		},
		labels: {
			hasPurchasedLabel: jest.fn( () => false ),
			selectedLabelSize: {
				key: 'label',
				name: 'Label (4"x6")',
			},
			requestLabelPurchase: jest
				.fn()
				.mockImplementationOnce( () => Promise.resolve( true ) ),
			printLabel: jest
				.fn()
				.mockImplementationOnce( () => Promise.resolve( true ) ),
			getCurrentShipmentLabel: jest.fn().mockReturnValue( {
				status: LABEL_PURCHASE_STATUS.PURCHASED,
			} ),
			hasMissingPurchase: jest.fn( () => false ),
		},
		rates: {
			getSelectedRate: jest.fn( () => ( {
				rate: {
					rate: 50,
					title: 'USPS Ground Advantage',
				},
				parent: {
					rate: 10,
					title: 'USPS Ground Advantage',
				},
			} ) ),
		},
		account: {
			canPurchase: jest.fn( () => true ),
			getAccountCompleteOrder: jest.fn( () => false ),
			setAccountCompleteOrder: jest.fn(),
			accountSettings,
		},
		customs: {
			isCustomsNeeded: jest.fn( () => false ),
		},
		hazmat: {
			getShipmentHazmat: jest.fn( () => ( {
				0: {
					isHazmat: false,
					category: '',
				},
			} ) ),
		},
		packages: {
			isPackageSpecified: jest.fn( () => true ),
		},
	};

	it( 'should display the "Mark order as completed" checkbox if the order is not completed', () => {
		render(
			<LabelPurchaseContext.Provider value={ initialContext }>
				<PaymentButtons
					order={ { ...orderTestData, status: 'on-hold' } }
				/>
			</LabelPurchaseContext.Provider>
		);

		expect(
			screen.queryByLabelText(
				'After purchasing a label, mark this order as complete and notify the customer'
			)
		).toBeInTheDocument();
	} );

	it( 'should not display the "Mark order as completed" checkbox if the order is completed', () => {
		render(
			<LabelPurchaseContext.Provider value={ initialContext }>
				<PaymentButtons
					order={ { ...orderTestData, status: 'completed' } }
				/>
			</LabelPurchaseContext.Provider>
		);

		expect(
			screen.queryByLabelText(
				'After purchasing a label, mark this order as complete and notify the customer'
			)
		).not.toBeInTheDocument();
	} );

	it( 'should update order status when "Mark order as completed" is checked for successful purchase', async () => {
		// @ts-ignore
		apiFetch.mockResolvedValueOnce( { success: true } );
		getPurchasedLabels.mockReturnValue( {
			0: [ singleLabel ],
		} );
		render(
			<LabelPurchaseContext.Provider value={ initialContext }>
				<PaymentButtons order={ orderTestData } />
			</LabelPurchaseContext.Provider>
		);

		// Click the "mark order as completed" checkbox
		fireEvent.click(
			screen.getByLabelText(
				'After purchasing a label, mark this order as complete and notify the customer'
			)
		);

		// Click "Purchase Label" button
		fireEvent.click( screen.getByText( 'Purchase label' ) );

		// This should trigger the WC endpoint that update the order status, verify it.
		await act( () => flushPromises() );
		expect( apiFetch ).toHaveBeenCalledWith( {
			method: 'PUT',
			path: '/wc/v3/orders/176',
			data: {
				status: 'completed',
			},
		} );
	} );

	it( 'should not update order status when "Mark order as completed" is checked and purchase is not successful', async () => {
		// @ts-ignore
		apiFetch.mockResolvedValue( { success: true } );
		initialContext.labels.getCurrentShipmentLabel.mockReturnValue( {
			status: LABEL_PURCHASE_STATUS.PURCHASE_ERROR,
		} );

		getPurchasedLabels.mockReturnValue( {
			0: [
				{
					...singleLabel,
					status: LABEL_PURCHASE_STATUS.PURCHASE_ERROR,
				},
			],
		} );

		render(
			<LabelPurchaseContext.Provider value={ initialContext }>
				<PaymentButtons order={ orderTestData } />
			</LabelPurchaseContext.Provider>
		);

		// Click the "mark order as completed" checkbox
		fireEvent.click(
			screen.getByLabelText(
				'After purchasing a label, mark this order as complete and notify the customer'
			)
		);

		// Click "Purchase Label" button
		fireEvent.click( screen.getByText( 'Purchase label' ) );

		// This should trigger the WC endpoint that update the order status, verify it.
		await act( () => flushPromises() );
		expect( apiFetch ).not.toHaveBeenCalledWith( {
			method: 'PUT',
			path: '/wc/v3/orders/176',
			data: {
				status: 'completed',
			},
		} );
	} );

	it( 'should call the setAccountCompleteOrder function when the mark order complete checkbox is checked/unchecked', async () => {
		render(
			<LabelPurchaseContext.Provider value={ initialContext }>
				<PaymentButtons order={ orderTestData } />
			</LabelPurchaseContext.Provider>
		);

		// Click the "mark order as completed" checkbox
		fireEvent.click(
			screen.getByLabelText(
				'After purchasing a label, mark this order as complete and notify the customer'
			)
		);

		// This should trigger the WC endpoint that update the order status, verify it.
		await act( () => flushPromises() );
		expect(
			initialContext.account.setAccountCompleteOrder
		).toHaveBeenCalledWith( true );

		// Click the "mark order as completed" checkbox again to uncheck
		fireEvent.click(
			screen.getByLabelText(
				'After purchasing a label, mark this order as complete and notify the customer'
			)
		);
		await act( () => flushPromises() );
		expect(
			initialContext.account.setAccountCompleteOrder
		).toHaveBeenCalled();
	} );

	it( 'should keep the purchase button enabled after a failed payment', async () => {
		render(
			<ProvideTestState
				initialValue={ {
					rates: {
						getSelectedRate: () => ( {
							rate: {
								rate: 50,
								title: 'USPS Ground Advantage',
							},
							parent: {
								rate: 10,
								title: 'USPS Ground Advantage',
							},
						} ),
					},
					labels: {
						getCurrentShipmentLabel: () => ( {
							status: LABEL_PURCHASE_STATUS.PURCHASE_ERROR,
						} ),
						hasMissingPurchase: jest.fn( () => true ),
					},
					shipment: {
						selections: {},
						getShipmentOrigin: () => ( {
							id: 1,
							city: 'San Francisco',
							country: 'US',
							state: 'CA',
							isVerified: true,
						} ),
					},
					account: {
						canPurchase: jest.fn( () => true ),
					},
				} }
			>
				<PaymentButtons order={ orderTestData } />
			</ProvideTestState>
		);

		expect( screen.getByText( 'Purchase label' ) ).toBeEnabled();
	} );

	it( 'enables purchase button for valid extra label purchase', () => {
		initialContext.shipment.selections = { 0: [ { id: 1 } ] };
		render(
			<LabelPurchaseContext.Provider value={ initialContext }>
				<PaymentButtons order={ { id: 1 } } />
			</LabelPurchaseContext.Provider>
		);

		const purchaseButton = screen.getByText( 'Purchase label' );
		expect( purchaseButton ).not.toBeDisabled();
	} );

	it( 'updates shipments from selection when purchasing', async () => {
		const mockUpdateShipments = jest.fn();
		const mockDispatch = jest.fn().mockReturnValue( {
			updateShipments: mockUpdateShipments,
		} );

		jest.spyOn(
			require( '@wordpress/data' ),
			'dispatch'
		).mockImplementation( mockDispatch );

		initialContext.shipment.selections = { 0: [ { id: 1 } ] };
		initialContext.shipment.setShipments = jest.fn();
		render(
			<LabelPurchaseContext.Provider value={ initialContext }>
				<PaymentButtons
					order={ {
						id: 1,
						line_items: [ { id: 1 } ],
						shipping_address: { country: 'US' },
					} }
				/>
			</LabelPurchaseContext.Provider>
		);

		const purchaseButton = screen.getByText( 'Purchase label' );
		await fireEvent.click( purchaseButton );

		expect( initialContext.shipment.setShipments ).toHaveBeenCalled();
		expect( mockDispatch ).toHaveBeenCalled();
		expect( mockUpdateShipments ).toHaveBeenCalled();
	} );

	it( 'correctly handles sub-item selection with parent ID matching for extra label purchase', async () => {
		const mockUpdateShipments = jest.fn();
		const mockDispatch = jest.fn().mockReturnValue( {
			updateShipments: mockUpdateShipments,
		} );

		jest.spyOn(
			require( '@wordpress/data' ),
			'dispatch'
		).mockImplementation( mockDispatch );

		// Create test data with items that have sub-items
		const shipmentsWithSubItems = {
			0: [
				{
					id: 1,
					quantity: 2,
					subItems: [
						{
							id: '1-sub-0',
							quantity: 1,
							subItems: [],
						},
						{
							id: '1-sub-1',
							quantity: 1,
							subItems: [],
						},
					],
				},
			],
		};

		// Mock context with sub-items and selections that include parent ID
		const contextWithSubItems = {
			...initialContext,
			shipment: {
				...initialContext.shipment,
				shipments: shipmentsWithSubItems,
				selections: { 0: [ { id: 1 } ] }, // Parent item selected
				setShipments: jest.fn(),
			},
		};

		render(
			<LabelPurchaseContext.Provider value={ contextWithSubItems }>
				<PaymentButtons
					order={ {
						id: 1,
						line_items: [ { id: 1 } ],
						shipping_address: { country: 'US' },
					} }
				/>
			</LabelPurchaseContext.Provider>
		);

		const purchaseButton = screen.getByText( 'Purchase label' );
		await fireEvent.click( purchaseButton );

		// Verify that setShipments was called with the correct updated shipment
		expect( contextWithSubItems.shipment.setShipments ).toHaveBeenCalled();

		// Get the call arguments to verify the shipment structure
		const setShipmentsCall =
			contextWithSubItems.shipment.setShipments.mock.calls[ 0 ][ 0 ];
		const updatedShipment = setShipmentsCall[ 0 ];

		// Verify that the parent item is included with its sub-items
		expect( updatedShipment[ 0 ] ).toEqual(
			expect.objectContaining( {
				id: 1,
				subItems: expect.arrayContaining( [
					expect.objectContaining( { id: '1-sub-0' } ),
					expect.objectContaining( { id: '1-sub-1' } ),
				] ),
				quantity: 2, // Should use original quantity when all sub-items are selected
			} )
		);

		expect( mockDispatch ).toHaveBeenCalled();
		expect( mockUpdateShipments ).toHaveBeenCalled();
	} );

	it( 'correctly handles individual sub-item selection for extra label purchase', async () => {
		const mockUpdateShipments = jest.fn();
		const mockDispatch = jest.fn().mockReturnValue( {
			updateShipments: mockUpdateShipments,
		} );

		jest.spyOn(
			require( '@wordpress/data' ),
			'dispatch'
		).mockImplementation( mockDispatch );

		// Create test data with items that have sub-items
		const shipmentsWithSubItems = {
			0: [
				{
					id: 1,
					quantity: 2,
					subItems: [
						{
							id: '1-sub-0',
							quantity: 1,
							subItems: [],
						},
						{
							id: '1-sub-1',
							quantity: 1,
							subItems: [],
						},
					],
				},
			],
		};

		// Mock context with sub-items and selections that include only one sub-item
		const contextWithSubItems = {
			...initialContext,
			shipment: {
				...initialContext.shipment,
				shipments: shipmentsWithSubItems,
				selections: { 0: [ { id: '1-sub-0' } ] }, // Only one sub-item selected
				setShipments: jest.fn(),
			},
		};

		render(
			<LabelPurchaseContext.Provider value={ contextWithSubItems }>
				<PaymentButtons
					order={ {
						id: 1,
						line_items: [ { id: 1 } ],
						shipping_address: { country: 'US' },
					} }
				/>
			</LabelPurchaseContext.Provider>
		);

		const purchaseButton = screen.getByText( 'Purchase label' );
		await fireEvent.click( purchaseButton );

		// Verify that setShipments was called with the correct updated shipment
		expect( contextWithSubItems.shipment.setShipments ).toHaveBeenCalled();

		// Get the call arguments to verify the shipment structure
		const setShipmentsCall =
			contextWithSubItems.shipment.setShipments.mock.calls[ 0 ][ 0 ];
		const updatedShipment = setShipmentsCall[ 0 ];

		// Verify that only the selected sub-item is included
		expect( updatedShipment[ 0 ] ).toEqual(
			expect.objectContaining( {
				id: 1,
				subItems: [ expect.objectContaining( { id: '1-sub-0' } ) ],
				quantity: 1, // Should be 1 since only one sub-item is selected
			} )
		);

		expect( mockDispatch ).toHaveBeenCalled();
		expect( mockUpdateShipments ).toHaveBeenCalled();
	} );
} );
