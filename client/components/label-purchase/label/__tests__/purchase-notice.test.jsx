import { render, screen } from '@testing-library/react';
import { PurchaseNotice } from '../purchase-notice';
import { useLabelPurchaseContext } from 'context/label-purchase';

jest.mock( 'context/label-purchase' );

jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
	return mockUtils();
} );

describe( 'PurchaseNotice', () => {
	let mockContextValues;

	beforeEach( () => {
		mockContextValues = {
			labels: {
				printLabel: jest.fn(),
				isPurchasing: false,
				isUpdatingStatus: false,
				isPrinting: false,
				isRefunding: false,
				hasPurchasedLabel: jest.fn( () => true ),
				updatePurchaseStatus: jest.fn(),
				getCurrentShipmentLabel: jest.fn( () => ( {
					labelId: '123',
					isLegacy: false,
					name: 'Test Label',
				} ) ),
				labelStatusUpdateErrors: [],
				selectedLabelSize: {
					key: '4x6',
					name: '4x6',
				},
				setLabelSize: jest.fn(),
				paperSizes: [
					{
						key: '4x6',
						name: '4x6',
					},
				],
			},
			shipment: {
				isShipmentAutogeneratedFromLabel: jest.fn( () => false ),
			},
			rates: {
				getSelectedRate: jest.fn( () => {
					return { rate: { serviceId: 'First' } };
				} ),
			},
		};

		useLabelPurchaseContext.mockReturnValue( mockContextValues );
	} );

	test( 'displays the heading based on isPurchasing and isUpdatingStatus', () => {
		mockContextValues.labels.isPurchasing = true;
		useLabelPurchaseContext.mockReturnValue( mockContextValues );
		const { rerender } = render( <PurchaseNotice /> );
		expect(
			screen.getByText(
				/Please wait while we process your shipping label purchase/i
			)
		).toBeInTheDocument();

		mockContextValues.labels.isUpdatingStatus = false;
		mockContextValues.labels.isUpdatingStatus = true;
		useLabelPurchaseContext.mockReturnValue( mockContextValues );
		rerender( <PurchaseNotice /> );
		expect(
			screen.getByText(
				/Please wait while we process your shipping label purchase/i
			)
		).toBeInTheDocument();

		mockContextValues.labels.isPurchasing = false;
		mockContextValues.labels.isUpdatingStatus = false;
		useLabelPurchaseContext.mockReturnValue( mockContextValues );
		rerender( <PurchaseNotice /> );
		expect(
			screen.getByText( /Your shipping label is ready to print/i )
		).toBeInTheDocument();
	} );

	test( 'displays the error heading when there are labelStatusUpdateErrors', () => {
		mockContextValues.hasPurchasedLabel = jest.fn( () => false );
		mockContextValues.labels.labelStatusUpdateErrors = [ 'Error message' ];
		useLabelPurchaseContext.mockReturnValue( mockContextValues );
		render( <PurchaseNotice /> );
		expect(
			screen.getByText( /There was an issue processing your purchase/i )
		).toBeInTheDocument();
	} );

	test( 'displays fallback notice when isShipmentAutogeneratedFromLabel returns true', () => {
		// Override the shipment part of the context so that the fallback callback returns true.
		mockContextValues.shipment = {
			isShipmentAutogeneratedFromLabel: jest.fn( () => true ),
		};
		useLabelPurchaseContext.mockReturnValue( mockContextValues );
		render( <PurchaseNotice /> );
		expect(
			screen.getByText(
				/This shipping label was created via the mobile app and/i
			)
		).toBeInTheDocument();
	} );

	test( 'does not display fallback notice when isShipmentAutogeneratedFromLabel returns false', () => {
		// Override the shipment context so that the fallback callback returns false.
		mockContextValues.shipment = {
			isShipmentAutogeneratedFromLabel: jest.fn( () => false ),
		};
		useLabelPurchaseContext.mockReturnValue( mockContextValues );
		render( <PurchaseNotice /> );
		expect(
			screen.queryByText(
				/This shipping label was created via the mobile app and/i
			)
		).toBeNull();
	} );
} );
