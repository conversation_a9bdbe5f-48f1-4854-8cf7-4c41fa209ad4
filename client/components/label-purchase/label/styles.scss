@import "./refund";
@import "./legacy-warning";

.purchase-notice {
	border: none;
	color: var(--gutenberg-gray-900, #1e1e1e);
	font-size: 13px;
	margin: 0;
	padding: 16px;

	p {
		margin-top: 0;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.components-notice__content {
		box-sizing: border-box;
		max-width: 100%;
	}

	.components-button {
		border-radius: 2px;
		box-shadow: inset 0 0 0 1px var(--wp-green-green-70, #005c12);
		color: 0 0 0 1px var(--wp-green-green-70, #005c12);

		&.is-primary {
			background: var(--wp-green-green-70, #005c12);
			color: #fff;

			&:disabled {
				color: rgba(255, 255, 255, 0.8);
				opacity: 0.4;
				border-color: #ccc;
			}

			&:hover:not(:disabled) {
				background: var(--wp-green-green-70-darker-20, #003301ff);
			}

			&.is-busy {
				background-image:
					linear-gradient(-45deg, var(--wp-components-color-accent, var(--wp-green-green-70, #005c12))
					33%, var(--wp-components-color-accent-darker-20, var(--wp-green-green-70-darker-20, #003301ff))
					33%, var(--wp-components-color-accent-darker-20, var(--wp-green-green-70-darker-20, #003301ff))
					70%, var(--wp-components-color-accent, var(--wp-green-green-70, #005c12))
					70%);
				border-color: var(--wp-green-green-70, #005c12);
				color: #fff;
			}
		}

		&.is-tertiary {
			border: none;
			box-shadow: none;
		}
	}

	.components-dropdown button.has-icon {
		flex-direction: row-reverse;
	}

	.components-popover__content {
		min-width: 140px;

		button {
			border-radius: 0;
			box-shadow: none;
			display: flex;
			justify-content: space-between;
			text-align: left;
			width: 100%;
		}
	}

	.purchase-notice-actions {

		.components-dropdown,
		.components-button {
			justify-content: center;
			text-align: center;

			.components-button {
				width: 100%;
			}

			&.is-secondary.print-packing-slip-button {
				color: var(--wp-green-green-70, #005c12);

				&:hover {
					border-color: var(--wp-green-green-70-darker-20, #003301);
					box-shadow: inset 0 0 0 1px var(--wp-green-green-70-darker-20, #003301);
					color: var(--wp-green-green-70-darker-20, #003301);
				}
			}
		}

		.components-divider {
			background: var(--Gutenberg-Gray-300, #ddd);
			height: 20px;
		}
	}

	a {
		align-items: center;
		box-sizing: border-box;
		color: var(--Gutenberg-Gray-900, #1e1e1e);
		display: inline-flex;
		gap: 8px;
		height: 36px;
		justify-content: center;
		padding: 8px;
		text-decoration: none;

		&:first-child {
			padding-left: 0;
		}

		svg {
			display: inline-block;
			height: 24px;
			width: 24px;
		}
	}
}
