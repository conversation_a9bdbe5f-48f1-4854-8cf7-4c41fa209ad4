import { render } from '@testing-library/react';
import { LabelPurchaseContext } from 'context/label-purchase';
import { registerLabelPurchaseStore } from 'data/label-purchase';
import { ShippingRates } from '../shipping-rates';
import { NoRatesAvailable } from '../no-rates-available';
import { rates } from 'utils/__tests__/fixtures/rates';
import { SHIPPING_SERVICE_SECTION } from 'components/label-purchase/essential-details/constants';
import { camelCaseKeys } from 'utils';

jest.mock( 'utils', () => {
	const {
		packagesSettings,
	} = require( 'utils/__tests__/fixtures/package-settings' );
	const utils = jest.requireActual( 'utils' );
	return {
		__esModule: true,
		...utils,
		getConfig: () => ( {
			packagesSettings,
			shippingLabelData: {
				storedData: {
					selectedDestination: {
						city: 'San Francisco',
						country: 'US',
						state: 'CA',
						zip: '94103',
						company: 'WooCommerce',
						email: '<EMAIL>',
					},
				},
			},
		} ),
		getIsDestinationVerified: () => false,
		getCurrentOrderShipTo: () => ( {} ),
		getStoreOrigin: () => ( {
			country: 'US',
			state: 'CA',
		} ),
		getPurchasedLabels: () => ( {
			0: null,
		} ),
		getSelectedRates: () => null,
		getSelectedHazmat: () => null,
		getOriginAddresses: () => [],
		getFirstSelectableOriginAddress: () => ( {} ),
		getLabelDestinations: () => [
			{
				city: 'San Francisco',
				country: 'US',
				state: 'CA',
				zip: '94103',
				company: 'WooCommerce',
				email: '<EMAIL>',
			},
		],
		getLabelOrigins: () => [
			{
				city: 'San Francisco',
				country: 'US',
				state: 'CA',
				zip: '94103',
				company: 'WooCommerce',
				email: '<EMAIL>',
			},
		],
		getCustomsInformation: () => '',
		getSelectedRateOptions: () => ( {} ),
	};
} );

describe( 'Shipping Services', () => {
	const initialValue = {
		shipment: {
			currentShipmentId: 0,
		},
		rates: {
			isFetching: false,
			getSelectedRate: jest.fn(),
			sortRates: jest
				.fn()
				.mockReturnValue(
					rates[ 0 ].default.rates.map( camelCaseKeys )
				),
			preselectRateBasedOnLastSelections: jest.fn(),
			getSelectedRateOptions: jest.fn(),
			selectRateOption: jest.fn(),
		},
		essentialDetails: {
			focusArea: SHIPPING_SERVICE_SECTION,
		},
		storeCurrency: {
			formatAmount: ( value ) => value,
		},
	};
	beforeAll( () => {
		registerLabelPurchaseStore();
	} );

	it( 'should render ShippingRates properly', () => {
		const { asFragment } = render(
			<LabelPurchaseContext.Provider value={ initialValue }>
				<ShippingRates
					availableRates={ {
						usps: rates[ 0 ].default.rates.map( camelCaseKeys ),
					} }
					isFetching={ false }
				/>
			</LabelPurchaseContext.Provider>
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render NoRatesAvailable component properly', () => {
		const { asFragment } = render( <NoRatesAvailable /> );
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should filter out undefined rates when selecting carriers', () => {
		const availableRates = {
			usps: [ { rateId: 1 } ],
			fedex: undefined,
			ups: [ { rateId: 2 } ],
		};

		const { asFragment } = render(
			<LabelPurchaseContext.Provider value={ initialValue }>
				<ShippingRates
					availableRates={ availableRates }
					selectedCarriers={ [ 'usps', 'fedex', 'ups' ] }
					isFetching={ false }
				/>
			</LabelPurchaseContext.Provider>
		);

		// Should only show USPS and UPS rates, filtering out undefined FedEx rates
		expect( asFragment() ).toMatchSnapshot();
	} );
} );
