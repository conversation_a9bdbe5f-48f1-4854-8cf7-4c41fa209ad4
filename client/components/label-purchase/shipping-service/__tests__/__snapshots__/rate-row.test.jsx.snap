// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Shipping Services - RateRow should render RateRow properly 1`] = `
<DocumentFragment>
  <input
    name="shipping-rate"
    type="radio"
  />
  <label
    class="components-flex selected css-1ce5ub7-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <span />
    <div
      class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="FlexBlock"
    >
      <div
        class="components-flex css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Flex"
      >
        <span
          class="components-truncate components-text css-2y3sct-PolymorphicDiv-Text-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Text"
        >
          USPS - Parcel Select Mail
        </span>
        <div
          class="components-flex rate-extras css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <div
            class="components-flex css-1ub13jk-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <svg
              aria-hidden="true"
              focusable="false"
              height="20"
              viewBox="0 0 24 24"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"
              />
            </svg>
            <span
              class="components-truncate components-text css-1kv768g-PolymorphicDiv-Text-sx-Base e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="Text"
            >
              tracking
            </span>
          </div>
          <div
            class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <div
              class="components-base-control components-checkbox-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-h-stack css-1m5h1bg-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="HStack"
                >
                  <span
                    class="components-checkbox-control__input-container"
                  >
                    <input
                      checked=""
                      class="components-checkbox-control__input"
                      id="inspector-checkbox-control-0"
                      type="checkbox"
                      value="1"
                    />
                    <svg
                      aria-hidden="true"
                      class="components-checkbox-control__checked"
                      focusable="false"
                      height="24"
                      role="presentation"
                      viewBox="0 0 24 24"
                      width="24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"
                      />
                    </svg>
                  </span>
                  <label
                    class="components-checkbox-control__label"
                    for="inspector-checkbox-control-0"
                  >
                    Signature Required ( +$3.40 )
                  </label>
                </div>
              </div>
            </div>
          </div>
          <div
            class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <div
              class="components-base-control components-checkbox-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-h-stack css-1m5h1bg-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="HStack"
                >
                  <span
                    class="components-checkbox-control__input-container"
                  >
                    <input
                      checked=""
                      class="components-checkbox-control__input"
                      id="inspector-checkbox-control-1"
                      type="checkbox"
                      value="1"
                    />
                    <svg
                      aria-hidden="true"
                      class="components-checkbox-control__checked"
                      focusable="false"
                      height="24"
                      role="presentation"
                      viewBox="0 0 24 24"
                      width="24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"
                      />
                    </svg>
                  </span>
                  <label
                    class="components-checkbox-control__label"
                    for="inspector-checkbox-control-1"
                  >
                    Adult Signature Required ( +$-0.33 )
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="components-flex-item css-115jeco-PolymorphicDiv-Item-sx-Base e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="FlexItem"
    >
      <div
        class="components-flex css-1h9seei-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Flex"
      >
        <data
          aria-label="rate-price"
          value="17.41"
        >
          $17.41
        </data>
      </div>
    </div>
  </label>
</DocumentFragment>
`;
