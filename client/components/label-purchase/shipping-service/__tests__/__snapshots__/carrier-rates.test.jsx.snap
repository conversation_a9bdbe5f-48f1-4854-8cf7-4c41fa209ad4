// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Shipping Services - CarrierRates should render CarrierRates properly 1`] = `
<DocumentFragment>
  <div
    class="components-flex carrier-rates css-1d8y48u-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <input
      id="rate_f23e85e96a3640a88d787a0daf871143"
      name="shipping-rate"
      type="radio"
    />
    <label
      class="components-flex css-1ce5ub7-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
      for="rate_f23e85e96a3640a88d787a0daf871143"
    >
      <div
        class="carrier-icon"
        style="width: 54px; max-width: 54px; background-image: url(test-file-stub); background-repeat: no-repeat; background-position-x: center; background-position-y: center; height: 100%; min-height: 54px; background-size: contain;"
      />
      <div
        class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexBlock"
      >
        <div
          class="components-flex css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <span
            class="components-truncate components-text css-2y3sct-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            USPS - Parcel Select Mail
          </span>
          <span
            class="components-truncate components-text rate-extras css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            includes tracking, free pickup
          </span>
        </div>
      </div>
      <div
        class="components-flex-item css-115jeco-PolymorphicDiv-Item-sx-Base e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexItem"
      >
        <div
          class="components-flex css-1h9seei-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <data
            aria-label="rate-price"
            value="17.41"
          >
            $17.41
          </data>
          <time>
            5 business days
          </time>
        </div>
      </div>
    </label>
    <input
      id="rate_04e821e2df5140719a669be2ba2c1b14"
      name="shipping-rate"
      type="radio"
    />
    <label
      class="components-flex css-1ce5ub7-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
      for="rate_04e821e2df5140719a669be2ba2c1b14"
    >
      <div
        class="carrier-icon"
        style="width: 54px; max-width: 54px; background-image: url(test-file-stub); background-repeat: no-repeat; background-position-x: center; background-position-y: center; height: 100%; min-height: 54px; background-size: contain;"
      />
      <div
        class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexBlock"
      >
        <div
          class="components-flex css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <span
            class="components-truncate components-text css-2y3sct-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            USPS - Priority Mail
          </span>
          <span
            class="components-truncate components-text rate-extras css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            includes tracking, insurance (up to $100.00), free pickup
          </span>
        </div>
      </div>
      <div
        class="components-flex-item css-115jeco-PolymorphicDiv-Item-sx-Base e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexItem"
      >
        <div
          class="components-flex css-1h9seei-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <data
            aria-label="rate-price"
            value="51.77"
          >
            $51.77
          </data>
          <time>
            2 business days
          </time>
        </div>
      </div>
    </label>
    <input
      id="rate_027277bd723447b88cafafab5c3eab1c"
      name="shipping-rate"
      type="radio"
    />
    <label
      class="components-flex css-1ce5ub7-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
      for="rate_027277bd723447b88cafafab5c3eab1c"
    >
      <div
        class="carrier-icon"
        style="width: 54px; max-width: 54px; background-image: url(test-file-stub); background-repeat: no-repeat; background-position-x: center; background-position-y: center; height: 100%; min-height: 54px; background-size: contain;"
      />
      <div
        class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexBlock"
      >
        <div
          class="components-flex css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <span
            class="components-truncate components-text css-2y3sct-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            USPS - Express Mail
          </span>
          <span
            class="components-truncate components-text rate-extras css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            includes tracking, insurance (up to $100.00), free pickup
          </span>
        </div>
      </div>
      <div
        class="components-flex-item css-115jeco-PolymorphicDiv-Item-sx-Base e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexItem"
      >
        <div
          class="components-flex css-1h9seei-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <data
            aria-label="rate-price"
            value="133.65"
          >
            $133.65
          </data>
          <time>
            November 25
          </time>
        </div>
      </div>
    </label>
    <input
      id="rate_027438cfda8d4bb5b3325e1de7289f89"
      name="shipping-rate"
      type="radio"
    />
    <label
      class="components-flex css-1ce5ub7-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
      for="rate_027438cfda8d4bb5b3325e1de7289f89"
    >
      <div
        class="carrier-icon"
        style="width: 54px; max-width: 54px; background-image: url(test-file-stub); background-repeat: no-repeat; background-position-x: center; background-position-y: center; height: 100%; min-height: 54px; background-size: contain;"
      />
      <div
        class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexBlock"
      >
        <div
          class="components-flex css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <span
            class="components-truncate components-text css-2y3sct-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            USPS - Ground Advantage
          </span>
          <span
            class="components-truncate components-text rate-extras css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            includes tracking, insurance (up to $100.00), free pickup
          </span>
        </div>
      </div>
      <div
        class="components-flex-item css-115jeco-PolymorphicDiv-Item-sx-Base e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexItem"
      >
        <div
          class="components-flex css-1h9seei-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <data
            aria-label="rate-price"
            value="8.03"
          >
            $8.03
          </data>
        </div>
      </div>
    </label>
    <input
      id="rate_1b16134270b74a2da02df665c6a010c8"
      name="shipping-rate"
      type="radio"
    />
    <label
      class="components-flex css-1ce5ub7-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
      for="rate_1b16134270b74a2da02df665c6a010c8"
    >
      <div
        class="carrier-icon"
        style="width: 54px; max-width: 54px; background-image: url(test-file-stub); background-repeat: no-repeat; background-position-x: center; background-position-y: center; height: 100%; min-height: 54px; background-size: contain;"
      />
      <div
        class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexBlock"
      >
        <div
          class="components-flex css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <span
            class="components-truncate components-text css-2y3sct-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            USPS - First Class Mail
          </span>
          <span
            class="components-truncate components-text rate-extras css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            includes tracking, free pickup
          </span>
        </div>
      </div>
      <div
        class="components-flex-item css-115jeco-PolymorphicDiv-Item-sx-Base e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexItem"
      >
        <div
          class="components-flex css-1h9seei-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <data
            aria-label="rate-price"
            value="8.03"
          >
            $8.03
          </data>
        </div>
      </div>
    </label>
    <input
      id="rate_622b2d2a189445d09e9c3693f7993e44"
      name="shipping-rate"
      type="radio"
    />
    <label
      class="components-flex css-1ce5ub7-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
      for="rate_622b2d2a189445d09e9c3693f7993e44"
    >
      <div
        class="carrier-icon"
        style="width: 54px; max-width: 54px; background-image: url(test-file-stub); background-repeat: no-repeat; background-position-x: center; background-position-y: center; height: 100%; min-height: 54px; background-size: contain;"
      />
      <div
        class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexBlock"
      >
        <div
          class="components-flex css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <span
            class="components-truncate components-text css-2y3sct-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            FedEx - Parcel Select Mail
          </span>
          <span
            class="components-truncate components-text rate-extras css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            includes tracking, free pickup
          </span>
        </div>
      </div>
      <div
        class="components-flex-item css-115jeco-PolymorphicDiv-Item-sx-Base e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexItem"
      >
        <div
          class="components-flex css-1h9seei-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <data
            aria-label="rate-price"
            value="8.03"
          >
            $8.03
          </data>
        </div>
      </div>
    </label>
    <input
      id="rate_c08844fdc75945849b8865a22dddfd05"
      name="shipping-rate"
      type="radio"
    />
    <label
      class="components-flex css-1ce5ub7-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
      for="rate_c08844fdc75945849b8865a22dddfd05"
    >
      <div
        class="carrier-icon"
        style="width: 54px; max-width: 54px; background-image: url(test-file-stub); background-repeat: no-repeat; background-position-x: center; background-position-y: center; height: 100%; min-height: 54px; background-size: contain;"
      />
      <div
        class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexBlock"
      >
        <div
          class="components-flex css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <span
            class="components-truncate components-text css-2y3sct-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            DHL - Priority Mail
          </span>
          <span
            class="components-truncate components-text rate-extras css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            includes tracking, insurance (up to $100.00), free pickup
          </span>
        </div>
      </div>
      <div
        class="components-flex-item css-115jeco-PolymorphicDiv-Item-sx-Base e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexItem"
      >
        <div
          class="components-flex css-1h9seei-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <data
            aria-label="rate-price"
            value="10.85"
          >
            $10.85
          </data>
        </div>
      </div>
    </label>
    <input
      id="rate_7204f45db2bb4d819de17de9b695bd67"
      name="shipping-rate"
      type="radio"
    />
    <label
      class="components-flex css-1ce5ub7-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
      for="rate_7204f45db2bb4d819de17de9b695bd67"
    >
      <div
        class="carrier-icon"
        style="width: 54px; max-width: 54px; background-image: url(test-file-stub); background-repeat: no-repeat; background-position-x: center; background-position-y: center; height: 100%; min-height: 54px; background-size: contain;"
      />
      <div
        class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexBlock"
      >
        <div
          class="components-flex css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <span
            class="components-truncate components-text css-2y3sct-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            UPS - Express Mail
          </span>
          <span
            class="components-truncate components-text rate-extras css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Text"
          >
            includes tracking, insurance (up to $100.00), free pickup
          </span>
        </div>
      </div>
      <div
        class="components-flex-item css-115jeco-PolymorphicDiv-Item-sx-Base e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexItem"
      >
        <div
          class="components-flex css-1h9seei-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <data
            aria-label="rate-price"
            value="97.8"
          >
            $97.80
          </data>
        </div>
      </div>
    </label>
  </div>
</DocumentFragment>
`;
