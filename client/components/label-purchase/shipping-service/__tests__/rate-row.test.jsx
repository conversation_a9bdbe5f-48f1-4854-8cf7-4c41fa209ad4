import { render } from '@testing-library/react';
import CurrencyFactory from '@woocommerce/currency';
import { LabelPurchaseContext } from 'context/label-purchase';
import { RateRow } from '../carrier-rates/rate-row';
import { rates } from 'utils/__tests__/fixtures/rates';

jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
	return {
		...mockUtils(),
		applyPromo: jest.fn( ( rate, promoId ) => {
			// Mock promo application: return discounted rate if promoId exists
			return promoId ? rate * 0.8 : rate;
		} ),
	};
} );

describe( 'Shipping Services - RateRow', () => {
	const initialValue = {
		storeCurrency: new CurrencyFactory(),
		rates: {
			getSelectedRateOptions: jest.fn(),
			selectRateOption: jest.fn(),
		},
	};

	it( 'should render RateRow properly', () => {
		const rate = rates[ 0 ];
		const props = {
			rate: rate.default.rates[ 0 ],
			adultSignatureRequiredRate:
				rate.adult_signature_required.rates[ 0 ],
			signatureRequiredRate: rate.signature_required.rates[ 0 ],
			selected: '',
			setSelected: jest.fn(),
		};
		const { asFragment } = render(
			<LabelPurchaseContext.Provider value={ initialValue }>
				<RateRow { ...props } />
			</LabelPurchaseContext.Provider>
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render RateRow with promo rate', () => {
		const rate = rates[ 0 ];
		const rateWithPromo = {
			...rate.default.rates[ 0 ],
			promoId: 'test-promo-123',
		};
		const props = {
			rate: rateWithPromo,
			adultSignatureRequiredRate:
				rate.adult_signature_required.rates[ 0 ],
			signatureRequiredRate: rate.signature_required.rates[ 0 ],
			selected: '',
			setSelected: jest.fn(),
		};
		const { getByText, getByLabelText } = render(
			<LabelPurchaseContext.Provider value={ initialValue }>
				<RateRow { ...props } />
			</LabelPurchaseContext.Provider>
		);

		expect( getByText( 'Promo applied' ) ).toBeInTheDocument();
		expect( getByLabelText( 'rate-price' ) ).toBeInTheDocument();
	} );
} );
