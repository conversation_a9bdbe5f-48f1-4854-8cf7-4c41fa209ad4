import { render } from '@testing-library/react';
import { LabelPurchaseContext } from 'context/label-purchase';
import { rates } from 'utils/__tests__/fixtures/rates';
import { CarrierRates } from '../carrier-rates';
import { camelCaseKeys } from 'utils';
import { registerLabelPurchaseStore } from 'data/label-purchase';
import CurrencyFactory from '@woocommerce/currency';

jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
	return mockUtils();
} );

describe( 'Shipping Services - CarrierRates', () => {
	const rate = rates[ 0 ];
	const initialValue = {
		rates: {
			getSelectedRate: () => camelCaseKeys( rate.default.rates[ 0 ] ),
			selectRate: jest.fn(),
			getSelectedRateOptions: jest.fn(),
			selectRateOption: jest.fn(),
		},
		storeCurrency: new CurrencyFactory(),
		shipment: { currentShipmentId: 0 },
		essentialDetails: {
			resetFocusArea: jest.fn(),
			setShippingServiceCompleted: jest.fn(),
		},
	};

	beforeAll( () => {
		registerLabelPurchaseStore();
	} );

	it( 'should render CarrierRates properly', () => {
		const props = {
			rates: rate.default.rates.map( camelCaseKeys ),
		};
		const { asFragment } = render(
			<LabelPurchaseContext.Provider value={ initialValue }>
				<CarrierRates { ...props } />
			</LabelPurchaseContext.Provider>
		);
		expect( asFragment() ).toMatchSnapshot();
	} );
} );
