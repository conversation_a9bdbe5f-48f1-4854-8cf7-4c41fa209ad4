import { render } from '@testing-library/react';
import CurrencyFactory from '@woocommerce/currency';
import { rates } from 'utils/__tests__/fixtures/rates';
import { RowExtras } from 'components/label-purchase/shipping-service/carrier-rates/row-extras';

jest.mock( 'utils', () => {
	const {
		packagesSettings,
	} = require( 'utils/__tests__/fixtures/package-settings' );
	return {
		__esModule: true,
		getConfig: () => ( {
			packagesSettings,
		} ),
		getIsDestinationVerified: () => false,
		getCurrentOrderShipTo: jest.fn(),
		camelCasePackageResponse: ( packages ) => packages,
	};
} );

describe( 'Shipping Services - RowExtras', () => {
	it( 'should render RateRow properly', () => {
		const rate = rates[ 0 ];
		const props = {
			rate: rate.default.rates[ 0 ],
			adultSignatureRequiredRate:
				rate.adult_signature_required.rates[ 0 ],
			signatureRequiredRate: rate.signature_required.rates[ 0 ],
			selected: '',
			setSelected: jest.fn(),
			extrasText: [ 'test extra text 1', 'test extra text 2' ],
			formatAmount: new CurrencyFactory().formatAmount,
		};
		const { asFragment } = render( <RowExtras { ...props } /> );
		expect( asFragment() ).toMatchSnapshot();
	} );
} );
