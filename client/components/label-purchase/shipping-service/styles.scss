.shipping-rates {
	--dropdown-height: 32px;
	--dropdown-width: 162px;

	&.components-animate__loading {
		pointer-events: none;
	}

	.components-dropdown {
		align-items: flex-end;
		display: flex;
		height: var(--dropdown-height);
		margin-left: -10px;

		> .components-button {
			align-items: center;
			color: var(--gutenberg-gray-900, #1e1e1e);
			display: flex;
			flex-direction: row-reverse;
			justify-content: space-between;
			outline: none;
			padding: 0;
			text-align: center;
			width: 100%;

			&:hover,
			&:focus {
				background: none;
				border: none;
				box-shadow: none;
				outline: none;
			}
		}

		.components-popover__content {
			box-sizing: border-box;
			width: var(--dropdown-width);

			> * {
				width: 100%;
			}

			.components-menu-item__item {
				min-width: unset;
			}

			.components-checkbox-control {
				padding: 12px 8px;

				&__input-container {
					height: 100%;
				}
			}

			.components-button {
				height: 36px;
				justify-content: center;
			}

			.components-base-control__field {
				display: flex;

				.components-checkbox-control__label {
					gap: 8px;
					cursor: pointer;
					display: flex;
				}
			}
		}
	}

	.carrier-rates {
		--row-height: 86px;
		width: 100%;

		> label {
			align-items: flex-start;

			align-self: stretch;
			background: var(--gutenberg-white, #fff);
			border: 1px solid var(--gutenberg-gray-400, #ccc);
			border-radius: var(--grid-unit-05, 4px);
			box-sizing: border-box;
			color: var(--gutenberg-gray-900, #1e1e1e);
			cursor: pointer;
			height: var(--row-height);
			line-height: 18px;
			max-height: var(--row-height);
			padding: 16px;
			overflow: hidden;
			transition:
				color ease 300ms,
				max-height ease 300ms;

			&.has-rate-caveat {
				height: var(--row-height + 20px);
				max-height: var(--row-height + 20px);

				.components-flex:first-child {
					gap: 0;

					> span:not(:first-child):not(:nth-child(2)) {
						margin-top: 8px;
					}
				}
			}

			.rate-caveat {
				margin-top: 4px;
				color: var(--gutenberg-gray-700, #757575);
			}

			.carrier-icon {
				border-radius: 4px;
			}

			.rate-extras {
				color: var(--gutenberg-gray-700, #757575);
				margin-top: 8px;

				&::first-letter,
				.components-text::first-letter {
					text-transform: uppercase;
				}
			}

			data {
				color: var(--gutenberg-gray-900, #1e1e1e);
				font-size: 14px;
				font-style: normal;
				font-weight: 600;

				s {
					color: var(--gutenberg-gray-700, #757575);
					font-weight: 400;
				}
			}

			time {
				color: var(--gutenberg-gray-700, #757575);
				font-size: 12px;
				font-weight: 400;
				line-height: 16px;
			}
		}

		input[type="radio"] {
			display: none;
		}

		label.selected,
		input[type="radio"]:checked + label {
			background: var(--wp-blue-blue-0, #f0f6fc);
			border: 1px solid var(--gutenberg-blue, #007cba);
			height: 100%;
			max-height: 300px;

			/**
* we'll transitioning based on the max-height as
* we can't use css transitions on height:100%
*/
			outline: 1px solid var(--gutenberg-blue, #007cba);

			.rate-extras {
				color: var(--gutenberg-gray-700, #1e1e1e);
			}
		}
	}

	.shipping-rates-notice {
		margin-bottom: 12px;
		margin-top: 12px;
	}
}

.label-purchase-rates__placeholder {
	background: #fff;
	border: 1px dashed #ccc;
	border-radius: 2px;
	box-sizing: border-box;
	height: 330px;
	padding: 40px;

	p {
		font-weight: 600;
	}
}
