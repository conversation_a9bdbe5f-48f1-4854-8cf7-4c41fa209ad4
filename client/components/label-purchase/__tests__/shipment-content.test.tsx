import {
	act,
	fireEvent,
	render,
	screen,
	waitFor,
} from '@testing-library/react';
import { merge } from 'lodash';
import { ShipmentContent } from '../shipment-content';
import { LABEL_PURCHASE_STATUS } from 'data/constants';
import {
	useLabelPurchaseContext,
	LabelPurchaseContextType,
} from 'context/label-purchase';
import { registerLabelPurchaseStore } from 'data/label-purchase';
import { registerAddressStore } from 'data/address';
import { DeepPartial } from 'types';
import { registerCarrierStrategyStore } from 'data/carrier-strategy';

registerLabelPurchaseStore();
registerAddressStore( true );
registerCarrierStrategyStore();

jest.mock( '@wordpress/data', () => {
	const originalModule = jest.requireActual( '@wordpress/data' );
	return {
		...originalModule,
		select: jest.fn().mockReturnValue( {
			getOrderStatus: jest.fn().mockReturnValue( 'processing' ),
			getUPSDAPCarrierStrategyForAddressId: jest.fn().mockReturnValue( {
				hasAgreedToTos: true,
			} ),
		} ),
		useSelect: jest.fn().mockImplementation( ( selector, deps ) => {
			// Check if this is the isDismissed call from ShippingDateSpotlight
			if ( typeof selector === 'function' ) {
				// Create a mock select function that returns an object with a get method
				const mockSelect = ( storeSelector: { name: string } ) => {
					if ( storeSelector.name === 'core/preferences' ) {
						return {
							get: ( scope: string, key: string ) => {
								// Return false specifically for the shipping date spotlight preference
								if (
									scope === 'woocommerce-shipping' &&
									key === 'shipping-date-spotlight-dismissed'
								) {
									return false;
								}

								// For other preferences, return undefined or a default value
								return "Can't find it check here";
							},
						};
					}
					// For other stores, use the original select implementation if possible
					return originalModule.select
						? originalModule.select( storeSelector, deps )
						: {};
				};

				// Call the selector with our mock select function
				return selector( mockSelect );
			}

			// For all other useSelect calls, use the original implementation
			return originalModule.useSelect( selector, deps );
		} ),
	};
} );

jest.mock( 'context/label-purchase', () => ( {
	useLabelPurchaseContext: jest.fn(),
} ) );

jest.mock( 'utils/config', () => {
	return {
		getConfig: jest.fn().mockReturnValue( {
			packagesSettings: {},
			shippingLabelData: {
				currentOrderLabels: [],
			},
			origin_addresses: [],
			order: {
				shipping_address: {},
			},
		} ),
	};
} );

jest.mock( 'utils/payment', () => ( {
	hasPaymentMethod: jest.fn().mockReturnValue( true ),
	hasSelectedPaymentMethod: jest.fn().mockReturnValue( true ),
	canManagePayments: jest.fn().mockReturnValue( true ),
} ) );

jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
	return mockUtils( {
		getCurrentOrder: jest.fn().mockReturnValue( {
			id: 1,
			line_items: [],
			shipping_address: {
				country: 'US',
			},
		} ),
		getCarrierStrategies: jest.fn().mockReturnValue( {
			upsdap: {
				originAddress: {
					store_details: {
						has_agreed_to_tos: false,
					},
				},
			},
		} ),
	} );
} );

describe( 'ShipmentContent', () => {
	const purchaseError = 'Purchase error message';

	const mockContext = (
		overrides: DeepPartial< LabelPurchaseContextType > = {},
		labelStatusUpdateErrors = [ purchaseError ]
	) => {
		const getCurrentShipmentLabel = jest.fn().mockReturnValue( {
			status: LABEL_PURCHASE_STATUS.PURCHASE_ERROR,
		} );
		// Mock the context values
		( useLabelPurchaseContext as jest.Mock ).mockReturnValue(
			merge(
				{
					labels: {
						hasPurchasedLabel: jest.fn().mockReturnValue( false ),
						hasRequestedRefund: jest.fn().mockReturnValue( false ),
						getCurrentShipmentLabel,
						selectedLabelSize: jest.fn().mockReturnValue( {} ),
						labelStatusUpdateErrors,
						isCurrentTabPurchasingExtraLabel: jest
							.fn()
							.mockReturnValue( false ),
					},
					customs: {
						isCustomsNeeded: jest.fn().mockReturnValue( false ),
						getCustomsState: jest.fn(),
					},
					shipment: {
						currentShipmentId: '1',
						shipments: {
							0: {},
							1: {},
						},
						getShipmentOrigin: jest.fn().mockReturnValue( {
							address: '',
							address1: '',
							address2: '',
							city: '',
							id: 'address_12345',
							isVerified: true,
						} ),
						getShipmentDestination: jest.fn().mockReturnValue( {
							address: '',
							address1: '',
							address2: '',
							city: '',
						} ),
						getCurrentShipmentDate: jest.fn().mockReturnValue( {
							shippingDate: new Date( '2025-02-25' ),
							estimatedDeliveryDate: new Date( '2025-02-28' ),
						} ),
					},
					rates: {
						isFetching: false,
						getSelectedRate: jest.fn().mockReturnValue( null ),
						updateRates: jest.fn().mockReturnValue( [] ),
						getSelectedRateOptions: jest.fn().mockReturnValue( {} ),
					},
					packages: {
						isCustomPackageTab: jest.fn().mockReturnValue( false ),
						getSelectedPackage: jest.fn().mockReturnValue( null ),
						getCustomPackage: jest.fn().mockReturnValue( {} ),
						setSelectedPackage: jest.fn(),
						setCurrentPackageTab: jest.fn(),
						isPackageSpecified: jest.fn().mockReturnValue( false ),
						currentPackageTab: jest
							.fn()
							.mockReturnValue( 'custom' ),
					},
					hazmat: {
						getShipmentHazmat: jest
							.fn()
							.mockReturnValue( { isHazmat: false } ),
					},
					essentialDetails: {
						focusArea: jest.fn().mockReturnValue( {} ),
						resetFocusArea: jest.fn(),
						isShippingServiceCompleted: jest
							.fn()
							.mockReturnValue( false ),
					},
					account: {
						accountSettings: jest.fn().mockReturnValue( {} ),
						canPurchase: jest.fn().mockReturnValue( true ),
						setAccountCompleteOrder: jest.fn(),
						getAccountCompleteOrder: jest
							.fn()
							.mockReturnValue( false ),
					},
					storeCurrency: {
						formatAmount: jest.fn().mockReturnValue( '$0.00' ),
					},
				},
				overrides
			)
		);
	};

	afterEach( () => {
		jest.restoreAllMocks();
	} );

	it( 'renders PurchaseErrorNotice when there is a purchase error', async () => {
		mockContext();
		await act( async () => {
			render( <ShipmentContent items={ [] } /> );
		} );

		// Check if {purchaseError} is rendered
		expect( screen.getByText( purchaseError ) ).toBeInTheDocument();
	} );

	it( "doesn't render render the content of  PurchaseErrorNotice when there no a purchase error", async () => {
		mockContext(
			{
				customs: {
					getCustomsState: jest.fn(),
					isCustomsNeeded: jest.fn(),
				},
			},
			[]
		);

		await act( async () => {
			render( <ShipmentContent items={ [] } /> );
		} );

		// Check if {purchaseError} is rendered
		expect( screen.queryByText( purchaseError ) ).not.toBeInTheDocument();
	} );

	it( 'shows error when UPS DAP label purchase fails due to missing TOS acceptance', async () => {
		const getSelectedRate = jest.fn().mockReturnValue( {
			rate: {
				carrierId: 'upsdap',
			},
			parent: {
				carrierId: 'upsdap',
			},
		} );

		const errorMessage =
			'You must agree to the UPS® Terms and Conditions to purchase a UPS® label.';
		const requestLabelPurchase = jest.fn().mockRejectedValue( {
			code: 'missing_upsdap_terms_of_service_acceptance',
			message: [ errorMessage ],
		} );

		const getCurrentShipmentLabel = jest.fn().mockReturnValue( {
			status: LABEL_PURCHASE_STATUS.PURCHASE_ERROR,
		} );

		mockContext(
			{
				rates: {
					isFetching: false,
					getSelectedRate,
				},
				labels: {
					requestLabelPurchase,
					getCurrentShipmentLabel,
					labelStatusUpdateErrors: [ errorMessage ],
					hasPurchasedLabel: jest.fn().mockReturnValue( false ),
					hasMissingPurchase: jest.fn().mockReturnValue( true ),
				},
				shipment: {
					currentShipmentId: '1',
					shipments: {
						'0': [
							{
								id: 1,
								name: 'Test Item',
								quantity: 1,
							},
						],
						'1': [
							{
								id: 2,
								name: 'Test Item 2',
								quantity: 1,
							},
						],
					},
					selections: {},
					getShipmentOrigin: jest.fn().mockReturnValue( {
						id: 'address_12345',
						name: 'Test Store',
						address: '123 Test St',
						city: 'Test City',
						state: 'TS',
						postcode: '12345',
						country: 'US',
						isVerified: true,
					} ),
					isExtraLabelPurchaseValid: jest
						.fn()
						.mockReturnValue( true ),
					getCurrentShipmentDate: jest.fn().mockReturnValue( {
						shippingDate: new Date( '2025-02-25' ),
						estimatedDeliveryDate: new Date( '2025-02-28' ),
					} ),
				},
				customs: {
					getCustomsState: jest.fn(),
					isCustomsNeeded: jest.fn(),
				},
			},
			[]
		);

		render( <ShipmentContent items={ [] } /> );

		const purchaseButton = screen.getByRole( 'button', {
			name: /Purchase shipment/i,
		} );
		expect( purchaseButton ).toBeEnabled();

		await act( async () => {
			fireEvent.click( purchaseButton );
		} );

		// Wait for the error messages to appear
		await waitFor(
			() => {
				// Check for error heading.
				expect(
					screen.getByText(
						'An error occurred while purchasing the label'
					)
				).toBeInTheDocument();

				// Check for error notice.
				const errorNotice = screen.getByText( errorMessage );
				expect( errorNotice ).toBeInTheDocument();
				expect(
					errorNotice.closest( '.components-notice.is-error' )
				).toBeInTheDocument();
			},
			{ timeout: 3000 }
		);

		// Assert that the purchase function was called with the correct argument
		expect( requestLabelPurchase ).toHaveBeenCalledWith(
			1,
			expect.objectContaining( {
				parent: { carrierId: 'upsdap' },
				rate: { carrierId: 'upsdap' },
			} )
		);
	} );
} );
