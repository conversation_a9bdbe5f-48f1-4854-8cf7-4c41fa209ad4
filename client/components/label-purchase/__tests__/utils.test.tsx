import { getShipmentSummaryText } from '../utils';
import { getCustomFulfillmentSummary } from 'utils';

// Mock the utils module
jest.mock( 'utils', () => ( {
	getCustomFulfillmentSummary: jest.fn(),
} ) );

describe( 'getShipmentSummaryText', () => {
	beforeEach( () => {
		// Clear mock before each test
		jest.clearAllMocks();
	} );

	it( 'should return custom message when there is 1 or more products not fulfilled by us', () => {
		const customMessage = 'Already fulfilled by third party.';
		( getCustomFulfillmentSummary as jest.Mock ).mockReturnValue(
			customMessage
		);

		const result = getShipmentSummaryText( false, 1, 3 );

		expect( getCustomFulfillmentSummary ).toHaveBeenCalled();
		expect( result ).toEqual(
			expect.objectContaining( {
				props: expect.objectContaining( {
					className: 'wcshipping-shipping-label-meta-box__summary',
					children: customMessage,
				} ),
			} )
		);
	} );

	it( 'should return custom message even if orderFulfilled is set to true', () => {
		const customMessage = 'Already fulfilled by third party.';
		( getCustomFulfillmentSummary as jest.Mock ).mockReturnValue(
			customMessage
		);

		const result = getShipmentSummaryText( true, 1, 3 );

		expect( getCustomFulfillmentSummary ).toHaveBeenCalled();
		expect( result ).toEqual(
			expect.objectContaining( {
				props: expect.objectContaining( {
					className: 'wcshipping-shipping-label-meta-box__summary',
					children: customMessage,
				} ),
			} )
		);
	} );

	it( 'should show fulfilled message when order is fulfilled', () => {
		( getCustomFulfillmentSummary as jest.Mock ).mockReturnValue( '' );

		const result = getShipmentSummaryText( true, 2, 2 );

		expect( result ).toEqual(
			expect.objectContaining( {
				props: expect.objectContaining( {
					className: 'wcshipping-shipping-label-meta-box__summary',
					children: '2 items were fulfilled.',
				} ),
			} )
		);
	} );

	it( 'should show partial fulfillment message when some items are fulfilled', () => {
		( getCustomFulfillmentSummary as jest.Mock ).mockReturnValue( '' );

		const result = getShipmentSummaryText( false, 1, 2 );

		expect( result ).toEqual(
			expect.objectContaining( {
				props: expect.objectContaining( {
					className: 'wcshipping-shipping-label-meta-box__summary',
					children: expect.arrayContaining( [
						'1 item was fulfilled, ',
						'1 item still requires fulfillment.',
					] ),
				} ),
			} )
		);
	} );

	it( 'should show ready to fulfill message when no items are fulfilled', () => {
		( getCustomFulfillmentSummary as jest.Mock ).mockReturnValue( '' );

		const result = getShipmentSummaryText( false, 0, 2 );

		expect( result ).toEqual(
			expect.objectContaining( {
				props: expect.objectContaining( {
					className: 'wcshipping-shipping-label-meta-box__summary',
					children: '2 items are ready to be fulfilled',
				} ),
			} )
		);
	} );
} );
