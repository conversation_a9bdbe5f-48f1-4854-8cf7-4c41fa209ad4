import { render, fireEvent, screen, act } from '@testing-library/react';
import { registerLabelPurchaseStore } from 'data/label-purchase';
import { registerAddressStore } from 'data/address';
import { OrderLabelPurchase } from '../order-label-purchase';

registerLabelPurchaseStore();
registerAddressStore( true );

jest.mock( '@wordpress/data', () => {
	const getOriginAddresses = jest.fn().mockReturnValue( [
		{
			id: 1,
			city: 'San Francisco',
			country: 'US',
			state: 'CA',
			isVerified: true,
		},
	] );
	const getNormalizedAddress = jest.fn().mockReturnValue( {
		city: 'San Francisco',
		country: 'US',
		state: 'CA',
		isVerified: true,
	} );
	return {
		createSelector: jest.fn().mockImplementation( ( fn ) => fn ),
		combineReducers: jest
			.fn()
			.mockImplementation( ( reducers ) => reducers ),
		createReduxStore: jest.fn(),
		registerStore: jest.fn(),
		register: jest.fn(),
		dispatch: jest.fn(),
		subscribe: jest.fn(),
		getState: jest.fn(),
		useSelect: jest.fn().mockImplementation( ( fn ) =>
			fn( () => ( {
				getOrderDestination: jest.fn().mockReturnValue( {
					city: 'San Francisco',
					country: 'US',
					state: 'CA',
					isVerified: true,
				} ),
				getSavedPackages: jest.fn().mockReturnValue( {} ),
				getCustomsInformation: jest.fn().mockReturnValue( {} ),
				getRatesForShipment: jest.fn().mockReturnValue( {} ),
				getPurchaseAPIError: jest.fn(),
				getLabelOrigins: jest.fn().mockReturnValue( {} ),
				getLabelDestinations: jest.fn().mockReturnValue( {} ),
				getPackageUpdateErrors: jest.fn().mockReturnValue( {} ),
				getIsAddressVerified: jest.fn().mockReturnValue( true ),
				getNormalizedAddress,
				getIsAddressVerificationInProgress: jest
					.fn()
					.mockReturnValue( false ),
				getOriginAddresses,
				getSelectedRateOptions: jest.fn().mockReturnValue( [] ),
				get: jest.fn().mockReturnValue( true ),
			} ) )
		),
		select: jest.fn().mockReturnValue( {
			getIsAddressVerified: jest.fn().mockReturnValue( true ),
			getPurchasedLabels: jest.fn().mockReturnValue( [] ),
			getPurchasedLabel: jest.fn().mockReturnValue( null ),
			getSelectedHazmatConfig: jest.fn().mockReturnValue( {} ),
			getRefundedLabel: jest.fn().mockReturnValue( null ),
			getOrderStatus: jest.fn().mockReturnValue( 'processing' ),
			getOriginAddresses,
		} ),
	};
} );

jest.mock( 'utils/config', () => {
	return {
		getConfig: jest.fn().mockReturnValue( {
			accountSettings: {
				purchaseSettings: {
					selected_payment_method_id: 1,
				},
			},
			packagesSettings: {
				packages: {},
			},
			shippingLabelData: {
				currentOrderLabels: [],
			},
			origin_addresses: [
				{
					id: 1,
					city: 'San Francisco',
					country: 'US',
					state: 'CA',
					isVerified: true,
				},
			],
			order: {
				shipping_address: {},
				line_items: [
					{
						id: 0,
					},
				],
			},
			eu_countries: [],
			shipments: JSON.stringify( { 0: [ { id: 0 } ] } ),
			custom_fulfillment_summary: '',
		} ),
	};
} );

jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
	return mockUtils();
} );

jest.mock( 'components/label-purchase/hooks', () => {
	const originalHooks = jest.requireActual(
		'components/label-purchase/hooks'
	);
	return {
		__esModule: true,
		...originalHooks,
		useAccountState: jest.fn().mockReturnValue( {
			getAccountCompleteOrder: jest.fn().mockReturnValue( {} ),
			canPurchase: jest.fn().mockReturnValue( true ),
			accountSettings: {
				purchaseSettings: {
					selected_payment_method_id: 1,
				},
				purchaseMeta: {
					payment_methods: [ {} ],
				},
			},
		} ),
		useLabelsState: jest.fn().mockReturnValue( {
			hasMissingPurchase: jest.fn().mockReturnValue( false ),
			hasUnfinishedShipment: jest.fn().mockReturnValue( false ),
			purchasedLabelsProductIds: jest.fn().mockReturnValue( [ 1, 2 ] ),
			hasPurchasedLabel: jest.fn().mockReturnValue( true ),
			getCurrentShipmentLabel: jest
				.fn()
				.mockReturnValue( { status: 'UNKNOWN' } ),
			hasRequestedRefund: jest.fn().mockReturnValue( false ),
			labelStatusUpdateErrors: [],
			selectedLabelSize: {},
			setLabelSize: jest.fn(),
			paperSizes: [],
			getShipmentsWithoutLabel: jest.fn().mockReturnValue( [] ),
			isPurchasing: false,
			isUpdatingStatus: false,
			isCurrentTabPurchasingExtraLabel: jest
				.fn()
				.mockReturnValue( false ),
		} ),
		useCustomsState: jest.fn().mockReturnValue( {
			getShipmentDestination: jest.fn(),
			updateCustomsItems: jest.fn(),
			isCustomsNeeded: jest.fn().mockReturnValue( false ),
			getCustomsState: jest.fn().mockReturnValue( { items: [] } ),
		} ),
		useShipmentState: jest.fn().mockReturnValue( {
			shipments: { 0: [ { id: 0 } ] },
			setShipments: jest.fn(),
			getShipmentWeight: jest.fn().mockReturnValue( 0.5 ),
			resetSelections: jest.fn(),
			selections: {},
			setSelection: jest.fn(),
			currentShipmentId: '0',
			setCurrentShipmentId: jest.fn(),
			getShipmentItems: jest.fn(),
			getSelectionItems: jest.fn(),
			setShipmentOrigin: jest.fn(),
			getShipmentOrigin: jest.fn().mockReturnValue( {
				id: 1,
				city: 'San Francisco',
				country: 'US',
				state: 'CA',
				isVerified: true,
			} ),
			getShipmentDestination: jest.fn(),
			revertLabelShipmentIdsToUpdate: jest.fn(),
			labelShipmentIdsToUpdate: [],
			getShipmentPurchaseOrigin: jest.fn(),
			hasVariations: jest.fn().mockReturnValue( false ),
			hasMultipleShipments: jest.fn().mockReturnValue( false ),
			isExtraLabelPurchaseValid: jest.fn().mockReturnValue( true ),
		} ),
		useRatesState: jest.fn().mockReturnValue( {
			selectedRates: {},
			selectRates: jest.fn(),
			selectRate: jest.fn(),
			getSelectedRate: jest.fn(),
			removeSelectedRate: jest.fn(),
			isFetching: false,
			updateRates: jest.fn(),
			fetchRates: jest.fn(),
			sortRates: jest.fn(),
			errors: {},
			setErrors: jest.fn(),
			getSelectedRateOptions: jest.fn().mockReturnValue( [] ),
		} ),
		usePackageState: jest.fn().mockReturnValue( {
			getSelectedPackage: jest.fn(),
			setSelectedPackage: jest.fn(),
			setRawPackageData: jest.fn(),
			getCustomPackage: jest.fn(),
			setCustomPackage: jest.fn(),
			currentPackageTab: 'custom',
			setCurrentPackageTab: jest.fn(),
			isPackageSpecified: jest.fn().mockReturnValue( false ),
			isCustomPackageTab: jest.fn().mockReturnValue( false ),
		} ),
		useEssentialDetails: jest.fn().mockReturnValue( {
			focusArea: jest.fn().mockReturnValue( {} ),
			resetFocusArea: jest.fn(),
			isShippingServiceCompleted: jest.fn().mockReturnValue( false ),
			setCustomsCompleted: jest.fn(),
			isCustomsCompleted: jest.fn(),
			setShippingServiceCompleted: jest.fn(),
			setExtraLabelPurchaseCompleted: jest.fn(),
			isExtraLabelPurchaseCompleted: jest.fn(),
			setFocusArea: jest.fn(),
		} ),
	};
} );

const labelState = {
	currentShipmentId: '0',
	weight: 0,
	getPackageForRequest: jest.fn(),
	getShipmentItems: jest.fn(),
	getSelectionItems: jest.fn(),
	getShipmentHazmat: jest.fn(),
	updateRates: jest.fn(),
	getShipmentOrigin: () => ( {
		id: 1,
		city: 'San Francisco',
		country: 'US',
		state: 'CA',
		isVerified: true,
	} ),
	customs: {},
	shipments: {
		0: [ { id: 0 } ],
		1: [ { id: 1 } ],
	},
};

const customsState = {
	currentShipmentId: '0',
	shipments: {
		0: [ { id: 0 } ],
		1: [ { id: 1 } ],
	},
	selections: {},
	getShipmentItems: jest.fn().mockReturnValue( [ { id: 0 } ] ),
	getSelectionItems: jest.fn(),
	getShipmentOrigin: () => ( {
		id: 1,
		city: 'San Francisco',
		country: 'US',
		state: 'CA',
		isVerified: true,
	} ),
	getShipmentDestination: jest.fn(),
	getCustomsInformation: jest.fn().mockReturnValue( {} ),
	isCustomsNeeded: jest.fn().mockReturnValue( false ),
	errors: {
		customs: {},
	},
};

describe( 'OrderLabelPurchase', () => {
	let hooks;
	let setShipmentsMock;
	let setSelectedPackageMock;
	let updateCustomsItemsMock;
	let mockSelectedPackage;

	beforeAll( () => {
		hooks = jest.requireActual( 'components/label-purchase/hooks' );
		setShipmentsMock = jest.fn();
		setSelectedPackageMock = jest.fn();
		updateCustomsItemsMock = jest.fn();
		mockSelectedPackage = { id: 'test-package' };

		jest.spyOn(
			require( 'components/label-purchase/hooks' ),
			'useLabelsState'
		).mockImplementation( () => ( {
			...hooks.useLabelsState( { ...labelState } ),
			hasMissingPurchase: jest.fn().mockReturnValue( false ),
			getShipmentsWithoutLabel: jest.fn().mockReturnValue( [] ),
		} ) );

		jest.spyOn(
			require( 'components/label-purchase/hooks' ),
			'useShipmentState'
		).mockImplementation( () => ( {
			...hooks.useShipmentState(),
			currentShipmentId: '0',
			shipments: { 0: [ { id: 0 } ] },
			setShipments: setShipmentsMock,
			getShipmentWeight: jest.fn().mockReturnValue( 0.5 ),
		} ) );

		jest.spyOn(
			require( 'components/label-purchase/hooks' ),
			'usePackageState'
		).mockImplementation( () => ( {
			...hooks.usePackageState( '0', { 0: {} }, 1 ),
			getSelectedPackage: jest
				.fn()
				.mockReturnValue( mockSelectedPackage ),
			setSelectedPackage: setSelectedPackageMock,
		} ) );

		jest.spyOn(
			require( 'components/label-purchase/hooks' ),
			'useCustomsState'
		).mockImplementation( () => ( {
			...customsState,
			updateCustomsItems: updateCustomsItemsMock,
			getCustomsState: jest.fn().mockReturnValue( { items: [] } ),
		} ) );
	} );

	afterEach( () => {
		jest.clearAllMocks();
	} );

	it( 'renders Create new label tab when there are no missing purchases', async () => {
		await act( async () => {
			render( <OrderLabelPurchase orderId={ 1 } openModal={ true } /> );
		} );

		expect( screen.getByText( 'Add shipment' ) ).toBeInTheDocument();

		await act( async () => {
			fireEvent.click( screen.getByText( 'Add shipment' ) );
		} );

		expect( setShipmentsMock ).toHaveBeenCalled();
	} );

	it( 'sets selected package when creating new shipment for extra label', async () => {
		await act( async () => {
			render( <OrderLabelPurchase orderId={ 1 } openModal={ true } /> );
		} );

		// Trigger new shipment creation
		await act( async () => {
			fireEvent.click( screen.getByText( 'Add shipment' ) );
		} );

		// Verify the package was set and customs items were updated
		expect( setSelectedPackageMock ).toHaveBeenCalledWith(
			mockSelectedPackage
		);
		expect( updateCustomsItemsMock ).toHaveBeenCalled();
	} );
} );
