.essential-details {
	background: var(--gutenberg-white, #fff);
	border: 1px solid var(--gutenberg-gray-300, #ddd);
	border-radius: var(--grid-unit-05, 4px);
	box-sizing: border-box;

	.components-base-control {
		overflow: hidden;
	}

	li {
		display: flex;
		align-items: center;
	}
}

.essential-details__h3 {
	background-color: var(--wp-yellow-yellow-0, #fcf9e8);

	h3 {
		padding: 24px 32px 24px 32px;
		margin: 0;
	}
}

.essential-details__ul {
	padding-left: 24px;

	li {
		padding-top: 4px;
		padding-bottom: 4px;
	}
}

.essential-details__text {
	margin: 0;
}

/**
 * Overwrite styles from the
 ".label-purchase-modal__content > div:nth-child(2)".
*/
.label-purchase-modal__content > div:nth-child(2)
.purchase-label-buttons button.essential-details-cta__button {
	padding: 0;
	flex-direction: row;
	height: auto;
	color: var(--Upcoming-Blueberry, #3858e9);
	display: inline-flex;
}

.label-purchase-modal__content > div:nth-child(2) .essential-details__h3 h3 {
	margin: 0;
}
