import { render, screen } from '@testing-library/react';
import { EssentialDetails } from '../index';
import { useLabelPurchaseContext } from 'context/label-purchase';

jest.mock( 'context/label-purchase', () => ( {
	useLabelPurchaseContext: jest.fn(),
} ) );

describe( 'EssentialDetails', () => {
	const mockContext = {
		essentialDetails: {
			setFocusArea: jest.fn(),
			isExtraLabelPurchaseCompleted: () => false,
			isCustomsCompleted: () => false,
			isShippingServiceCompleted: () => false,
		},
		customs: {
			isCustomsNeeded: () => false,
		},
		shipment: {
			getShipmentOrigin: () => ( { isVerified: false } ),
		},
		rates: {
			getSelectedRate: () => null,
		},
		hazmat: {
			getShipmentHazmat: () => ( { isHazmat: false } ),
		},
		packages: {
			isPackageSpecified: () => false,
		},
		labels: {
			getCurrentShipmentLabel: () => ( { isLegacy: false } ),
			isCurrentTabPurchasingExtraLabel: () => false,
		},
	};

	beforeEach( () => {
		useLabelPurchaseContext.mockReturnValue( mockContext );
	} );

	it( 'renders extra label purchase section when isCurrentTabPurchasingExtraLabel is true', () => {
		useLabelPurchaseContext.mockReturnValue( {
			...mockContext,
			labels: {
				...mockContext.labels,
				isCurrentTabPurchasingExtraLabel: () => true,
			},
		} );

		render( <EssentialDetails /> );

		const products = screen.getByRole( 'button', {
			name: /the products/i,
		} );
		const productsText = screen.getByText( /Select/i, {
			selector: 'p',
		} ).textContent;

		expect( productsText ).toContain(
			'Select the products you want to ship with the new label'
		);
		expect( products ).toBeInTheDocument();
	} );

	it( 'renders hazmat section when isHazmat is true', () => {
		useLabelPurchaseContext.mockReturnValue( {
			...mockContext,
			hazmat: {
				getShipmentHazmat: () => ( { isHazmat: true } ),
			},
		} );

		render( <EssentialDetails /> );

		expect( screen.getByText( /HAZMAT/i ) ).toBeInTheDocument();
	} );

	it( 'renders customs section when isCustomsNeeded is true', () => {
		useLabelPurchaseContext.mockReturnValue( {
			...mockContext,
			customs: {
				isCustomsNeeded: () => true,
			},
		} );

		render( <EssentialDetails /> );

		expect(
			screen.getByText( /the Customs section/i )
		).toBeInTheDocument();
	} );

	it( 'does not render when label is legacy', () => {
		useLabelPurchaseContext.mockReturnValue( {
			...mockContext,
			labels: {
				...mockContext.labels,
				getCurrentShipmentLabel: () => ( { isLegacy: true } ),
			},
		} );

		render( <EssentialDetails /> );

		expect(
			screen.queryByText( /Essential details to provide/i )
		).not.toBeInTheDocument();
	} );

	it( 'renders package and shipping service sections by default', () => {
		render( <EssentialDetails /> );

		expect(
			screen.getByText( /the Package section/i )
		).toBeInTheDocument();
		expect( screen.getByText( /a shipping service/i ) ).toBeInTheDocument();
	} );
} );
