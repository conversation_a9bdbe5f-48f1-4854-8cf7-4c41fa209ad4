// This import should be before all other imports for the mock to work
import { ProvideTestState } from 'test-helpers';
import wpData from '@wordpress/data';
import { act, fireEvent, render } from '@testing-library/react';
import { registerLabelPurchaseStore } from 'data/label-purchase';
import { Packages } from '../packages';

jest.mock( '@wordpress/data', () => {
	const originalModule = jest.requireActual( '@wordpress/data' );

	return {
		...originalModule,
		select: jest.fn(),
		dispatch: jest.fn(),
	};
} );

describe( 'Composer package selectors', () => {
	beforeAll( () => {
		registerLabelPurchaseStore();
	} );

	beforeEach( () => {
		jest.clearAllMocks();
	} );

	it( 'should reset availableRates and selectedRates when switching tabs', async () => {
		const mockGetRatesForShipment = jest.fn().mockReturnValue( {
			some: 'value',
		} );
		const mockRatesReset = jest.fn();

		// Mock the specific store functions
		const mockLabelPurchaseStore = {
			getRatesForShipment: mockGetRatesForShipment,
			getSelectedRates: jest.fn().mockReturnValue( null ),
			getCustomsInformation: jest.fn().mockReturnValue( null ),
			getSelectedRateOptions: jest.fn().mockReturnValue( {} ),
			getSavedPackages: jest.fn().mockReturnValue( [] ),
		};

		// Mock select and dispatch to return our mocked store functions
		( wpData.select as jest.Mock ).mockImplementation( () => {
			return mockLabelPurchaseStore;
		} );

		( wpData.dispatch as jest.Mock ).mockImplementation( () => {
			return {
				ratesReset: mockRatesReset,
			};
		} );

		const removeSelectedRate = jest.fn();

		const { getByText } = render(
			<ProvideTestState
				initialValue={ {
					// @ts-ignore
					rates: {
						removeSelectedRate,
					},
				} }
			>
				<Packages />
			</ProvideTestState>
		);

		// Click on each tab and check if rates are reset
		const tabs = [ 'Saved templates', 'Custom package', 'Carrier package' ];

		for ( const tab of tabs ) {
			await act( async () => {
				fireEvent.click( getByText( tab ) );
			} );

			expect( mockGetRatesForShipment ).toHaveBeenCalled();
			expect( mockRatesReset ).toHaveBeenCalled();
			expect( removeSelectedRate ).toHaveBeenCalled();

			mockGetRatesForShipment.mockClear();
			mockRatesReset.mockClear();
			removeSelectedRate.mockClear();
		}
	} );
} );
