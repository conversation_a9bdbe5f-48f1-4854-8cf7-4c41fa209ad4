// This import should be before all other imports for the mock to work
import { ProvideTestState } from 'test-helpers';
import {
	act,
	cleanup,
	fireEvent,
	render,
	screen,
	waitFor,
} from '@testing-library/react';
import { useState } from '@wordpress/element';
import { apiFetch } from '@wordpress/data-controls';
import { registerLabelPurchaseStore } from 'data/label-purchase';
import { registerAddressStore } from 'data/address';
import { getRatesPath, getDeletePackagePath } from 'data/routes';
import { defaultCustomPackageData } from 'components/label-purchase/constants';
import { TAB_NAMES } from '../constants';
import { CarrierPackage, CustomPackage, SavedTemplates } from '../tab-views';
import { getWeightUnit } from 'utils';

jest.mock( '@wordpress/data-controls', () => {
	return {
		__esModule: true,
		apiFetch: jest.fn(),
	};
} );

/**
 * Custom snapshot serializer to ignore dynamic attributes
 * This is needed because some attributes are set differently between local and CI environments
 */
expect.addSnapshotSerializer( {
	test: ( val ) => {
		return val && val.$$typeof === Symbol.for( 'react.test.json' );
	},
	print: ( val, serialize ) => {
		const copy = { ...val };
		if ( copy.props ) {
			// Remove both lowercase and camelCase tabindex attributes
			if ( 'tabindex' in copy.props ) {
				delete copy.props.tabindex;
			}
			if ( 'tabIndex' in copy.props ) {
				delete copy.props.tabIndex;
			}
			// Remove data-enter attribute that appears in CI but not locally
			if ( 'data-enter' in copy.props ) {
				delete copy.props[ 'data-enter' ];
			}
		}
		return serialize( copy );
	},
} );

describe( 'Packages section', () => {
	let selectedPackage = null;
	let setSelectedPackage = jest.fn();

	const buttonsInDropDownSelector =
		'.components-popover .components-menu-item__button';
	let props = {
		selectedPackage,
		setSelectedPackage,
		setRawPackageData: jest.fn(),
		rawPackageData: defaultCustomPackageData,
	};
	beforeAll( () => {
		registerLabelPurchaseStore();
		registerAddressStore( true );
	} );

	afterEach( () => {
		jest.clearAllMocks();
		cleanup();
	} );

	const SavedTemplatesTestWrapper = () => {
		const [ selectedPackage1, setSelectedPackage1 ] = useState( null );
		return (
			<SavedTemplates
				{ ...props }
				setSelectedPackage={ setSelectedPackage1 }
				selectedPackage={ selectedPackage1 }
			/>
		);
	};

	const CustomPackageTestWrapper = () => {
		const [ rawPackageData, setRawPackageData ] = useState(
			defaultCustomPackageData
		);

		return (
			<CustomPackage
				{ ...props }
				setRawPackageData={ setRawPackageData }
				rawPackageData={ rawPackageData }
			/>
		);
	};

	it( 'should render Custom package tab', async () => {
		let asFragment;
		await act( async () => {
			( { asFragment } = render(
				<ProvideTestState>
					<CustomPackageTestWrapper />
				</ProvideTestState>
			) );
		} );
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should show error message for invalid input', async () => {
		let container;
		await act( async () => {
			const renderResult = render(
				<ProvideTestState>
					<CustomPackageTestWrapper />
				</ProvideTestState>
			);
			container = renderResult.container;
		} );

		await act( async () => {
			await fireEvent.change( screen.getByLabelText( 'Length' ), {
				target: { value: 0 },
			} );
			await fireEvent.change( screen.getByLabelText( 'Width' ), {
				target: { value: 0 },
			} );
			await fireEvent.change( screen.getByLabelText( 'Height' ), {
				target: { value: 0 },
			} );
		} );

		expect( container.querySelectorAll( '.has-error' ) ).toHaveLength( 3 );
	} );

	it( 'should render Carrier package tab', async () => {
		let asFragment;
		await act( async () => {
			const renderResult = render(
				<ProvideTestState>
					<CarrierPackage { ...props } />
				</ProvideTestState>
			);

			asFragment = renderResult.asFragment;
		} );

		expect( asFragment() ).toMatchSnapshot();
	} );
	it( 'should render Saved templates tab', async () => {
		let asFragment;
		await act( async () => {
			const renderResult = render(
				<ProvideTestState>
					<SavedTemplates { ...props } />
				</ProvideTestState>
			);
			asFragment = renderResult.asFragment;
		} );
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should show and hide the dropdown for Saved templates', async () => {
		let container, getByText;
		await act( async () => {
			const renderResult = render(
				<ProvideTestState>
					<SavedTemplatesTestWrapper />
				</ProvideTestState>
			);
			container = renderResult.container;
			getByText = renderResult.getByText;
		} );
		const selectItemText = 'Some custom package!';
		await fireEvent.click( getByText( 'Please select' ) );

		expect( screen.getByText( selectItemText ) ).toBeTruthy();
		expect(
			container.querySelectorAll( buttonsInDropDownSelector )
		).toHaveLength( 5 );

		await act( async () => {
			await fireEvent.click( screen.getByText( selectItemText ) );
		} );

		// The dropdown should be closed
		expect(
			container.querySelectorAll( buttonsInDropDownSelector )
		).toHaveLength( 0 );

		// The selected item should be shown.
		expect( screen.getByText( selectItemText ) ).toBeTruthy();
	} );

	it( 'Selecting a carrier package enables the get rates button, but 0 weight disables it', async () => {
		const setErrors = jest.fn();
		let totalWeight = 10;
		const getShipmentTotalWeight = jest.fn( () => totalWeight );
		const setShipmentTotalWeight = jest.fn( ( weight ) => {
			totalWeight = weight;
		} );
		let getByText, container, getAllByTitle, rerender;
		await act( async () => {
			( { getByText, container, getAllByTitle, rerender } = render(
				<ProvideTestState
					initialValue={ {
						weight: {
							getShipmentTotalWeight,
							setShipmentTotalWeight,
						},
					} }
				>
					<CarrierPackage { ...props } />
				</ProvideTestState>
			) );
		} );

		setSelectedPackage.mockImplementation( ( selection ) => {
			selectedPackage = selection;
		} );

		const groupText = 'USPS Priority Mail Express Boxes';
		const optionText = 'Priority Mail Express Box';
		await waitFor( () => getByText( groupText ) );
		await act( async () => {
			await fireEvent.click( getByText( groupText ) );
		} );
		await waitFor( () => getByText( optionText ) );
		expect(
			container.querySelector( `input[title="${ optionText }"]` )
		).toBeTruthy();

		await act( async () => {
			fireEvent.click( getAllByTitle( optionText )[ 0 ] );
		} );
		await act( async () => {
			rerender(
				<ProvideTestState
					initialValue={ {
						rates: {
							setErrors,
						},
					} }
				>
					<CarrierPackage
						{ ...props }
						selectedPackage={ selectedPackage }
					/>
				</ProvideTestState>
			);
		} );
		expect( setSelectedPackage ).toHaveBeenCalled();
		await waitFor( () =>
			expect( getAllByTitle( optionText )[ 0 ] ).toBeChecked()
		);
		expect( getByText( 'Get shipping rates' ) ).toBeEnabled();
		waitFor( () =>
			expect(
				container.querySelector( 'input[type="number"]' )
			).toBeInTheDocument()
		);

		await act( async () => {
			fireEvent.input(
				container.querySelector( 'input[type="number"]' ),
				{
					target: { value: 0 },
				}
			);
			fireEvent.blur( container.querySelector( 'input[type="number"]' ) );
		} );

		expect( setShipmentTotalWeight ).toHaveBeenCalled();
		expect( setErrors ).toHaveBeenCalled();
		expect( container.querySelector( 'input[type="number"]' ).value ).toBe(
			'0'
		);

		await act( async () => {
			rerender(
				<ProvideTestState
					initialValue={ {
						rates: {
							setErrors,
						},
					} }
				>
					<CarrierPackage { ...props } totalWeight={ totalWeight } />
				</ProvideTestState>
			);
		} );

		expect( getByText( 'Get shipping rates' ) ).toBeDisabled();
	} );

	it( 'custom package dimension should accept decimal points', async () => {
		let container;
		await act( async () => {
			const renderResult = render(
				<ProvideTestState>
					<CustomPackageTestWrapper />
				</ProvideTestState>
			);
			container = renderResult.container;
		} );
		await act( async () => {
			fireEvent.change( screen.getByLabelText( 'Length' ), {
				target: { value: 1.2 },
			} );
			fireEvent.change( screen.getByLabelText( 'Width' ), {
				target: { value: 0.4 },
			} );
			fireEvent.change( screen.getByLabelText( 'Height' ), {
				target: { value: 100.3 },
			} );
		} );

		expect( container.querySelector( '.has-error' ) ).toBeNull();
	} );

	it( 'custom package save name field should accept any string as name', async () => {
		let container;
		await act( async () => {
			const renderResult = render(
				<ProvideTestState>
					<CustomPackageTestWrapper />
				</ProvideTestState>
			);
			container = renderResult.container;
		} );

		const nameFieldPlaceholder = 'Enter a unique package name';
		await act( async () => {
			fireEvent.change( screen.getByLabelText( 'Length' ), {
				target: { value: 1.2 },
			} );
			fireEvent.change( screen.getByLabelText( 'Width' ), {
				target: { value: 0.4 },
			} );
			fireEvent.change( screen.getByLabelText( 'Height' ), {
				target: { value: 100.3 },
			} );

			fireEvent.click(
				screen.getByLabelText( 'Save this as a new package template' )
			);

			fireEvent.input(
				screen.getByPlaceholderText( nameFieldPlaceholder ),
				{
					target: {
						value: 'some string',
					},
				}
			);
		} );

		expect( container.querySelector( '.has-error' ) ).toBeNull();

		await act( async () => {
			fireEvent.input(
				screen.getByPlaceholderText( nameFieldPlaceholder ),
				{
					target: {
						value: 124343,
					},
				}
			);
		} );

		expect( container.querySelector( '.has-error' ) ).toBeNull();
	} );

	it( 'Invalid length of custom package name should show an error message', async () => {
		await act( async () => {
			render(
				<ProvideTestState>
					<CustomPackageTestWrapper />
				</ProvideTestState>
			);
		} );

		const nameFieldPlaceholder = 'Enter a unique package name';

		await act( async () => {
			fireEvent.change( screen.getByLabelText( 'Length' ), {
				target: { value: 1.2 },
			} );
			fireEvent.change( screen.getByLabelText( 'Width' ), {
				target: { value: 0.4 },
			} );
			fireEvent.change( screen.getByLabelText( 'Height' ), {
				target: { value: 100.3 },
			} );

			fireEvent.click(
				screen.getByLabelText( 'Save this as a new package template' )
			);

			fireEvent.change( screen.getByLabelText( 'Package weight' ), {
				target: { value: 0.3 },
			} );
		} );

		expect( screen.getByText( 'Save' ) ).toBeEnabled();

		await act( async () => {
			fireEvent.click( screen.getByText( 'Save' ) );
		} );

		expect(
			screen
				.getByPlaceholderText( nameFieldPlaceholder )
				.closest( '.has-error' )
		).toBeTruthy();

		expect(
			screen.findByText(
				'Package name should be at least 3 characters long.'
			)
		).toBeTruthy();
	} );

	it( 'No error is shown for a valid custom package name', async () => {
		await act( async () => {
			render(
				<ProvideTestState>
					<CustomPackageTestWrapper />
				</ProvideTestState>
			);
		} );

		const nameFieldPlaceholder = 'Enter a unique package name';

		await act( async () => {
			await fireEvent.change( screen.getByLabelText( 'Length' ), {
				target: { value: 1.2 },
			} );
			await fireEvent.change( screen.getByLabelText( 'Width' ), {
				target: { value: 0.4 },
			} );
			await fireEvent.change( screen.getByLabelText( 'Height' ), {
				target: { value: 100.3 },
			} );

			await fireEvent.click(
				screen.getByLabelText( 'Save this as a new package template' )
			);
			await fireEvent.click( screen.getByText( 'Save' ) );
			await fireEvent.input(
				screen.getByPlaceholderText( nameFieldPlaceholder ),
				{
					target: {
						value: 'some name',
					},
				}
			);
		} );

		expect(
			screen
				.getByPlaceholderText( nameFieldPlaceholder )
				.closest( '.has-error' )
		).toBeNull();
	} );

	it( 'SavedTemplates send request to get rates with correct paypload', async () => {
		const defaultUnit = getWeightUnit();

		let container, getByText;
		await act( async () => {
			( { container, getByText } = render(
				<ProvideTestState>
					<SavedTemplatesTestWrapper />
				</ProvideTestState>
			) );
		} );
		const selectItemText = 'Some custom package!';

		await fireEvent.click( getByText( 'Please select' ) );

		expect( screen.getByText( selectItemText ) ).toBeTruthy();
		expect(
			container.querySelectorAll( buttonsInDropDownSelector )
		).toHaveLength( 5 );

		await act( async () => {
			await fireEvent.click( screen.getByText( selectItemText ) );
		} );

		// The dropdown should be closed.
		expect(
			container.querySelectorAll( buttonsInDropDownSelector )
		).toHaveLength( 0 );

		// The selected item should be shown.
		expect( screen.getByText( selectItemText ) ).toBeTruthy();

		const unitSelect = container.querySelector(
			'.package-total-weight-unit select'
		);
		// make sure the default unit is selected
		await act( async () => {
			await fireEvent.change( unitSelect, {
				target: { value: defaultUnit },
			} );
			await fireEvent.change(
				screen.getByLabelText( 'Total shipment weight (with package)' ),
				{
					target: {
						value: 15,
					},
				}
			);
		} );

		expect(
			screen.getByLabelText( 'Total shipment weight (with package)' )
		).toHaveValue( 15 );
		const getRatesButton = screen.getByText( 'Get shipping rates' );
		expect( getRatesButton ).toBeEnabled();

		await act( async () => {
			await fireEvent.click( getRatesButton );
		} );
		expect( apiFetch ).toHaveBeenCalledTimes( 2 ); // preselected package triggers a fetch rates call
		expect( apiFetch ).toHaveBeenCalledWith(
			expect.objectContaining( {
				data: expect.objectContaining( {
					packages: expect.arrayContaining( [
						expect.objectContaining( {
							weight: 15,
						} ),
					] ),
				} ),
				method: 'POST',
				path: getRatesPath(),
			} )
		);
	} );

	it( 'SavedTemplates renders delete button and sends correct delete requests [predefined package]', async () => {
		let container;
		await act( async () => {
			( { container } = render(
				<ProvideTestState>
					<SavedTemplatesTestWrapper />
				</ProvideTestState>
			) );
		} );
		const selectItemText = 'Some custom package!';
		await act( async () => {
			await fireEvent.click( screen.getByText( 'Please select' ) );
		} );

		expect( screen.getByText( selectItemText ) ).toBeTruthy();
		expect(
			container.querySelectorAll( buttonsInDropDownSelector )
		).toHaveLength( 5 );

		expect( screen.getAllByLabelText( /Remove package/i ) ).toHaveLength(
			2
		);

		expect( screen.getAllByLabelText( /Delete package/i ) ).toHaveLength(
			3
		);

		await waitFor( () => {
			const deleteButtons = screen.getAllByLabelText( /Remove package/i );
			expect( deleteButtons[ 0 ] ).toBeVisible(); // Ensure the first button is available
		} );

		// Click the first delete icon.
		await act( async () => {
			await fireEvent.click(
				screen.getAllByLabelText( /Remove package/i )[ 0 ]
			);
		} );

		// The dropdown should not close.
		expect(
			container.querySelectorAll( buttonsInDropDownSelector )
		).toHaveLength( 5 );

		// Click the first delete button in the confirmation dialog.
		await act( async () => {
			fireEvent.click(
				screen.getByRole( 'button', {
					name: 'Delete',
				} )
			);
		} );

		expect( apiFetch ).toHaveBeenCalledTimes( 1 );
		expect( apiFetch ).toHaveBeenCalledWith(
			expect.objectContaining( {
				method: 'DELETE',
				path: getDeletePackagePath(
					'predefined',
					'medium_flat_box_top'
				),
			} )
		);
	} );

	it( 'SavedTemplates renders delete button and sends correct delete requests [custom package]', async () => {
		let container;
		await act( async () => {
			( { container } = render(
				<ProvideTestState>
					<SavedTemplatesTestWrapper />
				</ProvideTestState>
			) );
		} );
		const selectItemText = 'Some custom package!';
		await act( async () => {
			await fireEvent.click( screen.getByText( 'Please select' ) );
		} );
		const lastItemIndex = 2;

		expect( screen.getByText( selectItemText ) ).toBeTruthy();
		expect(
			container.querySelectorAll( buttonsInDropDownSelector )
		).toHaveLength( 5 );

		expect( screen.getAllByLabelText( /Remove package/i ) ).toHaveLength(
			2
		);

		expect( screen.getAllByLabelText( /Delete package/i ) ).toHaveLength(
			3
		);

		await waitFor( () => {
			const deleteButtons = screen.getAllByLabelText( /Delete package/i );
			expect( deleteButtons[ lastItemIndex ] ).toBeVisible(); // Ensure the first button is available
		} );

		// Click the first delete icon.
		await act( async () => {
			await fireEvent.click(
				screen.getAllByLabelText( /Delete package/i )[ lastItemIndex ]
			);
		} );

		// The dropdown should not close.
		expect(
			container.querySelectorAll( buttonsInDropDownSelector )
		).toHaveLength( 5 );

		// Click the first delete icon.
		await act( async () => {
			await fireEvent.click(
				screen.getByRole( 'button', {
					name: 'Delete',
				} )
			);
		} );

		expect( apiFetch ).toHaveBeenCalledTimes( 1 );
		expect( apiFetch ).toHaveBeenCalledWith(
			expect.objectContaining( {
				method: 'DELETE',
				path: getDeletePackagePath( 'custom', 'custom_box_3' ),
			} )
		);
	} );

	it( 'SavedTemplates renders correctly and automatically fetches rates', async () => {
		selectedPackage = {
			id: 'custom_box_3',
			name: 'Some custom package!',
			width: 10,
			height: 10,
			length: 10,
			weight: 1,
		};
		setSelectedPackage = jest.fn();
		props = {
			selectedPackage,
			setSelectedPackage,
		};
		const fetchRates = jest.fn();
		await act( async () => {
			render(
				<ProvideTestState
					initialValue={ {
						packages: {
							currentPackageTab: TAB_NAMES.SAVED_TEMPLATES,
							isSelectedASavedPackage: jest.fn( () => true ),
						},
						rates: {
							errors: {},
							fetchRates,
							setErrors: jest.fn(),
							isFetching: false,
						},
						labels: {
							hasMissingPurchase: jest
								.fn()
								.mockReturnValue( false ),
						},
						shipment: {
							selections: { 0: [ {} ] },
							currentShipmentId: 0,
						},
					} }
				>
					<SavedTemplates { ...props } />
				</ProvideTestState>
			);
		} );

		expect( fetchRates ).toHaveBeenCalledTimes( 1 );
	} );

	it( 'CarrierPackage renders correctly and automatically fetches rates', async () => {
		selectedPackage = {
			id: 'carrier_box_3',
			name: 'carrier box 3',
			width: 10,
			height: 10,
			length: 10,
			weight: 1,
		};
		setSelectedPackage = jest.fn();
		props = {
			selectedPackage,
			setSelectedPackage,
		};
		const fetchRates = jest.fn();

		await act( async () => {
			render(
				<ProvideTestState
					initialValue={ {
						packages: {
							currentPackageTab: TAB_NAMES.SAVED_TEMPLATES,
							isSelectedASavedPackage: jest.fn( () => true ),
						},
						rates: {
							errors: {},
							fetchRates,
							setErrors: jest.fn(),
							isFetching: false,
						},
					} }
				>
					<CarrierPackage { ...props } />
				</ProvideTestState>
			);
		} );

		expect( fetchRates ).toHaveBeenCalledTimes( 1 );
	} );

	it( 'CarrierPackage sends weight in default unit regardless of selected unit', async () => {
		const defaultUnit = getWeightUnit();
		const setShipmentTotalWeight = jest.fn();

		selectedPackage = {
			id: 'carrier_box_3',
			name: 'carrier box 3',
			width: 10,
			height: 10,
			length: 10,
			weight: 0,
		};
		props = {
			selectedPackage,
			setSelectedPackage: jest.fn(),
		};

		let getByText, getByLabelText;
		await act( async () => {
			( { getByText, getByLabelText } = render(
				<ProvideTestState
					initialValue={ {
						rates: {
							errors: {},
							setErrors: jest.fn(),
							isFetching: false,
						},
						weight: {
							setShipmentTotalWeight,
						},
					} }
				>
					<CarrierPackage { ...props } />
				</ProvideTestState>
			) );
		} );

		// Select a carrier package
		const groupText = 'USPS Priority Mail Express Boxes';
		const optionText = 'Priority Mail Express Box';

		await waitFor( () => getByText( groupText ) );
		await act( async () => {
			await fireEvent.click( getByText( groupText ) );
		} );

		await waitFor( () => getByText( optionText ) );
		await act( async () => {
			await fireEvent.click( screen.getAllByTitle( optionText )[ 0 ] );
		} );

		// Set weight and change unit
		const weightInput = getByLabelText( /Total shipment weight/i );
		await act( async () => {
			await fireEvent.change( weightInput, { target: { value: 100 } } );
		} );

		const unitSelect = screen.getByRole( 'option', {
			name: new RegExp( defaultUnit, 'i' ),
		} );
		await act( async () => {
			await fireEvent.change( unitSelect, { target: { value: 'kg' } } );
		} );

		expect( setShipmentTotalWeight ).toHaveBeenCalledWith( 100 );
	} );

	it( 'weight input value updates when changing units', async () => {
		const defaultUnit = getWeightUnit();
		let container;
		await act( async () => {
			const renderResult = render(
				<ProvideTestState>
					<CarrierPackage />
				</ProvideTestState>
			);
			container = renderResult.container;
		} );

		// Set initial weight value
		const weightInput = screen.getByLabelText( /Total shipment weight/i );
		await act( async () => {
			await fireEvent.change( weightInput, { target: { value: 2 } } );
		} );

		// Get unit selector and change from default to kg
		const unitSelect = container.querySelector(
			'.package-total-weight-unit select'
		);
		await act( async () => {
			await fireEvent.change( unitSelect, { target: { value: 'kg' } } );
		} );

		// Value should be converted from default unit to kg (roughly 0.91 kg)
		expect( weightInput.value ).toBe( '0.91' );

		// Change back to default unit
		await act( async () => {
			await fireEvent.change( unitSelect, {
				target: { value: defaultUnit },
			} );
		} );

		// Value should be back to original 2 or 2.00
		expect( weightInput.value ).toMatch( /^2(\.00)?$/ );
	} );

	it( 'shows error when weight is less than minimum weight threshold', async () => {
		let container;
		await act( async () => {
			const renderResult = render(
				<ProvideTestState>
					<CarrierPackage />
				</ProvideTestState>
			);
			container = renderResult.container;
		} );

		// Get unit selector and change from default to kg
		const unitSelect = container.querySelector(
			'.package-total-weight-unit select'
		);

		await act( async () => {
			await fireEvent.change( unitSelect, { target: { value: 'g' } } );
		} );

		// Set initial weight value
		const weightInput = screen.getByLabelText( /Total shipment weight/i );
		await act( async () => {
			await fireEvent.change( weightInput, { target: { value: 1 } } );
			await fireEvent.blur( weightInput );
		} );

		expect( unitSelect.value ).toBe( 'g' );

		expect(
			screen.getByText( /Weight must be greater than/i )
		).toBeInTheDocument();
		// add assertions for other units and values
		await act( async () => {
			await fireEvent.change( unitSelect, { target: { value: 'oz' } } );
			await fireEvent.change( weightInput, { target: { value: 0.03 } } );
			await fireEvent.blur( weightInput );
		} );

		expect(
			screen.getByText( /Weight must be greater than/i )
		).toBeInTheDocument();

		await act( async () => {
			await fireEvent.change( unitSelect, { target: { value: 'lbs' } } );
			await fireEvent.change( weightInput, { target: { value: 0.001 } } );
			await fireEvent.blur( weightInput );
		} );

		expect(
			screen.getByText( /Weight must be greater than/i )
		).toBeInTheDocument();

		await act( async () => {
			await fireEvent.change( unitSelect, { target: { value: 'kg' } } );
			await fireEvent.change( weightInput, { target: { value: 0.001 } } );
			await fireEvent.blur( weightInput );
		} );

		expect(
			screen.getByText( /Weight must be greater than/i )
		).toBeInTheDocument();
	} );
} );
