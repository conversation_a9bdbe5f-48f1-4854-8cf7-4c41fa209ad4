// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Packages section should render Carrier package tab 1`] = `
<DocumentFragment>
  <div
    class="carrier-package-tabs"
  >
    <div
      aria-orientation="horizontal"
      class="components-tab-panel__tabs"
      role="tablist"
    >
      <button
        aria-controls="tab-panel-0-usps-view"
        aria-selected="true"
        class="components-button components-tab-panel__tabs-item is-active is-next-40px-default-size has-icon"
        data-active-item="true"
        id="tab-panel-0-usps"
        role="tab"
        type="button"
      >
        <div
          class="carrier-icon"
          style="width: 24px; max-width: 24px; background-image: url(test-file-stub); background-repeat: no-repeat; background-position-x: center; background-position-y: center; height: 100%; min-height: 24px; background-size: contain;"
        />
        USPS
      </button>
      <button
        aria-controls="tab-panel-0-fedex-view"
        aria-selected="false"
        class="components-button components-tab-panel__tabs-item is-next-40px-default-size has-icon"
        id="tab-panel-0-fedex"
        role="tab"
        type="button"
      >
        <div
          class="carrier-icon"
          style="width: 24px; max-width: 24px; background-image: url(test-file-stub); background-repeat: no-repeat; background-position-x: center; background-position-y: center; height: 100%; min-height: 24px; background-size: contain;"
        />
        FedEx
      </button>
    </div>
    <div
      aria-labelledby="tab-panel-0-usps"
      class="components-tab-panel__tab-content"
      data-open="true"
      id="tab-panel-0-usps-view"
      role="tabpanel"
      tabindex="0"
    >
      <div
        class="components-panel"
      >
        <div
          class="components-panel__body is-opened"
        >
          <h2
            class="components-panel__body-title"
          >
            <button
              aria-expanded="true"
              class="components-button components-panel__body-toggle is-next-40px-default-size"
              type="button"
            >
              <span
                aria-hidden="true"
              >
                <svg
                  aria-hidden="true"
                  class="components-panel__arrow"
                  focusable="false"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"
                  />
                </svg>
              </span>
              USPS Priority Mail Flat Rate Boxes
            </button>
          </h2>
          <div
            class="components-item-group label-purchase-packages css-kkmvuj-PolymorphicDiv-rounded e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="ItemGroup"
            role="list"
          >
            <div
              class="css-dcjs67-itemWrapper"
              role="listitem"
            >
              <div
                class="components-item css-1knoh3g-PolymorphicDiv-medium-item-spacedAround e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="Item"
              >
                <fieldset
                  class="components-radio-control"
                  id="inspector-radio-control-0"
                >
                  <legend
                    class="components-base-control__label css-1w2n0ib-StyledVisualLabel-baseLabelTypography-labelStyles ej5x27r0"
                  />
                  <div
                    class="components-flex components-h-stack components-v-stack components-radio-control__group-wrapper css-1ykmsse-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="VStack"
                  >
                    <div
                      class="components-radio-control__option"
                    >
                      <input
                        checked=""
                        class="components-radio-control__input"
                        id="inspector-radio-control-0-0"
                        name="inspector-radio-control-0"
                        title="Small Flat Rate Box"
                        type="radio"
                        value=""
                      />
                      <label
                        class="components-radio-control__label"
                        for="inspector-radio-control-0-0"
                      >
                        <section>
                          <span
                            class="components-truncate components-text css-14afdk-PolymorphicDiv-Truncate-Text-sx-Base e19lxcc00"
                            data-wp-c16t="true"
                            data-wp-component="Text"
                            title="Small Flat Rate Box"
                          >
                            Small Flat Rate Box
                          </span>
                          <span>
                             cm
                          </span>
                          <span>
                            lbs
                          </span>
                          <button
                            class="components-button has-icon"
                            title="By selecting this package, you add it to the saved templates section."
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              focusable="false"
                              height="24"
                              viewBox="0 0 24 24"
                              width="24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M9.706 8.646a.25.25 0 01-.188.137l-4.626.672a.25.25 0 00-.139.427l3.348 3.262a.25.25 0 01.072.222l-.79 4.607a.25.25 0 00.362.264l4.138-2.176a.25.25 0 01.233 0l4.137 2.175a.25.25 0 00.363-.263l-.79-4.607a.25.25 0 01.072-.222l3.347-3.262a.25.25 0 00-.139-.427l-4.626-.672a.25.25 0 01-.188-.137l-2.069-4.192a.25.25 0 00-.448 0L9.706 8.646zM12 7.39l-.948 1.921a1.75 1.75 0 01-1.317.957l-2.12.308 1.534 1.495c.412.402.6.982.503 1.55l-.362 2.11 1.896-.997a1.75 1.75 0 011.629 0l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39z"
                                fill-rule="evenodd"
                              />
                            </svg>
                          </button>
                        </section>
                      </label>
                    </div>
                  </div>
                </fieldset>
              </div>
            </div>
            <div
              class="css-dcjs67-itemWrapper"
              role="listitem"
            >
              <div
                class="components-item css-1knoh3g-PolymorphicDiv-medium-item-spacedAround e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="Item"
              >
                <fieldset
                  class="components-radio-control"
                  id="inspector-radio-control-1"
                >
                  <legend
                    class="components-base-control__label css-1w2n0ib-StyledVisualLabel-baseLabelTypography-labelStyles ej5x27r0"
                  />
                  <div
                    class="components-flex components-h-stack components-v-stack components-radio-control__group-wrapper css-1ykmsse-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="VStack"
                  >
                    <div
                      class="components-radio-control__option"
                    >
                      <input
                        class="components-radio-control__input"
                        id="inspector-radio-control-1-0"
                        name="inspector-radio-control-1"
                        title="Medium Flat Rate Box 1, Top Loading"
                        type="radio"
                        value="medium_flat_box_top"
                      />
                      <label
                        class="components-radio-control__label"
                        for="inspector-radio-control-1-0"
                      >
                        <section>
                          <span
                            class="components-truncate components-text css-14afdk-PolymorphicDiv-Truncate-Text-sx-Base e19lxcc00"
                            data-wp-c16t="true"
                            data-wp-component="Text"
                            title="Medium Flat Rate Box 1, Top Loading"
                          >
                            Medium Flat Rate Box 1, Top Loading
                          </span>
                          <span>
                            28.57 x 22.22 x 15.24 cm
                          </span>
                          <span>
                            0lbs
                          </span>
                          <button
                            class="components-button has-icon"
                            title="By selecting this package, you add it to the saved templates section."
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              focusable="false"
                              height="24"
                              viewBox="0 0 24 24"
                              width="24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M9.706 8.646a.25.25 0 01-.188.137l-4.626.672a.25.25 0 00-.139.427l3.348 3.262a.25.25 0 01.072.222l-.79 4.607a.25.25 0 00.362.264l4.138-2.176a.25.25 0 01.233 0l4.137 2.175a.25.25 0 00.363-.263l-.79-4.607a.25.25 0 01.072-.222l3.347-3.262a.25.25 0 00-.139-.427l-4.626-.672a.25.25 0 01-.188-.137l-2.069-4.192a.25.25 0 00-.448 0L9.706 8.646zM12 7.39l-.948 1.921a1.75 1.75 0 01-1.317.957l-2.12.308 1.534 1.495c.412.402.6.982.503 1.55l-.362 2.11 1.896-.997a1.75 1.75 0 011.629 0l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39z"
                                fill-rule="evenodd"
                              />
                            </svg>
                          </button>
                        </section>
                      </label>
                    </div>
                  </div>
                </fieldset>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="components-panel"
      >
        <div
          class="components-panel__body"
        >
          <h2
            class="components-panel__body-title"
          >
            <button
              aria-expanded="false"
              class="components-button components-panel__body-toggle is-next-40px-default-size"
              type="button"
            >
              <span
                aria-hidden="true"
              >
                <svg
                  aria-hidden="true"
                  class="components-panel__arrow"
                  focusable="false"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                  />
                </svg>
              </span>
              USPS Priority Mail Flat Rate Envelopes
            </button>
          </h2>
        </div>
      </div>
      <div
        class="components-panel"
      >
        <div
          class="components-panel__body"
        >
          <h2
            class="components-panel__body-title"
          >
            <button
              aria-expanded="false"
              class="components-button components-panel__body-toggle is-next-40px-default-size"
              type="button"
            >
              <span
                aria-hidden="true"
              >
                <svg
                  aria-hidden="true"
                  class="components-panel__arrow"
                  focusable="false"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                  />
                </svg>
              </span>
              USPS Priority Mail Boxes
            </button>
          </h2>
        </div>
      </div>
      <div
        class="components-panel"
      >
        <div
          class="components-panel__body"
        >
          <h2
            class="components-panel__body-title"
          >
            <button
              aria-expanded="false"
              class="components-button components-panel__body-toggle is-next-40px-default-size"
              type="button"
            >
              <span
                aria-hidden="true"
              >
                <svg
                  aria-hidden="true"
                  class="components-panel__arrow"
                  focusable="false"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                  />
                </svg>
              </span>
              USPS Priority Mail Express Flat Rate Envelopes
            </button>
          </h2>
        </div>
      </div>
      <div
        class="components-panel"
      >
        <div
          class="components-panel__body"
        >
          <h2
            class="components-panel__body-title"
          >
            <button
              aria-expanded="false"
              class="components-button components-panel__body-toggle is-next-40px-default-size"
              type="button"
            >
              <span
                aria-hidden="true"
              >
                <svg
                  aria-hidden="true"
                  class="components-panel__arrow"
                  focusable="false"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                  />
                </svg>
              </span>
              USPS Priority Mail Express Boxes
            </button>
          </h2>
        </div>
      </div>
      <div
        class="components-spacer css-1i4aeis-PolymorphicDiv-classes-classes e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Spacer"
      />
      <div
        class="components-flex css-15o8liu-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Flex"
      >
        <div
          class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="FlexBlock"
        >
          <div
            class="components-flex css-k256ns-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <div
              class="components-base-control components-input-control package-total-weight css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-input-base em5sgkm5 css-o61hjq-PolymorphicDiv-Flex-base-ItemsColumn-Root e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="InputBase"
                >
                  <div
                    class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="FlexItem"
                  >
                    <label
                      class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                      data-wp-c16t="true"
                      data-wp-component="Text"
                      for="inspector-input-control-8"
                    >
                      Total shipment weight (with package)
                    </label>
                  </div>
                  <div
                    class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                  >
                    <input
                      class="components-input-control__input css-mxhwb4-Input-dragStyles-fontSizeStyles-sizeStyles-customPaddings em5sgkm3"
                      id="inspector-input-control-8"
                      min="10"
                      step="0.1"
                      type="number"
                      value="10.00"
                    />
                    <div
                      aria-hidden="true"
                      class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div
              class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-input-base components-select-control package-total-weight-unit e1mv6sxx3 em5sgkm5 css-16rj6q3-PolymorphicDiv-Flex-base-ItemsColumn-Root-StyledInputBase e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="InputBase"
                >
                  <div
                    class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="FlexItem"
                  >
                    <label
                      class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                      data-wp-c16t="true"
                      data-wp-component="Text"
                      for="inspector-select-control-4"
                    >
                      Unit
                    </label>
                  </div>
                  <div
                    class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                  >
                    <select
                      class="components-select-control__input css-1i1tjy6-Select-fontSizeStyles-sizeStyles-rtl e1mv6sxx2"
                      id="inspector-select-control-4"
                    >
                      <option
                        value="oz"
                      >
                        oz
                      </option>
                      <option
                        value="lbs"
                      >
                        lbs
                      </option>
                      <option
                        value="g"
                      >
                        g
                      </option>
                      <option
                        value="kg"
                      >
                        kg
                      </option>
                    </select>
                    <span
                      class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                    >
                      <div
                        class="components-input-control-suffix-wrapper e1mv6sxx0 css-1hy8q7r-PrefixSuffixWrapper-prefixSuffixWrapperStyles-InputControlSuffixWrapperWithClickThrough-rtl em5sgkm0"
                        data-wp-c16t="true"
                        data-wp-component="InputControlSuffixWrapper"
                      >
                        <div
                          class="css-di1hl1-DownArrowWrapper e1mv6sxx1"
                        >
                          <svg
                            aria-hidden="true"
                            focusable="false"
                            height="18"
                            viewBox="0 0 24 24"
                            width="18"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                            />
                          </svg>
                        </div>
                      </div>
                    </span>
                    <div
                      aria-hidden="true"
                      class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="components-flex get-rates-button-wrapper css-8owsvq-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <button
            class="components-button is-secondary"
            disabled=""
            title="Complete package selection/fields to get shipping rates"
            type="button"
          >
            Get shipping rates
          </button>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`Packages section should render Custom package tab 1`] = `
<DocumentFragment>
  <div
    class="components-flex css-1lv5pi3-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <div
      class="components-flex-item css-qt3kf4-PolymorphicDiv-Item-sx-Base e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="FlexItem"
    >
      <div
        class="components-flex custom-package__details css-cygsde-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Flex"
      >
        <div
          class="components-flex css-f4ltlq-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <div
            class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="FlexBlock"
          >
            <div
              class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-input-base components-select-control e1mv6sxx3 em5sgkm5 css-16rj6q3-PolymorphicDiv-Flex-base-ItemsColumn-Root-StyledInputBase e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="InputBase"
                >
                  <div
                    class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="FlexItem"
                  >
                    <label
                      class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                      data-wp-c16t="true"
                      data-wp-component="Text"
                      for="inspector-select-control-0"
                    >
                      Package type
                    </label>
                  </div>
                  <div
                    class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                  >
                    <select
                      class="components-select-control__input css-1i1tjy6-Select-fontSizeStyles-sizeStyles-rtl e1mv6sxx2"
                      id="inspector-select-control-0"
                      style="flex: 2;"
                    >
                      <option
                        value="box"
                      >
                        Box
                      </option>
                      <option
                        value="envelope"
                      >
                        Envelope
                      </option>
                    </select>
                    <span
                      class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                    >
                      <div
                        class="components-input-control-suffix-wrapper e1mv6sxx0 css-1hy8q7r-PrefixSuffixWrapper-prefixSuffixWrapperStyles-InputControlSuffixWrapperWithClickThrough-rtl em5sgkm0"
                        data-wp-c16t="true"
                        data-wp-component="InputControlSuffixWrapper"
                      >
                        <div
                          class="css-di1hl1-DownArrowWrapper e1mv6sxx1"
                        >
                          <svg
                            aria-hidden="true"
                            focusable="false"
                            height="18"
                            viewBox="0 0 24 24"
                            width="18"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                            />
                          </svg>
                        </div>
                      </div>
                    </span>
                    <div
                      aria-hidden="true"
                      class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="components-flex css-17uadny-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
          style="width: auto;"
        >
          <div
            class="components-base-control components-input-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
          >
            <div
              class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
            >
              <div
                class="components-flex components-input-base em5sgkm5 css-o61hjq-PolymorphicDiv-Flex-base-ItemsColumn-Root e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="InputBase"
              >
                <div
                  class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="FlexItem"
                >
                  <label
                    class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="Text"
                    for="inspector-input-control-0"
                  >
                    Length
                  </label>
                </div>
                <div
                  class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                >
                  <input
                    aria-describedby="inspector-input-control-0__help"
                    class="components-input-control__input css-fy2ds2-Input-dragStyles-fontSizeStyles-sizeStyles-customPaddings em5sgkm3"
                    id="inspector-input-control-0"
                    min="0"
                    type="number"
                    value=""
                  />
                  <span
                    class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                  >
                    <div
                      class="components-input-control-suffix-wrapper css-ol253t-PrefixSuffixWrapper-prefixSuffixWrapperStyles em5sgkm0"
                      data-wp-c16t="true"
                      data-wp-component="InputControlSuffixWrapper"
                    >
                      cm
                    </div>
                  </span>
                  <div
                    aria-hidden="true"
                    class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                  />
                </div>
              </div>
            </div>
            <p
              class="components-base-control__help css-shdw93-StyledHelp ej5x27r1"
              id="inspector-input-control-0__help"
            />
          </div>
          <div
            class="components-spacer css-1ipqe46-PolymorphicDiv-classes-rtl-rtl-classes e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Spacer"
            direction="vertical"
          >
            x
          </div>
          <div
            class="components-base-control components-input-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
          >
            <div
              class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
            >
              <div
                class="components-flex components-input-base em5sgkm5 css-o61hjq-PolymorphicDiv-Flex-base-ItemsColumn-Root e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="InputBase"
              >
                <div
                  class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="FlexItem"
                >
                  <label
                    class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="Text"
                    for="inspector-input-control-1"
                  >
                    Width
                  </label>
                </div>
                <div
                  class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                >
                  <input
                    aria-describedby="inspector-input-control-1__help"
                    class="components-input-control__input css-fy2ds2-Input-dragStyles-fontSizeStyles-sizeStyles-customPaddings em5sgkm3"
                    id="inspector-input-control-1"
                    min="0"
                    type="number"
                    value=""
                  />
                  <span
                    class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                  >
                    <div
                      class="components-input-control-suffix-wrapper css-ol253t-PrefixSuffixWrapper-prefixSuffixWrapperStyles em5sgkm0"
                      data-wp-c16t="true"
                      data-wp-component="InputControlSuffixWrapper"
                    >
                      cm
                    </div>
                  </span>
                  <div
                    aria-hidden="true"
                    class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                  />
                </div>
              </div>
            </div>
            <p
              class="components-base-control__help css-shdw93-StyledHelp ej5x27r1"
              id="inspector-input-control-1__help"
            />
          </div>
          <div
            class="components-spacer css-1ipqe46-PolymorphicDiv-classes-rtl-rtl-classes e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Spacer"
            direction="vertical"
          >
            x
          </div>
          <div
            class="components-base-control components-input-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
          >
            <div
              class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
            >
              <div
                class="components-flex components-input-base em5sgkm5 css-o61hjq-PolymorphicDiv-Flex-base-ItemsColumn-Root e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="InputBase"
              >
                <div
                  class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="FlexItem"
                >
                  <label
                    class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="Text"
                    for="inspector-input-control-2"
                  >
                    Height
                  </label>
                </div>
                <div
                  class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                >
                  <input
                    aria-describedby="inspector-input-control-2__help"
                    class="components-input-control__input css-fy2ds2-Input-dragStyles-fontSizeStyles-sizeStyles-customPaddings em5sgkm3"
                    id="inspector-input-control-2"
                    min="0"
                    type="number"
                    value=""
                  />
                  <span
                    class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                  >
                    <div
                      class="components-input-control-suffix-wrapper css-ol253t-PrefixSuffixWrapper-prefixSuffixWrapperStyles em5sgkm0"
                      data-wp-c16t="true"
                      data-wp-component="InputControlSuffixWrapper"
                    >
                      cm
                    </div>
                  </span>
                  <div
                    aria-hidden="true"
                    class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                  />
                </div>
              </div>
            </div>
            <p
              class="components-base-control__help css-shdw93-StyledHelp ej5x27r1"
              id="inspector-input-control-2__help"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="components-flex-item save-custom-template css-qt3kf4-PolymorphicDiv-Item-sx-Base e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="FlexItem"
    >
      <div
        class="components-flex css-supf9p-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Flex"
      >
        <div
          class="components-flex-item css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="FlexItem"
        >
          <div
            class="components-base-control components-checkbox-control save-custom-template__toggle css-fothu4-Wrapper-boxSizingReset ej5x27r4"
          >
            <div
              class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
            >
              <div
                class="components-flex components-h-stack css-1m5h1bg-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="HStack"
              >
                <span
                  class="components-checkbox-control__input-container"
                >
                  <input
                    class="components-checkbox-control__input"
                    id="inspector-checkbox-control-0"
                    type="checkbox"
                    value="1"
                  />
                </span>
                <label
                  class="components-checkbox-control__label"
                  for="inspector-checkbox-control-0"
                >
                  Save this as a new package template
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="components-flex-item css-qt3kf4-PolymorphicDiv-Item-sx-Base e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="FlexItem"
    >
      <div
        class="components-flex css-msclxj-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Flex"
      >
        <div
          class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="FlexBlock"
        >
          <div
            class="components-flex css-k256ns-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <div
              class="components-base-control components-input-control package-total-weight css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-input-base em5sgkm5 css-o61hjq-PolymorphicDiv-Flex-base-ItemsColumn-Root e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="InputBase"
                >
                  <div
                    class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="FlexItem"
                  >
                    <label
                      class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                      data-wp-c16t="true"
                      data-wp-component="Text"
                      for="inspector-input-control-3"
                    >
                      Total shipment weight (with package)
                    </label>
                  </div>
                  <div
                    class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                  >
                    <input
                      class="components-input-control__input css-mxhwb4-Input-dragStyles-fontSizeStyles-sizeStyles-customPaddings em5sgkm3"
                      id="inspector-input-control-3"
                      min="10"
                      step="0.1"
                      type="number"
                      value="10.00"
                    />
                    <div
                      aria-hidden="true"
                      class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div
              class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
            >
              <div
                class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
              >
                <div
                  class="components-flex components-input-base components-select-control package-total-weight-unit e1mv6sxx3 em5sgkm5 css-16rj6q3-PolymorphicDiv-Flex-base-ItemsColumn-Root-StyledInputBase e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="InputBase"
                >
                  <div
                    class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="FlexItem"
                  >
                    <label
                      class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                      data-wp-c16t="true"
                      data-wp-component="Text"
                      for="inspector-select-control-1"
                    >
                      Unit
                    </label>
                  </div>
                  <div
                    class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
                  >
                    <select
                      class="components-select-control__input css-1i1tjy6-Select-fontSizeStyles-sizeStyles-rtl e1mv6sxx2"
                      id="inspector-select-control-1"
                    >
                      <option
                        value="oz"
                      >
                        oz
                      </option>
                      <option
                        value="lbs"
                      >
                        lbs
                      </option>
                      <option
                        value="g"
                      >
                        g
                      </option>
                      <option
                        value="kg"
                      >
                        kg
                      </option>
                    </select>
                    <span
                      class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                    >
                      <div
                        class="components-input-control-suffix-wrapper e1mv6sxx0 css-1hy8q7r-PrefixSuffixWrapper-prefixSuffixWrapperStyles-InputControlSuffixWrapperWithClickThrough-rtl em5sgkm0"
                        data-wp-c16t="true"
                        data-wp-component="InputControlSuffixWrapper"
                      >
                        <div
                          class="css-di1hl1-DownArrowWrapper e1mv6sxx1"
                        >
                          <svg
                            aria-hidden="true"
                            focusable="false"
                            height="18"
                            viewBox="0 0 24 24"
                            width="18"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                            />
                          </svg>
                        </div>
                      </div>
                    </span>
                    <div
                      aria-hidden="true"
                      class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="components-flex get-rates-button-wrapper css-8owsvq-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <button
            class="components-button is-secondary"
            disabled=""
            title="Complete package selection/fields to get shipping rates"
            type="button"
          >
            Get shipping rates
          </button>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`Packages section should render Saved templates tab 1`] = `
<DocumentFragment>
  <label
    for="saved-templates"
  >
    Package template
  </label>
  <div
    class="components-dropdown saved-templates"
    tabindex="-1"
  >
    <button
      aria-disabled="false"
      aria-expanded="false"
      class="components-button saved-template__toggle is-secondary has-text has-icon"
      type="button"
    >
      <svg
        aria-hidden="true"
        focusable="false"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
        />
      </svg>
      Please select
    </button>
  </div>
  <div
    class="components-spacer css-18ntprw-PolymorphicDiv-classes-classes e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Spacer"
  />
  <div
    class="components-flex css-msclxj-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <div
      class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="FlexBlock"
    >
      <div
        class="components-flex css-k256ns-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Flex"
      >
        <div
          class="components-base-control components-input-control package-total-weight css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <div
              class="components-flex components-input-base em5sgkm5 css-o61hjq-PolymorphicDiv-Flex-base-ItemsColumn-Root e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="InputBase"
            >
              <div
                class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="FlexItem"
              >
                <label
                  class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="Text"
                  for="inspector-input-control-9"
                >
                  Total shipment weight (with package)
                </label>
              </div>
              <div
                class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
              >
                <input
                  class="components-input-control__input css-mxhwb4-Input-dragStyles-fontSizeStyles-sizeStyles-customPaddings em5sgkm3"
                  id="inspector-input-control-9"
                  min="10"
                  step="0.1"
                  type="number"
                  value="10.00"
                />
                <div
                  aria-hidden="true"
                  class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                />
              </div>
            </div>
          </div>
        </div>
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <div
              class="components-flex components-input-base components-select-control package-total-weight-unit e1mv6sxx3 em5sgkm5 css-16rj6q3-PolymorphicDiv-Flex-base-ItemsColumn-Root-StyledInputBase e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="InputBase"
            >
              <div
                class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="FlexItem"
              >
                <label
                  class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="Text"
                  for="inspector-select-control-5"
                >
                  Unit
                </label>
              </div>
              <div
                class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
              >
                <select
                  class="components-select-control__input css-1i1tjy6-Select-fontSizeStyles-sizeStyles-rtl e1mv6sxx2"
                  id="inspector-select-control-5"
                >
                  <option
                    value="oz"
                  >
                    oz
                  </option>
                  <option
                    value="lbs"
                  >
                    lbs
                  </option>
                  <option
                    value="g"
                  >
                    g
                  </option>
                  <option
                    value="kg"
                  >
                    kg
                  </option>
                </select>
                <span
                  class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                >
                  <div
                    class="components-input-control-suffix-wrapper e1mv6sxx0 css-1hy8q7r-PrefixSuffixWrapper-prefixSuffixWrapperStyles-InputControlSuffixWrapperWithClickThrough-rtl em5sgkm0"
                    data-wp-c16t="true"
                    data-wp-component="InputControlSuffixWrapper"
                  >
                    <div
                      class="css-di1hl1-DownArrowWrapper e1mv6sxx1"
                    >
                      <svg
                        aria-hidden="true"
                        focusable="false"
                        height="18"
                        viewBox="0 0 24 24"
                        width="18"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                        />
                      </svg>
                    </div>
                  </div>
                </span>
                <div
                  aria-hidden="true"
                  class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="components-flex get-rates-button-wrapper css-8owsvq-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
    >
      <button
        class="components-button is-secondary"
        disabled=""
        title="Complete package selection/fields to get shipping rates"
        type="button"
      >
        Get shipping rates
      </button>
    </div>
  </div>
</DocumentFragment>
`;
