@import "tab-views/styles";

.shipment-package {

	.package-tabs .components-tab-panel__tab-content {
		padding-top: 24px;
	}

	.package-tabs {
		width: 100%;
		margin-top: 24px;
	}

	.package-tabs > .components-tab-panel__tabs {
		border-radius: 4px;
		border: 1px solid var(--gutenberg-gray-600, #949494);
		background: var(--gutenberg-white, #fff);
		padding: 3px;

		.components-tab-panel__tabs-item {
			font-size: 13px;
			font-style: normal;
			font-weight: 400;
			height: 36px;
			line-height: 16px; /* 123.077% */
			border-radius: 2px;
			padding: 10px 0;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			flex: 1 0 0;
			display: flex;
			text-align: center;
			color: var(--gutenberg-gray-900, #1e1e1e);
			gap: 8px;
		}

		.active-package-tab {
			background: var(--gutenberg-blue, #007cba);
			color: var(--black-white-white, #fff);
		}

	}

	.package-total-weight {
		flex: 1;
		margin-bottom: 0;
	}
}

.components-notice.packages-notice {
	margin-top: 24px;
}
