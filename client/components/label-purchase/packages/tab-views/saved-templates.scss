label[for="saved-templates"] {
	color: var(--gutenberg-gray-900, #1e1e1e);
	display: block;
	font-size: 11px;
	font-style: normal;
	font-weight: 500;
	margin-bottom: 8px;
	text-transform: uppercase;
}

.saved-templates {
	position: relative;
	width: 100%;

	section {
		align-items: center;
		display: flex;
		flex-direction: row;
		width: 100%;

		@include selectRow;

		svg {
			margin: 0;
			padding: 0;
			position: absolute;
			right: 36px;
		}
	}

	// Making it specific to nullify the styles from the Button component.
	.components-button.has-icon.has-text.saved-template__toggle,
	.components-button.has-icon.saved-template__toggle,
	.components-button.saved-template__toggle,
	.components-button.is-secondary.saved-template__toggle {
		background: var(--gutenberg-white, #fff);
		border: 1px solid var(--gutenberg-gray-600, #949494);
		border-radius: 2px;
		box-shadow: none;

		color: var(--gutenberg-gray-600, #949494);
		flex-direction: row-reverse;
		justify-content: space-between;
		padding: 12px;
		width: 100%;
	}

	.components-popover,
	.components-popover__content {
		width: 100%;
	}
}


.components-flex.saved-template__details {

	>div {

		.components-base-control {
			flex: 1;
			margin-bottom: 0;
			position: relative;

			p {
				margin: 0;
			}

			&__help {
				position: absolute;
			}
		}

		.components-input-control__backdrop {
			border-color: var(--gutenberg-gray-600, #949494);
		}

		.components-input-control__suffix {
			color: #007cba;
			margin-right: 12px;
		}

		@include hasError;
	}

	.components-flex .components-select-control__input {
		height: 40px;

		+.components-input-control__suffix {
			margin-right: 0;
		}
	}

	.components-flex .components-input-control__input {
		border-radius: 2px;
		height: 40px;
		padding: 12px;
	}

	.components-spacer {
		color: var(--gutenberg-gray-600, #949494);
	}
}

.saved-template-options {
	display: flex;
	flex-direction: column;

	.components-menu-item__button.components-button[role="menuitemradio"] .components-menu-item__item {
		padding-right: 0;
		width: 100%;

		@include selectRow;

		// The delete icon.
		.saved-template-options__delete {
			color: var(--gutenberg-gray-800, #2f2f2f);
			margin: 0 0 0 auto;
			// Make sure it doesn't shrink on smaller screens.
			min-width: 24px;

			&:hover {
				color: var(--gutenberg-alert-red, #cc1818);
			}
		}

		&:hover,
		&:hover>:first-child {
			color: #007cba;
		}
	}
}

.custom-template__save {

	.components-notice {
		margin-left: 32px;
	}
}

.rates-fetch-error {
	margin-left: 0;
	margin-right: 0;
}
