.save-custom-template {
	border-radius: 2px;
	border: 1px solid var(--gutenberg-gray-600, #949494);
	padding: 12px 12px;

	label {
		user-select: none;
	}

	.components-notice {
		margin-bottom: 8px;
	}

	&__toggle>div {
		display: flex;
	}

}

.save-template-form {
	align-items: flex-start;

	@include hasError;

	&__name {
		flex-grow: 1;
		width: auto;
	}

	&__save-button {
		margin-top: 23px;
	}

	.components-input-control__suffix {
		color: #007cba;
		margin-right: 12px;
	}

	.components-base-control__help {
		margin: 0;
	}

	.components-flex input.components-input-control__input {
		height: 40px;
	}

	.components-base-control {
		flex: 1;
		margin-bottom: 0;
	}
}


.components-flex.custom-package__details {

	>div {

		.components-base-control {
			margin-bottom: 0;
			flex: 1;
			position: relative;

			p {
				margin: 0;
			}

			&__help {
				position: absolute;
			}
		}


		.components-input-control__backdrop {
			border-color: var(--gutenberg-gray-600, #949494);
		}

		.components-input-control__suffix {
			color: #007cba;
			margin-right: 12px;
		}

		@include hasError;

	}


	.components-flex .components-select-control__input {
		height: 40px;

		+.components-input-control__suffix {
			margin-right: 0;
		}
	}

	.components-flex .components-input-control__input {
		border-radius: 2px;
		height: 40px;
		padding: 12px;
	}


	.components-spacer {
		color: var(--gutenberg-gray-600, #949494);
	}

}

.rates-fetch-error {
	margin-left: 0;
	margin-right: 0;
}
