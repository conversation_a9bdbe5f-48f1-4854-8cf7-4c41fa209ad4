@mixin selectRow {
	color: var(--gutenberg-gray-700, #757575);
	font-size: 13px;
	font-style: normal;
	font-weight: 400;
	line-height: 16px;
	gap: 10px;

	:first-child {
		color: var(--gutenberg-gray-900, #1e1e1e);
	}
}

@mixin hasError {

	.has-error {

		.components-flex .components-input-control__input,
		.components-input-control__suffix,
		.components-base-control__help {
			color: var(--wc-red, #f00);
		}

		.components-flex .components-input-control__backdrop {
			border-color: var(--wc-red, #f00);
		}
	}
}
