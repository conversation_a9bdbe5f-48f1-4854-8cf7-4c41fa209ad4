.label-purchase-packages {
	display: flex;
	flex-direction: column;
	gap: 16px;
	margin-top: 12px;

	>div {
		align-items: center;
		align-self: stretch;
		background: var(--gutenberg-white, #fff);
		border: 1px solid var(--gutenberg-gray-300, #ddd);
		border-radius: var(--grid-unit-05, 4px);
		box-sizing: border-box;
		display: flex;
		height: 56px;
		padding: 16px;

		.components-item {
			padding: 0;

			legend {
				display: none;
			}
		}

		.components-radio-control__option {
			align-items: center;
			display: flex;

			.components-radio-control__input {
				margin-right: 16px;
			}

			label {
				width: 100%;

				section {
					align-items: center;
					display: flex;
					justify-content: space-between;

					span:not(:first-child) {
						color: var(--gutenberg-gray-700, #757575);
						flex: 3;
						font-size: 13px;

						font-style: normal;
						font-weight: 400;
						line-height: 16px;
						text-align: right;
					}

					span:first-child {
						flex: 4;
					}


					span:nth-child(3) {
						flex: 1;
					}

					>button {
						margin-left: 16px;
						padding: 0;

						&:focus {
							box-shadow: none;
							outline: none;
						}

						svg {
							fill: #949494;
						}

						&.is-selected svg {
							fill: #007cba;
						}
					}
				}
			}
		}
	}
}

.carrier-package-tabs {
	width: 100%;

	.components-tab-panel__tabs {
		background: var(--gutenberg-white, #fff);
		border-bottom: 1px solid var(--gutenberg-gray-300, #ddd);

		.components-tab-panel__tabs-item {
			height: 40px;
			margin-bottom: -1px;

			.carrier-icon {
				border-radius: 4px;
				margin-left: -6px; // compensate for the padding left of the Button component
				margin-right: 8px;
			}

			.promo-badge {
				margin-left: 6px;
			}
		}
	}

	.components-panel {
		border: none;
		border-radius: var(--grid-unit-05, 4px);
		margin-bottom: 12px;

		:last-child {
			margin-bottom: 0;
		}
	}

	.components-panel__body {
		border-color: var(--gray-gray-0, #f6f7f7);

		.components-item-group {
			margin-left: -16px; // make the items take up the full width of the panel
			margin-right: -16px;
		}
	}

	.components-panel__body-title {
		background: var(--gray-gray-0, #f6f7f7);
		border-radius: var(--grid-unit-05, 4px);
		height: 40px;
		line-height: 40px;

		button.components-button.components-panel__body-toggle {
			background: var(--gray-gray-0, #f6f7f7);
			border-radius: var(--grid-unit-05, 4px);
			color: var(--gutenberg-gray-900, #1e1e1e);

			font-size: 13px;
			font-style: normal;
			font-weight: 400;
			height: 40px;
			line-height: 16px;
			padding: 8px 16px 8px 56px;
			position: relative;
			text-align: left;

			span {
				position: absolute;
			}

			.components-panel__arrow {
				right: 16px;
			}
		}
	}
}
