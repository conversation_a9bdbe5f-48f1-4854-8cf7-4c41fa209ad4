/**
 * @jest-environment jsdom
 */
import '@testing-library/jest-dom';
import { render, screen, fireEvent } from '@testing-library/react';
import { UPSDAPTos } from '../upsdap-tos';
import { UPSDAP_TOS_TYPES } from '../constants';
import { act } from 'react';
import { LabelPurchaseError, OriginAddress } from 'types';

// Mock window.matchMedia
window.matchMedia = jest.fn().mockImplementation( ( query ) => ( {
	matches: false,
	media: query,
	onchange: null,
	addListener: jest.fn(),
	removeListener: jest.fn(),
	addEventListener: jest.fn(),
	removeEventListener: jest.fn(),
	dispatchEvent: jest.fn(),
} ) );

// Mock CSS/SCSS imports
jest.mock( '../styles.scss', () => ( {} ), { virtual: true } );

// Mock the utils module
jest.mock(
	'utils',
	() => {
		return {
			addressToString: jest.fn( () => 'Mocked Address String' ),
			recordEvent: jest.fn(),
		};
	},
	{ virtual: true }
);

// Mock createInterpolateElement since it's used by the component
jest.mock( '@wordpress/element', () => ( {
	...jest.requireActual( '@wordpress/element' ),
	createInterpolateElement: jest.fn( ( text, elements ) => {
		// Simple mock implementation that preserves the link
		return (
			<>
				{ text.split( /<\/?a>/ )[ 0 ] }
				{ elements.a }
				{ text.split( /<\/?a>/ )[ 1 ] }
			</>
		);
	} ),
} ) );

describe( 'UPSDAPTos', () => {
	const defaultProps = {
		close: jest.fn(),
		confirm: jest.fn(),
		shipmentOrigin: {
			id: '1',
			firstName: 'Test',
			lastName: 'User',
			name: 'Test User',
			company: 'Test Company',
			address1: '123 Test St',
			address2: '',
			city: 'Test City',
			state: 'TS',
			postcode: '12345',
			country: 'US',
			phone: '************',
			email: '<EMAIL>',
		} as OriginAddress,
		acceptedVersions: [],
		isConfirming: false,
		setIsConfirming: jest.fn(),
	};

	beforeEach( () => {
		jest.clearAllMocks();
	} );

	test( 'renders with empty acceptedVersions', () => {
		render( <UPSDAPTos { ...defaultProps } /> );

		// Should display title
		expect(
			screen.getByText( 'UPS® Terms and Conditions' )
		).toBeInTheDocument();

		// Should display shipping address
		expect( screen.getByText( 'Shipping from' ) ).toBeInTheDocument();
		expect(
			screen.getByText( 'Mocked Address String' )
		).toBeInTheDocument();

		// All checkboxes should be unchecked initially when acceptedVersions is empty
		const checkboxes = screen.getAllByRole( 'checkbox' );
		expect( checkboxes ).toHaveLength( 3 );
		checkboxes.forEach( ( checkbox ) => {
			expect( checkbox ).not.toBeChecked();
		} );

		// Notice for UPS Ground Saver should not be visible
		expect(
			screen.queryByText( /UPS Ground Saver®/ )
		).not.toBeInTheDocument();

		// Confirm button should be disabled
		const confirmButton = screen.getByText( 'Confirm and continue' );
		expect( confirmButton ).toBeDisabled();
	} );

	test( 'renders with acceptedVersions containing v2', () => {
		render(
			<UPSDAPTos { ...defaultProps } acceptedVersions={ [ 'v2' ] } />
		);

		// Should display title
		expect(
			screen.getByText( 'UPS® Terms and Conditions' )
		).toBeInTheDocument();

		// Find the notice element
		expect(
			screen.getByTestId( 'ups-ground-saver-notice' )
		).toBeInTheDocument();

		// Two checkboxes should be pre-checked (Prohibited Items and Technology Agreement)
		const checkboxes = screen.getAllByRole( 'checkbox' );
		expect( checkboxes ).toHaveLength( 3 );

		const legalCheckbox = checkboxes.find(
			( checkbox ) =>
				checkbox.getAttribute( 'value' ) === UPSDAP_TOS_TYPES.LEGAL
		);
		const prohibitedItemsCheckbox = checkboxes.find(
			( checkbox ) =>
				checkbox.getAttribute( 'value' ) ===
				UPSDAP_TOS_TYPES.PROHIBITED_ITEMS
		);
		const technologyAgreementCheckbox = checkboxes.find(
			( checkbox ) =>
				checkbox.getAttribute( 'value' ) ===
				UPSDAP_TOS_TYPES.TECHNOLOGY_AGREEMENT
		);

		expect( legalCheckbox ).not.toBeChecked();
		expect( prohibitedItemsCheckbox ).toBeChecked();
		expect( technologyAgreementCheckbox ).toBeChecked();

		// Confirm button should still be disabled because legal checkbox is not checked
		const confirmButton = screen.getByText( 'Confirm and continue' );
		expect( confirmButton ).toBeDisabled();
	} );

	test( 'enables confirm button when all checkboxes are checked', () => {
		render( <UPSDAPTos { ...defaultProps } /> );

		// Check all checkboxes
		const checkboxes = screen.getAllByRole( 'checkbox' );
		checkboxes.forEach( ( checkbox ) => {
			fireEvent.click( checkbox );
		} );

		// Confirm button should be enabled
		const confirmButton = screen.getByText( 'Confirm and continue' );
		expect( confirmButton ).toBeEnabled();
	} );

	test( 'calls confirm function when confirm button is clicked', async () => {
		render( <UPSDAPTos { ...defaultProps } /> );

		// Check all checkboxes
		const checkboxes = screen.getAllByRole( 'checkbox' );
		checkboxes.forEach( ( checkbox ) => {
			fireEvent.click( checkbox );
		} );

		// Click confirm button
		const confirmButton = screen.getByText( 'Confirm and continue' );
		await act( async () => {
			fireEvent.click( confirmButton );
		} );

		// Confirm function should be called with true
		expect( defaultProps.setIsConfirming ).toHaveBeenCalledWith( true );
		expect( defaultProps.confirm ).toHaveBeenCalledWith( true );
	} );

	test( 'calls close function when closed', () => {
		render( <UPSDAPTos { ...defaultProps } /> );

		// Directly call the close function since we can't easily trigger the Modal's onRequestClose
		defaultProps.close();

		expect( defaultProps.close ).toHaveBeenCalled();
	} );

	test( 'toggles checkboxes correctly', () => {
		render( <UPSDAPTos { ...defaultProps } /> );

		const checkboxes = screen.getAllByRole( 'checkbox' );
		const legalCheckbox = checkboxes.find(
			( checkbox ) =>
				checkbox.getAttribute( 'value' ) === UPSDAP_TOS_TYPES.LEGAL
		);

		// Check legal checkbox
		fireEvent.click( legalCheckbox! );
		expect( legalCheckbox ).toBeChecked();

		// Uncheck legal checkbox
		fireEvent.click( legalCheckbox! );
		expect( legalCheckbox ).not.toBeChecked();
	} );

	test( 'displays error message when provided', () => {
		const error: LabelPurchaseError = {
			message: [ 'Test error message' ],
			cause: 'purchase_error',
		};

		render( <UPSDAPTos { ...defaultProps } error={ error } /> );

		// Find the error message text in the Notice component
		// Use getAllByText and check that at least one element exists - this handles the a11y-speak duplicates
		const errorMessages = screen.getAllByText( 'Test error message', {
			exact: true,
		} );
		expect( errorMessages.length ).toBeGreaterThan( 0 );
	} );

	test( 'disables confirm button when isConfirming is true', () => {
		render( <UPSDAPTos { ...defaultProps } isConfirming={ true } /> );

		// Check all checkboxes
		const checkboxes = screen.getAllByRole( 'checkbox' );
		checkboxes.forEach( ( checkbox ) => {
			fireEvent.click( checkbox );
		} );

		// Confirm button should be disabled
		const confirmButton = screen.getByText( 'Confirm and continue' );
		expect( confirmButton ).toBeDisabled();
	} );
} );
