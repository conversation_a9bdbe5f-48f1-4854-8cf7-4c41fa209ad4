.wcshipping-ups-tos-modal {
	line-height: 20px;
	max-width: 650px;

	p {
		font-size: 14px;
		font-weight: 400;
		font-style: normal;
		line-height: 20px;
	}

	label {
		cursor: pointer;
	}

	header {
		margin-bottom: 8px;
	}
	
	.components-modal__content {
		padding: 8px 32px 32px 32px;
	}
	
	.components-modal__header {
		padding: 24px 32px 8px 32px;
		
		.components-modal__header-heading {
			font-size: 20px;
			font-weight: 500;
		}
	}
	

	.ups-shipping-from {
		color: #000;
		padding-bottom: 16px;
		border-bottom: 1px solid #F0F0F0;
		margin-bottom: 0;

		h3 {
			font-weight: 500;
			margin: 0 0 4px 0;
			font-size: 14px;
		}

		.address {
			margin: 0;
		}
	}

	.ups-ground-saver-notice {
		background: #fff;
		padding: 0 16px 0 20px;
		border-left: 4px solid #000;
		margin: 0 0 8px 0;
		
		p {
			margin: 0 0 8px 0;
			font-size: 14px;
			line-height: 1.5;
			
			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	button {
		height: 40px;
	}
}

.upsdap-trademark-notice {
	color: var(--<PERSON><PERSON><PERSON>-Gray-700, #757575);
	font-size: 11px;
	font-style: italic;
	margin-top: 25px;

	&.upsdap-trademark-notice--mobile {

		@media only screen and (min-width: 640px) {
			display: none;
		}
	}

	&.upsdap-trademark-notice--desktop {

		@media only screen and (max-width: 639px) {
			display: none;
		}
	}
}
