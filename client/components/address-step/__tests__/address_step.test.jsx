import { render, fireEvent, act, screen } from '@testing-library/react';

import { AddressStep } from '../address_step';

import { registerLabelPurchaseStore } from 'data/label-purchase';
import { LabelPurchaseContext } from 'context/label-purchase';
import { registerAddressStore } from 'data/address';
import { apiFetch } from '@wordpress/data-controls';
import { getAddressNormalizationPath } from '../../../data/routes';
import { emptyOriginAddress } from '../../shipping-settings/constants';
import { registerCarrierStrategyStore } from 'data/carrier-strategy';

const nonce = 'test1234567';
const address = {
	name: '<PERSON>',
	email: '<EMAIL>',
	address: '123 Main St',
	address_2: 'Apt 1',
	city: 'San Francisco',
	state: 'CA',
	postcode: '94105',
	phone: '******-422-5555',
	country: 'US',
};

jest.mock( '@wordpress/data-controls', () => {
	return {
		__esModule: true,
		apiFetch: jest.fn(),
	};
} );

jest.mock( 'utils/config', () => {
	const {
		packagesSettings,
	} = require( 'utils/__tests__/fixtures/package-settings' );
	const utils = jest.requireActual( 'utils' );

	return {
		...utils,
		getConfig: () => ( {
			shippingLabelData: {
				storedData: {
					destination: address,
					origin: address,
				},
			},
			order: {
				id: 123,
				shipping_address: address,
			},
			nonce,
			is_destination_verified: false,
			is_origin_verified: false,
			continents: [
				{
					code: 'NA',
					name: 'North America',
					countries: [
						{
							code: 'US',
							currency_code: 'USD',
							name: 'United States (US)',
							states: [
								{
									code: 'CA',
									name: 'California',
								},
							],
						},
					],
				},
			],
			packagesSettings,
			accountSettings: {
				storeOptions: {
					origin_country: 'US',
				},
			},
			origin_addresses: [ address ],
		} ),
		getSelectedRates: () => null,
		getSelectedHazmat: () => null,
		getOriginAddresses: () => [],
		getCustomsInformation: () => null,
		getCarrierStrategies: () => ( {
			upsdap: {
				originAddress: {
					1: {
						has_agreed_to_tos: true,
					},
				},
			},
		} ),
		getSelectedRateOptions: () => ( {} ),
	};
} );

jest.mock( '@wordpress/api-fetch', () => {
	const originalModule = jest.requireActual( '@wordpress/api-fetch' );
	return {
		__esModule: true,
		createNonceMiddleware: jest.fn( originalModule.createNonceMiddleware ),
		use: jest.fn( originalModule.use ),
		default: jest.fn(),
	};
} );

registerAddressStore( true );
registerLabelPurchaseStore();
registerCarrierStrategyStore();

describe( 'Address step', () => {
	beforeEach( () => {
		apiFetch.mockClear();
	} );

	it( 'should render the destination address', () => {
		const { asFragment } = render(
			<LabelPurchaseContext.Provider
				value={ {
					rates: {
						updateRates: jest.fn(),
					},
				} }
			>
				<AddressStep
					address={ address }
					type="destination"
					orderId={ 123 }
				/>
			</LabelPurchaseContext.Provider>
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should render the origin address', () => {
		const { asFragment } = render(
			<LabelPurchaseContext.Provider
				value={ {
					rates: {
						updateRates: jest.fn(),
					},
				} }
			>
				<AddressStep address={ address } type="origin" />
			</LabelPurchaseContext.Provider>
		);
		expect( asFragment() ).toMatchSnapshot();
	} );

	it( 'should send the expected data for normalization on form submit', async () => {
		const inputs = {
			name: 'Some name',
			email: '<EMAIL>',
			phone: '**********',
			address: 'Some place',
			countryCode: 'US',
			city: 'SAN FRANCISCO',
			stateCode: 'CA',
			postalCode: '94117-1050',
		};

		act( () => {
			render(
				<LabelPurchaseContext.Provider
					value={ {
						rates: {
							updateRates: jest.fn(),
						},
					} }
				>
					<AddressStep address={ emptyOriginAddress } type="origin" />
				</LabelPurchaseContext.Provider>
			);
		} );

		act( () => {
			fireEvent.change( screen.getByLabelText( 'Name' ), {
				target: {
					value: inputs.name,
				},
			} );

			fireEvent.change( screen.getByLabelText( 'Email address' ), {
				target: {
					value: inputs.email,
				},
			} );

			fireEvent.change( screen.getByLabelText( 'Phone' ), {
				target: {
					value: inputs.phone,
				},
			} );

			fireEvent.change( screen.getByLabelText( 'Country' ), {
				target: {
					value: inputs.countryCode,
				},
			} );

			fireEvent.change( screen.getByLabelText( 'Address' ), {
				target: {
					value: inputs.address,
				},
			} );

			fireEvent.change( screen.getByLabelText( 'City' ), {
				target: {
					value: inputs.city,
				},
			} );

			fireEvent.change( screen.getByLabelText( 'State' ), {
				target: {
					value: inputs.stateCode,
				},
			} );

			fireEvent.change( screen.getByLabelText( 'Postal code' ), {
				target: {
					value: inputs.postalCode,
				},
			} );

			fireEvent.click(
				screen.getByLabelText( 'Save as default origin address' )
			);
		} );

		expect( screen.getByText( 'Validate and save' ) ).toBeEnabled();

		await act( async () => {
			await fireEvent.click( screen.getByText( 'Validate and save' ) );
		} );

		expect( apiFetch ).toHaveBeenCalledWith( {
			method: 'POST',
			path: getAddressNormalizationPath(),
			data: {
				address: {
					address: inputs.address,
					address_1: inputs.address,
					address_2: '',
					city: inputs.city,
					company: '',
					country: inputs.countryCode,
					default_address: true,
					email: inputs.email,
					first_name: '',
					id: '',
					is_verified: false,
					last_name: '',
					name: inputs.name,
					phone: inputs.phone,
					postcode: inputs.postalCode,
					state: inputs.stateCode,
				},
			},
		} );
	} );
} );
