// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Address step should render the destination address 1`] = `
<DocumentFragment>
  <div>
    <p>
      Please complete all required fields and click the 'Validate and save' button below to confirm and validate your address details.
    </p>
    <div>
      <div
        class="components-flex css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Flex"
      >
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <label
              class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
              for="inspector-text-control-0"
            >
              Name
            </label>
            <input
              checked=""
              class="components-text-control__input is-next-40px-default-size"
              id="inspector-text-control-0"
              required=""
              type="text"
              value="John"
            />
          </div>
        </div>
        <div
          class="components-spacer css-1az5d2-PolymorphicDiv-classes-classes e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Spacer"
        />
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <label
              class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
              for="inspector-text-control-1"
            >
              Company
            </label>
            <input
              class="components-text-control__input is-next-40px-default-size"
              id="inspector-text-control-1"
              type="text"
              value=""
            />
          </div>
        </div>
        <div
          class="components-spacer css-1az5d2-PolymorphicDiv-classes-classes e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Spacer"
        />
        <div
          class="components-flex-item css-qt3kf4-PolymorphicDiv-Item-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="FlexItem"
        >
          <div
            class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <div
              class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="FlexBlock"
            >
              <div
                class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
              >
                <div
                  class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
                >
                  <label
                    class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
                    for="inspector-text-control-2"
                  >
                    Email address
                  </label>
                  <input
                    checked=""
                    class="components-text-control__input is-next-40px-default-size"
                    id="inspector-text-control-2"
                    type="text"
                    value="<EMAIL>"
                  />
                </div>
              </div>
            </div>
            <div
              class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="FlexBlock"
            >
              <div
                class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
              >
                <div
                  class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
                >
                  <label
                    class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
                    for="inspector-text-control-3"
                  >
                    Phone
                  </label>
                  <input
                    checked=""
                    class="components-text-control__input is-next-40px-default-size"
                    id="inspector-text-control-3"
                    type="text"
                    value="******-422-5555"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="components-spacer css-1az5d2-PolymorphicDiv-classes-classes e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Spacer"
        />
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <div
              class="components-flex components-input-base components-select-control e1mv6sxx3 em5sgkm5 css-16rj6q3-PolymorphicDiv-Flex-base-ItemsColumn-Root-StyledInputBase e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="InputBase"
            >
              <div
                class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="FlexItem"
              >
                <label
                  class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="Text"
                  for="inspector-select-control-0"
                >
                  Country
                </label>
              </div>
              <div
                class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
              >
                <select
                  class="components-select-control__input css-1i1tjy6-Select-fontSizeStyles-sizeStyles-rtl e1mv6sxx2"
                  id="inspector-select-control-0"
                  required=""
                >
                  <option
                    value="US"
                  >
                    United States (US)
                  </option>
                </select>
                <span
                  class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                >
                  <div
                    class="components-input-control-suffix-wrapper e1mv6sxx0 css-1hy8q7r-PrefixSuffixWrapper-prefixSuffixWrapperStyles-InputControlSuffixWrapperWithClickThrough-rtl em5sgkm0"
                    data-wp-c16t="true"
                    data-wp-component="InputControlSuffixWrapper"
                  >
                    <div
                      class="css-di1hl1-DownArrowWrapper e1mv6sxx1"
                    >
                      <svg
                        aria-hidden="true"
                        focusable="false"
                        height="18"
                        viewBox="0 0 24 24"
                        width="18"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                        />
                      </svg>
                    </div>
                  </div>
                </span>
                <div
                  aria-hidden="true"
                  class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                />
              </div>
            </div>
          </div>
        </div>
        <div
          class="components-spacer css-1az5d2-PolymorphicDiv-classes-classes e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Spacer"
        />
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <label
              class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
              for="inspector-text-control-4"
            >
              Address
            </label>
            <input
              checked=""
              class="components-text-control__input is-next-40px-default-size"
              id="inspector-text-control-4"
              required=""
              type="text"
              value="123 Main St"
            />
          </div>
        </div>
        <div
          class="components-spacer css-1az5d2-PolymorphicDiv-classes-classes e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Spacer"
        />
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <label
              class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
              for="inspector-text-control-5"
            >
              City
            </label>
            <input
              checked=""
              class="components-text-control__input is-next-40px-default-size"
              id="inspector-text-control-5"
              required=""
              type="text"
              value="San Francisco"
            />
          </div>
        </div>
        <div
          class="components-spacer css-1az5d2-PolymorphicDiv-classes-classes e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Spacer"
        />
        <div
          class="components-flex-item css-qt3kf4-PolymorphicDiv-Item-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="FlexItem"
          direction="column"
        >
          <div
            class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <div
              class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="FlexBlock"
            >
              <div
                class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
              >
                <div
                  class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
                >
                  <label
                    class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
                    for="inspector-text-control-6"
                  >
                    State
                  </label>
                  <input
                    checked=""
                    class="components-text-control__input is-next-40px-default-size"
                    id="inspector-text-control-6"
                    required=""
                    type="text"
                    value="CA"
                  />
                </div>
              </div>
            </div>
            <div
              class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="FlexBlock"
            >
              <div
                class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
              >
                <div
                  class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
                >
                  <label
                    class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
                    for="inspector-text-control-7"
                  >
                    Postal code
                  </label>
                  <input
                    checked=""
                    class="components-text-control__input is-next-40px-default-size"
                    id="inspector-text-control-7"
                    required=""
                    type="text"
                    value="94105"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <footer
      class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
    >
      <span
        class="verification not-verified"
      >
        <svg
          aria-hidden="true"
          focusable="false"
          height="22"
          viewBox="0 0 24 24"
          width="22"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M5.5 12a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0ZM12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16Zm.75 4v1.5h-1.5V8h1.5Zm0 8v-5h-1.5v5h1.5Z"
            fill-rule="evenodd"
          />
        </svg>
        Unvalidated address
      </span>
      <div
        class="components-flex-item css-115jeco-PolymorphicDiv-Item-sx-Base e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexItem"
      >
        <div
          class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <button
            class="components-button is-tertiary"
            type="button"
          >
            Cancel
          </button>
          <button
            class="components-button is-primary"
            type="button"
          >
            Validate and save
          </button>
        </div>
      </div>
    </footer>
  </div>
</DocumentFragment>
`;

exports[`Address step should render the origin address 1`] = `
<DocumentFragment>
  <div>
    <p>
      Please complete all required fields and click the 'Validate and save' button below to confirm and validate your address details.
    </p>
    <div>
      <div
        class="components-flex css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Flex"
      >
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <label
              class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
              for="inspector-text-control-8"
            >
              Name
            </label>
            <input
              checked=""
              class="components-text-control__input is-next-40px-default-size"
              id="inspector-text-control-8"
              required=""
              type="text"
              value="John"
            />
          </div>
        </div>
        <div
          class="components-spacer css-1az5d2-PolymorphicDiv-classes-classes e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Spacer"
        />
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <label
              class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
              for="inspector-text-control-9"
            >
              Company
            </label>
            <input
              class="components-text-control__input is-next-40px-default-size"
              id="inspector-text-control-9"
              type="text"
              value=""
            />
          </div>
        </div>
        <div
          class="components-spacer css-1az5d2-PolymorphicDiv-classes-classes e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Spacer"
        />
        <div
          class="components-flex-item css-qt3kf4-PolymorphicDiv-Item-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="FlexItem"
        >
          <div
            class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <div
              class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="FlexBlock"
            >
              <div
                class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
              >
                <div
                  class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
                >
                  <label
                    class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
                    for="inspector-text-control-10"
                  >
                    Email address
                  </label>
                  <input
                    checked=""
                    class="components-text-control__input is-next-40px-default-size"
                    id="inspector-text-control-10"
                    required=""
                    type="text"
                    value="<EMAIL>"
                  />
                </div>
              </div>
            </div>
            <div
              class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="FlexBlock"
            >
              <div
                class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
              >
                <div
                  class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
                >
                  <label
                    class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
                    for="inspector-text-control-11"
                  >
                    Phone
                  </label>
                  <input
                    checked=""
                    class="components-text-control__input is-next-40px-default-size"
                    id="inspector-text-control-11"
                    required=""
                    type="text"
                    value="******-422-5555"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="components-spacer css-1az5d2-PolymorphicDiv-classes-classes e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Spacer"
        />
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <div
              class="components-flex components-input-base components-select-control e1mv6sxx3 em5sgkm5 css-16rj6q3-PolymorphicDiv-Flex-base-ItemsColumn-Root-StyledInputBase e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="InputBase"
            >
              <div
                class="components-flex-item em5sgkm1 css-123zd0a-PolymorphicDiv-Item-sx-Base-LabelWrapper e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="FlexItem"
              >
                <label
                  class="components-truncate components-text components-input-control__label em5sgkm2 css-1y9luhs-PolymorphicDiv-Text-sx-Base-BaseLabel-baseLabelTypography e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="Text"
                  for="inspector-select-control-1"
                >
                  Country
                </label>
              </div>
              <div
                class="components-input-control__container css-p2w7oi-Container-containerDisabledStyles-containerWidthStyles em5sgkm4"
              >
                <select
                  class="components-select-control__input css-1i1tjy6-Select-fontSizeStyles-sizeStyles-rtl e1mv6sxx2"
                  id="inspector-select-control-1"
                  required=""
                >
                  <option
                    value="US"
                  >
                    United States (US)
                  </option>
                </select>
                <span
                  class="components-input-control__suffix css-oobb1x-Suffix em5sgkm7"
                >
                  <div
                    class="components-input-control-suffix-wrapper e1mv6sxx0 css-1hy8q7r-PrefixSuffixWrapper-prefixSuffixWrapperStyles-InputControlSuffixWrapperWithClickThrough-rtl em5sgkm0"
                    data-wp-c16t="true"
                    data-wp-component="InputControlSuffixWrapper"
                  >
                    <div
                      class="css-di1hl1-DownArrowWrapper e1mv6sxx1"
                    >
                      <svg
                        aria-hidden="true"
                        focusable="false"
                        height="18"
                        viewBox="0 0 24 24"
                        width="18"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"
                        />
                      </svg>
                    </div>
                  </div>
                </span>
                <div
                  aria-hidden="true"
                  class="components-input-control__backdrop css-btckfs-BackdropUI-rtl em5sgkm6"
                />
              </div>
            </div>
          </div>
        </div>
        <div
          class="components-spacer css-1az5d2-PolymorphicDiv-classes-classes e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Spacer"
        />
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <label
              class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
              for="inspector-text-control-12"
            >
              Address
            </label>
            <input
              checked=""
              class="components-text-control__input is-next-40px-default-size"
              id="inspector-text-control-12"
              required=""
              type="text"
              value="123 Main St"
            />
          </div>
        </div>
        <div
          class="components-spacer css-1az5d2-PolymorphicDiv-classes-classes e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Spacer"
        />
        <div
          class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
        >
          <div
            class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
          >
            <label
              class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
              for="inspector-text-control-13"
            >
              City
            </label>
            <input
              checked=""
              class="components-text-control__input is-next-40px-default-size"
              id="inspector-text-control-13"
              required=""
              type="text"
              value="San Francisco"
            />
          </div>
        </div>
        <div
          class="components-spacer css-1az5d2-PolymorphicDiv-classes-classes e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Spacer"
        />
        <div
          class="components-flex-item css-qt3kf4-PolymorphicDiv-Item-sx-Base e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="FlexItem"
          direction="column"
        >
          <div
            class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <div
              class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="FlexBlock"
            >
              <div
                class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
              >
                <div
                  class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
                >
                  <label
                    class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
                    for="inspector-text-control-14"
                  >
                    State
                  </label>
                  <input
                    checked=""
                    class="components-text-control__input is-next-40px-default-size"
                    id="inspector-text-control-14"
                    required=""
                    type="text"
                    value="CA"
                  />
                </div>
              </div>
            </div>
            <div
              class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="FlexBlock"
            >
              <div
                class="components-base-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
              >
                <div
                  class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
                >
                  <label
                    class="components-base-control__label css-1jylnrs-StyledLabel-baseLabelTypography-labelStyles ej5x27r2"
                    for="inspector-text-control-15"
                  >
                    Postal code
                  </label>
                  <input
                    checked=""
                    class="components-text-control__input is-next-40px-default-size"
                    id="inspector-text-control-15"
                    required=""
                    type="text"
                    value="94105"
                  />
                </div>
              </div>
            </div>
          </div>
          <div
            class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Flex"
          >
            <div
              class="components-flex-item components-flex-block css-1mqm1jz-PolymorphicDiv-Item-sx-Base-block e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="FlexBlock"
            >
              <div
                class="components-base-control components-checkbox-control css-fothu4-Wrapper-boxSizingReset ej5x27r4"
              >
                <div
                  class="components-base-control__field css-2txpkp-StyledField ej5x27r3"
                >
                  <div
                    class="components-flex components-h-stack css-1m5h1bg-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="HStack"
                  >
                    <span
                      class="components-checkbox-control__input-container"
                    >
                      <input
                        class="components-checkbox-control__input"
                        id="inspector-checkbox-control-0"
                        type="checkbox"
                        value="1"
                      />
                    </span>
                    <label
                      class="components-checkbox-control__label"
                      for="inspector-checkbox-control-0"
                    >
                      Save as default origin address
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <footer
      class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
    >
      <span
        class="verification not-verified"
      >
        <svg
          aria-hidden="true"
          focusable="false"
          height="22"
          viewBox="0 0 24 24"
          width="22"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M5.5 12a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0ZM12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16Zm.75 4v1.5h-1.5V8h1.5Zm0 8v-5h-1.5v5h1.5Z"
            fill-rule="evenodd"
          />
        </svg>
        Unvalidated address
      </span>
      <div
        class="components-flex-item css-115jeco-PolymorphicDiv-Item-sx-Base e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="FlexItem"
      >
        <div
          class="components-flex css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="Flex"
        >
          <button
            class="components-button is-tertiary"
            type="button"
          >
            Cancel
          </button>
          <button
            class="components-button is-primary"
            type="button"
          >
            Validate and save
          </button>
        </div>
      </div>
    </footer>
  </div>
</DocumentFragment>
`;
