
.address-suggestion-modal {

	.components-modal__header {
		padding: 32px 32px 16px 32px;

		.components-modal__header-heading {
			color: var(--<PERSON><PERSON><PERSON>-Gray-900, #1e1e1e);

			/* WordPress/Title Small */
			font-size: 20px;
			font-style: normal;
			font-weight: 400;
			line-height: 28px; /* 140% */
		}
	}

	.components-toggle-group-control {
		margin-top: 32px;
		margin-bottom: 16px;
		gap: 16px;

		&:focus,
		&:hover {
			border-color: transparent;
			outline-color: transparent;
			box-shadow: none;
		}

		strong {
			color: var(--<PERSON><PERSON><PERSON>-Gray-900, #1e1e1e);

			/* WordPress/Section Heading */
			font-size: 11px;
			font-weight: 500;
			line-height: 16px;
			text-transform: uppercase;
		}

		button {
			color: var(--<PERSON><PERSON><PERSON>-Gray-900, #1e1e1e);
			height: 80px;
			max-width: 100%;
			white-space: normal;
			text-align: left;
			border-radius: 2px;
			border: 1px solid var(--<PERSON><PERSON><PERSON>-<PERSON>-600, #949494);
			padding: var(--grid-unit-20, 16px);
			font-weight: 400;

			&[data-active-item] {
				border-radius: 2px;
				// 2px border will cause a movement that's why we use outline + border
				border: 1px solid var(--Upcoming-Blueberry, #3858e9);
				outline: 1px solid var(--Upcoming-Blueberry, #3858e9);
				background: var(--WP-Blue-Blue-0, #f0f6fc);
			}
		}

		button + div {
			background: var(--WP-Blue-Blue-0, #f0f6fc);
		}

		&::before {
			background-color: transparent;
		}
	}
}
