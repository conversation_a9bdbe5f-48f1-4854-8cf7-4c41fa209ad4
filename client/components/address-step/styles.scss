@import "./suggestion";

.edit-address-modal,
.address-suggestion-modal {
	max-height: 90%;
	max-width: 640px;
	--footer-height: 96px;

	h1 {
		font-style: normal;
		line-height: 28px; /* 140% */
	}

	.components-base-control {
		position: relative;

		input[aria-describedby$="help"],
		select[aria-describedby$="help"] {
			border: 1.5px solid var(--gutenberg-alert-red, #cc1818);
			border-radius: 2px;
		}
	}

	.components-base-control:has([required]) {

		label::after {
			content: " *";
		}
	}

	.components-base-control__help {
		bottom: -32px;
		color: var(--gutenberg-alert-red, #cc1818);
		position: absolute;
		top: 54px;
	}

	.components-button-group {
		align-items: center;
		display: flex;
		justify-content: space-between;
		padding: 0;

		button:last-child {
			margin-left: 12px;
		}
	}

	.components-modal__content {
		margin-bottom: var(--footer-height);
	}

	footer {
		height: var(--footer-height);
		box-sizing: border-box;
		padding: 24px 32px 32px;
		bottom: 0;
		position: fixed;
		left: 0;
		border-top: 1px solid var(--<PERSON><PERSON><PERSON>-Gray-200, #e0e0e0);
		background: #fff;

		.components-button {
			height: 40px;
		}
	}

	.components-checkbox-control {
		margin-top: 16px;
	}

	.components-notice {
		border-left: 0;
	}
}
