.wcs-confirm-modal-overlay {
	z-index: calc(var(--wcs-main-modal-z-index) + 1);

	.components-modal__header .components-modal__header-heading {
		color: var(--<PERSON><PERSON><PERSON>-<PERSON>-900, #1e1e1e);
		font-size: 20px;
		font-style: normal;
		font-weight: 400;
		line-height: 28px;
	}

	.components-modal__frame {
		background: var(--<PERSON><PERSON><PERSON>-<PERSON>, #fff);
		border-radius: 2px !important;
		max-width: 512px;
		width: 512px;
	}

	.components-notice {
		margin: 0;
	}

	.components-modal__content {
		padding: 0;

		> div > section {
			padding: 16px 32px;
		}
	}

	.components-modal__header {
		padding: 32px 32px 20px 32px;
	}

	.components-button {
		height: 40px;
	}

	footer {
		box-sizing: border-box;
		padding: 24px 32px 32px 32px;
	}
}
