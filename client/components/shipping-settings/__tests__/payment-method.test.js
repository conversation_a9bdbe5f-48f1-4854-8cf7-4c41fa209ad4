import { render } from '@testing-library/react';
import PaymentMethod from 'wcshipping/components/shipping-settings/payment-method';

describe( 'Payment method', () => {
	it( 'should render a payment method', () => {
		const props = {
			type: 'Visa',
			cardDigits: '1234',
			cardName: '<PERSON>',
			expiry: '12/2020',
		};

		const { asFragment } = render( <PaymentMethod { ...props } /> );

		expect( asFragment() ).toMatchSnapshot();
	} );
} );
