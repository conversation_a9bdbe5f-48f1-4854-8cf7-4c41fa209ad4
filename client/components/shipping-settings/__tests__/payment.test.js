import React from 'react';
import { render, screen } from '@testing-library/react';
import { getPaymentMethods } from 'wcshipping/data/settings/selectors';
import PaymentCard from 'wcshipping/components/shipping-settings/payment-card';
import { registerSettingsStore } from 'data/settings';

registerSettingsStore();

beforeEach( () => {
	jest.clearAllMocks();
} );

jest.mock( 'wcshipping/data/settings/selectors', () => {
	// Require the original module to not be mocked...
	const originalModule = jest.requireActual(
		'wcshipping/data/settings/selectors'
	);

	return {
		...originalModule,
		getPaymentMethods: jest.fn(),
	};
} );

jest.mock( 'utils', () => {
	return {
		getOriginAddresses: () => [],
		getConfig: jest.fn( {
			// @ts-ignore
			origin_addresses: [],
		} ),
		getAccountSettings: () => ( {
			formMeta: {
				can_manage_payments: true,
			},
		} ),
	};
} );
describe( 'Shipping settings > Payment', () => {
	it( 'should show payment methods if added', async () => {
		getPaymentMethods.mockReturnValue( [
			{
				payment_method_id: *********,
				name: 'My Special Visa',
				card_type: 'visa',
				card_digits: '4242',
				expiry: '2043-02-28',
			},
		] );
		render( <PaymentCard /> );

		const message = await screen.findAllByText( 'My Special Visa' );
		expect( message ).toHaveLength( 1 );

		expect(
			screen.queryByText( 'No card found. To purchase shipping labels', {
				selector: '.components-notice__content',
			} )
		).toBeNull();
	} );

	it( 'should display relevant UI when no payment method is added', async () => {
		getPaymentMethods.mockReturnValue( [] );
		render( <PaymentCard /> );
		const message = await screen.findAllByText(
			'No card found. To purchase shipping labels',
			{
				exact: false,
				selector: '.components-notice__content',
			}
		);
		expect( message ).toHaveLength( 1 );
	} );
} );
