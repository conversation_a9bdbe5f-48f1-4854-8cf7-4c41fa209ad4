import { act, fireEvent, render, screen } from '@testing-library/react';
import { registerSettingsStore } from 'data/settings';
import apiFetch from '@wordpress/api-fetch';
import flushPromises from 'tests/utilities/flush-promises';
import ShippingSettings from '..';
import { ShippingSettingsContextProvider } from 'context/shipping-settings';
import { getAccountSettingsPath } from 'data/routes';
// Shouldn't import from index to keep label-purchase decoupled from shipping-settings.
import { getPaperSizes } from 'components/label-purchase/label/utils';
import { registerAddressStore } from 'data/address';
import { getEnabledServices } from 'data/settings/selectors';
import { useSettings } from 'data/settings/hooks';

registerAddressStore( false );
registerSettingsStore();

jest.mock( '@wordpress/api-fetch', () => jest.fn() );
jest.mock( '@wordpress/data-controls', () => {
	const originalModule = jest.requireActual( '@wordpress/data-controls' );
	return {
		...originalModule,
		apiFetch: jest.fn(),
	};
} );

jest.mock( 'utils/location', () => {
	return {
		getStoreOrigin: () => ( {
			country: 'US',
			state: 'CA',
		} ),
		getOriginAddresses: () => [],
		getFirstSelectableOriginAddress: () => ( {} ),
	};
} );

jest.mock( 'utils', () => {
	const utils = jest.requireActual( 'utils' );
	return {
		...utils,
		getAccountSettings: () => ( {
			formMeta: {
				can_manage_payments: true,
				payment_methods: [
					{
						payment_method_id: *********,
						name: 'My Visa',
						card_type: 'visa',
						card_digits: '4242',
						expiry: '2043-02-28',
					},
					{
						payment_method_id: 456456,
						name: 'My second visa',
						card_type: 'visa',
						card_digits: '4242',
						expiry: '2024-12-31',
					},
				],
			},
		} ),
	};
} );

jest.mock( 'data/settings/selectors', () => ( {
	...jest.requireActual( 'data/settings/selectors' ),
	getEnabledServices: jest.fn(),
} ) );

jest.mock( '@wordpress/data', () => ( {
	...jest.requireActual( '@wordpress/data' ),
	dispatch: jest.fn().mockImplementation( ( store ) => {
		if ( store === 'core/notices' ) {
			return {
				createSuccessNotice: jest.fn(),
			};
		}
		return jest.requireActual( '@wordpress/data' ).dispatch( store );
	} ),
} ) );

const defaultSettings = {
	labelSize: 'label',
	emailReceiptEnabled: false,
	rememberServiceEnabled: false,
	rememberPackageEnabled: true,
	checkoutAddressValidation: true,
	taxIdentifiers: {
		ioss: '',
		voec: '',
	},
	storeOwnerUsername: 'admin',
	storeOwnerLogin: 'admin',
	storeOwnerEmail: '<EMAIL>',
	canManagePayments: true,
};

jest.mock( 'data/settings/hooks', () => ( {
	useSettings: jest.fn().mockReturnValue( defaultSettings ),
} ) );

const renderShippingSettings = () => {
	render(
		<ShippingSettingsContextProvider
			initialValue={ {
				originAddresses: {
					originAddresses: [],
					fetchOriginAddresses: jest.fn(),
					isOriginAddressFormOpen: false,
					saveOriginAddress: jest.fn(),
					isOriginAddressDestroyConfirmationOpen: false,
					isOriginAddressFormBusy: false,
					selectedOriginAddress: undefined,
					updateOriginAddress: jest.fn(),
					openOriginAddressForm: jest.fn(),
					closeOriginAddressForm: jest.fn(),
					openOriginAddressDestroyConfirmation: jest.fn(),
					closeOriginAddressDestroyConfirmation: jest.fn(),
				},
			} }
		>
			<ShippingSettings />
		</ShippingSettingsContextProvider>
	);
};

describe( 'Shipping settings', () => {
	beforeEach( () => {
		// Define default values.
		getEnabledServices.mockReturnValue( [] );
		useSettings.mockReturnValue( defaultSettings );
	} );

	afterEach( () => {
		apiFetch.mockClear();
	} );

	it( 'should save the service selection', async () => {
		apiFetch.mockResolvedValue( { success: true } );

		renderShippingSettings();

		// Click the checkbox, set "use_last_service: true"
		fireEvent.click( screen.getByText( 'Remember service selection' ) );

		// Click save
		fireEvent.click( screen.getByText( 'Save changes' ) );

		// Trigger save, which should be a POST with the proper payload
		await act( () => flushPromises() );
		expect( apiFetch ).toHaveBeenCalledWith( {
			method: 'POST',
			path: '/wcshipping/v1/account/settings',
			data: {
				use_last_service: true,
			},
		} );

		expect( apiFetch ).toHaveBeenCalledTimes( 1 );
	} );

	it( 'should select label size from dropdown and save', async () => {
		apiFetch.mockResolvedValue( { success: true } );

		renderShippingSettings();

		// Click dropdown for label size, select A4.
		fireEvent.change( screen.getByLabelText( 'Paper size' ), {
			target: { value: getPaperSizes( 'US' )[ 0 ].key },
		} );

		// Click save
		fireEvent.click( screen.getByText( 'Save changes' ) );

		// Trigger save, which should be a POST with the proper payload
		await act( () => flushPromises() );
		expect( apiFetch ).toHaveBeenCalledWith( {
			method: 'POST',
			path: getAccountSettingsPath(),
			data: {
				paper_size: getPaperSizes( 'US' )[ 0 ].key,
				use_last_service: true,
			},
		} );

		expect( apiFetch ).toHaveBeenCalledTimes( 1 );
	} );

	it( 'should display the correct store owner information for email receipts', async () => {
		apiFetch.mockResolvedValueOnce( {
			formMeta: {
				master_user_name: 'Demo Automattic Woo',
				master_user_login: 'demo1234',
				master_user_email: '<EMAIL>',
			},
		} );

		renderShippingSettings();

		// Select the checkbox with the store owner information.
		fireEvent.click( screen.getByText( 'Email label purchase receipts' ) );

		// Click save
		fireEvent.click( screen.getByText( 'Save changes' ) );

		// Trigger save, which should be a POST with the proper payload
		await act( () => flushPromises() );
		expect( apiFetch ).toHaveBeenCalledWith( {
			method: 'POST',
			path: getAccountSettingsPath(),
			data: {
				email_receipts: true,
				paper_size: 'label',
				use_last_service: true,
			},
		} );

		expect( apiFetch ).toHaveBeenCalledTimes( 1 );
	} );

	it( 'should select the second Visa card and save', async () => {
		// Mock the "save change" result.
		apiFetch.mockResolvedValueOnce( { success: true } );

		renderShippingSettings();

		// Select the second visa card.
		fireEvent.click( screen.getByText( 'My second visa' ) );

		// Click save
		fireEvent.click( screen.getByText( 'Save changes' ) );

		// Trigger save, which should be a POST with the proper payload
		await act( () => flushPromises() );
		expect( apiFetch ).toHaveBeenCalledWith( {
			method: 'POST',
			path: '/wcshipping/v1/account/settings',
			data: {
				email_receipts: true,
				use_last_service: true,
				paper_size: 'label',
				selected_payment_method_id: 456456,
			},
		} );

		expect( apiFetch ).toHaveBeenCalledTimes( 1 );
	} );

	it( 'should display the live rates if the service is enabled.', async () => {
		getEnabledServices.mockReturnValue( [
			{
				zone_id: '1',
				instance_id: '8',
				method_id: 'wc_services_usps',
				method_order: '2',
				is_enabled: '1',
				zone_name: 'United States (US)',
				zone_order: '0',
				service_type: 'shipping',
				title: 'USPS',
			},
		] );

		renderShippingSettings();

		// The "Live rates" text should be there
		expect( screen.getByText( 'Live rates' ) ).toBeTruthy();

		// The "Select package" button should be there
		expect( screen.getByText( 'Select packages' ) ).toBeTruthy();
	} );

	it( 'should not display the live rates if the service is disabled.', async () => {
		getEnabledServices.mockReturnValue( [] ); // no live rates enabled.

		renderShippingSettings();

		// The "Live rates" text should not be there
		expect( screen.queryByText( 'Live rates' ) ).toBeNull();

		// The "Select package" button should not be there
		expect( screen.queryByText( 'Select packages' ) ).toBeNull();
	} );

	it( 'should update and save tax identifiers', async () => {
		// Mock the "save change" result.
		apiFetch.mockResolvedValueOnce( { success: true } );
		renderShippingSettings();

		// Find and update both inputs.
		const iossInput = screen.getByLabelText(
			'Import One-Stop Shop (IOSS)'
		);
		fireEvent.change( iossInput, { target: { value: 'IM123456789' } } );

		const voecInput = screen.getByLabelText(
			'Norwegian VAT On E-Commerce (VOEC)'
		);
		fireEvent.change( voecInput, { target: { value: 'NO123456789' } } );

		const pvaInput = screen.getByLabelText(
			'Postponed VAT Accounting (PVA)'
		);
		fireEvent.change( pvaInput, { target: { value: 'GB12345678901' } } );

		// Click save.
		fireEvent.click( screen.getByText( 'Save changes' ) );

		// Trigger save request.
		await act( () => flushPromises() );
		expect( apiFetch ).toHaveBeenCalledWith( {
			method: 'POST',
			path: '/wcshipping/v1/account/settings',
			data: {
				email_receipts: true,
				paper_size: 'label',
				selected_payment_method_id: 456456,
				tax_identifier_ioss: 'IM123456789',
				tax_identifier_voec: 'NO123456789',
				tax_identifier_pva: 'GB12345678901',
				use_last_service: true,
			},
		} );

		expect( apiFetch ).toHaveBeenCalledTimes( 1 );
	} );

	it( 'should display tax identifier values if populated', async () => {
		useSettings.mockReturnValue( {
			...defaultSettings,
			taxIdentifiers: {
				ioss: 'IM987654321',
				voec: 'NO987654321',
				pva: 'GB12345678901',
			},
		} );

		renderShippingSettings();

		const iossInput = screen.getByLabelText(
			'Import One-Stop Shop (IOSS)'
		);
		expect( iossInput ).toHaveValue( 'IM987654321' );

		const voecInput = screen.getByLabelText(
			'Norwegian VAT On E-Commerce (VOEC)'
		);
		expect( voecInput ).toHaveValue( 'NO987654321' );

		const pvaInput = screen.getByLabelText(
			'Postponed VAT Accounting (PVA)'
		);
		expect( pvaInput ).toHaveValue( 'GB12345678901' );
	} );

	it( 'should display empty values when no tax identifiers exist', async () => {
		useSettings.mockReturnValue( {
			...defaultSettings,
			taxIdentifiers: {},
		} );

		renderShippingSettings();

		const iossInput = screen.getByLabelText(
			'Import One-Stop Shop (IOSS)'
		);
		expect( iossInput ).toHaveValue( '' );

		const voecInput = screen.getByLabelText(
			'Norwegian VAT On E-Commerce (VOEC)'
		);
		expect( voecInput ).toHaveValue( '' );

		const pva = screen.getByLabelText( 'Postponed VAT Accounting (PVA)' );
		expect( pva ).toHaveValue( '' );
	} );

	it( 'should save the remember last shipping date setting', async () => {
		apiFetch.mockResolvedValue( { success: true } );

		renderShippingSettings();

		// Click the checkbox to enable remembering last shipping date
		fireEvent.click( screen.getByText( 'Remember last shipping date' ) );

		// Click save
		fireEvent.click( screen.getByText( 'Save changes' ) );

		// Trigger save, which should be a POST with the proper payload
		await act( () => flushPromises() );
		expect( apiFetch ).toHaveBeenCalledWith(
			expect.objectContaining( {
				method: 'POST',
				path: '/wcshipping/v1/account/settings',
				data: expect.objectContaining( {
					remember_last_used_shipping_date: true,
				} ),
			} )
		);

		expect( apiFetch ).toHaveBeenCalledTimes( 1 );
	} );
} );
