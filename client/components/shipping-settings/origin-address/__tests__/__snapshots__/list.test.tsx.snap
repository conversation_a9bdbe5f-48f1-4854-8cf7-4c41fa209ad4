// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Origin address list should render a list of origin addresses 1`] = `
<DocumentFragment>
  <div
    class="components-flex wcshipping-settings css-7hlvk3-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
    data-wp-c16t="true"
    data-wp-component="Flex"
  >
    <div
      class="components-flex css-5i76gp-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Flex"
    >
      <div
        class="components-spacer css-19l0t81-PolymorphicDiv-classes-classes e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Spacer"
      />
      <h4
        class="components-truncate components-text components-heading css-83s3ru-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Heading"
      >
        Origin addresses
      </h4>
      <span
        class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Text"
      >
        Origin address sets where your products are shipped from, influencing shipping rates and taxes. You can always change your default address.
      </span>
    </div>
    <div
      class="components-surface components-card wcshipping-settings__card css-yy81gt-PolymorphicDiv-Surface-getBorders-primary-Card-rounded e19lxcc00"
      data-wp-c16t="true"
      data-wp-component="Card"
    >
      <div
        class="css-1wse5yy-PolymorphicDiv-Content e19lxcc00"
      >
        <div
          class="components-flex components-card__header components-card-header css-m1y8tl-PolymorphicDiv-Flex-base-ItemsRow-Header-borderRadius-borderColor-large e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="CardHeader"
        >
          <h5
            class="components-truncate components-text components-heading css-d97yjc-PolymorphicDiv-Text-sx-Base-block e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="Heading"
          >
            Locations
          </h5>
        </div>
        <div
          class="components-flex components-h-stack components-v-stack origin-address-list-item css-1htv1lk-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="VStack"
        >
          <div
            class="components-card__body components-card-body css-ociyh0-PolymorphicDiv-Body-borderRadius-large e19lxcc00"
            data-wp-c16t="true"
            data-wp-component="CardBody"
          >
            <div
              class="components-flex components-h-stack css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
              data-wp-c16t="true"
              data-wp-component="HStack"
            >
              <div
                class="components-flex components-h-stack components-v-stack origin-address-list-item__container css-1wcxw22-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="VStack"
              >
                <div
                  class="components-flex components-h-stack css-1ub13jk-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="HStack"
                >
                  <span
                    class="components-truncate components-text css-1wqj73d-PolymorphicDiv-Text-sx-Base e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="Text"
                  >
                    Jenny Doe
                  </span>
                  <span
                    class="components-truncate components-text css-kdpwij-PolymorphicDiv-Text-sx-Base-muted e19lxcc00"
                    data-wp-c16t="true"
                    data-wp-component="Text"
                  >
                    Default
                  </span>
                </div>
                <span
                  class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="Text"
                >
                  Noone Inc.
                </span>
                <span
                  class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="Text"
                />
                <span
                  class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="Text"
                >
                  Void City ZZ 00000
                </span>
                <span
                  class="components-truncate components-text css-rgxig0-PolymorphicDiv-Text-sx-Base e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="Text"
                >
                  Unknown
                </span>
              </div>
              <div
                class="components-flex components-h-stack components-v-stack css-19k2y83-PolymorphicDiv-Flex-base-ItemsColumn e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="VStack"
              />
              <div
                class="components-flex components-h-stack origin-address-list-item__action-buttons-container css-1897a1g-PolymorphicDiv-Flex-base-ItemsRow e19lxcc00"
                data-wp-c16t="true"
                data-wp-component="HStack"
              >
                <button
                  class="components-button origin-address-list-item__action-buttons-button is-link"
                  type="button"
                >
                  Edit
                </button>
                <span
                  class="components-truncate components-text css-17eddve-PolymorphicDiv-Text-sx-Base e19lxcc00"
                  data-wp-c16t="true"
                  data-wp-component="Text"
                >
                  |
                </span>
                <button
                  class="components-button origin-address-list-item__action-buttons-button is-link is-destructive"
                  disabled=""
                  type="button"
                >
                  Delete
                </button>
                <div
                  class="origin-address-list-item__action-tooltip"
                >
                  <svg
                    aria-expanded="false"
                    aria-haspopup="true"
                    aria-hidden="true"
                    focusable="false"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 4.75a7.25 7.25 0 100 14.5 7.25 7.25 0 000-14.5zM3.25 12a8.75 8.75 0 1117.5 0 8.75 8.75 0 01-17.5 0zM12 8.75a1.5 1.5 0 01.167 2.99c-.465.052-.917.44-.917 1.01V14h1.5v-.845A3 3 0 109 10.25h1.5a1.5 1.5 0 011.5-1.5zM11.25 15v1.5h1.5V15h-1.5z"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="components-flex components-card__footer components-card-footer css-1vpxjqa-PolymorphicDiv-Flex-base-ItemsRow-Footer-borderRadius-borderColor-large e19lxcc00"
          data-wp-c16t="true"
          data-wp-component="CardFooter"
        >
          <button
            class="components-button is-secondary"
            type="button"
          >
            Add new address
          </button>
        </div>
      </div>
      <div
        aria-hidden="true"
        class="components-elevation css-1garmpw-PolymorphicDiv-Elevation-sx-Base-sx-Base-elevationClassName e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Elevation"
      />
      <div
        aria-hidden="true"
        class="components-elevation css-1garmpw-PolymorphicDiv-Elevation-sx-Base-sx-Base-elevationClassName e19lxcc00"
        data-wp-c16t="true"
        data-wp-component="Elevation"
      />
    </div>
  </div>
</DocumentFragment>
`;
