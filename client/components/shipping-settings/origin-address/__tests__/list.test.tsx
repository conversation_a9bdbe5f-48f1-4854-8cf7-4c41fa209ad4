import React from 'react';
import { render, waitFor } from '@testing-library/react';
import { OriginAddress } from 'types';
import { OriginAddressList } from '../list';
import { registerSettingsStore } from 'data/settings';
import { registerAddressStore } from 'data/address';
import { ShippingSettingsContextProvider } from 'context/shipping-settings';

export const testAddress: OriginAddress = {
	id: '1',
	name: '<PERSON>',
	firstName: '<PERSON>',
	lastName: '<PERSON><PERSON>',
	email: '<EMAIL>',
	phone: '*********',
	company: 'Noone Inc.',
	address1: '000 Nowhere St',
	address2: 'Apt 000',
	city: 'Void City',
	state: 'ZZ',
	country: 'Unknown',
	postcode: '00000',
	defaultAddress: true,
	isVerified: true,
};

registerAddressStore( false );
registerSettingsStore();
jest.mock( 'utils', () => {
	const utils = jest.requireActual( 'utils' );
	return {
		...utils,
		getOriginAddresses: () => [ testAddress ],
		getAllowedOriginCountries: jest.fn(),
		getFirstSelectableOriginAddress: () => testAddress,
		getStoreOrigin: () => testAddress,
		getAccountSettings: () => ( {} ),
	};
} );
describe( 'Origin address list', () => {
	afterEach( () => {
		jest.clearAllMocks();
	} );

	it( 'should render a list of origin addresses', async () => {
		const fetchOriginAddressesMock = jest.fn();

		const { asFragment } = render(
			<ShippingSettingsContextProvider
				initialValue={ {
					originAddresses: {
						addresses: [ testAddress ],
						isOriginAddressFormOpen: false,
						isOriginAddressDestroyConfirmationOpen: false,
						selectedOriginAddress: undefined,
						updateOriginAddress: jest.fn(),
						openOriginAddressForm: jest.fn(),
						closeOriginAddressForm: jest.fn(),
						openOriginAddressDestroyConfirmation: jest.fn(),
						closeOriginAddressDestroyConfirmation: jest.fn(),
					},
				} }
			>
				<OriginAddressList />
			</ShippingSettingsContextProvider>
		);

		expect( asFragment() ).toMatchSnapshot();

		waitFor( () => {
			expect( fetchOriginAddressesMock ).toHaveBeenCalledTimes( 1 );
		} );
	} );
} );
