@import "components/address-verified-icon/styles";
@import "components/address-step/styles";

.wcshipping-radio-control {

	.components-radio-control__option {
		// Align the RadioControl component circle to our component.
		align-items: center;
		box-shadow: 0 0 0 1px #e1e2e2;

		// Style around the boxes based on WCS&T style.
		display: flex;
		padding: 16px 24px;
	}

	label {
		// Fill <PaymentMethod> to the whole width.
		flex-grow: 1;
	}
}

.wcshipping-settings {

	> .components-flex:first-of-type {
		max-width: 330px;
	}

	.components-card,
	> .components-flex:last-of-type {
		max-width: 680px;
		width: 100%;
	}

	&__card.loading {
		// Set opacity to 0.5 when loading.
		opacity: 0.5;
	}

	&__spinner {
		height: 100px;
		left: 50%;
		position: absolute;
		top: 50%;
		width: 100px;
		z-index: 1000;
	}

	.components-select-control {

		label.components-text {
			color: var(--G<PERSON>nberg-Gray-900, #1e1e1e);
			font-size: 13px;

			font-weight: 400;
			line-height: 16px;
			text-transform: none;
		}
	}

	&__extras {
		color: var(--<PERSON><PERSON><PERSON>-<PERSON>-700, #757575);
		font-size: 12px;
		font-style: normal;
		font-weight: 400;
		line-height: 16px;
		margin-top: 0;
	}

	h4 {
		font-size: 14px;
		margin-bottom: 16px;
		margin-top: 24px;

		&:first-of-type {
			margin-top: 0;
		}
	}

	.components-notice {
		border: none;
		margin: 8px;
	}

	.components-checkbox-control {

		label {
			cursor: pointer;
		}
	}
}


.payment-method-card__container {
	align-items: center;
	display: flex;
	flex-grow: 1;
}

.payment-method-icon {
	margin-right: 14px;
}

.payment-method-card__text {
	flex-grow: 1;

	p {
		margin: 0;
	}

	.payment-method-card__text-title {
		font-weight: 700;
		text-transform: uppercase;
	}
}

.payment-method-card__expiry {
	float: right;
	font-style: italic;
}

.origin-address-list-item {

	.components-flex:nth-child(2) {
		flex: 1;
	}
}

.origin-address-list-item__container {
	width: 60%;
}

.origin-address-list-item__default-address-container,
.origin-address-list-item__action-buttons-container {
	width: auto;

	.origin-address-list-item__default-address-icon {
		border: 1px solid #008420;
		border-radius: 100%;
	}

	.origin-address-list-item__default-address-text {
		color: var(--wp-green-green-50, #008a20);
	}

	.origin-address-list-item__action-buttons-button {
		text-decoration: none;
	}

	.origin-address-list-item__action-tooltip {
		width: 24px;

		.tooltip-content {
			display: block;
			padding: 5px;
			width: 130px;
		}
	}

	.origin-address-list-item__action-spacer {
		width: 24px;
	}
}

.origin-address-form__container {
	// Helps are only shown when the field is invalid.
	.components-base-control__help {
		color: var(--wp-alert-red, #cc1818);
	}

	.origin-address-form__field-invalid {

		.components-input-control__backdrop {
			border-color: var(--wp-alert-red, #cc1818);
		}

		.components-base-control__help {
			color: var(--wp-alert-red, #cc1818);
		}
	}

	.has-error {

		.components-input-control__backdrop {
			border-color: var(--wp-alert-red, #cc1818);
		}
	}

	.components-notice {
		margin: 0;
	}
}

.origin-address-form__actions-container {
	border-top: 1px solid var(--Gutenberg-Gray-200, #e0e0e0);
	margin: 0 -32px -32px;
	padding: 1.125rem 2rem;
	width: 100%;

	.origin-address-form__address-verified-icon {
		border: 1px solid #008420;
		border-radius: 100%;
	}

	.origin-address-form__address-verified-text {
		color: var(--wp-green-green-50, #008a20);
	}

	.origin-address-form__address-invalid-icon {
		border: 1px solid #cc1818;
		border-radius: 100%;
		color: var(--wp-alert-red, #cc1818);
		font-weight: 700;
		text-align: center;
		width: 15px;
	}

	.origin-address-form__address-invalid-text {
		color: var(--wp-alert-red, #cc1818);
	}

	.origin-address-form__actions-cancel {
		text-decoration: none;
	}

	.origin-address-form__actions-save {
		border-radius: 0.125rem;
	}
}

.origin-address__delete_confirmation {

	.components-notice {
		border: 0;
		margin: 0;
	}
}
