import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AddressValidationNotices } from 'components/checkout';
import { useDispatch, useSelect } from '@wordpress/data';

jest.mock( '@wordpress/data', () => ( {
	useDispatch: jest.fn(),
	useSelect: jest.fn(),
} ) );

jest.mock( '@woocommerce/blocks-checkout', () => ( {
	extensionCartUpdate: jest.fn().mockResolvedValue( {} ), // Mock resolved value if needed
} ) );

describe( 'AddressValidationNotices', () => {
	const noticeIdPrefix = 'wcshipping-av-';
	const noticesContext = 'wc/checkout/shipping-address';

	let createNoticeMock;
	let removeNoticesMock;
	let getNoticesMock;

	beforeEach( () => {
		createNoticeMock = jest.fn();
		removeNoticesMock = jest.fn();
		getNoticesMock = jest.fn().mockReturnValue( [] );

		// Mock the useDispatch hook to return our createNoticeMock and removeNoticesMock functions.
		useDispatch.mockReturnValue( {
			createNotice: createNoticeMock,
			removeNotices: removeNoticesMock,
		} );

		// Mock the useSelect hook to return an empty list of notices.
		useSelect.mockImplementation( ( fn ) =>
			fn( () => ( {
				getNotices: getNoticesMock,
			} ) )
		);
	} );

	afterEach( () => {
		jest.clearAllMocks();
	} );

	it( 'should remove existing notices if no new notices are present', () => {
		render(
			<AddressValidationNotices
				extensions={ { 'woocommerce-shipping': { notices: [] } } }
				cart={ { shippingAddress: { country: 'US' } } }
			/>
		);

		expect( removeNoticesMock ).toHaveBeenCalledTimes( 2 );
		expect( removeNoticesMock ).toHaveBeenCalledWith( [], noticesContext );
		expect( createNoticeMock ).not.toHaveBeenCalled();
	} );

	it( 'should create notices when notices are provided in the extensions', () => {
		const notices = [
			{ type: 'info', message: 'Notice 1' },
			{ type: 'error', message: 'Notice 2' },
		];

		render(
			<AddressValidationNotices
				extensions={ { 'woocommerce-shipping': { notices } } }
				cart={ { shippingAddress: { country: 'US' } } }
			/>
		);

		expect( createNoticeMock ).toHaveBeenCalledTimes( 2 );
		expect( createNoticeMock ).toHaveBeenCalledWith(
			'info',
			'Notice 1',
			expect.objectContaining( {
				id: noticeIdPrefix + '0',
				context: noticesContext,
			} )
		);
		expect( createNoticeMock ).toHaveBeenCalledWith(
			'error',
			'Notice 2',
			expect.objectContaining( {
				id: noticeIdPrefix + '1',
				context: noticesContext,
			} )
		);
	} );
} );
