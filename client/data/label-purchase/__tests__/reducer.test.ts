import { LABEL_PURCHASE_STATUS } from '../../constants';
import { LABEL_STATUS_RESOLVED } from '../label/action-types';
import { labelPurchaseReducer } from '../reducer';
import { LabelPurchaseState } from '../../types';
import { Rate, RateWithParent, Label, SelectedRates } from 'types';

jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );

	return mockUtils();
} );

describe( 'labelPurchaseReducer', () => {
	describe( 'LABEL_STATUS_RESOLVED', () => {
		const mockRate: Rate = {
			carrierId: 'usps',
			freePickup: false,
			insurance: 0,
			isSelected: true,
			listRate: 10,
			rate: 10,
			rateId: 'rate_123',
			retailRate: 12,
			serviceId: 'priority',
			shipmentId: 'shipment_0',
			title: 'Priority Mail',
			tracking: true,
			caveats: [],
		};

		const mockRateWithParent: RateWithParent = {
			rate: mockRate,
			parent: null,
		};

		const mockLabel: Label = {
			labelId: 123,
			id: '0',
			tracking: null,
			refundableAmount: 10,
			created: Date.now(),
			carrierId: 'usps',
			serviceName: 'Priority Mail',
			status: LABEL_PURCHASE_STATUS.PURCHASED,
			commercialInvoiceUrl: '',
			isCommercialInvoiceSubmittedElectronically: false,
			packageName: 'Package',
			isLetter: false,
			productNames: [ 'Product' ],
			productIds: [ 1 ],
			rate: 10,
			receiptItemId: 1,
			createdDate: Date.now(),
			currency: 'USD',
			expiryDate: Date.now() + 86400000,
			labelCached: Date.now(),
			mainReceiptId: 1,
			refund: undefined,
		};

		let mockSelectedRates: SelectedRates;
		let initialState: Partial< LabelPurchaseState >;

		beforeEach( () => {
			mockSelectedRates = {
				shipment_0: mockRateWithParent,
				shipment_1: {
					...mockRateWithParent,
					rate: { ...mockRate, rateId: 'rate_456' },
				},
			};

			initialState = {
				labels: {
					0: [ { ...mockLabel } ],
					1: [
						{
							...mockLabel,
							labelId: 456,
							id: 'label_456',
						},
					],
				},
				selectedRates: mockSelectedRates,
			};
		} );

		it( 'should update label status when payload contains labelId', () => {
			const action = {
				type: LABEL_STATUS_RESOLVED,
				payload: {
					...mockLabel,
					status: LABEL_PURCHASE_STATUS.PURCHASE_ERROR,
					refund: {
						is_manual: false,
						requested_date: Date.now(),
						status: 'complete' as const,
					},
				},
			};

			const newState = labelPurchaseReducer(
				initialState as LabelPurchaseState,
				action
			);

			expect( newState.labels?.[ 0 ][ 0 ] ).toEqual( {
				...mockLabel,
				status: LABEL_PURCHASE_STATUS.PURCHASE_ERROR,
				refund: {
					is_manual: false,
					requested_date: expect.any( Number ),
					status: 'complete',
				},
			} );
			// Other shipment should remain unchanged
			expect( newState.labels?.[ 1 ][ 0 ] ).toEqual(
				initialState.labels?.[ 1 ][ 0 ]
			);
		} );

		it( 'should remove selected rate when label has refund', () => {
			const action = {
				type: LABEL_STATUS_RESOLVED,
				payload: {
					...mockLabel,
					status: LABEL_PURCHASE_STATUS.PURCHASED,
					refund: {
						is_manual: false,
						requested_date: Date.now(),
						status: 'complete' as const,
					},
				},
			};

			const newState = labelPurchaseReducer(
				initialState as LabelPurchaseState,
				action
			);

			const selectedRates = newState.selectedRates as SelectedRates;

			expect( selectedRates.shipment_0 ).toBeUndefined();
			expect( selectedRates.shipment_1 ).toEqual(
				mockSelectedRates.shipment_1
			);
		} );

		it( 'should remove selected rate when label status is unknown', () => {
			const action = {
				type: LABEL_STATUS_RESOLVED,
				payload: {
					...mockLabel,
					status: LABEL_PURCHASE_STATUS.UNKNOWN,
				},
			};

			const newState = labelPurchaseReducer(
				initialState as LabelPurchaseState,
				action
			);

			const selectedRates = newState.selectedRates as SelectedRates;
			expect( selectedRates.shipment_0 ).toBeUndefined();
			expect( selectedRates.shipment_1 ).toEqual(
				mockSelectedRates.shipment_1
			);
		} );

		it( 'should remove selected rate when label has purchase error', () => {
			const action = {
				type: LABEL_STATUS_RESOLVED,
				payload: {
					...mockLabel,
					status: LABEL_PURCHASE_STATUS.PURCHASE_ERROR,
				},
			};

			const newState = labelPurchaseReducer(
				initialState as LabelPurchaseState,
				action
			);

			const selectedRates = newState.selectedRates as SelectedRates;
			expect( selectedRates.shipment_0 ).toBeUndefined();
			expect( selectedRates.shipment_1 ).toEqual(
				mockSelectedRates.shipment_1
			);
		} );

		it( 'should not modify state when payload is missing labelId', () => {
			const action = {
				type: LABEL_STATUS_RESOLVED,
				payload: {
					status: LABEL_PURCHASE_STATUS.PURCHASE_ERROR,
				} as Label,
			};

			const newState = labelPurchaseReducer(
				initialState as LabelPurchaseState,
				action
			);

			expect( newState ).toEqual( initialState );
		} );

		it( 'should handle empty initial state', () => {
			const emptyState: Partial< LabelPurchaseState > = {
				labels: {},
				selectedRates: {},
			};

			const action = {
				type: LABEL_STATUS_RESOLVED,
				payload: {
					...mockLabel,
					status: LABEL_PURCHASE_STATUS.PURCHASE_ERROR,
				},
			};

			const newState = labelPurchaseReducer(
				emptyState as LabelPurchaseState,
				action
			);

			expect( newState ).toEqual( emptyState );
		} );
	} );
} );
