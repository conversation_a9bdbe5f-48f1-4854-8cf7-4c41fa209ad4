/* eslint-disable @typescript-eslint/no-explicit-any */

import {
	mockOriginAddress,
	mockStoreOrigin,
	mockDestination,
} from 'test-helpers/address-reducer-mocks';

// Mock the utils module before importing the reducer
jest.mock( 'utils', () => {
	const { mockUtils } = jest.requireActual( 'test-helpers/test-utils' );
	return mockUtils( {
		getOriginAddresses: () => [ mockOriginAddress ],
		getFirstSelectableOriginAddress: () => mockOriginAddress,
		getStoreOrigin: () => mockStoreOrigin,
		getCurrentOrderShipTo: () => mockDestination,
		getIsDestinationVerified: () => false,
		isOriginAddress: ( address: any ) => !! address.id,
	} );
} );

import { getReducer } from '../reducer';
import {
	ADDRESS_NORMALIZATION,
	ADDRESS_NORMALIZATION_FAILED,
	RESET_ADDRESS_NORMALIZATION,
	UPDATE_SHIPMENT_ADDRESS,
	UPDATE_SHIPMENT_ADDRESS_FAILED,
	ADD_ORIGIN_ADDRESS,
	ADD_ORIGIN_ADDRESS_FAILED,
	VERIFY_ORDER_SHIPPING_ADDRESS,
	VERIFY_ORDER_SHIPPING_ADDRESS_FAILED,
	VERIFY_ORDER_SHIPPING_ADDRESS_START,
	DELETE_ORIGIN_ADDRESS,
} from '../action-types';
import { ADDRESS_TYPES } from '../../constants';
import { AddressState } from '../../types';
import type { OriginAddress } from 'types';

describe( 'Address Reducer', () => {
	describe( 'with destination', () => {
		const addressReducer = getReducer( true );
		let initialState: AddressState;

		beforeEach( () => {
			initialState = addressReducer( undefined, {
				type: '@@INIT',
			} as any );
		} );

		describe( 'ADDRESS_NORMALIZATION', () => {
			it( 'should update origin address normalization data', () => {
				const normalizedAddress = {
					...mockOriginAddress,
					address: '123 Test Street',
				};

				const action = {
					type: ADDRESS_NORMALIZATION,
					payload: {
						address: mockOriginAddress,
						normalizedAddress,
						isTrivialNormalization: true,
						addressType: ADDRESS_TYPES.ORIGIN,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.origin.normalizedAddress ).toEqual(
					normalizedAddress
				);
				expect( newState.origin.isTrivialNormalization ).toBe( true );
				expect( newState.origin.addressNeedsConfirmation ).toBe( true );
				expect( newState.origin.submittedAddress ).toEqual(
					mockOriginAddress
				);
				expect( newState.origin.formErrors ).toEqual( {} );
				expect( newState.origin.isAddressVerificationInProgress ).toBe(
					false
				);
			} );

			it( 'should update destination address normalization data', () => {
				const normalizedAddress = {
					...mockDestination,
					address: '789 Customer Street',
				};

				const action = {
					type: ADDRESS_NORMALIZATION,
					payload: {
						address: mockDestination,
						normalizedAddress,
						isTrivialNormalization: false,
						addressType: ADDRESS_TYPES.DESTINATION,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.destination?.normalizedAddress ).toEqual(
					normalizedAddress
				);
				expect( newState.destination?.isTrivialNormalization ).toBe(
					false
				);
				expect( newState.destination?.addressNeedsConfirmation ).toBe(
					true
				);
				expect( newState.destination?.submittedAddress ).toEqual(
					mockDestination
				);
			} );

			it( 'should handle warnings in normalization response', () => {
				const warnings = [
					{
						code: 'restricted_country',
						message: 'Country restricted',
					},
					{ code: 'po_box', message: 'PO Box detected' },
				];

				const action = {
					type: ADDRESS_NORMALIZATION,
					payload: {
						address: mockDestination,
						normalizedAddress: mockDestination,
						isTrivialNormalization: true,
						addressType: ADDRESS_TYPES.DESTINATION,
						warnings,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.destination?.warnings ).toEqual( warnings );
			} );
		} );

		describe( 'ADDRESS_NORMALIZATION_FAILED', () => {
			it( 'should handle normalization failure with errors', () => {
				const errors = {
					address: 'Address is invalid',
					postcode: 'Postcode is required',
					general: 'General error',
				};

				const action = {
					type: ADDRESS_NORMALIZATION_FAILED,
					payload: {
						addressType: ADDRESS_TYPES.ORIGIN,
						address: mockOriginAddress,
						errors,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.origin.formErrors ).toEqual( errors );
				expect( newState.origin.addressNeedsConfirmation ).toBe(
					false
				);
				expect( newState.origin.normalizedAddress ).toBeNull();
				expect( newState.origin.submittedAddress ).toEqual(
					mockOriginAddress
				);
				expect( newState.origin.isAddressVerificationInProgress ).toBe(
					false
				);
			} );

			it( 'should handle normalization failure with message', () => {
				const message = 'Normalization service unavailable';

				const action = {
					type: ADDRESS_NORMALIZATION_FAILED,
					payload: {
						addressType: ADDRESS_TYPES.DESTINATION,
						address: mockDestination,
						message,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.destination?.formErrors ).toEqual( {
					general: message,
				} );
			} );

			it( 'should merge errors and message when both present', () => {
				const errors = { address: 'Invalid' };
				const message = 'Service error';

				const action = {
					type: ADDRESS_NORMALIZATION_FAILED,
					payload: {
						addressType: ADDRESS_TYPES.ORIGIN,
						address: mockOriginAddress,
						errors,
						message,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.origin.formErrors ).toEqual( {
					address: 'Invalid',
					general: message, // message overrides errors.general
				} );
			} );
		} );

		describe( 'RESET_ADDRESS_NORMALIZATION', () => {
			it( 'should reset address confirmation for origin', () => {
				// Set up state with address needing confirmation
				const stateWithConfirmation = {
					...initialState,
					origin: {
						...initialState.origin,
						addressNeedsConfirmation: true,
					},
				};

				const action = {
					type: RESET_ADDRESS_NORMALIZATION,
					payload: {
						addressType: ADDRESS_TYPES.ORIGIN,
					},
				};

				const newState = addressReducer(
					stateWithConfirmation,
					action as any
				);

				expect( newState.origin.addressNeedsConfirmation ).toBe(
					false
				);
			} );

			it( 'should reset address confirmation for destination', () => {
				// Set up state with address needing confirmation
				const stateWithConfirmation = {
					...initialState,
					destination: {
						...initialState.destination!,
						addressNeedsConfirmation: true,
					},
				};

				const action = {
					type: RESET_ADDRESS_NORMALIZATION,
					payload: {
						addressType: ADDRESS_TYPES.DESTINATION,
					},
				};

				const newState = addressReducer(
					stateWithConfirmation,
					action as any
				);

				expect( newState.destination?.addressNeedsConfirmation ).toBe(
					false
				);
			} );
		} );

		describe( 'UPDATE_SHIPMENT_ADDRESS', () => {
			it( 'should update origin address and verification status', () => {
				const updatedAddress = {
					...mockOriginAddress,
					name: 'Updated Origin',
				};

				const action = {
					type: UPDATE_SHIPMENT_ADDRESS,
					payload: {
						address: updatedAddress,
						isVerified: true,
						addressType: ADDRESS_TYPES.ORIGIN,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.origin.address ).toEqual( updatedAddress );
				expect( newState.origin.isVerified ).toBe( true );
				expect( newState.origin.formErrors ).toEqual( {} );
				expect( newState.origin.addressNeedsConfirmation ).toBe(
					false
				);
				expect( newState.origin.isAddressVerificationInProgress ).toBe(
					false
				);

				// Check that the address is also updated in the addresses array
				const updatedInArray = newState.origin.addresses.find(
					( addr ) => addr.id === updatedAddress.id
				);
				expect( updatedInArray ).toEqual( updatedAddress );
			} );

			it( 'should update destination address', () => {
				const updatedAddress = {
					...mockDestination,
					name: 'Updated Customer',
				};

				const action = {
					type: UPDATE_SHIPMENT_ADDRESS,
					payload: {
						address: updatedAddress,
						isVerified: false,
						addressType: ADDRESS_TYPES.DESTINATION,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.destination?.address ).toEqual(
					updatedAddress
				);
				expect( newState.destination?.isVerified ).toBe( false );
			} );
		} );

		describe( 'UPDATE_SHIPMENT_ADDRESS_FAILED', () => {
			it( 'should handle update failure for origin', () => {
				const message = 'Failed to update address';

				const action = {
					type: UPDATE_SHIPMENT_ADDRESS_FAILED,
					payload: {
						addressType: ADDRESS_TYPES.ORIGIN,
						message,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.origin.isVerified ).toBe( false );
				expect( newState.origin.addressNeedsConfirmation ).toBe(
					false
				);
				expect( newState.origin.formErrors ).toEqual( {
					general: message,
				} );
				expect( newState.origin.isAddressVerificationInProgress ).toBe(
					false
				);
			} );

			it( 'should handle update failure for destination', () => {
				const message = 'Update failed';

				const action = {
					type: UPDATE_SHIPMENT_ADDRESS_FAILED,
					payload: {
						addressType: ADDRESS_TYPES.DESTINATION,
						message,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.destination?.isVerified ).toBe( false );
				expect( newState.destination?.formErrors ).toEqual( {
					general: message,
				} );
			} );
		} );

		describe( 'ADD_ORIGIN_ADDRESS', () => {
			it( 'should add new origin address to the list', () => {
				const newAddress: OriginAddress = {
					id: 'origin_2',
					name: 'New Origin',
					firstName: 'New',
					lastName: 'Origin',
					address: '999 New St',
					city: 'New City',
					state: 'WA',
					postcode: '98101',
					country: 'US',
					email: '<EMAIL>',
					phone: '555-0000',
					isVerified: true,
					defaultAddress: false,
				};

				const action = {
					type: ADD_ORIGIN_ADDRESS,
					payload: {
						address: newAddress,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.origin.addresses ).toContain( newAddress );
				expect( newState.origin.address ).toEqual( newAddress );
				expect( newState.origin.isVerified ).toBe( true );
				expect( newState.origin.formErrors ).toEqual( {} );
				expect( newState.origin.addressNeedsConfirmation ).toBe(
					false
				);
			} );

			it( 'should set new address as default and unset others', () => {
				const newDefaultAddress: OriginAddress = {
					id: 'origin_3',
					name: 'New Default',
					firstName: 'New',
					lastName: 'Default',
					address: '111 Default St',
					city: 'Default City',
					state: 'OR',
					postcode: '97201',
					country: 'US',
					email: '<EMAIL>',
					phone: '555-1111',
					isVerified: true,
					defaultAddress: true,
				};

				const action = {
					type: ADD_ORIGIN_ADDRESS,
					payload: {
						address: newDefaultAddress,
					},
				};

				const newState = addressReducer( initialState, action as any );

				// Check that previous default is now false
				const previousDefault = newState.origin.addresses.find(
					( addr ) => addr.id === mockOriginAddress.id
				);
				expect( previousDefault?.defaultAddress ).toBe( false );

				// Check that new address is default
				const newDefault = newState.origin.addresses.find(
					( addr ) => addr.id === newDefaultAddress.id
				);
				expect( newDefault?.defaultAddress ).toBe( true );
			} );
		} );

		describe( 'ADD_ORIGIN_ADDRESS_FAILED', () => {
			it( 'should set form errors when adding address fails', () => {
				const error = {
					name: 'Name is required',
					address: 'Address is invalid',
				};

				const action = {
					type: ADD_ORIGIN_ADDRESS_FAILED,
					payload: {
						error,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.origin.formErrors ).toEqual( error );
			} );
		} );

		describe( 'VERIFY_ORDER_SHIPPING_ADDRESS_START', () => {
			it( 'should set verification in progress for origin', () => {
				const action = {
					type: VERIFY_ORDER_SHIPPING_ADDRESS_START,
					payload: {
						addressType: ADDRESS_TYPES.ORIGIN,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.origin.isAddressVerificationInProgress ).toBe(
					true
				);
			} );

			it( 'should set verification in progress for destination', () => {
				const action = {
					type: VERIFY_ORDER_SHIPPING_ADDRESS_START,
					payload: {
						addressType: ADDRESS_TYPES.DESTINATION,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect(
					newState.destination?.isAddressVerificationInProgress
				).toBe( true );
			} );
		} );

		describe( 'VERIFY_ORDER_SHIPPING_ADDRESS', () => {
			it( 'should update verification status for origin', () => {
				const normalizedAddress = {
					...mockOriginAddress,
					address: '123 Test Street',
				};

				const action = {
					type: VERIFY_ORDER_SHIPPING_ADDRESS,
					payload: {
						normalizedAddress,
						isTrivialNormalization: true,
						isVerified: true,
						addressType: ADDRESS_TYPES.ORIGIN,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.origin.isVerified ).toBe( true );
				expect( newState.origin.normalizedAddress ).toEqual(
					normalizedAddress
				);
				expect( newState.origin.isTrivialNormalization ).toBe( true );
				expect( newState.origin.isAddressVerificationInProgress ).toBe(
					false
				);
			} );

			it( 'should update verification status for destination', () => {
				const normalizedAddress = {
					...mockDestination,
					address: '789 Customer Street',
				};

				const action = {
					type: VERIFY_ORDER_SHIPPING_ADDRESS,
					payload: {
						normalizedAddress,
						isTrivialNormalization: false,
						isVerified: true,
						addressType: ADDRESS_TYPES.DESTINATION,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.destination?.isVerified ).toBe( true );
				expect( newState.destination?.normalizedAddress ).toEqual(
					normalizedAddress
				);
				expect( newState.destination?.isTrivialNormalization ).toBe(
					false
				);
			} );
		} );

		describe( 'VERIFY_ORDER_SHIPPING_ADDRESS_FAILED', () => {
			it( 'should handle verification failure for origin', () => {
				const action = {
					type: VERIFY_ORDER_SHIPPING_ADDRESS_FAILED,
					payload: {
						addressType: ADDRESS_TYPES.ORIGIN,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.origin.isVerified ).toBe( false );
				expect( newState.origin.isAddressVerificationInProgress ).toBe(
					false
				);
			} );

			it( 'should handle verification failure for destination', () => {
				const action = {
					type: VERIFY_ORDER_SHIPPING_ADDRESS_FAILED,
					payload: {
						addressType: ADDRESS_TYPES.DESTINATION,
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.destination?.isVerified ).toBe( false );
				expect(
					newState.destination?.isAddressVerificationInProgress
				).toBe( false );
			} );
		} );

		describe( 'DELETE_ORIGIN_ADDRESS', () => {
			it( 'should remove origin address from the list', () => {
				// Add a second address first
				const secondAddress: OriginAddress = {
					id: 'origin_2',
					name: 'Second Origin',
					firstName: 'Second',
					lastName: 'Origin',
					address: '222 Second St',
					city: 'Second City',
					state: 'NY',
					postcode: '10002',
					country: 'US',
					email: '<EMAIL>',
					phone: '555-2222',
					isVerified: true,
					defaultAddress: false,
				};

				const stateWithTwoAddresses = {
					...initialState,
					origin: {
						...initialState.origin,
						addresses: [ mockOriginAddress, secondAddress ],
					},
				};

				const action = {
					type: DELETE_ORIGIN_ADDRESS,
					payload: {
						deletedId: 'origin_2',
					},
				};

				const newState = addressReducer(
					stateWithTwoAddresses,
					action as any
				);

				expect( newState.origin.addresses ).toHaveLength( 1 );
				expect( newState.origin.addresses ).not.toContain(
					secondAddress
				);
				expect( newState.origin.addresses ).toContain(
					mockOriginAddress
				);
			} );

			it( 'should handle deleting non-existent address', () => {
				const action = {
					type: DELETE_ORIGIN_ADDRESS,
					payload: {
						deletedId: 'non_existent',
					},
				};

				const newState = addressReducer( initialState, action as any );

				expect( newState.origin.addresses ).toEqual(
					initialState.origin.addresses
				);
			} );
		} );
	} );

	describe( 'without destination', () => {
		const addressReducer = getReducer( false );
		let initialState: AddressState;

		beforeEach( () => {
			initialState = addressReducer( undefined, {
				type: '@@INIT',
			} as any );
		} );

		it( 'should not include destination in initial state', () => {
			expect( initialState.destination ).toBeUndefined();
		} );

		it( 'should have origin and storeOrigin in initial state', () => {
			expect( initialState.origin ).toBeDefined();
			expect( initialState.storeOrigin ).toBeDefined();
			expect( initialState.origin.address ).toEqual( mockOriginAddress );
			expect( initialState.storeOrigin ).toEqual( mockStoreOrigin );
		} );
	} );
} );
