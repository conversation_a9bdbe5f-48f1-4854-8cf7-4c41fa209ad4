import {
	labelPurchaseStore,
	registerLabelPurchaseStore,
} from '../../data/label-purchase';
import { addressStore, registerAddressStore } from '../../data/address';
import {
	carrierStrategyStore,
	registerCarrierStrategyStore,
} from '../../data/carrier-strategy';
import { getConfig } from './../../utils';
import { useRef } from 'react';
import { LabelPurchaseContextProvider } from '../../context/label-purchase';
import { LabelPurchaseEffects } from '../../effects/label-purchase';
import { LabelPurchaseTabs } from '../../components/label-purchase/label-purchase-tabs';

const PurchaseShippingLabelPlugin = () => {
	if ( ! addressStore ) {
		registerAddressStore( true );
	}
	if ( ! labelPurchaseStore ) {
		registerLabelPurchaseStore();
	}
	if ( ! carrierStrategyStore ) {
		registerCarrierStrategyStore();
	}

	const ref = useRef( null );
	const orderId = getConfig().order.id;
	const noop = () => getConfig;

	return (
		<LabelPurchaseContextProvider orderId={ orderId } nextDesign>
			<LabelPurchaseEffects />
			<LabelPurchaseTabs ref={ ref } setStartSplitShipment={ noop } />
		</LabelPurchaseContextProvider>
	);
};

window.WCShipping_Plugin = PurchaseShippingLabelPlugin;

export default PurchaseShippingLabelPlugin;
