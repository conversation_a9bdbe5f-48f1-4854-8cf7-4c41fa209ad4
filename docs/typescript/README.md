# WooCommerce Shipping TypeScript Guidelines

This set of guidelines aims to make it easier for developers working on WooCommerce Shipping to get started using TypeScript, as well as make it easy to work through common hurdles when writing TypeScript.

## Table of Contents

-   [Should I use `interface` or `type` when declaring types?](./interface-vs-type.md)
-   [How do I write React components in TypeScript?](./react-components.md)
-   [Where should I declare my types?](./declaring-types.md)
-   [Utility types and manipulation](./utility-and-manipulation.md)

## External resources

-   [Cheat Sheets](https://www.typescriptlang.org/cheatsheets)
