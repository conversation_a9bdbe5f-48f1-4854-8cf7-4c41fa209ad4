# Should I use `interface` or `type` when declaring types?

We ALWAYS prefer `interface`.

```ts
// Avoid:
type Props = {
  shipmentId: string;
  isLoading: bool;
}

// Instead do:
interface Props {
  shipmentId: string;
  isLoading: bool;
}
```

The biggest reason to prefer `interface` is that they're more flexible.
They can be extended to add more properties when needed whereas types cannot.
The TypeScript Handbook itself [recommends](https://www.typescriptlang.org/docs/handbook/2/everyday-types.html#differences-between-type-aliases-and-interfaces) using `interface` unless there's a specific feature you need from `type`, in which case the TypeScript compiler will likely tell you about it:

> For the most part, you can choose based on personal preference, and TypeScript will tell you if it needs something to be the other kind of declaration. If you would like a heuristic, use interface until you need to use features from type.

## What might be a case where I need to use `type` instead of `interface`?

The one useful case for this is aliasing a primitive type such as `string` or `number`.
For example, you might want to make it easier for whoever is reading the code that the `string` some function accepts is a Shipment ID which could have a unique format, so instead of using `string` you'd prefer to use `ShipmentID`:

```ts
interface ShipmentID = string; // Error: can't assign type primitive to an interface.

type ShipmentID = string;

function shipit( id: ShipmentID ) {
  // ...
}
```
