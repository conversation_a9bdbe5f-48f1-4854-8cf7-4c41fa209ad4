# Service Data Refresh API

## Fetch Service Schemas from Connect Server

### Endpoint
`POST /wcshipping/v1/service-data-refresh`

### Description
Fetch the latest service schemas from the connect server and update the local store.

### Response Example
```json
{
  "success": true,
  "timestamp": 1625247600,
  "has_service_schemas": true
}
```

### Error Response Example
```json
{
  "success": false,
  "status": 500
}
```
