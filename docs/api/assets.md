# Assets API

## Overview

The `AssetsRESTController` class extends `WCShippingRESTController` and provides a REST API endpoint for retrieving asset URLs used in the WooCommerce Shipping module.

### REST Base

The REST base for this controller is `assets`.

### Available Routes

This API provides the following route:

- `GET /wp-json/wcshipping/v1/assets`

## Endpoints

### `GET /wp-json/wcshipping/v1/assets`

#### Description

Retrieves the URLs for various assets (scripts and styles) used in the WooCommerce Shipping module.

#### Method

- **GET**

#### Response

The response is a JSON object containing the following fields:

- **`success`**: A boolean value indicating whether the request was successful.
- **`assets`**: An object containing the URLs for the assets.

#### Response Structure

```json
{
  "success": true,
  "assets": {
    "wcshipping_create_label_script": "URL to the script for creating a label",
    "wcshipping_create_label_style": "URL to the style for creating a label",
    "wcshipping_shipment_tracking_script": "URL to the script for shipment tracking",
    "wcshipping_shipment_tracking_style": "URL to the style for shipment tracking"
  }
}
```

#### Example Response

```json
{
  "success": true,
  "assets": {
    "wcshipping_create_label_script": "wcshipping-create-label-script-absolute-url.js",
    "wcshipping_create_label_style": "wcshipping-create-label-style-absolute-url.css",
    "wcshipping_shipment_tracking_script": "wcshipping-shipment-tracking-script-absolute-url.js",
    "wcshipping_shipment_tracking_style": "wcshipping-shipment-tracking-style-absolute-url.css"
  }
}
```

### Permissions

The `permission_callback` for this route ensures that the user has the necessary permissions to access the endpoint. If the user does not have the required permissions, the API will return a `403 Forbidden` response.

## Summary

This API endpoint is useful for retrieving the URLs of various scripts and styles associated with WooCommerce Shipping, allowing developers to dynamically include or reference these assets in their custom solutions.
