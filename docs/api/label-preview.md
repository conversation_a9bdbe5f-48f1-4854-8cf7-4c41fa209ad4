### Label Preview API

## Retrieve Label Preview

### Endpoint

`GET /wp-json/wcshipping/v1/label/preview`

### Description

Retrieve the test label and return it as a base64 encoded content.

### Parameters

- `paper_size` (string, required): The size of the paper for the label.

### Response Example

```json
{
  "mimeType": "application/pdf",
  "b64Content": "JVBERi0xLjQKJcfs...",
  "success": true
}
```

### Error Response Example

```json
{
  "code": "preview_error_code",
  "message": "Error message describing the issue.",
  "data": {
    "message": 'error message from the connect server'
  }
}
```
