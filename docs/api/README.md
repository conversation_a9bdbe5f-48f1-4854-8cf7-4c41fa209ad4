# WooCommerce Shipping API

### Namespace
Resources in the WooCommerce Shipping API are all found within the wcshipping/v1 namespace, and since this API extends the WordPress API, accessing it requires the /wp-json/ base. Currently, the only version is v1.

### Glossary of Contents and APIs
Available resources in the WooCommerce Shipping API are listed below, with links to more detailed documentation.

| API                        | HTTP Method | Endpoint                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      | Description |
|----------------------------|-------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------|
| **Account Settings API**    | GET, POST        | [`GET /wp-json/wcshipping/v1/account/settings`](account-settings.md#get-account-settings)<br>[`POST /wp-json/wcshipping/v1/account/settings`](account-settings.md#post-account-settings)                                                                                                                                                                                                                                                                                                                      | Retrieve or save the account settings for the user. |
| **Eligibility API**         | GET             | [`GET /wp-json/wcshipping/v1/eligibility/{order_id}`](eligibility.md#get-eligibilityorder_id)                                                                                                                                                                                                                                                                                                                                                                                                                | Check if an order is eligible for shipping label creation. |
| **Packages API**            | GET, POST, PUT   | [`GET /wp-json/wcshipping/v1/packages`](packages.md#post-packages)<br>[`POST /wp-json/wcshipping/v1/packages`](packages.md#post-packages)<br>[`PUT /wp-json/wcshipping/v1/packages`](packages.md#put-packages)                                                                                                                                                                                                                                                                                                | Retrieve, create or update custom and/or predefined packages. |
| **Shipments API**           | POST             | [`POST /wp-json/wcshipping/v1/shipments/{order_id}`](shipments.md#post-shipmentsorder_id)                                                                                                                                                                                                                                                                                                                                                                                                                     | Update the shipments for a specific order. |
| **Address API**             | GET, POST, DELETE | [`GET /wp-json/wcshipping/v1/address/origins`](address.md#get-addressorigins)<br>[`POST /wp-json/wcshipping/v1/address/update_origin`](address.md#post-addressupdate_origin)<br>[`POST /wp-json/wcshipping/v1/address/{order_id}/update_destination`](address.md#post-addressorder_idupdate_destination)<br>[`POST /wp-json/wcshipping/v1/address/{order_id}/verify_order`](address.md#post-addressorder_idverify_order)<br>[`POST /wp-json/wcshipping/v1/address/normalize`](address.md#post-addressnormalize)<br>[`DELETE /wp-json/wcshipping/v1/address/{id}`](address.md#delete-addressid) | Manage origin and destination addresses including: retrieving all origin addresses, updating origin/destination addresses, verifying addresses, normalizing addresses, and deleting origin addresses. |
| **Label Print API**         | GET              | [`GET /wp-json/wcshipping/v1/label/print`](label-print.md#get-labelprint)                                                                                                                                                                                                                                                                                                                                                                                                                                     | Print a shipping label. |
| **Label Refund API**        | POST             | [`POST /wp-json/wcshipping/v1/label/refund/{order_id}/{label_id}`](label-refund.md#post-labelrefundorder_idlabel_id)                                                                                                                                                                                                                                                                                                                                                                                          | Request a refund for a specific shipping label associated with an order. |
| **Label Purchase API**      | GET, POST        | [`GET /wp-json/wcshipping/v1/label/purchase/{order_id}`](label-purchase.md#get-labelpurchaseorder_id)<br>[`POST /wp-json/wcshipping/v1/label/purchase/{order_id}`](label-purchase.md#post-labelpurchaseorder_id)                                                                                                                                                                                                                                                                                              | Retrieve purchased labels for a specific order or purchase a shipping label for a specific order. |
| **Label Preview API**       | GET              | [`GET /wp-json/wcshipping/v1/label/preview`](label-preview.md#get-labelpreview)                                                                                                                                                                                                                                                                                                                                                                                                                               | Retrieve the test label and return it as a base64 encoded content. |
| **Self Help API**           | POST             | [`POST /wp-json/wcshipping/v1/self-help`](self-help.md#post-self-help)                                                                                                                                                                                                                                                                                                                                                                                                                                        | Update the self-help settings for the user. |
| **Service Data Refresh API**| POST             | [`POST /wp-json/wcshipping/v1/service-data-refresh`](service-data-refresh.md#post-service-data-refresh)                                                                                                                                                                                                                                                                                                                                                                                                       | Fetch the latest service schemas from the connect server and update the local store. |
| **Config API**              | GET              | [`GET /wp-json/wcshipping/v1/config/label-purchase/{order_id}`](config.md#get-configlabel-purchaseorder_id)                                                                                                                                                                                                                                                                                                                                                                                                   | Retrieve configuration data necessary for purchasing a shipping label for a specific order. |
| **TOS API**                 | GET              | [`GET /wp-json/wcshipping/v1/tos`](tos.md#get-tos)                                                                                                                                                                                                                                                                                                                                                                                                                                                            | Retrieve the terms of service. |
| **Assets API**              | GET              | [`GET /wp-json/wcshipping/v1/assets`](assets.md#get-assets)                                                                                                                                                                                                                                                                                                                                                                                                                                                   | Retrieve assets related to shipping. |
| **UPS Carrier Strategy API**| POST             | [`POST /wp-json/wcshipping/v1/carrier-strategy/ups`](ups-carrier-strategy.md#post-wp-json-wcshippingv1carrier-strategyups)                                                                                                                                                                                                                                                                                                                                                                                    | Update the TOS agreement status for an origin address in the UPS carrier strategies. |

### Authentication and access management

We use standard WordPress authentication based on cookies and nonces for the APIs. The API controllers extend `WCShippingRESTController` and utilize the `WCShippingRESTController::ensure_rest_permission` method to verify that the user has the appropriate permissions, this method internally uses `current_user_can` to check the user's capabilities and login status.

Third-party plugins can modify access to the API endpoints by using the `wcshipping_user_can_manage_labels` filter.

However, there are some exceptions where `WC_Connect_Functions::user_can_manage_labels` is used to check the user's capabilities, as seen in `LabelPurchaseRESTController`.

