# Self Help API

## Update Self Help Settings

### Endpoint
`POST /wcshipping/v1/self-help`

### Description
Update the self-help settings for the user.

### Parameters
- `wcc_debug_on` (integer, required): Enable or disable debug mode. Accepts 1 (enable) or 0 (disable).
- `wcc_logging_on` (integer, required): Enable or disable logging. Accepts 1 (enable) or 0 (disable).

### Response Example
```json
{
  "success": true
}
```

### Error Response Example
```json
{
  "code": "bad_form_data",
  "message": "Unable to update settings. The form data could not be read.",
  "data": {
    "status": 400
  }
}
```
