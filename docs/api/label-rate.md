# Label Rate API

## Quote Rates

### Endpoint
`POST /wcshipping/v1/label/rate`

### Description
Retrieve shipping rates for a given order.

### Request Body
- `order_id` (integer, required): The ID of the order.
- `origin` (object, required): The origin address.
- `destination` (object, required): The destination address.
- `packages` (array, required): The package details.
- `shipment_options` (object, optional): Extra options for the shipment.
  - `label_date` (string, optional): ISO 8601 formatted date string for the shipping label. It won't change the response but for some carriers such as USPS this date will be printed on the label.

### Request Example
```http
POST /wcshipping/v1/label/rate
Content-Type: application/json

{
  "order_id": 123,
  "origin": {
    "company": "Company Name",
    "name": "John Doe",
    "address_1": "123 Main St",
    "address_2": "Suite 100",
    "city": "Anytown",
    "state": "CA",
    "postcode": "12345",
    "country": "US",
    "phone": "555-1234"
  },
  "destination": {
    "company": "Company Name",
    "name": "<PERSON>",
    "address_1": "456 Elm St",
    "address_2": "",
    "city": "Othertown",
    "state": "NY",
    "postcode": "67890",
    "country": "US",
    "phone": "555-5678"
  },
  "packages": [
    {
      "id": "default_box",
      "box_id": "small_flat_box",
      "length": 10,
      "width": 5,
      "height": 5,
      "weight": 2,
      "is_letter": false,
      "contents_type": "merchandise",
      "restriction_type": "none",
      "non_delivery_option": "return",
      "itn": "123456",
      "items": [
        {
          "description": "Item 1",
          "quantity": 1,
          "value": 100,
          "weight": 1,
          "hs_tariff_number": "1234.56.78",
          "origin_country": "US",
          "product_id": 1
        }
      ]
    }
  ]
}
```

### Response Example
```json
{
  "success": true,
  "rates": [
    {
      "service": "UPS Ground",
      "rate": 10.00,
      "currency": "USD",
      "estimated_delivery": "2023-10-15"
    },
    {
      "service": "FedEx Express",
      "rate": 20.00,
      "currency": "USD",
      "estimated_delivery": "2023-10-14"
    }
  ]
}
```

### Error Response Example
```json
{
  "code": "invalid_request",
  "message": "Request payload is invalid.",
  "data": {
    "status": 400
  }
}
```
