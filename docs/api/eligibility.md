# Eligibility API

## Overview

The Eligibility API provides endpoints to check if an order is eligible for shipping label creation. It performs various checks including store settings, address validation, package availability, and payment method configuration.

## Endpoints

### `GET /wp-json/wcshipping/v1/eligibility/{order_id}`

Check if an order is eligible for shipping label creation.

#### Parameters

- **`order_id`** (integer, required): The ID of the order to check eligibility for.
- **`can_create_customs_form`** (boolean, optional): Whether the client can create customs forms. Defaults to true.
- **`can_create_package`** (boolean, optional): Whether the client can create packages. Defaults to true.
- **`can_create_payment_method`** (boolean, optional): Whether the client can create payment methods. Defaults to true.

#### Response

The response will be a JSON object containing:

- **`is_eligible`** (boolean): Whether the order is eligible for shipping label creation
- **`reason`** (string, optional): If not eligible, contains the reason code explaining why

#### Example Successful Response

```json
{
    "is_eligible": true
}
```

#### Example Error Responses

When order is not eligible due to invalid order ID:
```json
{
    "is_eligible": false,
    "reason": "order_id_is_not_valid"
}
```

When shipping labels are disabled in account settings:
```json
{
    "is_eligible": false,
    "reason": "account_settings_disabled"
}
```

When store is not eligible:
```json
{
    "is_eligible": false,
    "reason": "store_not_eligible"
}
```

When customs forms are not supported:
```json
{
    "is_eligible": false,
    "reason": "store_country_not_supported_when_customs_form_is_not_supported_by_client"
}
```

When no packages are available:
```json
{
    "is_eligible": false,
    "reason": "no_packages_when_client_cannot_create_package"
}
```

When order is not eligible:
```json
{
    "is_eligible": false,
    "reason": "order_not_eligible"
}
```

When payment methods are not available:
```json
{
    "is_eligible": false,
    "reason": "no_payment_methods_and_client_cannot_create_one"
}
```

When no payment method is selected:
```json
{
    "is_eligible": false,
    "reason": "no_selected_payment_method_and_user_cannot_manage_payment_methods"
}
```

### Error Codes

| Error Code | Description |
|------------|-------------|
| `order_id_is_not_valid` | The provided order ID is not valid |
| `order_not_found` | The order could not be found |
| `account_settings_disabled` | Shipping labels are disabled in account settings |
| `store_not_eligible` | The store is not eligible for shipping label creation |
| `store_country_not_supported_when_customs_form_is_not_supported_by_client` | Store country is not supported when customs forms are not supported |
| `origin_or_destination_country_not_supported_when_customs_form_is_not_supported_by_client` | Origin or destination country not supported when customs forms are not supported |
| `no_packages_when_client_cannot_create_package` | No packages available and client cannot create packages |
| `order_not_eligible` | The order is not eligible for shipping label creation |
| `no_payment_methods_and_client_cannot_create_one` | No payment methods available and client cannot create one |
| `no_selected_payment_method_and_user_cannot_manage_payment_methods` | No payment method selected and user cannot manage payment methods |

### Permissions

This endpoint requires authentication and the user must have permission to manage shipping labels. 