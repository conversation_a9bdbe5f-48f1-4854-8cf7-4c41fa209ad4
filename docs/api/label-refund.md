# Label Refund API

## Refund Label

### Endpoint

`POST /wp-json/wcshipping/v1/label/refund/{order_id}/{label_id}`

### Description

Request a refund for a specific shipping label associated with an order.

### Parameters

- `order_id` (integer, required): The ID of the order.
- `label_id` (integer, required): The ID of the label to be refunded.

### Request Example

```http
POST /wp-json/wcshipping/v1/label/refund/123/456
```

### Response Example

```json
{
  "success": true,
  "refund": {
    "status": "pending",
    "request_date": 1723147248369,
    "is_manual": false
  },
  "label": {
    "label_id": 3627,
    "carrier_id": "usps",
    ...
    "refund": {
      "request_date": 1723147248000,
      "status": "pending",
      "refund_date": 1
    }
  }
}
```

### Error Response Example

#### Generic error message

```json
{
  "code": "refund_error",
  "message": "Error message describing the issue.",
  "data": {
    "status": 400
  }
}
```

#### Error in getting label status despite refund being successful

```json
{
  "code": "status_error",
  "message": "Successful refund, but there was an error getting label status: Error message from get_status",
  "data": {
    "message": "error message from get_status"
  }
}
```
