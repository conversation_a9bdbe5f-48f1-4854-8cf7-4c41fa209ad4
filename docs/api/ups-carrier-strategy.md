# UPS DAP Carrier Strategy API

## Update UPS DAP Carrier Strategy

### Endpoint

`POST /wp-json/wcshipping/v1/carrier-strategy/upsdap`

### Description

Update the Terms of Service (TOS) agreement status for a specific origin address in the UPS DAP carrier strategies and created necessary carrier accounts.

### Request Body

- `origin` (object, required): The origin address object
  - `name` (string): Full name of the contact person
  - `company` (string): Company name
  - `address` (string): Primary street address
  - `address_2` (string): Secondary address line (apartment, suite, etc.)
  - `city` (string): City name
  - `state` (string): State/province code
  - `country` (string): Country code
  - `postcode` (string): Postal/ZIP code
  - `phone` (string): Contact phone number
  - `email` (string): Contact email address
- `confirmed` (boolean, required): Whether the user has agreed (`true`) or not agreed (`false`) to the TOS.

### Request Example

```http
POST /wp-json/wcshipping/v1/carrier-strategy/upsdap
Content-Type: application/json

{
  "origin": {
    "name": "<PERSON>",
    "company": "Acme Corp",
    "address": "123 Main St",
    "address_2": "Apt 4B",
    "city": "New York",
    "state": "NY",
    "country": "US",
    "postcode": "10001",
    "phone": "555-1234",
    "email": "<EMAIL>",
  },
  "confirmed": true
}
```


### Response Example

```json
{
  "success": true,
  "confirmed": true
}
```

