# Label Print API

## Print Label

### Endpoint
`GET /wp-json/wcshipping/v1/label/print`

### Description
Print one or more shipping label(s).

### Parameters
- `label_id_csv` (string, required): A comma-separated list of label IDs to print.
- `paper_size` (string, required): The paper size for printing the label.

### Request Example
```http
GET /wcshipping/v1/label/print?label_id_csv=123&paper_size=A4
```

### Response Example
```json
{
  "mimeType": "application/pdf",
  "b64Content": "JVBERi0xLjQKJcfs...",
  "success": true
}
```

### Error Response Example
```json
{
  "code": "invalid_pdf_request",
  "message": "Invalid PDF request.",
  "data": {
    "status": 400
  }
}
```
