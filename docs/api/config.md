# Config API

## Overview

The `ConfigRESTController` class extends `WCShippingRESTController` and provides a REST API endpoint for retrieving configuration data related to the purchase of shipping labels for a specific order within WooCommerce.

### REST Base

The REST base for this controller is `config`.

### Available Routes

This API provides the following route:

- `GET /wp-json/wcshipping/v1/config/label-purchase/(?P<order_id>\d+)`

## Endpoints

### `GET /wp-json/wcshipping/v1/config/label-purchase/(?P<order_id>\d+)`

#### Description

This endpoint retrieves configuration data necessary for purchasing a shipping label for a specific order.

#### Method

- **GET**

#### URL Parameters

- **`order_id`**: The ID of the order for which the configuration data is being retrieved. This parameter is required and must be a valid order ID.

#### Request Parameters

No additional request parameters are required beyond the URL parameter.

#### Response

The response is a JSON object containing the following fields:

- **`success`**: A boolean value indicating whether the request was successful.
- **`config`**: An object containing the configuration data necessary for purchasing a shipping label.

#### Error Handling

The endpoint may return several different error responses depending on the nature of the request failure:

- **Order Not Found**: If the specified `order_id` does not correspond to an existing order, the API will return a `404` error with the following structure:

  ```json
  {
    "code": "order_not_found",
    "message": "Order not found",
    "data": {
      "success": false,
      "message": "Order not found"
    }
  }
  ```

- **General Error**: If there is an error processing the request, the API will return a `500` error with the following structure:

  ```json
  {
    "code": "error",
    "message": "An error message",
    "data": {
      "success": false,
      "message": "An error message"
    }
  }
  ```

#### Example Response

- **Success Response**:

```json
{
  "success": true,
  "config": {
    "order": {},
    "accountSettings": {},
    "packagesSettings": {
      "schema": {
        "custom": {},
        "predefined": {}
      },
      "packages": {
        "custom": {},
        "predefined": {}
      }
    },
    "shippingLabelData": {},
    "continents": {},
    "eu_countries": {},
    "items": {},
    "is_destination_verified": {},
    "is_origin_verified": {},
    "shipments": {},
    "origin_addresses": {},
    "should_use_fulfillment_api": true
  }
}
```

- **Order Not Found Error Response**:

  ```json
  {
    "code": "order_not_found",
    "message": "Order not found",
    "data": {
      "success": false,
      "message": "Order not found"
    }
  }
  ```

- **General Error Response**:

  ```json
  {
    "code": "error",
    "message": "An error occurred",
    "data": {
      "success": false,
      "message": "An error occurred"
    }
  }
  ```

### Permissions

The `permission_callback` for this route ensures that the user has the necessary permissions to access the endpoint. If the user does not have the required permissions, the API will return a `403 Forbidden` response.

## Summary

This API endpoint is designed to provide the configuration data necessary for purchasing a shipping label for a specific order. It includes robust error handling to cover scenarios such as missing or invalid orders and other unexpected errors. The response data is tailored to facilitate the shipping label purchase process, providing necessary configurations directly related to the specified order.
