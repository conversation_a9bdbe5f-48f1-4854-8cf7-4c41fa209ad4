# TOS API

## Overview

The `TosRESTController` class extends `WCShippingRESTController` and provides a REST API endpoint for handling the acceptance of Terms of Service (TOS) within the WooCommerce Shipping module.

### REST Base

The REST base for this controller is `tos`.

### Available Routes

This API provides the following route:

- `POST /wp-json/wcshipping/v1/tos`

## Endpoints

### `POST /wp-json/wcshipping/v1/tos`

#### Description

This endpoint allows clients to accept the Terms of Service (TOS) for the WooCommerce Shipping module.

#### Method

- **POST**

#### Request Body

The request body must be a JSON object containing the following field:

- **`accepted`**: A boolean indicating whether the TOS has been accepted. This field is required.

#### Example Request

```json
{
  "accepted": true
}
```

#### Response

The response is a JSON object containing the following fields:

- **`success`**: A boolean value indicating whether the request was successful.
- **`accepted`**: A boolean value confirming whether the TOS acceptance was successfully stored.

#### Response Structure

```json
{
  "success": true,
  "accepted": true
}
```

#### Example Response

```json
{
  "success": true,
  "accepted": true
}
```

### Permissions

The `permission_callback` for this route ensures that the user has the necessary permissions to access the endpoint. If the user does not have the required permissions, the API will return a `403 Forbidden` response.

### Error Handling

If the request is malformed or the `accepted` field is missing or set to `false`, the API will return an error response.

#### Example Error Response

```json
{
  "code": "bad_request",
  "message": "Bad request",
  "data": {
    "status": 400
  }
}
```

- **400 Bad Request**: This status code is returned if the `accepted` field is not provided or is set to `false`.

## Summary

The `TosRESTController` API endpoint is used to record the acceptance of the Terms of Service for the WooCommerce Shipping module. This is typically used during the initial setup or configuration process to ensure that the user has agreed to the terms before proceeding with using the service. The endpoint requires a `POST` request with the `accepted` field set to `true`, and it will return a confirmation that the TOS has been accepted.
