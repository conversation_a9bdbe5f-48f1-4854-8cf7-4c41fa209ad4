# Shipping Labels REST API

## Endpoint

`GET wp-json/wcshipping/v1/reports/labels?{query_params}`

## Description

This endpoint retrieves shipping labels based on specified criteria such as date range, pagination, and fields to return.

## Parameters

- **before** (string, required): 
  - Description: The end date for the range of shipping labels to retrieve. Must be a valid ISO date string.
  - Validation: Must be a valid date string.
  - Sanitization: Text field sanitization.

- **after** (string, required): 
  - Description: The start date for the range of shipping labels to retrieve. Must be a valid ISO date string.
  - Validation: Must be a valid date string.
  - Sanitization: Text field sanitization.

- **offset** (integer, required): 
  - Description: The number of items to skip before starting to collect the result set.
  - Validation: Must be numeric.
  - Sanitization: Absolute integer.

- **per_page** (integer, required): 
  - Description: The number of items to return per page. If not a positive integer, it defaults to -1 indicating no limit.
  - Validation: Must be numeric.
  - Sanitization: Converts to integer, defaults to -1 for non-positive values.

- **fields** (array of strings, required): 
  - Description: Specific fields to return in the response.
  - Validation: Must be an array of strings.
  - Sanitization: Cleaned using `wc_clean`.

## Response

The response will include an array of shipping labels with the following fields (if specified):

- `created_date`
- `order_id`
- `rate`
- `service_name`
- `refund`

## Example Request

```http
GET /wp-json/wcshipping/v1/reports/labels?before=2023-12-31&after=2023-01-01&offset=10&per_page=25&fields[]=created_date&fields[]=order_id
```

## Success Response Example

```json
{
  "success": true,
  "rows": [
    {
      "created_date": "2024-02-15T08:01:00Z",
      "order_id": "2711",
      "rate": 10.0,
      "service_name": "USPS - Priority Mail International",
      "refund": ""
    },
    {
      "created_date": "2024-01-09T15:49:37Z",
      "order_id": "1758",
      "rate": 15.19,
      "service_name": "USPS - Priority Mail",
      "refund": "Completed"
    }
  ],
  "meta": {
    "pages": 1,
    "total_count": 2,
    "total_cost": 25.19,
    "total_refunds": 1
  }
}
```

## Error Response Example

### Invalid Date Parameter

If a date parameter is not a valid ISO date:
```json
{
    "code": "rest_invalid_param",
    "message": "Invalid parameter(s): before",
    "data": {
        "status": 400,
        "params": {
            "before": "Invalid parameter."
        },
        "details": []
    }
}
```
### Invalid offset or per_page Value

If offset or per_page is not a valid integer:
```json
{
    "code": "rest_invalid_param",
    "message": "Invalid parameter(s): per_page",
    "data": {
        "status": 400,
        "params": {
            "per_page": "Invalid parameter."
        },
        "details": []
    }
}
```