# Account Settings API

## Get Account Settings

### Endpoint

`GET /wp-json/wcshipping/v1/account/settings`

### Description

Retrieve the account settings for the user.

### Response Example

```json
{
	"success": true,
	"storeOptions": {
		"currency_symbol": "$",
		"dimension_unit": "cm",
		"weight_unit": "kg",
		"origin_country": "US"
	},
	"purchaseSettingsKey": {
		"selected_payment_method_id": 0,
		"enabled": true,
		"paper_size": "value_from_get_preferred_paper_size",
		"email_receipts": true,
		"use_last_service": false,
		"use_last_package": true
	},
	"purchaseSettingsMetaKey": {
		"can_manage_payments": true,
		"can_edit_settings": true,
		"master_user_name": "Master User",
		"master_user_login": "master_user_login",
		"master_user_wpcom_login": "wpcom_login",
		"master_user_email": "<EMAIL>",
		"payment_methods": [ "method1", "method2" ],
		"add_payment_method_url": "https://example.com/add_payment_method",
		"warnings": {
			"payment_methods": "There was a problem updating your saved credit cards."
		}
	},
	"userMeta": {
		"last_box_id": "box_id",
		"last_service_id": "service_id",
		"last_carrier_id": "carrier_id",
		"last_order_completed": true
	}
}
```

## Save Account Settings

### Endpoint

`POST /wp-json/wcshipping/v1/account/settings`

### Description

Save the account settings for the user.

### Request Body

-   `selected_payment_method_id` (string, optional): The ID of the selected payment method.
-   `paper_size` (string, optional): `label` or `letter` or `a4`.
-   `email_receipts` (boolean, optional): Wether to send email receipts after each shipping label purchase.
-   `use_last_package` (boolean, optional): Weather the last used package, on a user-level, should be pre-selected during the next shipping label purchase.
-   `use_last_service` (boolean, optional): Weather the last used shipping service, on a user-level, should be pre-selected during the next shipping label purchase.
-   `selected_payment_method_id` (integer, optional): The ID of the preferred payment method.
-   `last_order_completed` (boolean, optional): True or false to auto check the complete order checkbox on the label purchase screen.

### Request Example

```http
POST /wp-json/wcshipping/v1/account/settings
Content-Type: application/json

{
  "selected_payment_method_id": "pm_12345",
  "other_setting": "value"
}
```

### Response Example

```json
{
	"success": true
}
```

### Error Response Example

```json
{
	"code": "save_failed",
	"message": "Unable to update settings. Error message describing the issue.",
	"data": {
		"status": 400
	}
}
```
