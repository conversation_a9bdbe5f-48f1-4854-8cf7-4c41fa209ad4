# Label Status API

## Get Label Status

### Endpoint
`GET /wcshipping/v1/label/status/{order_id}/{label_id}`

### Description
Retrieve the status of a specific shipping label associated with an order.

### Parameters
- `order_id` (integer, required): The ID of the order.
- `label_id` (integer, required): The ID of the label.

### Request Example
```http
GET /wcshipping/v1/label/status/123/456
```

### Response Example
```json
{
  "success": true,
  "label": {
    "id": 456,
    "status": "in_transit",
    "tracking_number": "123456789",
    "carrier": "UPS"
  }
}
```

### Error Response Example
```json
{
  "code": "label_status_error",
  "message": "Error message describing the issue.",
  "data": {
    "status": 400
  }
}
```
