# Packages API

## Get Packages

### Endpoint

`GET /wp-json/wcshipping/v1/packages`

### Description

Retrieve custom and/or predefined packages.

### Response Example

```json
{
  "storeOptions": {
    "currency_symbol": "$",
    "dimension_unit": "cm",
    "weight_unit": "kg",
    "origin_country": "US"
  },
  "packages": {
    "saved": { // Saved/starred packages
      "custom": [
        {
          "id": "849225dc153eb8019ad91db72677a007",
          "name": "12x12x12",
          "dimensions": "12 x 11 x 10",
          "length": 12,
          "width": 11,
          "height": 10,
          "box_weight": 0.031,
          "is_letter": false,
          "is_user_defined": true
        },
        {
          "id": "745e3db6a7ffd50e1a72b39482f0882d",
          "name": "Flat",
          "dimensions": "15 x 12 x 0.75",
          "length": 15,
          "width": 12,
          "height": 0.75,
          "box_weight": 0,
          "is_letter": true,
          "is_user_defined": true
        }
      ],
      "predefined": { // Only saved/starred predefined packages
        "usps": [
          "small_flat_box",
          "medium_flat_box_top"
        ],
        "fedex": [
          ...
        ]
      }
    },
    "predefined": { // All predefined packages available
      "usps": { // Carrier
        "pri_flat_boxes": { // Package Group
          "title": "USPS Priority Mail Flat Rate Boxes",
          "definitions": [ // Packages
            {
              "inner_dimensions": "8.63 x 5.38 x 1.63",
              "outer_dimensions": "8.63 x 5.38 x 1.63",
              "box_weight": 0,
              "is_flat_rate": true,
              "id": "small_flat_box",
              "name": "Small Flat Rate Box",
              "dimensions": "8.63 x 5.38 x 1.63",
              "max_weight": 70,
              "is_letter": false,
              "group_id": "pri_flat_boxes",
              "can_ship_international": true
            },
            {
              "inner_dimensions": "11 x 8.5 x 5.5",
              "outer_dimensions": "11.25 x 8.75 x 6",
              "box_weight": 0,
              "is_flat_rate": true,
              "id": "medium_flat_box_top",
              "name": "Medium Flat Rate Box 1, Top Loading",
              "max_weight": 70,
              "is_letter": false,
              "group_id": "pri_flat_boxes",
              "can_ship_international": true
            },
          ]
        }
      },
      "fedex": { // Carrier
        "express": { // Package Group
          "title": "FedEx Express Packages",
          "definitions": [ // Packages
            {
              "inner_dimensions": "13.19 x 9.25 x 0.75",
              "outer_dimensions": "13.19 x 9.25 x 0.75",
              "box_weight": 0,
              "is_flat_rate": false,
              "id": "FedExEnvelope",
              "name": "Envelope",
              "dimensions": "13.19 x 9.25 x 0.75",
              "max_weight": 10,
              "is_letter": true,
              "group_id": "express"
            },
            {
              "inner_dimensions": "15.5 x 12 x 0.75",
              "outer_dimensions": "15.5 x 12 x 0.75",
              "box_weight": 0,
              "is_flat_rate": false,
              "id": "FedExPak",
              "name": "Large Pak",
              "dimensions": "15.5 x 12 x 0.75",
              "max_weight": 20,
              "is_letter": true,
              "group_id": "express"
            },
          ]
        }
      }
    }
  }
  "success": true
}
```

## Create Packages

### Endpoint
`POST /wcshipping/v1/packages`

### Description
Create custom and/or predefined packages.

### Request Body
- `custom` (array, optional): An array of custom packages.
- `predefined` (object, optional): A dictionary of predefined packages by carrier.

### Request Example
```http
POST /wcshipping/v1/packages
Content-Type: application/json

{
  "custom": [
    {
      "name": "Custom Package 1",
      "length": 10,
      "width": 5,
      "height": 5,
      "weight": 2
    }
  ],
  "predefined": {
    "UPS": ["Small Box", "Medium Box"],
    "FedEx": ["Envelope", "Pak"]
  }
}
```

### Response Example
```json
{
  "predefined": {
    "UPS": ["Small Box", "Medium Box"],
    "FedEx": ["Envelope", "Pak"]
  },
  "custom": [
    {
      "id": "5f7b1b3b1b1b4b1b8b1b1b1b1b1b1b1b",
      "name": "Custom Package 1",
      "length": 10,
      "width": 5,
      "height": 5,
      "weight": 2
    }
  ]
}
```

### Error Response Example
```json
{
  "code": "duplicate_custom_package_names",
  "message": "The new custom package names are not unique.",
  "data": {
    "package_names": ["Custom Package 1"]
  }
}
```

## Update Packages

### Endpoint
`PUT /wcshipping/v1/packages`

### Description
Update the existing custom and predefined packages.

### Request Body
- `custom` (array, optional): An array of custom packages.
- `predefined` (object, optional): A dictionary of predefined packages by carrier.

### Request Example
```http
PUT /wcshipping/v1/packages
Content-Type: application/json

{
  "custom": [
    {
      "id": "5f7b1b3b1b1b4b1b8b1b1b1b1b1b1b1b",
      "name": "Updated Custom Package 1",
      "length": 12,
      "width": 6,
      "height": 6,
      "weight": 3
    }
  ],
  "predefined": {
    "UPS": ["Large Box"],
    "FedEx": ["Box"]
  }
}
```

### Response Example
```json
{
  "predefined": {
    "UPS": ["Large Box"],
    "FedEx": ["Box"]
  },
  "custom": [
    {
      "id": "5f7b1b3b1b1b4b1b8b1b1b1b1b1b1b1b",
      "name": "Updated Custom Package 1",
      "length": 12,
      "width": 6,
      "height": 6,
      "weight": 3
    }
  ]
}
```

## Delete Package

### Endpoint
`DELETE /wcshipping/v1/packages/{type}/{id}`

### Description
Delete a saved package template by type and ID. The type can be either 'custom' or 'predefined'.

### URL Parameters
- `type` (string, required): The type of package to delete. Must be either 'custom' or 'predefined'.
- `id` (string, required): The ID of the package to delete. Must contain only alphanumeric characters, underscores, or hyphens.

### Request Example ( custom package )
```http
DELETE /wcshipping/v1/packages/custom/5f7b1b3b1b1b4b1b8b1b1b1b1b1b1b1b
```

### Success Response Example
```json
{
  "success": true,
  "predefined": {
    "upsdap": ["SmallExpressBox", "MediumExpressBox"],
    "fedex": ["Box"],
    "usps": ["small_flat_box"],
    "dhlexpress": ["Box2Cube"]
  },
  "custom": [
    {
      "id": "849225dc153eb8019ad91db72677a007",
      "name": "Custom Package 1",
      "dimensions": "10 x 20 x 30",
      "boxWeight": 0.2,
      "maxWeight": 0,
      "type": "envelope",
      "is_user_define": true
    }
  ]
}
```

### Request Example ( predefined package )
```http
DELETE /wcshipping/v1/packages/predefined/MediumExpressBox
```

### Success Response Example
```json
{
  "success": true,
  "predefined": {
    "upsdap": ["SmallExpressBox"],
    "fedex": ["Box"],
    "usps": ["small_flat_box"],
    "dhlexpress": ["Box2Cube"]
  },
  "custom": [
    {
      "id": "849225dc153eb8019ad91db72677a007",
      "name": "Custom Package 1",
      "dimensions": "10 x 20 x 30",
      "boxWeight": 0.2,
      "maxWeight": 0,
      "type": "envelope",
      "is_user_define": true
    }
  ]
}
```

### Error Response Examples

#### Package Not Found
```json
{
  "success": false,
  "error": {
    "code": "package_not_found",
    "message": "Predefined package not found."
  }
}
```

#### Invalid Parameters
The endpoint handles parameter validation in two ways:

1. **Route Matching (404 Not Found)**
   - If the `type` parameter is not 'custom' or 'predefined'
   - If the `id` parameter contains characters other than alphanumeric, underscores, or hyphens
   - These will result in a 404 error as no route matches the request

2. **Parameter Validation (400 Bad Request)**
   - If the parameters pass the URL route matching but fail the additional validation rules
   - This includes validation of parameter types and formats
