# Address API Endpoints

## Origin Addresses

### Get All Origin Addresses

- Endpoint: `GET /wp-json/wcshipping/v1/address/origins`
- Description: Retrieve all origin addresses
- Response Example:

```json
[
    {
        "company": "Sam's",
        "address_2": "",
        "city": "SAN FRANCISCO",
        "state": "CA",
        "postcode": "94104-1129",
        "country": "US",
        "phone": "9876543210",
        "address_1": "417 MONTGOMERY ST",
        "first_name": "TEST",
        "last_name": "233",
        "email": "<EMAIL>",
        "id": "store_details",
        "default_address": true,
        "is_verified": true
    },
    {
        "company": "Sam's",
        "city": "SAN FRANCISCO",
        "state": "CA",
        "country": "US",
        "postcode": "94104-1129",
        "address_2": "",
        "phone": "0098765416",
        "address_1": "417 MONTGOMERY ST",
        "first_name": "TEST",
        "last_name": "33",
        "email": "<EMAIL>",
        "id": "6762c20045dc0",
        "default_address": false,
        "is_verified": true
    }
]
```

### Update Origin Address

- Endpoint: `POST /wp-json/wcshipping/v1/address/update_origin`
- Description: Update an origin address. The address will be normalized before saving.
- Body:

```json
{
  "address": {
    "company": "APR",
    "city": "SAN FRANCISCO",
    "state": "CA",
    "country": "US",
    "postcode": "94104-1129",
    "address_2": "",
    "phone": "0987653090",
    "address_1": "417 MONTGOMERY ST",
    "first_name": "TEST",
    "last_name": "4",
    "email": "<EMAIL>",
    "id": "677672793dcc0",
    "default_address": false,
    "address": "417 MONTGOMERY ST",
    "name": "TEST 4",
    "is_verified": true
  },
  "isVerified": true
}
```

- Response Example:

```json
{
  "success": true,
  "address": {
    "company": "APR",
    "city": "SAN FRANCISCO",
    "state": "CA",
    "country": "US",
    "postcode": "94104-1129",
    "address_2": "",
    "phone": "0987653090",
    "address_1": "417 MONTGOMERY ST",
    "first_name": "TEST",
    "last_name": "4",
    "email": "<EMAIL>",
    "id": "677672793dcc0",
    "default_address": false,
    "is_verified": true // Indicates if the address is verified, this field is a property of the address object and not the response
  },
  /*
   * Indicates if the verification process has been successful, this is always set to true as we're saving the address only if it's verified
   * it's kept to provide uniformity with the destination address response
   */
  "isVerified": true
}
```

### Delete Origin Address

- Endpoint: `DELETE /wp-json/wcshipping/v1/address/{id}`
- Description: Delete an origin address by ID
- Parameters:
  - `id` (string, required): The ID of the address to delete
- Response Example:

```json
{
  "success": true,
  "deleted_id": "address_1"
}
```

## Destination Addresses

### Endpoint

`POST /wp-json/wcshipping/v1/address/{order_id}/update_destination`

### Description

Confirm and update the destination address for an order.

### Parameters

- `order_id` (integer, required): The ID of the order.
- `address` (object, required): The destination address.
- `isVerified` (boolean, required): Indicates if the address is verified.

### Request Example

```http
POST /wp-json/wcshipping/v1/address/123/update_destination
Content-Type: application/json

{
  "address": {
      "company": "Acme Corp",
      "city": "SOMETOWN",
      "state": "NC",
      "postcode": "27605-1481",
      "country": "US",
      "address_2": "",
      "phone": "9195551937",
      "address_1": "401 OAK ST APT 200",
      "first_name": "JOHN",
      "last_name": "SMITH",
      "email": "<EMAIL>",
      "address": "401 OAK ST APT 200",
      "name": "JOHN SMITH"
  },
  /*
   * It's always get's saved as true, because this endpoint is called after the address is normalized 
   * and the normalized address it suggested to the user to confirm and use.
   * They may however choose to not use the normalized address, in which case the isVerified will 
   * still be true, as they've confirmed the address
   */
  "isVerified": true
}
```

### Response Example

```json
{
  "success": true,
  "address": {
      "company": "Acme Corp",
      "city": "SOMETOWN",
      "state": "NC",
      "postcode": "27605-1481",
      "country": "US",
      "address_2": "",
      "phone": "9195551937",
      "address_1": "401 OAK ST APT 200",
      "first_name": "JOHN",
      "last_name": "SMITH",
      "email": "<EMAIL>"
  },
  "isVerified": true
}
```

## Verify destination address

### Endpoint

`GET /wp-json/wcshipping/v1/address/{order_id}/verify_order`

### Description

Verify if shipping destination is normalized.

### Parameters

- `order_id` (integer, required): The ID of the order.

### Request Example

```http
GET /wp-json/wcshipping/v1/address/123/verify_order
```

#### Response if destination is already verified

```json
{
  "success": true,
  "normalizedAddress": {
    "company": "ACME Corp",
    "city": "San Francisco",
    "state": "CA",
    "postcode": "94105",
    "country": "US",
    "phone": "************",
    "address_1": "123 Main St",
    "address_2": "Suite 100",
    "first_name": "John",
    "last_name": "Doe"
  },
  "isTrivialNormalization": true,
  "isVerified": true
}
```

#### Response if the normalization process results in an error

```json
{
  "code": "error_code_from_response",
  "message": "Error message from response",
  "data": {
    "message": "Error message from response"
  }
}
```

#### Response if the normalization process returns field errors

```json
{
  "success": false,
  "errors": {
    // Field errors details here
  },
  "isVerified": false
}
```

#### Response if the normalization process is successful but the address is not verified

```json
{
  "success": true,
  "normalizedAddress": {
    "company": "ACME Corp",
    "address_1": "123 Main St",
    "address_2": "Suite 100",
    "city": "San Francisco",
    "state": "CA",
    "postcode": "94105",
    "country": "US",
    "phone": "************",
    "first_name": "John",
    "last_name": "Doe"
  },
  "isTrivialNormalization": true_or_false, // depending on the response
  "isVerified": false
}
```

## Normalize address

### Endpoint

`POST /wp-json/wcshipping/v1/address/normalize`

### Description

Submit an address to the normalization service and return the normalized address.

### Parameters

- `address` (object, required): The address to normalize. {
  Required fields:
  - `address_1` (string): Street address
  - `city` (string): City name
  - `state` (string): State code
  - `postcode` (string): Postal code
  - `country` (string): Country code
  - `phone` (string): Phone number
  - `email` (string): Email address
  - `first_name` (string): First name (Required if company is not present)
  - `last_name` (string): Last name (Required if company is not present)
  - `company` (string): Company name (Required if first_name and last_name are not present)
  Optional fields:
  - `address_2` (string): Secondary address line

  }

### Request Example

```http
POST /wp-json/wcshipping/v1/address/normalize
Content-Type: application/json

{
  "address": {
    "address_1": "789 Oak St",
    "city": "Sometown",
    "state": "TX",
    "postcode": "54321",
    "country": "US",
    "address_2": "Suite 123",
    "first_name": "John",
    "last_name": "Smith",
    "company": "ACME Corp",
    "phone": "555-0123",
    "email": "<EMAIL>",
    "id": "12345",
    "default_address": false,
    "is_verified": false
  }
}
```

### Response Example

```json
{
  "success": true,
  "normalizedAddress": { // normalized address, which is used to suggest to the user to confirm and use
    "company": "ACME Corp",
    "city": "SOMETOWN",
    "state": "TX", 
    "country": "US",
    "postcode": "54321-1234",
    "address_2": "SUITE 123",
    "phone": "555-0123",
    "address_1": "789 OAK ST",
    "first_name": "John",
    "last_name": "Smith",
    "email": "<EMAIL>",
    "id": "12345"
  },
  "isTrivialNormalization": true,
  "address": { // original address, which is used to display to the user, it's only formatted, not normalized
    "company": "ACME Corp",
    "city": "Sometown",
    "state": "TX",
    "country": "US", 
    "postcode": "54321",
    "address_2": "Suite 123",
    "phone": "555-0123",
    "address_1": "789 Oak St",
    "first_name": "John",
    "last_name": "Smith",
    "email": "<EMAIL>",
    "id": "12345",
    "is_verified": false,
    "name": "John Smith", // combined from first_name and last_name
    "address": "789 Oak St Suite 123" // combined from address_1 and address_2
  }
}
```

### Error Response Example

#### Error response for general normalization error

```json
{
  "code": "error_code", 
  "message": "Error message", 
  "data": {
    "message": "Error message"
  }
}
```

#### Error Response for field errors

```json
{
  "success": false,
  "message": "address_normalization_failed",
  "errors": {
    // field errors details here, example structure
    "general": "General error message",
    "specific_field": "Specific field error message"
  },
  "isTrivialNormalization": false,
  "address": {
    // original address details here, example structure
    "first_name": "John",
    "last_name": "Doe",
    "address_1": "123 Main St",
    "city": "Anytown",
    "state": "CA",
    "postcode": "12345",
    "country": "US"
  }
}
```

## Error Responses

### Validation Error

```json
{
  "code": "validation_error",
  "message": "Validation failed",
  "data": {
    "status": 400,
    "errors": {
      "field_name": "Error message"
    }
  }
}
```

### Normalization Error

```json
{
  "code": "address_normalization_failed",
  "message": "Address normalization failed",
  "data": {
    "status": 400,
    "message": "Detailed error message"
  }
}
```
