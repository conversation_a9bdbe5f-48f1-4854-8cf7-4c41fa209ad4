# Shipments API

## Update Shipments

### Endpoint

`POST /wcshipping/v1/shipments/{order_id}`

### Description

Update the shipments for a specific order.

### Request Body

- `order_id` (integer, required): The ID of the order.
- `shipments` (array, required): The shipment details.
- `shipmentIdsToUpdate` (array, optional): The shipment IDs to update, if not provided, no shipment will be updated. It's a map of shipment ID to new Shipment ID.

### Request Example

```http
POST /wcshipping/v1/shipments/123
Content-Type: application/json

{
  "shipments":{
    "0":[
      {
        "id":"1-sub-0",
        "quantity":1
      },
      {
        "id":"1-sub-1",
        "quantity":1
      }
    ],
    "1":[
      {
        "id":2,
        "quantity":2,
        "subItems":[
          {
            "id":"2-sub-0",
            "quantity":1,
            "parentId":2,
            "subItems":[
              
            ]
          },
          {
            "id":"2-sub-1",
            "quantity":1,
            "parentId":2,
            "subItems":[
              
            ]
          }
        ]
      }
    ]
  },
  shipment_ids_to_update: {
    "2": "1"
  }
}
```

### Response Example

```json
{
  "success": true,
  "data": {
    "0": [
      {
        "id": "1-sub-0",
        "quantity": 1
      },
      {
        "id": "1-sub-1",
        "quantity": 1
      }
    ],
    "1": [
      {
        "id": 2,
        "quantity": 2,
        "subItems": [
          {
            "id": "2-sub-0",
            "quantity": 1,
            "parentId": 2,
            "subItems": []
          },
          {
            "id": "2-sub-1",
            "quantity": 1,
            "parentId": 2,
            "subItems": []
          }
        ]
      }
    ]
  }
}
```

### Error Response Example

```json
{
  "code": "invalid_request",
  "message": "Request payload is invalid.",
  "data": {
    "status": 400
  }
}
```
