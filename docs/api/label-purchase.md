# Label Purchase API

## Retrieve Purchased Labels

### Endpoint

`GET /wcshipping/v1/label/purchase/{order_id}`

### Description

Retrieve purchased labels for a specific order.

### Parameters

-   `order_id` (integer, required): The ID of the order.

### Response Example

```json
{
	"success": true,
	"labels": [
		{
			"id": 456,
			"tracking_number": "1Z999AA10123456784",
			"carrier": "UPS",
			"service": "Ground",
			"rate": 10.0,
			"status": "purchased"
		}
	]
}
```

### Error Response Example

```json
{
	"code": "retrieve_error",
	"message": "Error message describing the issue.",
	"data": {
		"status": 400
	}
}
```

## Purchase Labels

### Endpoint

`POST /wcshipping/v1/label/purchase/{order_id}`

### Description

Purchase a shipping label for a specific order.

### Parameters

-   `order_id` (integer, required): The ID of the order.
-   `origin` (object, required): The origin address details.
-   `destination` (object, required): The destination address details.
-   `packages` (array, required): The package details.
-   `selected_rate` (object, required): The selected shipping rate.
-   `selected_rates_options` (object, optional): Additional options for the selected rate.
-   `shipment_options` (object, optional): Extra options for the shipment.
    -   `label_date` (string, optional): ISO 8601 formatted date string for the shipping label. It won't change the response but for some carriers such as USPS this date will be printed on the label.
-   `hazmat` (boolean, optional): Indicates if the shipment contains hazardous materials.
-   `customs` (object, optional): Customs information for international shipments.

### Request Example

```http
POST /wcshipping/v1/label/purchase/123
Content-Type: application/json

{
  "origin": {
    "address_1": "123 Main St",
    "address_2": "",
    "city": "Anytown",
    "state": "CA",
    "postcode": "12345",
    "country": "US"
  },
  "destination": {
    "address_1": "456 Elm St",
    "address_2": "Apt 789",
    "city": "Othertown",
    "state": "NY",
    "postcode": "67890",
    "country": "US"
  },
  "packages": [
    {
      "length": 10,
      "width": 5,
      "height": 5,
      "weight": 2,
      "id": "0" // shipment index, 0 for the first shipment
    }
  ],
  "selected_rate": {
    "carrier": "UPS",
    "service": "Ground",
    "rate": 10.00
  },
  "selected_rates_options": {
    "signature": {
      "value": "adult"
    },
    "insurance": {
      "value": 100.00
    }
  },
  "hazmat": false,
  "user_meta": {
    "last_order_complete": true,
  },
  "customs": {
    "contents_type": "merchandise",
    "contents_explanation": "T-shirts",
    "non_delivery_option": "return",
    "customs_items": [
      {
        "description": "T-shirt",
        "quantity": 2,
        "weight": 1,
        "value": 20.00,
        "hs_tariff_number": "610910",
        "origin_country": "US"
      }
    ]
  }
}
```

### Response Example

```json
{
  "labels": [
    {
      "label_id": 3628,
      "tracking": null,
      "refundable_amount": 0,
      "created": 1723147883960,
      "carrier_id": null,
      "service_name": "USPS - Priority Mail",
      "status": "PURCHASE_IN_PROGRESS",
      "commercial_invoice_url": "",
      "is_commercial_invoice_submitted_electronically": "",
      "package_name": "Small Flat Rate Box",
      "is_letter": false,
      "product_names": [
        "A nice P",
        "Shirt",
        "Jacket",
        "Jacket",
        "Hat"
      ],
      "product_ids": [
        46,
        209,
        217,
        222,
        225
      ],
      "id": "0"
    }
  ],
  "selected_rates": {
    "shipment_0": {
      "rate": {
        "rateId": "rate_5df347cbe1b446b29869375ef6cc39e5",
        "serviceId": "Priority",
        "carrierId": "usps",
        "title": "USPS - Priority Mail",
        "rate": 8.71,
        "retailRate": 10.4,
        "listRate": 9.05,
        "isSelected": false,
        "tracking": true,
        "insurance": 100,
        "freePickup": true,
        "shipmentId": "shp_810aa6baa11b497db82b159435bfc1c0",
        "deliveryDays": 2,
        "deliveryDateGuaranteed": false,
        "deliveryDate": null
      },
      "parent": null
    }
  },
  "selected_hazmat": {
    "shipment_0": {
      "isHazmat": false,
      "category": ""
    }
  },
  "selected_origin": {
    "shipment_0": {
      "company": "Woot one",
      "name": "STORE ADDRESS",
      "phone": "9900876546",
      "country": "US",
      "state": "NY",
      "address": "3206 30TH AVE",
      "address_2": "",
      "city": "ASTORIA",
      "postcode": "11102-1561"
    }
  },
  "selected_destination": {
    "shipment_0": {
      "company": "",
      "city": "CITY NAME",
      "state": "CA",
      "postcode": "94104-1129",
      "country": "US",
      "phone": "9990900909",
      "name": "SAM ",
      "address": "ADDRESS",
    }
  },
  "customs_information": {
    "shipment_0": {
      "items": [
        {
          "id": 9,
          "subtotal": "95.00",
          "subtotal_tax": "0.00",
          "total": "95.00",
          "total_tax": "0.00",
          "price": "95.00",
          "quantity": 1,
          "tax_class": "",
          "name": "A nice P",
          "product_id": 46,
          "sku": "a-nice-p",
          "meta": [],
          "image": "image_url",
          "weight": "5",
          "dimensions": {
            "length": "50",
            "width": "50",
            "height": "60"
          },
          "variation": [
            {
              "key": "Warranty",
              "value": "Lifetime",
              "display_key": "Warranty",
              "display_value": "<p>Lifetime<\/p>\n"
            }
          ],
          "subItems": [],
          "description": "A nice P",
          "hsTariffNumber": "",
          "originCountry": "US"
        },
        {
          "id": 10,
          "subtotal": "36.00",
          "subtotal_tax": "0.00",
          "total": "36.00",
          "total_tax": "0.00",
          "price": "18.00",
          "quantity": 2,
          "tax_class": "",
          "name": "Shirt",
          "product_id": 209,
          "sku": "woo-fasion-shirt",
          "meta": [],
          "image": "image_url",
          "weight": "",
          "dimensions": {
            "length": "",
            "width": "",
            "height": ""
          },
          "variation": [
            {
              "key": "Warranty",
              "value": "Lifetime",
              "display_key": "Warranty",
              "display_value": "<p>Lifetime<\/p>\n"
            }
          ],
          "subItems": [
            {
              "id": "10-sub-0",
              "subtotal": "36.00",
              "subtotal_tax": "0.00",
              "total": "36.00",
              "total_tax": "0.00",
              "price": "18.00",
              "quantity": 1,
              "tax_class": "",
              "name": "Shirt",
              "product_id": 209,
              "sku": "woo-fasion-shirt",
              "meta": [],
              "image": "image_url",
              "weight": "",
              "dimensions": {
                "length": "",
                "width": "",
                "height": ""
              },
              "variation": [
                {
                  "key": "Warranty",
                  "value": "Lifetime",
                  "display_key": "Warranty",
                  "display_value": "<p>Lifetime<\/p>\n"
                }
              ],
              "parentId": 10,
              "subItems": []
            },
            {
              "id": "10-sub-1",
              ...
              "dimensions": {
                "length": "",
                "width": "",
                "height": ""
              },
              "variation": [
                {
                  "key": "Warranty",
                  "value": "Lifetime",
                  "display_key": "Warranty",
                  "display_value": "<p>Lifetime<\/p>\n"
                }
              ],
              "parentId": 10,
              "subItems": []
            }
          ],
          "description": "Shirt",
          "hsTariffNumber": "",
          "originCountry": "US"
        },
        {
          "id": 11,
          "subtotal": "75.00",
          "subtotal_tax": "0.00",
          ...
          "dimensions": {
            "length": "",
            "width": "",
            "height": ""
          },
          "variation": [
            {
              "key": "Warranty",
              "value": "Lifetime",
              "display_key": "Warranty",
              "display_value": "<p>Lifetime<\/p>\n"
            }
          ],
          "subItems": [
            {
              "id": "11-sub-0",
              "subtotal": "75.00",
              ...
              "dimensions": {
                "length": "",
                "width": "",
                "height": ""
              },
              "variation": [
                {
                  "key": "Warranty",
                  "value": "Lifetime",
                  "display_key": "Warranty",
                  "display_value": "<p>Lifetime<\/p>\n"
                }
              ],
              "parentId": 11,
              "subItems": []
            },
            {
              "id": "11-sub-1",
              "subtotal": "75.00",
              "subtotal_tax": "0.00",
              "total": "75.00",
              "total_tax": "0.00",
             ...
              "dimensions": {
                "length": "",
                "width": "",
                "height": ""
              },
              "variation": [
                {
                  "key": "Warranty",
                  "value": "Lifetime",
                  "display_key": "Warranty",
                  "display_value": "<p>Lifetime<\/p>\n"
                }
              ],
              "parentId": 11,
              "subItems": []
            },
            {
              "id": "11-sub-2",
              "subtotal": "75.00",
              "subtotal_tax": "0.00",
              ...
              "dimensions": {
                "length": "",
                "width": "",
                "height": ""
              },
              "variation": [
                {
                  "key": "Warranty",
                  "value": "Lifetime",
                  "display_key": "Warranty",
                  "display_value": "<p>Lifetime<\/p>\n"
                }
              ],
              "parentId": 11,
              "subItems": []
            }
          ],
          "description": "Jacket",
          "hsTariffNumber": "",
          "originCountry": "US"
        },
        {
          "id": 12,
          "subtotal": "10.00",
          ...
          "dimensions": {
            "length": "",
            "width": "",
            "height": ""
          },
          "variation": [
            {
              "key": "pa_size",
              "value": "large",
              "display_key": "Size",
              "display_value": "<p>Large<\/p>\n"
            },
            {
              "key": "Warranty",
              "value": "Lifetime",
              "display_key": "Warranty",
              "display_value": "<p>Lifetime<\/p>\n"
            }
          ],
          "subItems": [],
          "description": "Jacket - Large",
          "hsTariffNumber": "",
          "originCountry": "US"
        },
        {
          "id": 13,
         ...
          "subItems": [],
          "description": "Hat - Large",
          "hsTariffNumber": "",
          "originCountry": "US"
        }
      ],
      "contentsType": "merchandise",
      "restrictionType": "none",
      "isReturnToSender": false,
      "itn": ""
    }
  },
  "success": true
}
```

### Error Response Example

```json
{
	"code": "error_code_from_label_response",
	"message": "Error message from label_response",
	"data": {
		"success": false,
		"message": "Error message from label_response"
	}
}
```
