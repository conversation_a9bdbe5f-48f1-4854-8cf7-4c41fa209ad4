# Usage Tracking

_WooCommerce Shipping_ implements usage tracking, based on the native WooCommerce Usage Tracking, and is only enabled when WooCommerce Tracking is enabled.

When a store opts in to WooCommerce usage tracking and uses _WooCommerce Shipping_, they will also be opted in to the tracking added by _WooCommerce Shipping_.

## What is tracked

We only track data around how WooCommerce Shipping is used, no personal infornmation is tracked, and we do not track anything from a merchant's customers.

The following global information is passed with every tracking event:

- environment
- version
- is_atomic
- is_connected
- is_development_site
- is_safe_mode
- is_offline_mode

### Tracking events

All event names are prefixed with `wcadmin_wcshipping_`. When making use of the JS and PHP client methods only pass the event name without the prefix, event names are automatically prefixed through the clients.

When naming events please use the format `<context>_<optional subcontext>_<action>_<optional qualifier>` eg `<label_purchase>_<split_shipment>_<clicked>`. Try and stick the naming convention of existing events on screens, ie all events on the label purchase flow screen should have a context of `label_purchase`.

#### `plugin_activated`

Fires when the plugin is activated.

#### `plugin_deactivated`

Fires when the plugin is deactivated.

#### `settings_saved`

Fires when the WC Shipping settings are saved.

#### `wpcom_connect_site_connected`

Fires when the merchant returns from WordPress.com, after having gone through authorization.

#### `tos_accepted`

Fires when TOS is accepted. This event will not be triggered if our ToS have already been agreed to.

#### `tos_already_accepted`

Fires when a connection happens, but our ToS have already been accepted.

#### `onboarding_banner_viewed`

Fires when one of our onboarding banners is rendered.

#### `setup_complete_banner_dismissed`

Fires when the dismiss button is clicked on the setup complete banner.

#### `wpcom_connect_site_start`

Fires when a connection to WordPress.com is registered, and the merchant is about to get redirected to WordPress.com.

#### `wpcom_connect_site_error`

Fires when a connection to WordPress.com fails for any reason (e.g. a faulty HTTP request or an internal fatal error).

#### `onboarding_connect_component_viewed`

Fires when the "Connect your store" component is rendered.

#### `onboarding_connect_component_connect_button_clicked`

Fires when the "Connect your store" button is clicked in our onboarding connect component.

#### `onboarding_connect_component_connect_error_viewed`

Fires if we get an error while trying to connect a store from the onboarding connect component.

#### `order_create_shipping_label_clicked`

Fires when the Create Shipping Label button is clicked on an order, this is the button that opens up the label purchase flow.

#### `label_purchase_split_shipment_clicked`

Fires when the split shipment button is clicked on the label purchase flow. This button opens up the model to create a split shipment.

#### `label_purchase_essential_details_cta_clicked`

Fires when the CTA link is clicked in the essential details box on the label purchase flow. This brings focus to the section that requires attention.

#### `label_purchase_hazmat_toggled`

Fires when the Hazmat option is toggled on the label purchase flow.

#### `label_purchase_hazmat_category_selected`

Fires when a Hazmat category is selected on the label purchase flow.

#### `label_purchase_package_tab_clicked`

Fires when any of the package tabs is clicked on the label purchase flow.

#### `label_purchase_get_rates_clicked`

Fires when the Get Rates button is clicked on any of the packages tabs.

#### `label_purchase_package_favorite_clicked`

Fires when a package is favourited on the label purchase flow.

#### `label_purchase_purchase_shipping_label_clicked`

Fires when the Purchase Shipping Label button is clicked on the label purchase flow.

#### `split_shipment_modal_closed`

Fires when the split shipment modal is closed.

#### `split_shipment_modal_notice_dismissed`

Fires when the split shipment modal notice is dismissed.

#### `split_shipment_modal_close_confirm_clicked`

Fires when the split shipment modal close confirm message is clicked.

#### `split_shipment_modal_close_cancel_clicked`

Fires when cancel is clicked on the split shipment modal close message.

#### `label_purchase_upsdap_tos_confirmed`

Fires when the UPS TOS has been accepted. The TOS modal only appears if the merchant hasn’t previously accepted the UPS TOS for the selected origin address.

#### `label_purchase_upsdap_tos_closed`

Fires when the UPS TOS modal is closed when they try to purchase a UPS label. The UPS TOS modal is only shown if the merchant has not already accepted the UPS TOS for the selected origin address.

### `label_print_button_clicked`

Fire when the "Print label" button is clicked after a label has been purchased.

### `label_print_size_dropdown_clicked`

Fires when the chevron icon is cliked to select a different size on the "Print label" button.

### `label_print_size_dropdown_selected`

Fires when a label size is selected to print from the "Print label" dropdown.
