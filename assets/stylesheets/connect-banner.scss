$notice-bg-color-warning-: #fef8ee;
$notice-bg-color-success: #eff9f1;
$action-button-bg-color: #3858e9;
$action-button-hover-bg-color: #213fd4ff;

.wcshipping-nux__notice {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--grid-unit-15, 12px);

  @media (max-width: 1000px) {
    align-items: flex-start;
  }

  &.notice {
    padding: var(--grid-unit-15, 12px) var(--grid-unit-15, 12px) var(--grid-unit-15, 12px) 0 !important;
  }

  &.notice-warning {
    background: $notice-bg-color-warning-;
  }

  &.notice-success {
    background: $notice-bg-color-success;
  }

  &-content {
    color: var(--G<PERSON>nberg-Gray-900, #1e1e1e);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-left: var(--grid-unit-15, 12px);

    p {
      margin: 0;
      padding: 0;
      line-height: 24px;
    }

    a {
      color: var(--Upcoming-Blueberry, $action-button-bg-color);
    }
  }

  &-button {

    .button {
      background-color: $action-button-bg-color;
      color: #fff;
      text-align: center;
      display: inline-flex;
      height: 40px;
      justify-content: center;
      align-items: center;
      gap: 12px;
      border-radius: 2px;

      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;

      &:hover {
        background-color: $action-button-hover-bg-color;
      }
    }
  }
}

// Feature Banner Styles
.wcshipping-feature-banner {

  // Initially hidden to prevent FOUC
  display: none;

  align-items: flex-start;
  gap: 20px;
  padding: 24px 32px !important;
  margin-bottom: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #F0F0F0;

  // Show banner once JavaScript has loaded
  &.wcshipping-feature-banner--loaded {
    display: flex;
  }

  &__content {
    display: flex;
    flex-direction: row;
    flex: 1;
  }

  &__illustration {
    display: flex;
    align-items: flex-end;
    margin-left: auto;

    .wcshipping-feature-banner__image {
      max-width: 160px;

      @media (max-width: 1000px) {
        max-width: 100px;
      }
    }
  }

  &__text {
    flex: 1;
    min-width: 0;
    max-width: 60%;
  }

  &__label {
    margin-bottom: 16px;

    // Override WordPress components-badge default styling for feature banners
    &.components-badge {
      background-color: #EFF8F0;
      color: #345B37;
    }
  }

  &__title {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.3;
    margin: 0 0 8px 0;
    color: var(--Gutenberg-Gray-900, #1e1e1e);
  }

  &__description {
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
    color: var(--Gutenberg-Gray-800, #2c2c2c);
  }

  &__actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    flex-wrap: wrap;

    @media (max-width: 1000px) {
      margin-top: 16px;
    }
  }

  &__button {
    text-decoration: none;

    // Ensure proper spacing for multiple buttons
    &:not(:last-child) {
      margin-right: 8px;
    }
  }

  // Override WordPress notice-dismiss button styling for feature banners
  .notice-dismiss {
    background: none !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    color: #1E1E1E !important;
    cursor: pointer;
    display: block;
    float: right;
    font-size: 13px;
    font-weight: 400;
    height: 20px;
    margin: 0;
    overflow: hidden;
    padding: 0;
    position: absolute;
    right: 12px;
    text-align: center;
    top: 12px;
    width: 20px;
    z-index: 10000;

    &::before {
      background: none !important;
      border-radius: 0 !important;
      color: #787c82 !important;
      content: "\f335" !important;
      display: block;
      height: 20px;
      text-align: center;
      width: 20px;
    }

    &:hover {
      color: #d63638 !important;

      &::before {
        color: #d63638 !important;
      }
    }

    &:focus {
      color: #d63638 !important;
      box-shadow: 0 0 0 2px #2271b1 !important;
      outline: 2px solid transparent !important;

      &::before {
        color: #d63638 !important;
      }
    }
  }
}


@media (max-width: 1000px) {

  .wcshipping-nux__notice {
    flex-direction: column;

    .wcshipping-nux__notice-content {

      .wcshipping-nux__notice-content-tos {
        margin: 1.5em 0 0.5em;
        max-width: 90%;
      }

      .wcshipping-nux__notice-content-text {
        max-width: 100%;
      }

    }

    .wcshipping-nux__notice-jetpack {
      flex-direction: row;
      padding: 0.5em;

      img {
        height: 2em;
        margin: 0 1em 0 0;
        width: 2em;
      }

      .wcshipping-nux__notice-jetpack-text {
        margin: 0;
      }
    }

    &-button {
      align-self: auto;
      margin-left: var(--grid-unit-15, 12px);
      text-align: center;
    }
  }
}
