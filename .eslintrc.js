const path = require( 'path' );

module.exports = {
	root: true,
	env: {
		commonjs: true,
		node: true,
		browser: true,
		es6: true,
		jest: true,
	},
	globals: {},
	parserOptions: {
		ecmaVersion: 'latest',
		sourceType: 'module',
		parser: '@typescript-eslint/parser',
		requireConfigFile: false,
		babelOptions: {
			presets: [ '@babel/preset-react' ],
		},
		project: 'tsconfig.json',
		tsconfigRootDir: __dirname,
	},
	extends: [
		'plugin:react/recommended',
		'plugin:@woocommerce/eslint-plugin/recommended',
		'plugin:@typescript-eslint/stylistic-type-checked',
	],
	plugins: [
		'react',
		'import',
		'react-hooks',
		'@typescript-eslint',
		'jsdoc',
	],
	rules: {
		'react/react-in-jsx-scope': 'off',
		'react/jsx-uses-react': 'off',
		'jest/no-deprecated-functions': 'off',
		'@woocommerce/dependency-group': 'off',
		'@wordpress/no-unsafe-wp-apis': 'off',
		'@typescript-eslint/ban-ts-comment': 'off',
		'jsdoc/newline-after-description': 'off',
		'@typescript-eslint/no-duplicate-imports': 'off',
		'@wordpress/i18n-text-domain': [
			'error',
			{
				// override default of 'woocommerce' that comes with @woocommerce/eslint-plugin
				allowedTextDomain: 'woocommerce-shipping',
			},
		],
	},
	overrides: [
		{
			files: [
				'**/__tests__/**/*.test.js',
				'**/__tests__/**/*.test.tsx',
				'**/__tests__/**/*.test.ts',
				'**/__tests__/**/*.test.jsx',
				'webpack.config.js',
				'.eslintrc.js',
			],
			rules: {
				'import/named': 'off',
				'@typescript-eslint/no-require-imports': 'off',
			},
		},
		{
			files: [ '*.js', '*.ts', '*.tsx' ],
			rules: {
				'import/no-unresolved': [
					'error',
					{
						ignore: [
							'@woocommerce/blocks-checkout',
							'@wordpress/components/build-types/*',
						],
					},
				],
			},
		},
	],
	settings: {
		react: {
			version: 'detect', // "detect" automatically picks the version you have installed.
		},
		'import/resolver': {
			node: {},
			webpack: {
				config: path.resolve( './webpack.config.js' ),
			},
		},
	},
};
