const path = require( 'path' );
const defaultConfig = require( '@wordpress/scripts/config/webpack.config' );
const DependencyExtractionWebpackPlugin = require( '@woocommerce/dependency-extraction-webpack-plugin' );
const MiniCSSExtractPlugin = require( 'mini-css-extract-plugin' );
const isProduction = process.env.NODE_ENV === 'production';
const shouldWatchTS = process.env.npm_lifecycle_event === 'watch:ts';
const ForkTsCheckerWebpackPlugin = require( 'fork-ts-checker-webpack-plugin' );
const { BundleAnalyzerPlugin } = require( 'webpack-bundle-analyzer' );
const shouldAnalyze = process.env.ANALYZE_BUNDLES === 'true';

const dependencyMap = {
	'@wordpress/components': null,
	'@wordpress/dataviews/wp': null,
};

const handleMap = {
	'@wordpress/components': null,
	'@wordpress/dataviews/wp': null,
};

// Post-CSS plugins
const postcssPlugins = require( '@wordpress/postcss-plugins-preset' );

/**
 * Our CSS Loaders.
 */
const cssLoaders = [
	{
		loader: MiniCSSExtractPlugin.loader,
	},
	{
		loader: require.resolve( 'css-loader' ),
		options: {
			sourceMap: ! isProduction,
			modules: {
				auto: true,
			},
		},
	},
	{
		loader: require.resolve( 'postcss-loader' ),
		options: {
			postcssOptions: {
				plugins: isProduction
					? [
							...postcssPlugins,
							require( 'cssnano' )( {
								preset: [
									'default',
									{
										discardComments: {
											removeAll: true,
										},
									},
								],
							} ),
					  ]
					: postcssPlugins,
			},
		},
	},
];

const config = {
	...defaultConfig,
	entry: {
		'woocommerce-shipping-settings':
			'./client/entrypoints/shipping-settings',
		'woocommerce-shipping-onboarding':
			'./client/entrypoints/shipping-onboarding',
		'woocommerce-shipping-admin-status':
			'./client/entrypoints/plugin-status',
		'woocommerce-shipping-create-shipping-label':
			'./client/entrypoints/order-shipping-label',
		'woocommerce-shipping-shipment-tracking':
			'./client/entrypoints/shipment-tracking',
		'woocommerce-shipping-checkout-address-validation':
			'./client/entrypoints/checkout-address-validation',
		'woocommerce-shipping-analytics':
			'./client/entrypoints/shipping-analytics',
		'woocommerce-shipping-plugin': {
			import: './client/entrypoints/shipping-plugin',
			filename: 'woocommerce-shipping-plugin.js',
			library: {
				name: 'WCShipping_Plugin',
				type: 'umd',
			},
		},
	},
	resolve: {
		alias: {
			wcshipping: path.resolve( __dirname, 'client/' ),
			components: path.resolve( __dirname, 'client/components' ),
			entrypoints: path.resolve( __dirname, 'client/entrypoints' ),
			images: path.resolve( __dirname, 'images/' ),
			data: path.resolve( __dirname, 'client/data' ),
			tests: path.resolve( __dirname, 'tests/js/' ),
			utils: path.resolve( __dirname, 'client/utils' ),
			types: path.resolve( __dirname, 'client/types' ),
			'test-helpers': path.resolve( __dirname, 'client/test-helpers' ),
			context: path.resolve( __dirname, 'client/context' ),
			effects: path.resolve( __dirname, 'client/effects' ),
		},
		modules: [ 'node_modules' ],
		extensions: [ '.js', '.jsx', '.ts', '.tsx', '.json' ],
	},
	output: {
		path: path.resolve( __dirname, 'dist' ),
	},
	devtool: ! isProduction ? 'inline-source-map' : 'source-map',
	module: {
		rules: [
			{
				test: /\.(j|t)sx?$/,
				exclude: [ /node_modules/ ],
				loader: 'babel-loader',
				// Set sideEffects to true to ensure top-level side effects like polyfills,
				// global setup code, or injected runtime helpers aren't accidentally removed during tree-shaking.
				sideEffects: true,
			},
			{
				test: /\.css$/,
				use: cssLoaders,
				// Our CSS files have side effects (applying styles) and should not be tree-shaken
				sideEffects: true,
			},
			{
				test: /\.pcss$/,
				use: cssLoaders,
				// Our CSS files have side effects (applying styles) and should not be tree-shaken
				sideEffects: true,
			},
			{
				test: /\.(sc|sa)ss$/,
				use: [
					...cssLoaders,
					{
						loader: require.resolve( 'sass-loader' ),
						options: {
							sourceMap: ! isProduction,
						},
					},
				],
				// Our CSS files have side effects (applying styles) and should not be tree-shaken
				sideEffects: true,
			},
			{
				test: /\.svg$/,
				issuer: /\.(pc|sc|sa|c)ss$/,
				type: 'asset/inline',
			},
			{
				test: /\.(bmp|svg|png|jpe?g|gif|webp)$/i,
				type: 'asset/resource',
				generator: {
					filename: 'images/[name].[hash:8][ext]',
				},
			}, // Add your rules for custom modules here
			// Learn more about loaders from https://webpack.js.org/loaders/
		],
	},
	devServer: {
		client: {
			overlay: false,
			webSocketURL: {
				hostname: 'localhost',
				pathname: '/ws',
				port: 8081,
				protocol: 'ws',
			},
		},
		allowedHosts: 'all',
		port: 8081,
		headers: {
			'Access-Control-Allow-Origin': '*',
		},
		hot: process.argv.includes( '--hot' ),
		liveReload: false,
	},
	plugins: [
		...defaultConfig.plugins.filter(
			( plugin ) =>
				plugin.constructor.name !==
					'DependencyExtractionWebpackPlugin' &&
				plugin.constructor.name !== 'MiniCssExtractPlugin'
		),
		new MiniCSSExtractPlugin(),
		new DependencyExtractionWebpackPlugin( {
			injectPolyfill: true,
			requestToExternal( request ) {
				return dependencyMap[ request ];
			},
			requestToHandle( request ) {
				return handleMap[ request ];
			},
		} ),
		...( shouldWatchTS
			? [
					new ForkTsCheckerWebpackPlugin( {
						typescript: {
							configOverwrite: {
								exclude: [ 'node_modules', '**/__tests__/**' ],
							},
							memoryLimit: 2048,
						},
					} ),
			  ]
			: [] ),
		...( shouldAnalyze ? [ new BundleAnalyzerPlugin() ] : [] ),
	],
};

module.exports = () => {
	if ( isProduction ) {
		config.mode = 'production';
	} else {
		config.mode = 'development';
		config.optimization = {
			...config.optimization,
			// Enable tree shaking in development mode
			usedExports: true,
		};
	}
	return config;
};
