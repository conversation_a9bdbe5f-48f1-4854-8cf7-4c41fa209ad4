<?php

$folder     = 'classes';
$gitCommand = 'git status --porcelain ' . escapeshellarg( $folder );
$output     = array();
exec( $gitCommand, $output );

if ( ! empty( $output ) ) {
	// ANSI escape code for red color
	$redColor = "\033[31m";
	// ANSI escape code to reset color
	$resetColor = "\033[0m";

	echo $redColor . "Warning: Files in the '$folder' folder have been modified:\n" . $resetColor;
	foreach ( $output as $line ) {
		echo $redColor . $line . "\n" . $resetColor;
	}
	echo $redColor . "Please review these changes before committing.\n" . $resetColor;

	exit( 1 ); // Exit with error code to prevent commit
} else {
	echo "No changes in the '$folder' folder.\n";
}

exit( 0 ); // Exit successfully if no changes are found
