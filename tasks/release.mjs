'use strict';
import 'shelljs/global.js';
import chalk from 'chalk';
import archiver from 'archiver';
import fs from 'fs';
import shell from 'shelljs';

const { mkdir, cd, mv, error, env } = shell; // can't directly spread in the ESM import statement
const pluginSlug = process.env.npm_package_name;
const releaseFolder = 'release';
const targetFolder = 'release/' + pluginSlug;
const filesToCopy = [
	'assets',
	'classes',
	'client',
	'dist',
	'images',
	'languages',
	'src',
	'vendor',
	'changelog.txt',
	'readme.txt',
	'woocommerce-shipping.php',
];
const fileTypesToIgnore = [ '**/*.scss', '**/*.map' ];

console.log( chalk.blue( 'Building release for ' + pluginSlug ) );

// Clean dist folder and build client.
console.log( chalk.blue( 'Building the client' ) );
rm( '-rf', 'dist' );
exec( 'pnpm run build:client' );
console.log( chalk.green( 'Client built' ) );

// Start with a clean release folder.
console.log( chalk.blue( 'Starting release build' ) );
rm( '-rf', releaseFolder );
mkdir( releaseFolder );
mkdir( targetFolder );
console.log( chalk.green( 'Release folder created' ) );

if (
	! Object.hasOwn( env, 'WCSHIPPING_SKIP_SOURCEMAP_UPLOAD' ) ||
	[ '1', 'true' ].indexOf( env.WCSHIPPING_SKIP_SOURCEMAP_UPLOAD ) === -1
) {
	// Upload sourcemaps to Sentry.
	console.log( chalk.blue( 'Uploading sourcemaps' ) );
	if (
		! Object.hasOwn( env, 'WCS_SKIP_SOURCEMAPS' ) &&
		exec( 'pnpm run sentry:sourcemaps' ).code !== 0
	) {
		console.log( chalk.red( 'Error uploading sourcemaps' ) );
		console.log( chalk.red( error() ) );
		console.log(
			chalk.yellow(
				'If you need to login to Sentry run `npx sentry-cli login --auth-token SENTRY_TOKEN_HERE`'
			),
			'\n'
		);
		exit( 1 );
	}
	console.log( chalk.green( 'Sourcemaps uploaded' ) );
}

// Copy the plugin files to the release folder.
cp( '-Rf', filesToCopy, targetFolder );
console.log( chalk.green( 'Plugin files copied to release folder' ) );

// Remove files that is not needed in the release.
cd( releaseFolder );
rm( fileTypesToIgnore );
rm( '-rf', '**/__tests__' ); // @todo Remove this when we no longer include "uncompiled" code in the release.
rm( '-rf', '**/.DS_Store' ); // Remove all .DS_Store files from the release
console.log( chalk.green( 'Unnecessary files removed' ) );
cd( '..' );

const output = fs.createWriteStream(
	releaseFolder + '/' + pluginSlug + '.zip'
);
console.log( chalk.blue( 'Creating ' + pluginSlug + '.zip file' ) );
const archive = archiver( 'zip', { zlib: { level: 9 } } );

output.on( 'close', () => {
	console.log(
		chalk.green(
			'All done: The release is built and zipped in the ' +
				releaseFolder +
				' folder.'
		)
	);
	console.log( chalk.green( 'Moving the zip file to the root folder.' ) );
	mv( releaseFolder + '/' + pluginSlug + '.zip', pluginSlug + '.zip' );
	rm( '-rf', releaseFolder );
} );

archive.on( 'error', ( err ) => {
	console.error(
		chalk.red(
			'An error occured while creating the zip: ' +
				err +
				'\nYou can still probably create the zip manually from the ' +
				targetFolder +
				' folder.'
		)
	);
} );

archive.pipe( output );

archive.directory( targetFolder, pluginSlug );

archive.finalize();
