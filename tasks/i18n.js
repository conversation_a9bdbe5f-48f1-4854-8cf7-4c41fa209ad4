const { execSync } = require( 'child_process' );

const headers = {
	'Last-Translator': '',
	'Language-Team': '',
};
execSync(
	`php -d memory_limit=-1 "$(which wp)" i18n make-pot \
--domain=woocommerce-shipping \
--slug=woocommerce-shipping \
--headers='${ JSON.stringify( headers ) }' \
--exclude=node_modules,.git,**/__test__/*,tasks,tests,vendor \
. languages/woocommerce-shipping.pot \
--include="dist,src,classes" \
--merge \
`,
	{ stdio: 'inherit' }
);
