const { jsWithBabel: tsjPreset } = require('ts-jest/presets');

module.exports = {
	preset: '@wordpress/jest-preset-default',
	rootDir: '../../',
	verbose: true,
	moduleDirectories: [ 'node_modules', '<rootDir>/client' ],
	restoreMocks: true,
	transform: {
		...tsjPreset.transform,
	},
	transformIgnorePatterns: [
		'node_modules/(?!(@woocommerce|@automattic)/.+)/',
	],
	moduleNameMapper: {
		'\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
			'<rootDir>/tests/js/jest-file-mock.js',
		'^react$': '<rootDir>/node_modules/react',
		'^react-dom$': '<rootDir>/node_modules/react-dom',
		'^wcshipping(.*)$': '<rootDir>/client$1',
		'^tests(.*)$': '<rootDir>/tests/js$1',
		// Force module uuid to resolve with the CJS entry point, because Jest does not support package.json.exports. See https://github.com/uuidjs/uuid/issues/451
		uuid: require.resolve( 'uuid' ),
		'is-plain-obj': '<rootDir>/tests/js/is-plain-obj.js',
		'create-config': '<rootDir>/tests/js/calypso-create-config.js',
		'@woocommerce/blocks-checkout':
			'<rootDir>/tests/js/mocks/woocommerce-blocks-checkout.js',
		'@woocommerce/currency': require.resolve( '@woocommerce/currency' ),
	},
	setupFiles: [
		require.resolve(
			'@wordpress/jest-preset-default/scripts/setup-globals.js'
		),
	],
	setupFilesAfterEnv: [
		require.resolve(
			'@wordpress/jest-preset-default/scripts/setup-test-framework.js'
		),
		'<rootDir>/tests/js/jest-setup.js',
	],
	testMatch: [ '**/__tests__/**/*.(js|jsx|ts|tsx)' ],
	testPathIgnorePatterns: [
		'/node_modules/',
		'/vendor/',
		'/fixtures/',
		'<rootDir>/.*/build/',
		'<rootDir>/.*/build-module/',
		'<rootDir>/docker/',
		'<rootDir>/tests',
	],
	globalTeardown: '<rootDir>/tests/js/global-teardown.js',
	workerIdleMemoryLimit: '1024MB',
};
