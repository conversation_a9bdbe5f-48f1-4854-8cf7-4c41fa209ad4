/**
 * The origin config function in node_modules/.pnpm/%40automattic%2Bcalypso-config%401.2.0/node_modules/%40automattic/calypso-config/src/create-config.js
 * uses console.error if window object is defined, which is misleading in the context of component tests.
 */

const config = ( data ) => ( key ) => {
	if ( key in data ) {
		return data[ key ];
	}
};
const isEnabled = ( data ) => ( feature ) =>
	( data.features && !! data.features[ feature ] ) || false;

module.exports = ( data ) => {
	const configApi = config( data );
	configApi.isEnabled = isEnabled( data );

	return configApi;
};
