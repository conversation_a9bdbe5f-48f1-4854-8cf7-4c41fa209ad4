import { createReduxStore, register } from '@wordpress/data';
import { COUNTRIES_STORE_NAME } from '@woocommerce/data';

const countriesStore = createReduxStore( COUNTRIES_STORE_NAME, {
	selectors: {
		getCountries: () => [
			{
				code: 'US',
				name: 'United States',
			},
		],
	},
	actions: {
		setCountries: jest.fn(),
	},
	resolvers: {
		getCountries: () => [
			{
				code: 'US',
				name: 'United States',
			},
		],
	},
	reducer: jest.fn(),
} );

export const initCountriesStore = () => {
	register( countriesStore );
};
