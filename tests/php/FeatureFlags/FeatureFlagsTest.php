<?php

namespace Automattic\WCShipping\Tests\php\FeatureFlags;

use Automattic\WCShipping\FeatureFlags\FeatureFlags;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;

class FeatureFlagsTest extends WCShipping_Test_Case {

	private FeatureFlags $feature_flags;

	public function setUp(): void {
		parent::setUp();

		$this->feature_flags = new FeatureFlags();
	}

	public function test_it_decorates_request_body_with_features_supported_by_store() {
		$filter_stub = fn () => array( 'upsdap' );

		add_filter( 'wcshipping_features_supported_by_store', $filter_stub, 100 );
		$decorated_request = $this->feature_flags->decorate_api_request_body_with_feature_flags( array( 'settings' => array() ) );
		remove_filter( 'wcshipping_features_supported_by_store', $filter_stub, 100 );

		$this->assertEquals( array( 'upsdap' ), $decorated_request['settings']['features_supported_by_store'] );
	}

	public function test_it_decorates_request_body_with_feature_flags_supported_by_client_moved_to_settings_key() {
		$decorated_request = $this->feature_flags->decorate_api_request_body_with_feature_flags(
			array(
				'settings'                     => array(),
				'features_supported_by_client' => array( 'upsdap' ),
			)
		);

		$this->assertArrayNotHasKey( 'features_supported_by_client', $decorated_request );
		$this->assertEquals( array( 'upsdap' ), $decorated_request['settings']['features_supported_by_client'] );
	}
}
