<?php
namespace Automattic\WCShipping\Tests\php;

use Automattic\WCShipping\WCShippingRESTController;

class WCShippingRESTControllerTest extends WCShipping_Test_Case {
	public function test_prevent_route_caching() {
		// Test that DONOTCACHEPAGE is defined
		WCShippingRESTController::prevent_route_caching();
		$this->assertTrue( defined( 'DONOTCACHEPAGE' ) );
		$this->assertTrue( DONOTCACHEPAGE );
		// Test that the filter is added
		$this->assertEquals(
			PHP_INT_MAX,
			has_filter( 'rest_post_dispatch', array( 'Automattic\WCShipping\WCShippingRESTController', 'exclude_namespace_from_cache' ) )
		);
	}

	public function test_exclude_namespace_from_cache() {
		$result  = 'test_result';
		$server  = $this->createMock( WP_REST_Server::class );
		$request = $this->createMock( WP_REST_Request::class );

		// Test with a matching route
		$request->method( 'get_route' )->willReturn( '/wcshipping/v1/test' );
		$server->expects( $this->atLeastOnce() )->method( 'send_header' );

		$actual_result = WCShippingRESTController::exclude_namespace_from_cache( $result, $server, $request );
		$this->assertEquals( $result, $actual_result );

		// Test with a non-matching route
		$request = $this->createMock( WP_REST_Request::class );
		$request->method( 'get_route' )->willReturn( '/wp/v2/posts' );
		$server = $this->createMock( WP_REST_Server::class );
		$server->expects( $this->never() )->method( 'send_header' );

		$actual_result = WCShippingRESTController::exclude_namespace_from_cache( $result, $server, $request );
		$this->assertEquals( $result, $actual_result );
	}
}
