<?php

/**
 * A double for WCS&T's package compatibilty class.
 *
 * We need this as an actual class because if we were to build it with PHPUnit's mock builder, PHPUnit will give it
 * a fake name like `WC_Connect_Compatibility_WCShipping_Packages_foo1234`. This will not work with WP's `add_filter()`
 * which calculates `spl_object_hash()` of the passed callback which will not match the one of a legitimate
 * `WC_Connect_Compatibility_WCShipping_Packages` class (or a double of it with the same name which is the case here).
 */
class WC_Connect_Compatibility_WCShipping_Packages {
	public static function intercept_packages_read( $wc_connect_options ) {
		$wc_connect_options['packages'] = array( 'intercepted package' );

		return $wc_connect_options;
	}

	public static function intercept_predefined_packages_read( $wc_connect_options ) {
		$wc_connect_options['predefined_packages'] = array( 'intercepted predefined package' );

		return $wc_connect_options;
	}
}
