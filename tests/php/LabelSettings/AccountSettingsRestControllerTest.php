<?php

namespace Automattic\WCShipping\Tests\php\LabelSettings;

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\LabelSettings\AccountSettingsRestController;
use Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store;
use Automattic\WCShipping\Connect\WC_Connect_Payment_Methods_Store;
use Automattic\WCShipping\Connect\WC_Connect_Logger;
use Automattic\WCShipping\Connect\WC_Connect_Account_Settings;
use WP_REST_Request;
use WP_Error;

class AccountSettingsRestControllerTest extends WCShipping_Test_Case {

	/**
	 * @var WC_Connect_Service_Settings_Store|\PHPUnit\Framework\MockObject\MockObject
	 */
	private $settings_store;

	/**
	 * @var WC_Connect_Payment_Methods_Store|\PHPUnit\Framework\MockObject\MockObject
	 */
	private $payment_methods_store;

	/**
	 * @var WC_Connect_Logger|\PHPUnit\Framework\MockObject\MockObject
	 */
	private $logger;

	/**
	 * @var AccountSettingsRestController
	 */
	private $controller;

	public static function set_up_before_class() {
		parent::set_up_before_class();
		require_once __DIR__ . '/../../../classes/class-wc-connect-service-settings-store.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-payment-methods-store.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-logger.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-account-settings.php';
	}

	function setUp(): void {
		parent::setUp();

		$this->settings_store        = $this->createMock( WC_Connect_Service_Settings_Store::class );
		$this->payment_methods_store = $this->createMock( WC_Connect_Payment_Methods_Store::class );
		$this->logger                = $this->createMock( WC_Connect_Logger::class );

		$this->controller = new AccountSettingsRestController(
			$this->settings_store,
			$this->payment_methods_store,
			$this->logger
		);

		add_action( 'rest_api_init', array( $this->controller, 'register_routes' ) );
		do_action( 'rest_api_init' );
	}

	public function test_save_account_settings_preserves_enabled_when_user_cannot_manage_payment_methods() {
		// Arrange
		$old_settings = array(
			'enabled'                    => true,
			'selected_payment_method_id' => 'payment_123',
			'paper_size'                 => 'letter',
		);

		$new_settings = array(
			'paper_size' => 'a4',
		);

		$this->settings_store->method( 'can_user_manage_payment_methods' )
			->willReturn( false );

		$this->settings_store->method( 'get_account_settings' )
			->willReturn( $old_settings );

		// Expect that update_account_settings is called with preserved enabled and payment method
		$expected_settings = array(
			'paper_size'                 => 'a4',
			'selected_payment_method_id' => 'payment_123',
			'enabled'                    => true,
		);

		$this->settings_store->expects( $this->once() )
			->method( 'update_account_settings' )
			->with( $expected_settings )
			->willReturn( true );

		// Act
		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/account/settings' );
		$request->set_body( json_encode( $new_settings ) );
		$request->set_header( 'content-type', 'application/json' );

		$response = $this->controller->save_account_settings( $request );

		// Assert
		$this->assertEquals( array( 'success' => true ), $response->get_data() );
	}

	public function test_save_account_settings_preserves_payment_method_when_user_cannot_manage_payment_methods() {
		// Arrange
		$old_settings = array(
			'enabled'                    => false,
			'selected_payment_method_id' => 'payment_456',
			'paper_size'                 => 'letter',
		);

		$new_settings = array(
			'enabled'                    => true,
			'selected_payment_method_id' => 'payment_999', // This should be ignored
			'paper_size'                 => 'a4',
		);

		$this->settings_store->method( 'can_user_manage_payment_methods' )
			->willReturn( false );

		$this->settings_store->method( 'get_account_settings' )
			->willReturn( $old_settings );

		// Expect that update_account_settings is called with preserved payment method but updated enabled
		$expected_settings = array(
			'enabled'                    => true,
			'selected_payment_method_id' => 'payment_456', // Should be preserved from old settings
			'paper_size'                 => 'a4',
		);

		$this->settings_store->expects( $this->once() )
			->method( 'update_account_settings' )
			->with( $expected_settings )
			->willReturn( true );

		// Act
		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/account/settings' );
		$request->set_body( json_encode( $new_settings ) );
		$request->set_header( 'content-type', 'application/json' );

		$response = $this->controller->save_account_settings( $request );

		// Assert
		$this->assertEquals( array( 'success' => true ), $response->get_data() );
	}

	public function test_save_account_settings_allows_all_changes_when_user_can_manage_payment_methods() {
		// Arrange
		$new_settings = array(
			'enabled'                    => false,
			'selected_payment_method_id' => 'payment_new',
			'paper_size'                 => 'a4',
		);

		$this->settings_store->method( 'can_user_manage_payment_methods' )
			->willReturn( true );

		// Should not call get_account_settings when user can manage payment methods
		$this->settings_store->expects( $this->never() )
			->method( 'get_account_settings' );

		// Expect that update_account_settings is called with the exact settings provided
		$this->settings_store->expects( $this->once() )
			->method( 'update_account_settings' )
			->with( $new_settings )
			->willReturn( true );

		// Act
		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/account/settings' );
		$request->set_body( json_encode( $new_settings ) );
		$request->set_header( 'content-type', 'application/json' );

		$response = $this->controller->save_account_settings( $request );

		// Assert
		$this->assertEquals( array( 'success' => true ), $response->get_data() );
	}

	public function test_save_account_settings_handles_update_error() {
		// Arrange
		$new_settings = array( 'paper_size' => 'a4' );
		$error        = new WP_Error( 'update_failed', 'Database error', array() );

		$this->settings_store->method( 'can_user_manage_payment_methods' )
			->willReturn( true );

		$this->settings_store->method( 'update_account_settings' )
			->willReturn( $error );

		$this->logger->expects( $this->once() )
			->method( 'log' );

		// Act
		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/account/settings' );
		$request->set_body( json_encode( $new_settings ) );
		$request->set_header( 'content-type', 'application/json' );

		$response = $this->controller->save_account_settings( $request );

		// Assert
		$this->assertInstanceOf( WP_Error::class, $response );
		$this->assertEquals( 'save_failed', $response->get_error_code() );
	}

	public function test_save_account_settings_only_updates_paper_size_preserves_enabled() {
		// This test specifically covers the bug scenario described
		// Arrange
		$old_settings = array(
			'enabled'                    => true,
			'selected_payment_method_id' => 'payment_123',
			'paper_size'                 => 'letter',
		);

		$new_settings = array(
			'paper_size' => 'a4',
		);

		$this->settings_store->method( 'can_user_manage_payment_methods' )
			->willReturn( false );

		$this->settings_store->method( 'get_account_settings' )
			->willReturn( $old_settings );

		// The key assertion: enabled should remain true
		$expected_settings = array(
			'paper_size'                 => 'a4',
			'selected_payment_method_id' => 'payment_123',
			'enabled'                    => true, // This should be preserved!
		);

		$this->settings_store->expects( $this->once() )
			->method( 'update_account_settings' )
			->with( $expected_settings )
			->willReturn( true );

		// Act
		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/account/settings' );
		$request->set_body( json_encode( $new_settings ) );
		$request->set_header( 'content-type', 'application/json' );

		$response = $this->controller->save_account_settings( $request );

		// Assert
		$this->assertEquals( array( 'success' => true ), $response->get_data() );
	}
}
