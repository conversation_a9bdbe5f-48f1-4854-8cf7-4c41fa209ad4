<?php

namespace Automattic\WCShipping\Tests;

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\Integrations\ConfigRESTController;
use Automattic\WCShipping\LabelPurchase\View;
use Automattic\WCShipping\Utils;
use Exception;
use WC_Helper_Order;
use WC_Helper_Product;

class ConfigRESTControllerTest extends WCShipping_Test_Case {

	const REST_ENDPOINT = '/wcshipping/v1/config/label-purchase';

	/**
	 * @var View
	 */
	protected $view_mock;

	/**
	 * Setup the test case.
	 *
	 * @see WC_Unit_Test_Case::setUp()
	 */
	public function setUp(): void {
		parent::setUp();

		// Create a mock for the View class
		$this->view_mock = $this->createMock( View::class );

		// Initialize the controller and register the routes
		$controller = new ConfigRESTController( $this->view_mock );
		add_action(
			'rest_api_init',
			function () use ( $controller ) {
				$controller->register_routes();
			}
		);

		do_action( 'rest_api_init' );
	}

	/**
	 * Test getting configuration with a valid order ID.
	 */
	public function test_get_config_with_valid_order_id() {
		$product = \WC_Helper_Product::create_simple_product();
		$order   = \WC_Helper_Order::create_order( 1, $product );

		// Mock the get_meta_boxes_payload method
		$this->view_mock->expects( $this->once() )
			->method( 'get_meta_boxes_payload' )
			->willReturn( array( 'mocked_payload' ) );

		// Create a request with the order ID
		$request = $this->create_request( self::REST_ENDPOINT, 'GET', $order->get_id() );

		// Execute the request
		$response = rest_do_request( $request );

		// Assertions
		$this->assertEquals( 200, $response->status );
		$expected_data = array(
			'success'   => true,
			'config'    => array( 'mocked_payload' ),
			'scriptUrl' => Utils::get_enqueue_base_url() . 'woocommerce-shipping-plugin.js',
			'id'        => $order->get_id(),
		);
		$this->assertEquals( $expected_data, $response->get_data() );
	}

	/**
	 * Test getting configuration with an invalid order ID.
	 */
	public function test_get_config_with_invalid_order_id() {

		// Create a request with an invalid order ID
		$request = $this->create_request( self::REST_ENDPOINT, 'GET', 999 );

		// Execute the request
		$response = rest_do_request( $request );
		// Assertions
		$this->assertEquals( 500, $response->status );
		$this->assertArrayHasKey( 'code', $response->get_data() );
	}

	/**
	 * Test getting configuration when View throws an exception.
	 */
	public function test_get_config_with_exception_in_view() {
		// Create a mock order
		$product = WC_Helper_Product::create_simple_product();
		$order   = WC_Helper_Order::create_order( 1, $product );

		// Mock the get_meta_boxes_payload method to throw an exception
		$this->view_mock->expects( $this->once() )
			->method( 'get_meta_boxes_payload' )
			->willThrowException( new Exception( 'View exception' ) );

		// Create a request with the order ID
		$request = $this->create_request( self::REST_ENDPOINT, 'GET', $order->get_id() );

		// Execute the request
		$response = rest_do_request( $request );

		// Assertions
		$this->assertEquals( 500, $response->status );
		$this->assertArrayHasKey( 'code', $response->get_data() );
	}
}
