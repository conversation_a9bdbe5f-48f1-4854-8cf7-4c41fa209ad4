<?php

namespace Automattic\WCShipping\Tests;

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\Integrations\TosRESTController;

class TosRESTControllerTest extends WCShipping_Test_Case {

	const REST_ENDPOINT = '/wcshipping/v1/tos';

	/**
	 * Setup the test case.
	 *
	 * @see WC_Unit_Test_Case::setUp()
	 */
	public function setUp(): void {
		parent::setUp();

		// Initialize the controller and register the routes
		$controller = new TosRESTController();
		add_action(
			'rest_api_init',
			function () use ( $controller ) {
				$controller->register_routes();
			}
		);

		do_action( 'rest_api_init' );
	}

	/**
	 * Test POSTing with a valid 'accepted' value.
	 */
	public function test_post_with_valid_accepted_value() {
		// Mock the WC_Connect_Options::update_option and get_option methods
		$this->mockFunction(
			'WC_Connect_Options::update_option',
			function ( $key, $value ) {
				return true;
			}
		);

		$this->mockFunction(
			'WC_Connect_Options::get_option',
			function ( $key ) {
				return true;
			}
		);

		// Create a request with a valid 'accepted' value
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body( json_encode( array( 'accepted' => true ) ) );

		// Execute the request
		$response = rest_do_request( $request );

		// Assertions
		$this->assertEquals( 200, $response->status );
		$expected_data = array(
			'success'  => true,
			'accepted' => true,
		);
		$this->assertEquals( $expected_data, $response->get_data() );
	}

	/**
	 * Test POSTing with a missing 'accepted' value.
	 */
	public function test_post_with_missing_accepted_value() {
		// Create a request without 'accepted' value
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body( json_encode( array() ) );

		// Execute the request
		$response = rest_do_request( $request );

		// Assertions
		$this->assertEquals( 400, $response->status );
		$this->assertEquals( 'bad_request', $response->get_data()['code'] );
	}

	/**
	 * Test POSTing with 'accepted' set to false.
	 */
	public function test_post_with_accepted_set_to_false() {
		// Create a request with 'accepted' set to false
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body( json_encode( array( 'accepted' => false ) ) );

		// Execute the request
		$response = rest_do_request( $request );

		// Assertions
		$this->assertEquals( 400, $response->status );
		$this->assertEquals( 'bad_request', $response->get_data()['code'] );
	}
}
