<?php

namespace Automattic\WCShipping\Tests;

use Automattic\WCShipping\Loader;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\Integrations\AssetsRESTController;

class AssetsRESTControllerTest extends WCShipping_Test_Case {

	const REST_ENDPOINT = '/wcshipping/v1/assets';

	protected $loader_mock;

	public function setUp(): void {
		parent::setUp();

		// Pass the mock to the controller
		$controller = new AssetsRESTController();
		add_action(
			'rest_api_init',
			function () use ( $controller ) {
				$controller->register_routes();
			}
		);

		do_action( 'rest_api_init' );
	}

	public function test_get_assets_with_valid_request() {
		$request  = $this->create_request( self::REST_ENDPOINT, 'GET' );
		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->status );

		$expected_data = array(
			'success' => true,
			'assets'  => array(
				'wcshipping_create_label_script'      => Loader::get_wcs_admin_script_url(),
				'wcshipping_create_label_style'       => Loader::get_wcs_admin_style_url(),
				'wcshipping_shipment_tracking_script' => Loader::get_wcs_shipment_tracking_script_url(),
				'wcshipping_shipment_tracking_style'  => Loader::get_wcs_shipment_tracking_style_url(),
			),
		);

		$this->assertEquals( $expected_data, $response->get_data() );
	}
}
