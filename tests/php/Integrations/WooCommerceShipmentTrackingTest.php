<?php
/**
 * WooCommerceShipmentTrackingTest integration test file.
 *
 * @package Automattic\WCShipping\Integrations
 */

namespace Automattic\WCShipping\Tests;

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\Integrations\WooCommerceShipmentTracking;
use WC_Order;


/**
 * Class WooCommerceShipmentTrackingTest
 *
 * @package Automattic\WCShipping\Integrations
 */
class WooCommerceShipmentTrackingTest extends WCShipping_Test_Case {
	/**
	 * Test get_tracking_url.
	 */
	public function test_get_tracking_url() {
		$this->assertEquals( 'https://www.ups.com/track?tracknum=', WooCommerceShipmentTracking::get_tracking_url( 'ups' ) );
		$this->assertEquals( 'https://tools.usps.com/go/TrackConfirmAction?tLabels=', WooCommerceShipmentTracking::get_tracking_url( 'usps' ) );
		$this->assertEquals( 'https://www.fedex.com/apps/fedextrack/?tracknumbers=', WooCommerceShipmentTracking::get_tracking_url( 'fedex' ) );
		$this->assertEquals( 'https://www.dhl.com/en/express/tracking.html?AWB=', WooCommerceShipmentTracking::get_tracking_url( 'dhlexpress' ) );
		$this->assertEquals( '', WooCommerceShipmentTracking::get_tracking_url( 'unknown' ) );
	}

	/**
	 * Test modify_tracking_data_stored_erenously.
	 */
	public function test_modify_tracking_data_stored_erenously() {
		$order = $this->createMock( WC_Order::class );
		$order->method( 'get_meta' )->willReturn(
			array(
				array(
					'tracking'   => '123456789',
					'carrier_id' => 'usps',
				),
			)
		);

		$value = array(
			array(
				'tracking_number'      => '123456789',
				'custom_tracking_link' => '123456789',
			),
		);

		$expected = array(
			array(
				'tracking_number'      => '123456789',
				'custom_tracking_link' => 'https://tools.usps.com/go/TrackConfirmAction?tLabels=123456789',
			),
		);

		$result = WooCommerceShipmentTracking::modify_tracking_data_stored_erenously( $value, $order );

		$this->assertEquals( $expected, $result );
	}
}
