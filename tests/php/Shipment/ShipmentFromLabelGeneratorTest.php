<?php

use PHPUnit\Framework\TestCase;
use Automattic\WCShipping\Shipment\ShipmentFromLabelGenerator;

/**
 * Dummy order item to simulate a WC_Order item.
 * Implements ArrayAccess to be accessed as an array.
 */
class DummyOrderItem implements ArrayAccess {
	/**
	 * The item data.
	 *
	 * @var array
	 */
	private $data;

	/**
	 * Constructor.
	 *
	 * @param array $data Item data.
	 */
	public function __construct( array $data ) {
		$this->data = $data;
	}

	/**
	 * Get the item data.
	 *
	 * @return array
	 */
	public function get_data() {
		return $this->data;
	}

	/**
	 * Whether an offset exists.
	 *
	 * @param mixed $offset The offset to check for.
	 * @return bool True on success or false on failure.
	 */
	public function offsetExists( $offset ) {
		return isset( $this->data[ $offset ] );
	}

	/**
	 * Offset to retrieve.
	 *
	 * @param mixed $offset The offset to retrieve.
	 * @return mixed Can return all value types.
	 */
	public function offsetGet( $offset ) {
		return isset( $this->data[ $offset ] ) ? $this->data[ $offset ] : null;
	}

	/**
	 * Assign a value to the specified offset.
	 *
	 * @param mixed $offset The offset to assign the value to.
	 * @param mixed $value  The value to set.
	 * @return void
	 */
	public function offsetSet( $offset, $value ) {
		if ( is_null( $offset ) ) {
			$this->data[] = $value;
		} else {
			$this->data[ $offset ] = $value;
		}
	}

	/**
	 * Unset an offset.
	 *
	 * @param mixed $offset The offset to unset.
	 * @return void
	 */
	public function offsetUnset( $offset ) {
		unset( $this->data[ $offset ] );
	}
}

/**
 * Testable ShipmentFromLabelGenerator that extends ShipmentFromLabelGenerator.
 *
 * This class is used to bypass WC_Order type-hint requirements.
 */
class TestableShipmentFromLabelGenerator extends ShipmentFromLabelGenerator {
	/**
	 * Constructor.
	 *
	 * @param mixed $order A dummy order object.
	 */
	public function __construct( $order ) {
		parent::__construct( $order );
	}
}

/**
 * Tests for the ShipmentFromLabelGenerator class.
 */
class ShipmentFromLabelGeneratorTest extends TestCase {

	/**
	 * Test that a label with valid mappings creates a shipment with the correct quantities and subItems.
	 */
	public function test_generate_shipments_with_valid_mapping() {
		// Create a dummy order item with product_id 101.
		$itemData  = array(
			'id'         => 'item101',
			'product_id' => 101,
			'name'       => 'Product 101',
		);
		$dummyItem = new DummyOrderItem( $itemData );

		// Create a dummy order object mock.
		$orderMock = $this->getMockBuilder( 'WC_Order' )
			->disableOriginalConstructor()
			->getMock();
		$orderMock->method( 'get_items' )->willReturn( array( $dummyItem ) );

		// Create a testable shipment generator.
		$generator = new TestableShipmentFromLabelGenerator( $orderMock );

		// Create a label that includes two occurrences of product 101.
		$labels = array(
			array(
				'product_ids' => array( 101, 101 ),
			),
		);

		$result = $generator->generate_shipments( $labels );

		// Check the structure of the result.
		$this->assertArrayHasKey( 'shipments', $result, 'Result should have a shipments key.' );
		$this->assertArrayHasKey( 'autogenerated_from_labels', $result, 'Result should have a autogenerated_from_labels key.' );
		$this->assertEmpty( $result['autogenerated_from_labels'], 'Valid mapping should not need fallback.' );

		$shipments = $result['shipments'];
		// We expect one shipment.
		$this->assertCount( 1, $shipments, 'Expected one shipment array.' );

		$shipmentItems = $shipments[0];
		// Expect one base item.
		$this->assertCount( 1, $shipmentItems, 'Shipment should contain one base item.' );

		$baseItem = $shipmentItems[0];
		$this->assertEquals( 101, $baseItem['product_id'], 'Base item product_id should be 101.' );
		$this->assertEquals( 2, $baseItem['quantity'], 'Base item quantity should equal two occurrences.' );

		// Verify subItems are added.
		$this->assertArrayHasKey( 'subItems', $baseItem, 'Base item should have a subItems key.' );
		$this->assertCount( 1, $baseItem['subItems'], 'There should be one subItem for the duplicate.' );

		$subItem = $baseItem['subItems'][0];
		$this->assertEquals( $baseItem['id'], $subItem['parentId'], 'The subItem should reference the base item id.' );
		$this->assertEquals( 1, $subItem['quantity'], 'SubItem quantity should be 1.' );
	}

	/**
	 * Test that if a label contains product_ids that are not present in the order,
	 * the fallback to return all order items is used.
	 */
	public function test_generate_shipments_with_missing_mapping() {
		// Create a dummy order item with product_id 101.
		$itemData  = array(
			'id'         => 'item101',
			'product_id' => 101,
			'name'       => 'Product 101',
		);
		$dummyItem = new DummyOrderItem( $itemData );

		// Create a dummy order object mock.
		$orderMock = $this->getMockBuilder( 'WC_Order' )
			->disableOriginalConstructor()
			->getMock();
		$orderMock->method( 'get_items' )->willReturn( array( $dummyItem ) );

		// Create a testable shipment generator.
		$generator = new TestableShipmentFromLabelGenerator( $orderMock );

		// Create a label with a valid product_id and an invalid one.
		$labels = array(
			array(
				'product_ids' => array( 101, 202 ),
			),
		);

		$result = $generator->generate_shipments( $labels );

		// Check the structure of the result.
		$this->assertArrayHasKey( 'shipments', $result, 'Result should have a shipments key.' );
		$this->assertArrayHasKey( 'autogenerated_from_labels', $result, 'Result should have a autogenerated_from_labels key.' );
		$this->assertEquals( array( 0 ), $result['autogenerated_from_labels'], 'Missing mapping should require fallback.' );

		$shipments = $result['shipments'];
		// The fallback should return all order items.
		$this->assertCount( 1, $shipments, 'Expected one shipment array (fallback).' );

		$shipmentItems = $shipments[0];
		$this->assertCount( 1, $shipmentItems, 'Fallback should include one order item.' );
		$this->assertEquals( 101, $shipmentItems[0]['product_id'], 'Fallback item should be product 101.' );
	}

	/**
	 * Test that an empty label (with an empty product_ids array) returns fallback shipment items.
	 */
	public function test_generate_shipments_with_empty_label() {
		// Create a dummy order item with product_id 101.
		$itemData  = array(
			'id'         => 'item101',
			'product_id' => 101,
			'name'       => 'Product 101',
		);
		$dummyItem = new DummyOrderItem( $itemData );

		// Create a dummy order object mock.
		$orderMock = $this->getMockBuilder( 'WC_Order' )
			->disableOriginalConstructor()
			->getMock();
		$orderMock->method( 'get_items' )->willReturn( array( $dummyItem ) );

		// Create a testable shipment generator.
		$generator = new TestableShipmentFromLabelGenerator( $orderMock );

		// Create a label with an empty product_ids array.
		$labels = array(
			array(
				'product_ids' => array(),
			),
		);

		$result = $generator->generate_shipments( $labels );

		// Check the structure of the result.
		$this->assertArrayHasKey( 'shipments', $result, 'Result should have a shipments key.' );
		$this->assertArrayHasKey( 'autogenerated_from_labels', $result, 'Result should have a autogenerated_from_labels key.' );
		$this->assertEquals( array( 0 ), $result['autogenerated_from_labels'], 'Empty product_ids should require fallback.' );

		$shipments = $result['shipments'];
		// Expect fallback to all order items.
		$this->assertCount( 1, $shipments, 'Expected one shipment array (fallback).' );

		$shipmentItems = $shipments[0];
		$this->assertCount( 1, $shipmentItems, 'Fallback should include one order item.' );
		$this->assertEquals( 101, $shipmentItems[0]['product_id'], 'Fallback item should be product 101.' );
	}

	/**
	 * Test that null labels return an empty shipment array.
	 */
	public function test_generate_shipments_with_null_labels() {
		// Create a dummy order object mock with no items.
		$orderMock = $this->getMockBuilder( 'WC_Order' )
			->disableOriginalConstructor()
			->getMock();
		$orderMock->method( 'get_items' )->willReturn( array() );

		// Create a testable shipment generator.
		$generator = new TestableShipmentFromLabelGenerator( $orderMock );

		$result = $generator->generate_shipments( null );

		// Check the structure of the result.
		$this->assertArrayHasKey( 'shipments', $result, 'Result should have a shipments key.' );
		$this->assertArrayHasKey( 'autogenerated_from_labels', $result, 'Result should have a autogenerated_from_labels key.' );
		$this->assertEmpty( $result['shipments'], 'Null labels should return empty shipments array.' );
		$this->assertEmpty( $result['autogenerated_from_labels'], 'Null labels should not need fallback.' );
	}
}
