<?php

namespace Automattic\WCShipping\Tests\php\Shipment;

use WC_Unit_Test_Case;
use Automattic\WCShipping\Shipment\Address;
use WP_Error;

class AddressTest extends WC_Unit_Test_Case {
	const ADDRESS_LINE           = '1600 AMPHITHEATRE PKWY';
	const ADDRESS_LINE_1         = '1600 AMPHITHEATRE PKWY';
	const ADDRESS_LINE_2         = '';
	const CITY                   = 'San Francisco';
	const STATE_CODE             = 'CA';
	const COUNTRY_CODE           = 'US';
	const POSTCODE               = '94043';
	const LONG_POSTCODE          = '94043-1351';
	const ADDRESS_LINE_1_UNICODE = 'Automattic ☺☺☺☺ ¡™£¢∞§¶¶•ª';

	public function set_up() {
	}

	public function test_validate() {
		$address = new Address(
			array(
				'address'   => self::ADDRESS_LINE,
				'address_1' => self::ADDRESS_LINE_1,
				'address_2' => self::ADDRESS_LINE_2,
				'city'      => self::CITY,
				'state'     => self::STATE_CODE,
				'country'   => self::COUNTRY_CODE,
				'postcode'  => self::POSTCODE,
			)
		);

		$this->assertTrue( $address->validate() );
	}

	/**
	 * Test the scenario where a state is required but it is empty. This should return WP_Error as invalid.
	 */
	public function test_validate_state_empty_but_required() {
		$address = new Address(
			array(
				'address'   => self::ADDRESS_LINE,
				'address_1' => self::ADDRESS_LINE_1,
				'address_2' => self::ADDRESS_LINE_2,
				'city'      => self::CITY,
				'state'     => '',
				'country'   => self::COUNTRY_CODE,
				'postcode'  => self::POSTCODE,
			)
		);
		$actual  = $address->validate_state();
		$this->assertTrue( is_a( $actual, WP_Error::class ) );
		$this->assertEquals( 'Missing state code.', $actual->get_error_message() );
	}

	/**
	 * Test the scenario where state is not required but someone provided some value.
	 * The validation should skip over it.
	 * BG (https://github.com/woocommerce/woocommerce/blob/f24af0e197495c0c5a1e1d4b6293d69bd377117a/plugins/woocommerce/i18n/states.php#L160) has a list of states
	 * but not required https://github.com/woocommerce/woocommerce/blob/f24af0e197495c0c5a1e1d4b6293d69bd377117a/plugins/woocommerce/includes/class-wc-countries.php#L933.
	 *
	 */
	public function test_validate_state_not_required() {
		$address = new Address(
			array(
				'address'   => self::ADDRESS_LINE,
				'address_1' => self::ADDRESS_LINE_1,
				'address_2' => self::ADDRESS_LINE_2,
				'city'      => self::CITY,
				'state'     => 'MNABUQYWGEJAGUYQSBDYQWUGE',
				'country'   => 'BG',
				'postcode'  => self::POSTCODE,
			)
		);
		$this->assertTrue( $address->validate_state() );
	}

	public function test_validate_state() {
		$address = new Address(
			array(
				'address'   => self::ADDRESS_LINE,
				'address_1' => self::ADDRESS_LINE_1,
				'address_2' => self::ADDRESS_LINE_2,
				'city'      => self::CITY,
				'state'     => self::STATE_CODE,
				'country'   => self::COUNTRY_CODE,
				'postcode'  => self::POSTCODE,
			)
		);

		$this->assertTrue( $address->validate_state() );
	}

	public function test_validate_postcode() {
		$address = new Address(
			array(
				'address'   => self::ADDRESS_LINE,
				'address_1' => self::ADDRESS_LINE_1,
				'address_2' => self::ADDRESS_LINE_2,
				'city'      => self::CITY,
				'state'     => self::STATE_CODE,
				'country'   => self::COUNTRY_CODE,
				'postcode'  => self::POSTCODE,
			)
		);
		$this->assertTrue( $address->validate_postcode() );
	}

	public function test_validate_long_postcode() {
		$address = new Address(
			array(
				'address'   => self::ADDRESS_LINE,
				'address_1' => self::ADDRESS_LINE_1,
				'address_2' => self::ADDRESS_LINE_2,
				'city'      => self::CITY,
				'state'     => self::STATE_CODE,
				'country'   => self::COUNTRY_CODE,
				'postcode'  => self::LONG_POSTCODE,
			)
		);
		$this->assertTrue( $address->validate_postcode() );
	}

	// Should return WP_Error when provided a Canadian postal code for a US address.
	public function test_validate_postcode_with_another_country_postal() {
		$address = new Address(
			array(
				'address'   => self::ADDRESS_LINE,
				'address_1' => self::ADDRESS_LINE_1,
				'address_2' => self::ADDRESS_LINE_2,
				'city'      => self::CITY,
				'state'     => self::STATE_CODE,
				'country'   => self::COUNTRY_CODE,
				'postcode'  => 'M1M M1M',
			)
		);
		$actual  = $address->validate_postcode();
		$this->assertTrue( is_a( $actual, WP_Error::class ) );
		$this->assertEquals( 'The provided postal code / ZIP is not valid', $actual->get_error_message() );
	}

	public function test_validate_country_code() {
		$address = new Address(
			array(
				'address'   => self::ADDRESS_LINE,
				'address_1' => self::ADDRESS_LINE_1,
				'address_2' => self::ADDRESS_LINE_2,
				'city'      => self::CITY,
				'state'     => self::STATE_CODE,
				'country'   => self::COUNTRY_CODE,
				'postcode'  => self::POSTCODE,
			)
		);
		$this->assertTrue( $address->validate_country() );
	}

	public function test_validate_country_code_not_2_letters() {
		$address = new Address(
			array(
				'address'   => self::ADDRESS_LINE,
				'address_1' => self::ADDRESS_LINE_1,
				'address_2' => self::ADDRESS_LINE_2,
				'city'      => self::CITY,
				'state'     => self::STATE_CODE,
				'country'   => 'United States',
				'postcode'  => self::POSTCODE,
			)
		);
		$actual  = $address->validate_country();
		$this->assertTrue( is_a( $actual, WP_Error::class ) );
		$this->assertEquals( 'Invalid country code provided. Country code must be 2 characters.', $actual->get_error_message() );
	}

	public function test_validate_country_code_not_found() {
		$address = new Address(
			array(
				'address'   => self::ADDRESS_LINE,
				'address_1' => self::ADDRESS_LINE_1,
				'address_2' => self::ADDRESS_LINE_2,
				'city'      => self::CITY,
				'state'     => self::STATE_CODE,
				'country'   => 'ZZ',
				'postcode'  => self::POSTCODE,
			)
		);
		$actual  = $address->validate_country();
		$this->assertTrue( is_a( $actual, WP_Error::class ) );
		$this->assertStringContainsString( 'Invalid country code provided. Must be one of: ', $actual->get_error_message() );
	}

	public function is_state_required_for_country_code_data_provider() {
		return array(
			array( null, true, 'The default of wc()->get_default_address_fields() is always true.' ),
			array( 'US', true ),
			array( 'HK', true, "State does not have a 'required' property. if not specified, Woo defaulted it to be true." ),
			array( 'BE', false, 'State is not required for BE' ),
			array( 'ZW', true, 'ZW does not have a state property. It should fall back to default so state is required.' ),
		);
	}

	/**
	 * @dataProvider is_state_required_for_country_code_data_provider
	 */
	public function test_is_state_required_for_country_code( $country_code, $expected ) {
		$address = new Address(
			array(
				'address'   => self::ADDRESS_LINE,
				'address_1' => self::ADDRESS_LINE_1,
				'address_2' => self::ADDRESS_LINE_2,
				'city'      => self::CITY,
				'state'     => self::STATE_CODE,
				'country'   => $country_code,
				'postcode'  => self::POSTCODE,
			)
		);
		$this->assertSame( $expected, $address->is_state_required_for_country_code() );
	}

	public function test_sanitize_address() {
		$address = new Address(
			array(
				'address'   => '<a href="www.google.com">Click this, it is Woo</a>',
				'address_1' => '<a href="www.google.com">Click this, it is Woo</a>',
				'address_2' => self::ADDRESS_LINE_2,
				'city'      => self::CITY,
				'state'     => self::STATE_CODE,
				'country'   => self::COUNTRY_CODE,
				'postcode'  => self::POSTCODE,
			)
		);
		$actual  = $address->sanitize_address( $address->address_1 );
		$this->assertEquals( 'Click this, it is Woo', $actual );
	}

	public function test_sanitize_state_code() {
		$address = new Address(
			array(
				'address'   => self::ADDRESS_LINE,
				'address_1' => self::ADDRESS_LINE_1,
				'address_2' => self::ADDRESS_LINE_2,
				'state'     => '\\\n\n\t\r"<>US',
				'country'   => self::COUNTRY_CODE,
				'postcode'  => self::POSTCODE,
			)
		);

		$actual = $address->sanitize_state_code( $address->state_code );
		$this->assertEquals( 'NNTRUS', $actual );
	}

	public function test_sanitize_coutry_code() {
		$address = new Address(
			array(
				'address'   => self::ADDRESS_LINE,
				'address_1' => self::ADDRESS_LINE_1,
				'address_2' => self::ADDRESS_LINE_2,
				'state'     => self::STATE_CODE,
				'country'   => '\\\n\n\t\r"<>US',
				'postcode'  => self::POSTCODE,
			)
		);
		$actual  = $address->sanitize_country_code( $address->country_code );
		$this->assertEquals( 'NNTRUS', $actual );
	}

	public function test_sanitize_post_code() {
		$address = new Address(
			array(
				'address'   => self::ADDRESS_LINE,
				'address_1' => self::ADDRESS_LINE_1,
				'address_2' => self::ADDRESS_LINE_2,
				'state'     => self::STATE_CODE,
				'country'   => self::COUNTRY_CODE,
				'postcode'  => '<a href="">1234567890</a>',
			)
		);
		$actual  = $address->sanitize_postcode( $address->postcode );
		$this->assertEquals( '12345-67890', $actual );
	}

	public function test_sanitize_on_initialization() {
		$address = new Address(
			array(
				'address'   => '<a href="www.google.com">Click this, it is Woo</a>',
				'address_1' => '<a href="www.google.com">Click this, it is Woo</a>',
				'address_2' => '<a href="www.google.com">Click this, it is Woo</a>',
				'city'      => '<a href="www.google.com">Click this, it is Woo</a>',
				'state'     => '<a href="www.google.com">Click this, it is Woo</a>',
				'country'   => '<a href="www.google.com">US</a>',
				'postcode'  => '<a href="">1234567890</a>',
			)
		);

		$this->assertEquals( 'Click this, it is Woo', $address->address_1 );
		$this->assertEquals( 'Click this, it is Woo', $address->address_2 );
		$this->assertEquals( 'Click this, it is Woo', $address->city );
		$this->assertEquals( 'CLICK THIS IT IS WOO', $address->state_code );
		$this->assertEquals( 'US', $address->country_code );
		$this->assertEquals( '12345-67890', $address->postcode );
	}

	public function test_validate_runs_all_expected_checks() {
		$address = $this->getMockBuilder( Address::class )
			->disableOriginalConstructor()
			->setMethods( array( 'validate_city', 'validate_country', 'validate_state', 'validate_postcode' ) )
			->getMock();

		$address->expects( $this->once() )->method( 'validate_city' );
		$address->expects( $this->once() )->method( 'validate_country' );
		$address->expects( $this->once() )->method( 'validate_state' );
		$address->expects( $this->once() )->method( 'validate_postcode' );

		$address->validate();
	}

	public function test_returns_wp_error_causing_validation_to_fail() {
		$address = $this->getMockBuilder( Address::class )
			->disableOriginalConstructor()
			->setMethods( array( 'validate_city', 'validate_country', 'validate_state', 'validate_postcode' ) )
			->getMock();

		$address->expects( $this->once() )->method( 'validate_city' );
		$address->expects( $this->once() )->method( 'validate_country' );
		$address->expects( $this->once() )->method( 'validate_state' );
		$address->expects( $this->once() )->method( 'validate_postcode' )->willReturn( new WP_Error( 'fake_error' ) );

		$result = $address->validate();
		$this->assertEquals( 'fake_error', $result->get_error_code() );
	}
}
