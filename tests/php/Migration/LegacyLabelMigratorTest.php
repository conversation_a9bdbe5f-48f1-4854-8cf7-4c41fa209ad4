<?php

namespace Automattic\WCShipping\Tests\php\Migration;

require_once WCSHIPPING_PLUGIN_DIR . '/classes/class-wc-connect-service-settings-store.php';

use Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store;
use Automattic\WCShipping\Migration\LegacyLabelMigrator;
use Automattic\WCShipping\Shipments\ShipmentsService;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WooCommerce\Internal\BatchProcessing\BatchProcessingController;
use Automattic\WooCommerce\RestApi\UnitTests\Helpers\OrderHelper;

use WC_Order;
use WC_Product;
class LegacyLabelMigratorTest extends WCShipping_Test_Case {

	/**
	 * @var LegacyLabelMigrator $migrator
	 */
	private $migrator;
	/**
	 * @var $controller BatchProcessingController
	 */
	private $controller;

	private $settings_store_mock;

	public function setUp(): void {
		parent::setUp();
		$this->settings_store_mock = $this->getMockBuilder( WC_Connect_Service_Settings_Store::class )
			->disableOriginalConstructor()
			->onlyMethods( array( 'get_label_order_meta_data' ) )
			->getMock();

		$migrator         = $this->migrator = new LegacyLabelMigrator( $this->settings_store_mock );
		$this->controller = wc_get_container()->get( BatchProcessingController::class );

		add_filter(
			'woocommerce_get_batch_processor',
			function ( $processor, $processor_class_name ) use ( $migrator ) {
				if ( $processor_class_name === 'Automattic\WCShipping\Migration\LegacyLabelMigrator' ) {
					return $migrator;
				}

				return $processor;
			},
			10,
			2
		);
	}

	/**
	 * Tests that the batch processor is correctly enqueued and removed when the option is updated.
	 */
	public function test_batch_process_enqueing() {

		$this->controller->enqueue_processor( LegacyLabelMigrator::class );
		$this->assertTrue( $this->controller->is_enqueued( get_class( $this->migrator ) ) );

		$this->controller->remove_processor( LegacyLabelMigrator::class );
		$this->assertFalse( $this->controller->is_enqueued( get_class( $this->migrator ) ) );
	}

	/**
	 * Tests that the cleanup process works correctly as a batch processor.
	 */
	public function test_batch_processor_interface_hpos() {
		if ( defined( 'WC_VERSION' ) && version_compare( WC_VERSION, '9.0', '<' ) ) {
			$this->markTestSkipped( 'This test is skipped for WooCommerce versions below 9.0' );
		}

		// Running test with COT enabled
		OrderHelper::toggle_cot_feature_and_usage( true );
		list( $order_ids, $legacy_labels ) = $this->create_test_orders( 50, 10, 20 );
		$this->settings_store_mock
			->expects( $this->any() )
			->method( 'get_label_order_meta_data' )
			->willReturn( $legacy_labels );

		$this->controller->enqueue_processor( LegacyLabelMigrator::class );
		$this->migrator->start();

		$this->assertEquals( 48, $this->migrator->get_total_pending_count() );

		// Process one small batch.
		$batch_ids = $this->migrator->get_next_batch_to_process( 2 );
		$this->assertEquals( count( $batch_ids ), 2 );
		$this->migrator->process_batch( $batch_ids );
		$order = wc_get_order( $batch_ids[0] );

		$this->assertNotEmpty( $order->get_meta( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, true ) );
		$this->assertEquals( 46, $this->migrator->get_total_pending_count() );

		do_action( $this->controller::PROCESS_SINGLE_BATCH_ACTION_NAME, LegacyLabelMigrator::class ); // phpcs:ignore WooCommerce.Commenting.CommentHooks.HookCommentWrongStyle -- this is a test
		$this->assertEquals( 0, $this->migrator->get_total_pending_count() );
		$this->migrator->stop();
		$this->assertFalse( $this->controller->is_enqueued( get_class( $this->migrator ) ) );
	}

	/**
	 * Tests that the cleanup process works correctly as a batch processor.
	 */
	public function test_batch_processor_interface_cpt() {
		if ( defined( 'WC_VERSION' ) && version_compare( WC_VERSION, '9.0', '<' ) ) {
			$this->markTestSkipped( 'This test is skipped for WooCommerce versions below 9.0' );
		}

		// Running test without COT enabled
		OrderHelper::toggle_cot_feature_and_usage( false );
		list( $order_ids, $legacy_labels ) = $this->create_test_orders( 50, 10, 20 );
		$order                             = wc_get_order( $order_ids['with_legacy_label'][0] );
		$legacy_label_data                 = $order->get_meta( LegacyLabelMigrator::LEGACY_LABEL_META_KEY );

		$this->settings_store_mock->expects( $this->any() )->method( 'get_label_order_meta_data' )
			->willReturn( maybe_unserialize( $legacy_label_data ) );
		$this->controller->enqueue_processor( LegacyLabelMigrator::class );

		$this->assertEquals( 48, $this->migrator->get_total_pending_count() );

		// Process one small batch.
		$batch_ids = $this->migrator->get_next_batch_to_process( 2 );
		$this->assertEquals( count( $batch_ids ), 2 );
		$this->migrator->process_batch( $batch_ids );

		$this->assertEquals( 46, $this->migrator->get_total_pending_count() );

		do_action( $this->controller::PROCESS_SINGLE_BATCH_ACTION_NAME, LegacyLabelMigrator::class ); // phpcs:ignore WooCommerce.Commenting.CommentHooks.HookCommentWrongStyle -- this is a test
		$this->assertEquals( 0, $this->migrator->get_total_pending_count() );
		$this->migrator->stop();
		$this->assertFalse( $this->controller->is_enqueued( get_class( $this->migrator ) ) );
	}

	public function test_label_data_is_converted_and_copied_when_labels_exist() {
		$order = $this->createMock( WC_Order::class );
		$order->method( 'get_id' )->willReturn( 1 );
		$order->method( 'get_items' )->willReturn( array() );

		$this->settings_store_mock->method( 'get_label_order_meta_data' )->willReturn(
			array(
				array(
					'label_id'    => 1,
					'label_name'  => 'Label 1',
					'product_ids' => array(),
				),
				array(
					'label_id'    => 2,
					'label_name'  => 'Label 2',
					'product_ids' => array(),
				),
			)
		);

		$order->expects( $this->once() )->method( 'add_meta_data' );
		$order->expects( $this->exactly( 2 ) )->method( 'update_meta_data' )
			->withConsecutive(
				array( ShipmentsService::META_KEY, $this->anything() ),
				array( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, $this->anything() )
			);

		$this->migrator->convert_item_and_copy_label_data( $order );
	}

	public function test_destination_normalized_is_copied_when_it_exists() {
		$order = new WC_Order();
		$p1    = new \WC_Product();
		$p1->set_name( 'P1' );
		$p1->save();

		$p2 = new \WC_Product();
		$p2->set_name( 'P2' );
		$p2->save();

		$product_id_to_item_id_map = array();
		$legacy_destination        = 'legacy_destination';
		$legacy_labels             = array(
			array(
				'label_id'    => 1,
				'label_name'  => 'Label 1',
				'product_ids' => array(
					$p1->get_id(),
					$p2->get_id(),
				),
				'status'      => 'PURCHASED',
			),
			array(
				'label_id'    => 2,
				'label_name'  => 'Label 2',
				'product_ids' => array(
					$p1->get_id(),
					$p1->get_id(),
					$p1->get_id(),
				),
				'status'      => 'PURCHASED',
			),
		);
		$converted_labels          = array(
			array(
				'id'          => 0,
				'label_id'    => 1,
				'label_name'  => 'Label 1',
				'product_ids' => array(
					$p1->get_id(),
					$p2->get_id(),
				),
				'status'      => 'PURCHASED',
				'is_legacy'   => true,
			),
			array(
				'id'          => 1,
				'label_id'    => 2,
				'label_name'  => 'Label 2',
				'product_ids' => array(
					$p1->get_id(),
					$p1->get_id(),
					$p1->get_id(),
				),
				'status'      => 'PURCHASED',
				'is_legacy'   => true,
			),
		);
		$order->add_meta_data( LegacyLabelMigrator::DESTINATION_NORMALIZED['legacy'], $legacy_destination, true );
		$order->add_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels, true );
		$product_id_to_item_id_map[ $p1->get_id() ] = $order->add_product( $p1 );
		$product_id_to_item_id_map[ $p2->get_id() ] = $order->add_product( $p2 );
		$order->save();

		$this->settings_store_mock->method( 'get_label_order_meta_data' )->willReturn(
			$legacy_labels
		);

		$this->migrator->convert_item_and_copy_label_data( $order );
		$this->assertEquals( $legacy_destination, $order->get_meta( LegacyLabelMigrator::DESTINATION_NORMALIZED['wcshipping'], true ) );
		$this->assertEquals( $converted_labels, $order->get_meta( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, true ) );

		$expected_shipments = array(
			array(
				array(
					'id'       => $product_id_to_item_id_map[ $p1->get_id() ],
					'subItems' => array(),
				),
				array(
					'id'       => $product_id_to_item_id_map[ $p2->get_id() ],
					'subItems' => array(),
				),
			),
			array(
				array(
					'id'       => $product_id_to_item_id_map[ $p1->get_id() ],
					'subItems' => array(
						sprintf( '%s-sub-0', $product_id_to_item_id_map[ $p1->get_id() ] ),
						sprintf( '%s-sub-1', $product_id_to_item_id_map[ $p1->get_id() ] ),
						sprintf( '%s-sub-2', $product_id_to_item_id_map[ $p1->get_id() ] ),
					),
				),

			),
		);
		$this->assertEquals( $expected_shipments, $order->get_meta( ShipmentsService::META_KEY, true ) );
	}

	public function test_missing_product_ids_on_old_labels_doesnt_throw() {

		// Running test without COT enabled
		OrderHelper::toggle_cot_feature_and_usage( false );
		list( $order_ids ) = $this->create_test_orders( 50, 10, 20 );
		$order             = wc_get_order( $order_ids['with_legacy_label'][0] );
		$legacy_label_data = $order->get_meta( LegacyLabelMigrator::LEGACY_LABEL_META_KEY );
		foreach ( $legacy_label_data as $index => $label ) {
			unset( $legacy_label_data[ $index ]['product_ids'] );
		}
		$this->settings_store_mock->expects( $this->any() )->method( 'get_label_order_meta_data' )
			->willReturn( maybe_unserialize( $legacy_label_data ) );
		$this->controller->enqueue_processor( LegacyLabelMigrator::class );

		$this->assertEquals( 48, $this->migrator->get_total_pending_count() );

		// Process one small batch.
		$batch_ids = $this->migrator->get_next_batch_to_process( 2 );
		$this->assertEquals( count( $batch_ids ), 2 );
		$this->migrator->process_batch( $batch_ids );

		$this->assertEquals( 46, $this->migrator->get_total_pending_count() );

		do_action( $this->controller::PROCESS_SINGLE_BATCH_ACTION_NAME, LegacyLabelMigrator::class ); // phpcs:ignore WooCommerce.Commenting.CommentHooks.HookCommentWrongStyle -- this is a test
		$this->assertEquals( 0, $this->migrator->get_total_pending_count() );
		$this->migrator->stop();
		$this->assertFalse( $this->controller->is_enqueued( get_class( $this->migrator ) ) );
	}

	private function get_legacy_labels_data() {
		return unserialize( 'a:9:{i:0;a:19:{s:8:"label_id";i:3098;s:8:"tracking";s:22:"9405500106025016567233";s:17:"refundable_amount";d:33.37;s:7:"created";i:1707729818320;s:10:"carrier_id";s:4:"usps";s:12:"service_name";s:20:"USPS - Priority Mail";s:6:"status";s:9:"PURCHASED";s:22:"commercial_invoice_url";s:0:"";s:46:"is_commercial_invoice_submitted_electronically";b:0;s:12:"package_name";s:25:"Priority Mail Medium Tube";s:9:"is_letter";b:0;s:13:"product_names";a:8:{i:0;s:8:"A nice P";i:1;s:5:"Shirt";i:2;s:5:"Shirt";i:3;s:6:"Jacket";i:4;s:6:"Jacket";i:5;s:6:"Jacket";i:6;s:6:"Jacket";i:7;s:3:"Hat";}s:11:"product_ids";a:8:{i:0;i:46;i:1;i:209;i:2;i:209;i:3;i:217;i:4;i:217;i:5;i:217;i:6;i:222;i:7;i:225;}s:15:"receipt_item_id";i:24940542;s:12:"created_date";i:1707729821000;s:11:"expiry_date";i:1723281821000;s:15:"main_receipt_id";i:20182014;s:4:"rate";d:33.37;s:8:"currency";s:3:"USD";}i:1;a:19:{s:8:"label_id";i:3097;s:8:"tracking";s:22:"9405500106025016566779";s:17:"refundable_amount";d:33.37;s:7:"created";i:1707729610913;s:10:"carrier_id";s:4:"usps";s:12:"service_name";s:20:"USPS - Priority Mail";s:6:"status";s:9:"PURCHASED";s:22:"commercial_invoice_url";s:0:"";s:46:"is_commercial_invoice_submitted_electronically";b:0;s:12:"package_name";s:25:"Priority Mail Medium Tube";s:9:"is_letter";b:0;s:13:"product_names";a:8:{i:0;s:8:"A nice P";i:1;s:5:"Shirt";i:2;s:5:"Shirt";i:3;s:6:"Jacket";i:4;s:6:"Jacket";i:5;s:6:"Jacket";i:6;s:6:"Jacket";i:7;s:3:"Hat";}s:11:"product_ids";a:8:{i:0;i:46;i:1;i:209;i:2;i:209;i:3;i:217;i:4;i:217;i:5;i:217;i:6;i:222;i:7;i:225;}s:15:"receipt_item_id";i:24940541;s:12:"created_date";i:1707729614000;s:15:"main_receipt_id";i:20182013;s:4:"rate";d:33.37;s:8:"currency";s:3:"USD";s:11:"expiry_date";i:1723281614000;}i:2;a:19:{s:8:"label_id";i:3096;s:8:"tracking";s:22:"9405500106025016566717";s:17:"refundable_amount";d:33.37;s:7:"created";i:1707729591316;s:10:"carrier_id";s:4:"usps";s:12:"service_name";s:20:"USPS - Priority Mail";s:6:"status";s:9:"PURCHASED";s:22:"commercial_invoice_url";s:0:"";s:46:"is_commercial_invoice_submitted_electronically";b:0;s:12:"package_name";s:25:"Priority Mail Medium Tube";s:9:"is_letter";b:0;s:13:"product_names";a:8:{i:0;s:8:"A nice P";i:1;s:5:"Shirt";i:2;s:5:"Shirt";i:3;s:6:"Jacket";i:4;s:6:"Jacket";i:5;s:6:"Jacket";i:6;s:6:"Jacket";i:7;s:3:"Hat";}s:11:"product_ids";a:8:{i:0;i:46;i:1;i:209;i:2;i:209;i:3;i:217;i:4;i:217;i:5;i:217;i:6;i:222;i:7;i:225;}s:15:"receipt_item_id";i:24940540;s:12:"created_date";i:1707729595000;s:11:"expiry_date";i:1723281594000;s:15:"main_receipt_id";i:20182012;s:4:"rate";d:33.37;s:8:"currency";s:3:"USD";}i:3;a:19:{s:8:"label_id";i:2962;s:8:"tracking";s:22:"9470100106025012022213";s:17:"refundable_amount";d:26.65;s:7:"created";i:1706115809816;s:10:"carrier_id";s:4:"usps";s:12:"service_name";s:19:"USPS - Express Mail";s:6:"status";s:9:"PURCHASED";s:22:"commercial_invoice_url";s:0:"";s:46:"is_commercial_invoice_submitted_electronically";b:0;s:12:"package_name";s:24:"Legal Flat Rate Envelope";s:9:"is_letter";b:1;s:13:"product_names";a:8:{i:0;s:8:"A nice P";i:1;s:5:"Shirt";i:2;s:5:"Shirt";i:3;s:6:"Jacket";i:4;s:6:"Jacket";i:5;s:6:"Jacket";i:6;s:6:"Jacket";i:7;s:3:"Hat";}s:11:"product_ids";a:8:{i:0;i:46;i:1;i:209;i:2;i:209;i:3;i:217;i:4;i:217;i:5;i:217;i:6;i:222;i:7;i:225;}s:15:"receipt_item_id";i:24766844;s:12:"created_date";i:1706115813000;s:11:"expiry_date";i:1721667813000;s:15:"main_receipt_id";i:20140178;s:4:"rate";d:26.65;s:8:"currency";s:3:"USD";}i:4;a:19:{s:8:"label_id";i:2961;s:8:"tracking";s:22:"9405500106025012022163";s:17:"refundable_amount";d:8.8;s:7:"created";i:1706115792491;s:10:"carrier_id";s:4:"usps";s:12:"service_name";s:20:"USPS - Priority Mail";s:6:"status";s:9:"PURCHASED";s:22:"commercial_invoice_url";s:0:"";s:46:"is_commercial_invoice_submitted_electronically";b:0;s:12:"package_name";s:24:"Legal Flat Rate Envelope";s:9:"is_letter";b:1;s:13:"product_names";a:8:{i:0;s:8:"A nice P";i:1;s:5:"Shirt";i:2;s:5:"Shirt";i:3;s:6:"Jacket";i:4;s:6:"Jacket";i:5;s:6:"Jacket";i:6;s:6:"Jacket";i:7;s:3:"Hat";}s:11:"product_ids";a:8:{i:0;i:46;i:1;i:209;i:2;i:209;i:3;i:217;i:4;i:217;i:5;i:217;i:6;i:222;i:7;i:225;}s:15:"receipt_item_id";i:24766843;s:12:"created_date";i:1706115796000;s:11:"expiry_date";i:1721667795000;s:15:"main_receipt_id";i:20140177;s:4:"rate";d:8.8;s:8:"currency";s:3:"USD";}i:5;a:20:{s:8:"label_id";i:8285570;s:8:"tracking";s:22:"9434636106025475451881";s:17:"refundable_amount";d:6.32;s:7:"created";i:1696937765903;s:10:"carrier_id";s:4:"usps";s:12:"service_name";s:23:"USPS - Ground Advantage";s:6:"status";s:9:"PURCHASED";s:22:"commercial_invoice_url";s:0:"";s:46:"is_commercial_invoice_submitted_electronically";b:0;s:12:"package_name";s:6:"Random";s:9:"is_letter";b:0;s:13:"product_names";a:1:{i:0;s:8:"A nice P";}s:11:"product_ids";a:1:{i:0;i:46;}s:15:"receipt_item_id";i:124453562;s:12:"created_date";i:1696937770000;s:15:"main_receipt_id";i:85557395;s:4:"rate";d:6.32;s:8:"currency";s:3:"USD";s:11:"expiry_date";i:1712489769000;s:12:"label_cached";i:1696937773000;}i:6;a:19:{s:8:"label_id";i:8285568;s:8:"tracking";N;s:17:"refundable_amount";d:75.12;s:7:"created";i:1696937763798;s:10:"carrier_id";s:4:"usps";s:12:"service_name";s:34:"USPS - Priority Mail International";s:6:"status";s:14:"PURCHASE_ERROR";s:22:"commercial_invoice_url";s:0:"";s:46:"is_commercial_invoice_submitted_electronically";b:0;s:12:"package_name";s:35:"Medium Flat Rate Box 1, Top Loading";s:9:"is_letter";b:0;s:13:"product_names";a:1:{i:0;s:8:"A nice P";}s:11:"product_ids";a:1:{i:0;i:46;}s:15:"receipt_item_id";i:*********;s:12:"created_date";i:1696937763000;s:15:"main_receipt_id";i:85557392;s:4:"rate";d:75.12;s:8:"currency";s:3:"USD";s:5:"error";s:64:"missing required customs address data: name of person or company";}i:7;a:19:{s:8:"label_id";i:8285563;s:8:"tracking";N;s:17:"refundable_amount";d:75.12;s:7:"created";i:1696937621242;s:10:"carrier_id";s:4:"usps";s:12:"service_name";s:34:"USPS - Priority Mail International";s:6:"status";s:14:"PURCHASE_ERROR";s:22:"commercial_invoice_url";s:0:"";s:46:"is_commercial_invoice_submitted_electronically";b:0;s:12:"package_name";s:35:"Medium Flat Rate Box 1, Top Loading";s:9:"is_letter";b:0;s:13:"product_names";a:1:{i:0;s:8:"A nice P";}s:11:"product_ids";a:1:{i:0;i:46;}s:15:"receipt_item_id";i:*********;s:12:"created_date";i:1696937621000;s:15:"main_receipt_id";i:85557303;s:4:"rate";d:75.12;s:8:"currency";s:3:"USD";s:5:"error";s:64:"missing required customs address data: name of person or company";}i:8;a:19:{s:8:"label_id";i:8285560;s:8:"tracking";N;s:17:"refundable_amount";d:75.12;s:7:"created";i:1696937500005;s:10:"carrier_id";s:4:"usps";s:12:"service_name";s:34:"USPS - Priority Mail International";s:6:"status";s:14:"PURCHASE_ERROR";s:22:"commercial_invoice_url";s:0:"";s:46:"is_commercial_invoice_submitted_electronically";b:0;s:12:"package_name";s:35:"Medium Flat Rate Box 1, Top Loading";s:9:"is_letter";b:0;s:13:"product_names";a:1:{i:0;s:8:"A nice P";}s:11:"product_ids";a:1:{i:0;i:46;}s:15:"receipt_item_id";i:*********;s:12:"created_date";i:1696937499000;s:15:"main_receipt_id";i:85557239;s:4:"rate";d:75.12;s:8:"currency";s:3:"USD";s:5:"error";s:64:"missing required customs address data: name of person or company";}}' );
	}
	/**
	 * Creates a few test HPOS orders with minimal metadata.
	 *
	 * @param int $count Number of orders to generate.
	 *
	 * @return int[] Order IDs.
	 */
	private function create_test_orders( $with_legacy_label = 10, $with_wcs_label = 10, $without_label = 5, $with_both = 2 ): array {
		$order_ids = array(
			'with_legacy_label' => array(),
			'with_wcs_label'    => array(),
			'without_label'     => array(),
			'with_both'         => array(),
		);

		$p1 = new WC_Product();
		$p2 = new WC_Product();
		$p1->save();
		$p2->save();

		$legacy_labels = $this->get_legacy_labels_data();

		foreach ( $legacy_labels as &$label ) {
			$label['product_ids']   = array(
				$p1->get_id(),
				$p2->get_id(),
			);
			$label['product_names'] = array(
				$p1->get_name(),
				$p2->get_name(),
			);
		}

		for ( $i = 0; $i < $with_legacy_label; $i++ ) {
			$order = new WC_Order();
			$order->add_product( $p1 );
			$order->add_product( $p2 );

			$order->add_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels );
			if ( $i < $with_both ) {
				$order->add_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, $legacy_labels );
				$order_ids['with_both'][] = $order->save();
			} else {
				$order_ids['with_legacy_label'][] = $order->save();

			}
		}

		for ( $i = 0; $i < $with_wcs_label; $i++ ) {
			$order = new WC_Order();
			$order->add_product( $p1 );
			$order->add_product( $p2 );
			$order->add_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, 'is_converted' );
			$order_ids['with_wcs_label'][] = $order->save();
		}

		for ( $i = 0; $i < $without_label; $i++ ) {
			$order = new WC_Order();
			$order->add_product( $p1 );
			$order->add_product( $p2 );
			$order_ids['without_label'][] = $order->save();
		}

		return array( $order_ids, $legacy_labels );
	}
	function tearDown(): void {
		parent::tearDown();
		$orders = wc_get_orders(
			array(
				'limit' => - 1,
			)
		);

		foreach ( $orders as $order ) {
			$order->delete( true );
		}
	}

	public function test_migration_preserves_existing_wcshipping_labels() {
		$order = new WC_Order();
		$p1    = new \WC_Product();
		$p1->set_name( 'P1' );
		$p1->save();

		$p2 = new \WC_Product();
		$p2->set_name( 'P2' );
		$p2->save();

		// Add products to order
		$product_id_to_item_id_map                  = array();
		$product_id_to_item_id_map[ $p1->get_id() ] = $order->add_product( $p1 );
		$product_id_to_item_id_map[ $p2->get_id() ] = $order->add_product( $p2 );

		// Setup existing WCShipping labels
		$existing_wcshipping_labels = array(
			array(
				'id'          => 0,
				'label_id'    => 5000,
				'label_name'  => 'WCShipping Label',
				'product_ids' => array( $p1->get_id() ),
				'is_legacy'   => false,
				'status'      => 'PURCHASED',
			),
		);

		// Setup legacy labels
		$legacy_labels = array(
			array(
				'label_id'    => 1000,
				'label_name'  => 'Legacy Label',
				'product_ids' => array( $p2->get_id() ),
				'status'      => 'PURCHASED',
			),
		);

		// Add both types of labels to order
		$order->add_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, $existing_wcshipping_labels, true );
		$order->add_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels, true );
		$order->save();

		// Mock the settings store to return our legacy labels
		$this->settings_store_mock->method( 'get_label_order_meta_data' )
			->willReturn( $legacy_labels );

		// Perform migration
		$this->migrator->convert_item_and_copy_label_data( $order );

		// Get the resulting labels
		$final_labels = $order->get_meta( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, true );

		// Assert that both labels exist in the final result
		$this->assertCount( 2, $final_labels );

		// Verify the existing WCShipping label is preserved
		$this->assertEquals( 5000, $final_labels[0]['label_id'] );
		$this->assertFalse( $final_labels[0]['is_legacy'] );

		// Verify the legacy label was converted and added
		$this->assertEquals( 1000, $final_labels[1]['label_id'] );
		$this->assertTrue( $final_labels[1]['is_legacy'] );

		// Verify shipments were created correctly
		$shipments = $order->get_meta( ShipmentsService::META_KEY, true );

		$this->assertCount( 2, $shipments );

		// Verify first shipment (WCShipping)
		$this->assertEquals(
			$product_id_to_item_id_map[ $p1->get_id() ],
			$shipments[0][0]['id']
		);

		// Verify second shipment (Legacy)
		$this->assertEquals(
			$product_id_to_item_id_map[ $p2->get_id() ],
			$shipments[1][0]['id']
		);
	}

	public function test_migration_merges_with_multiple_existing_wcshipping_shipments() {
		$order = new WC_Order();
		$p1    = new \WC_Product();
		$p1->set_name( 'P1' );
		$p1->save();

		$p2 = new \WC_Product();
		$p2->set_name( 'P2' );
		$p2->save();

		$p3 = new \WC_Product();
		$p3->set_name( 'P3' );
		$p3->save();

		// Add products to order
		$product_id_to_item_id_map                  = array();
		$product_id_to_item_id_map[ $p1->get_id() ] = $order->add_product( $p1 );
		$product_id_to_item_id_map[ $p2->get_id() ] = $order->add_product( $p2 );
		$product_id_to_item_id_map[ $p3->get_id() ] = $order->add_product( $p3 );

		// Setup existing WCShipping labels with two shipments
		$existing_wcshipping_labels = array(
			array(
				'id'          => 0,
				'label_id'    => 5000,
				'label_name'  => 'WCShipping Label 1',
				'product_ids' => array( $p1->get_id() ),
				'is_legacy'   => false,
				'status'      => 'PURCHASED',
			),
			array(
				'id'          => 1,
				'label_id'    => 5001,
				'label_name'  => 'WCShipping Label 2',
				'product_ids' => array( $p2->get_id() ),
				'is_legacy'   => false,
				'status'      => 'PURCHASED',
			),
		);

		// Setup existing shipments
		$existing_shipments = array(
			array(
				array(
					'id'       => $product_id_to_item_id_map[ $p1->get_id() ],
					'subItems' => array(),
				),
			),
			array(
				array(
					'id'       => $product_id_to_item_id_map[ $p2->get_id() ],
					'subItems' => array(),
				),
			),
		);

		// Setup legacy label
		$legacy_labels = array(
			array(
				'label_id'    => 1000,
				'label_name'  => 'Legacy Label',
				'product_ids' => array( $p3->get_id() ),
				'status'      => 'PURCHASED',
			),
		);

		// Add both types of labels and existing shipments to order
		$order->add_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, $existing_wcshipping_labels, true );
		$order->add_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels, true );
		$order->add_meta_data( ShipmentsService::META_KEY, $existing_shipments, true );
		$order->save();

		// Mock the settings store to return our legacy labels
		$this->settings_store_mock->method( 'get_label_order_meta_data' )
			->willReturn( $legacy_labels );

		// Perform migration
		$this->migrator->convert_item_and_copy_label_data( $order );

		// Get the resulting labels
		$final_labels = $order->get_meta( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, true );

		// Assert that all three labels exist in the final result
		$this->assertCount( 3, $final_labels );

		// Verify the existing WCShipping labels are preserved
		$this->assertEquals( 5000, $final_labels[0]['label_id'] );
		$this->assertFalse( $final_labels[0]['is_legacy'] );
		$this->assertEquals( 5001, $final_labels[1]['label_id'] );
		$this->assertFalse( $final_labels[1]['is_legacy'] );

		// Verify the legacy label was converted and added
		$this->assertEquals( 1000, $final_labels[2]['label_id'] );
		$this->assertTrue( $final_labels[2]['is_legacy'] );

		// Verify shipments were merged correctly
		$final_shipments = $order->get_meta( ShipmentsService::META_KEY, true );

		// Should now have three shipments
		$this->assertCount( 3, $final_shipments );

		// Verify existing shipments are preserved
		$this->assertEquals(
			$product_id_to_item_id_map[ $p1->get_id() ],
			$final_shipments[0][0]['id']
		);
		$this->assertEquals(
			$product_id_to_item_id_map[ $p2->get_id() ],
			$final_shipments[1][0]['id']
		);

		// Verify new shipment was added
		$this->assertEquals(
			$product_id_to_item_id_map[ $p3->get_id() ],
			$final_shipments[2][0]['id']
		);
	}

	public function test_get_next_batch_to_process_returns_correct_number_of_legacy_label_orders() {
		// Running test without COT enabled
		OrderHelper::toggle_cot_feature_and_usage( false );

		// Create test orders with different label configurations
		list($order_ids, $legacy_labels) = $this->create_test_orders( 10, 5, 3, 2 );

		// Mock the settings store
		$this->settings_store_mock
			->expects( $this->any() )
			->method( 'get_label_order_meta_data' )
			->willReturn( $legacy_labels );

		// Get batch of orders to process
		$batch_size = 20;
		$batch_ids  = $this->migrator->get_next_batch_to_process( $batch_size );

		// We expect 10 orders with only legacy labels - 2 orders with both types = 8 total
		$this->assertCount( 8, $batch_ids );

		// Verify that all returned orders actually have legacy labels
		foreach ( $batch_ids as $order_id ) {
			$order = wc_get_order( $order_id );
			$this->assertNotEmpty(
				$order->get_meta( LegacyLabelMigrator::LEGACY_LABEL_META_KEY ),
				"Order {$order_id} should have legacy labels"
			);
		}

		// Verify that orders without legacy labels are not included
		foreach ( $order_ids['without_label'] as $order_id ) {
			$this->assertNotContains(
				$order_id,
				$batch_ids,
				"Order {$order_id} without legacy labels should not be included in batch"
			);
		}

		// Verify that orders with only WCS labels are not included
		foreach ( $order_ids['with_wcs_label'] as $order_id ) {
			$this->assertNotContains(
				$order_id,
				$batch_ids,
				"Order {$order_id} with only WCS labels should not be included in batch"
			);
		}
	}


	public function test_get_total_pending_count_includes_partially_migrated_orders() {
		// Create an order with legacy labels
		$order         = new WC_Order();
		$legacy_labels = array(
			array(
				'label_id'    => 1000,
				'tracking'    => '1234',
				'product_ids' => array(),
			),
			array(
				'label_id'    => 2000,
				'tracking'    => '5678',
				'product_ids' => array(),
			),
		);

		// Create partial WC Shipping labels (missing one label_id)
		$wcshipping_labels = array(
			array(
				'label_id'    => 1000,
				'tracking'    => '1234',
				'product_ids' => array(),
				'is_legacy'   => true,
			),
		);

		$order->add_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels );
		$order->add_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, $wcshipping_labels );
		$order->save();

		// Create another order with only legacy labels
		$order2 = new WC_Order();
		$order2->add_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels );
		$order2->save();

		// Create a fully migrated order (shouldn't be counted)
		$order3                           = new WC_Order();
		$fully_migrated_wcshipping_labels = array(
			array(
				'label_id'    => 1000,
				'tracking'    => '1234',
				'product_ids' => array(),
				'is_legacy'   => true,
			),
			array(
				'label_id'    => 2000,
				'tracking'    => '5678',
				'product_ids' => array(),
				'is_legacy'   => true,
			),
		);
		$order3->add_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels );
		$order3->add_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, $fully_migrated_wcshipping_labels );
		$order3->save();

		$this->settings_store_mock
			->expects( $this->any() )
			->method( 'get_label_order_meta_data' )
			->willReturn( $legacy_labels );

		// Should count both the partially migrated order and the non-migrated order
		$this->assertEquals( 2, $this->migrator->get_total_pending_count() );
	}

	public function test_get_total_pending_count_handles_empty_label_arrays() {
		// Create an order with empty legacy labels
		$order             = new WC_Order();
		$legacy_labels     = array();
		$wcshipping_labels = array();

		$order->add_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels );
		$order->add_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, $wcshipping_labels );
		$order->save();

		$this->settings_store_mock
			->expects( $this->any() )
			->method( 'get_label_order_meta_data' )
			->willReturn( $legacy_labels );

		// Should not count orders with empty label arrays
		$this->assertEquals( 0, $this->migrator->get_total_pending_count() );
	}

	public function test_get_total_pending_count_handles_malformed_label_data() {
		// Create an order with malformed legacy labels
		$order             = new WC_Order();
		$legacy_labels     = 'not an array';
		$wcshipping_labels = 'also not an array';

		$order->add_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels );
		$order->add_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, $wcshipping_labels );
		$order->save();

		$this->settings_store_mock
			->expects( $this->any() )
			->method( 'get_label_order_meta_data' )
			->willReturn( $legacy_labels );

		// Should handle malformed data gracefully
		$this->assertEquals( 0, $this->migrator->get_total_pending_count() );
	}

	public function test_get_total_pending_count_includes_orders_with_missing_label_ids(): void {

		// Create test orders
		$order1 = wc_create_order();
		$order2 = wc_create_order();
		$order3 = wc_create_order();

		// Order 1: Has legacy labels but no WC Shipping labels
		$legacy_labels1 = array(
			array(
				'label_id'    => 100,
				'tracking'    => '1234',
				'product_ids' => array( 1, 2 ),
			),
		);
		$order1->update_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels1 );
		$order1->save();

		// Order 2: Has both types but missing some label_ids in WC Shipping
		$legacy_labels2 = array(
			array(
				'label_id'    => 200,
				'tracking'    => '5678',
				'product_ids' => array( 3, 4 ),
			),
			array(
				'label_id'    => 201,
				'tracking'    => '9012',
				'product_ids' => array( 5, 6 ),
			),
		);
		$wc_labels2     = array(
			array(
				'label_id'    => 200,
				'tracking'    => '5678',
				'product_ids' => array( 3, 4 ),
				'is_legacy'   => true,
			),
			// Note: Missing label_id 201
		);
		$order2->update_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels2 );
		$order2->update_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, $wc_labels2 );
		$order2->save();

		// Order 3: Has both types with all label_ids properly migrated
		$legacy_labels3 = array(
			array(
				'label_id'    => 300,
				'tracking'    => '3456',
				'product_ids' => array( 7, 8 ),
			),
		);
		$wc_labels3     = array(
			array(
				'label_id'    => 300,
				'tracking'    => '3456',
				'product_ids' => array( 7, 8 ),
				'is_legacy'   => true,
			),
		);
		$order3->update_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels3 );
		$order3->update_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, $wc_labels3 );
		$order3->save();

		// Should count both order1 (no WC labels) and order2 (missing label_id)
		$this->assertEquals( 2, $this->migrator->get_total_pending_count() );
	}

	public function test_get_total_pending_count_handles_malformed_data(): void {
		// Create test order
		$order = wc_create_order();

		// Set malformed data
		$order->update_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, 'not an array' );
		$order->update_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, 'also not an array' );
		$order->save();

		// Should handle malformed data gracefully
		$this->assertEquals( 0, $this->migrator->get_total_pending_count() );
	}

	public function test_get_total_pending_count_handles_empty_arrays(): void {
		// Create test order
		$order = wc_create_order();

		// Set empty arrays
		$order->update_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, array() );
		$order->update_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, array() );
		$order->save();

		// Should handle empty arrays gracefully
		$this->assertEquals( 0, $this->migrator->get_total_pending_count() );
	}

	public function test_get_total_pending_count_handles_missing_label_id(): void {
		// Create test order
		$order = wc_create_order();

		// Set labels without label_id field
		$legacy_labels = array(
			array(
				'tracking'    => '1234',
				'product_ids' => array( 1, 2 ),
				// No label_id
			),
		);
		$wc_labels = array(
			array(
				'tracking'    => '1234',
				'product_ids' => array( 1, 2 ),
				'is_legacy'   => true,
				// No label_id
			),
		);
		$order->update_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels );
		$order->update_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, $wc_labels );
		$order->save();

		// Should handle missing label_id field gracefully
		$this->assertEquals( 0, $this->migrator->get_total_pending_count() );
	}

	public function test_get_next_batch_includes_orders_with_missing_label_ids(): void {
		// Create test orders
		$order1 = wc_create_order();
		$order2 = wc_create_order();
		$order3 = wc_create_order();
		$order4 = wc_create_order();

		// Order 1: Has legacy labels but no WC Shipping labels
		$legacy_labels1 = array(
			array(
				'label_id'    => 100,
				'tracking'    => '1234',
				'product_ids' => array( 1, 2 ),
			),
		);
		$order1->update_meta_data( 'wc_connect_labels', $legacy_labels1 );
		$order1->save();

		// Order 2: Has both types but missing some label_ids in WC Shipping
		$legacy_labels2 = array(
			array(
				'label_id'    => 200,
				'tracking'    => '5678',
				'product_ids' => array( 3, 4 ),
			),
			array(
				'label_id'    => 201,
				'tracking'    => '9012',
				'product_ids' => array( 5, 6 ),
			),
		);
		$wc_labels2     = array(
			array(
				'label_id'    => 200,
				'tracking'    => '5678',
				'product_ids' => array( 3, 4 ),
				'is_legacy'   => true,
			),
			// Note: Missing label_id 201
		);
		$order2->update_meta_data( 'wc_connect_labels', $legacy_labels2 );
		$order2->update_meta_data( 'wcshipping_labels', $wc_labels2 );
		$order2->save();

		// Order 3: Has both types with all label_ids properly migrated
		$legacy_labels3 = array(
			array(
				'label_id'    => 300,
				'tracking'    => '3456',
				'product_ids' => array( 7, 8 ),
			),
		);
		$wc_labels3     = array(
			array(
				'label_id'    => 300,
				'tracking'    => '3456',
				'product_ids' => array( 7, 8 ),
				'is_legacy'   => true,
			),
		);
		$order3->update_meta_data( 'wc_connect_labels', $legacy_labels3 );
		$order3->update_meta_data( 'wcshipping_labels', $wc_labels3 );
		$order3->save();

		// Order 4: Has legacy labels but no WC Shipping labels
		$legacy_labels4 = array(
			array(
				'label_id'    => 400,
				'tracking'    => '7890',
				'product_ids' => array( 9, 10 ),
			),
		);
		$order4->update_meta_data( 'wc_connect_labels', $legacy_labels4 );
		$order4->save();

		// Test with different batch sizes
		$batch1 = $this->migrator->get_next_batch_to_process( 1 );
		$this->assertCount( 1, $batch1 );
		$this->assertTrue( in_array( $order1->get_id(), $batch1 ) || in_array( $order2->get_id(), $batch1 ) || in_array( $order4->get_id(), $batch1 ) );

		$batch2 = $this->migrator->get_next_batch_to_process( 2 );
		$this->assertCount( 2, $batch2 );
		// Should not include order3 as it's fully migrated
		$this->assertFalse( in_array( $order3->get_id(), $batch2 ) );

		$batch4 = $this->migrator->get_next_batch_to_process( 4 );
		$this->assertCount( 3, $batch4 ); // Should only return 3 orders as only 3 need migration
		$this->assertTrue( in_array( $order1->get_id(), $batch4 ) );
		$this->assertTrue( in_array( $order2->get_id(), $batch4 ) );
		$this->assertTrue( in_array( $order4->get_id(), $batch4 ) );
		$this->assertFalse( in_array( $order3->get_id(), $batch4 ) );
	}

	public function test_get_next_batch_handles_empty_result(): void {
		// Create an order with fully migrated labels
		$order         = wc_create_order();
		$legacy_labels = array(
			array(
				'label_id'    => 100,
				'tracking'    => '1234',
				'product_ids' => array( 1, 2 ),
			),
		);
		$wc_labels     = array(
			array(
				'label_id'    => 100,
				'tracking'    => '1234',
				'product_ids' => array( 1, 2 ),
				'is_legacy'   => true,
			),
		);
		$order->update_meta_data( 'wc_connect_labels', $legacy_labels );
		$order->update_meta_data( 'wcshipping_labels', $wc_labels );
		$order->save();

		$batch = $this->migrator->get_next_batch_to_process( 10 );
		$this->assertEmpty( $batch );
	}

	public function test_get_next_batch_handles_malformed_data(): void {
		// Create order with malformed data
		$order = wc_create_order();
		$order->update_meta_data( 'wc_connect_labels', 'not an array' );
		$order->update_meta_data( 'wcshipping_labels', 'also not an array' );
		$order->save();

		$batch = $this->migrator->get_next_batch_to_process( 10 );
		$this->assertEmpty( $batch );
	}

	public function test_get_next_batch_respects_batch_size(): void {
		// Create multiple orders needing migration
		$orders = array();
		for ( $i = 0; $i < 5; $i++ ) {
			$order         = wc_create_order();
			$legacy_labels = array(
				array(
					'label_id'    => 100 + $i,
					'tracking'    => '1234',
					'product_ids' => array( 1, 2 ),
				),
			);
			$order->update_meta_data( 'wc_connect_labels', $legacy_labels );
			$order->save();
			$orders[] = $order;
		}

		// Test different batch sizes
		$batch1 = $this->migrator->get_next_batch_to_process( 1 );
		$this->assertCount( 1, $batch1 );

		$batch3 = $this->migrator->get_next_batch_to_process( 3 );
		$this->assertCount( 3, $batch3 );

		$batch10 = $this->migrator->get_next_batch_to_process( 10 );
		$this->assertCount( 5, $batch10 ); // Should only return 5 as that's all we created
	}

	public function test_convert_item_and_copy_label_data_skips_existing_labels(): void {
		// Create an order
		$order = new WC_Order();

		// Add some products to the order
		$product1 = new WC_Product();
		$product1->set_name( 'Test Product 1' );
		$product1->save();

		$product2 = new WC_Product();
		$product2->set_name( 'Test Product 2' );
		$product2->save();

		$product3 = new WC_Product();
		$product3->set_name( 'Test Product 3' );
		$product3->save();

		$product4 = new WC_Product();
		$product4->set_name( 'Test Product 4' );
		$product4->save();

		$order->add_product( $product1 );
		$order->add_product( $product2 );
		$order->add_product( $product3 );
		$order->add_product( $product4 );

		// Add existing WC Shipping labels with some purchased ones
		$existing_labels = array(
			array(
				'label_id'    => '123',
				'status'      => 'PURCHASED',
				'product_ids' => array( $product1->get_id() ),
				'is_legacy'   => true,
			),
			array(
				'label_id'    => '124',
				'status'      => 'FAILED',
				'product_ids' => array( $product2->get_id() ),
				'is_legacy'   => true,
			),
			array(
				'label_id'    => '125',
				'status'      => 'PURCHASED',
				'product_ids' => array( $product3->get_id() ),
				'is_legacy'   => true,
			),
		);
		$order->update_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, $existing_labels );
		// Add existing shipments for the purchased labels
		$shipments = array(
			array(
				array(
					'id'       => $product1->get_id(),
					'subItems' => array(),
				),
			),
			array(
				array(
					'id'       => $product3->get_id(),
					'subItems' => array(),
				),
			),
		);
		$order->update_meta_data( ShipmentsService::META_KEY, $shipments );

		// Add a legacy label to migrate
		$legacy_labels = array(
			array(
				'label_id'    => '126',
				'status'      => 'PURCHASED',
				'product_ids' => array( $product4->get_id() ),
			),
		);
		$order->update_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels );
		$order->save();

		// Mock the settings store to return our legacy labels
		$this->settings_store_mock
			->method( 'get_label_order_meta_data' )
			->willReturn( $legacy_labels );

		$this->migrator->convert_item_and_copy_label_data( $order );

		// Get the updated labels
		$migrated_labels = $order->get_meta( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY );

		// Verify we have all 4 labels (3 existing + 1 new)
		$this->assertCount( 4, $migrated_labels );

		// Verify the new label was added and has correct properties
		$this->assertEquals( '126', $migrated_labels[3]['label_id'] );
		$this->assertEquals( 'PURCHASED', $migrated_labels[3]['status'] );
		$this->assertEquals( array( $product4->get_id() ), $migrated_labels[3]['product_ids'] );
		$this->assertTrue( $migrated_labels[3]['is_legacy'] );

		// Get the updated shipments
		$shipments = $order->get_meta( ShipmentsService::META_KEY );

		// Verify we have the correct number of shipments (2 PURCHASED + 1 new = 3)
		$this->assertCount( 3, $shipments );

		// Verify the new shipment was added at index 2 (after the two existing PURCHASED labels)
		$this->assertArrayHasKey( 2, $shipments );
	}

	public function test_internal_id_with_existing_wcshipping_labels(): void {
		$order = new WC_Order();
		$p1    = new WC_Product();
		$p1->set_name( 'P1' );
		$p1->save();

		// Add existing WC Shipping labels with internal IDs 0 and 1
		$existing_wcshipping_labels = array(
			array(
				'id'          => 0,
				'label_id'    => '1234',
				'label_name'  => 'Label 1',
				'product_ids' => array( $p1->get_id() ),
				'status'      => 'PURCHASED',
				'is_legacy'   => true,
			),
			array(
				'id'          => 1,
				'label_id'    => '5678',
				'label_name'  => 'Label 2',
				'product_ids' => array( $p1->get_id() ),
				'status'      => 'PURCHASE_FAILED',
				'is_legacy'   => true,
			),
		);

		// Add legacy label to migrate
		$legacy_labels = array(
			array(
				'label_id'    => '9012',
				'label_name'  => 'Legacy Label',
				'product_ids' => array( $p1->get_id() ),
				'status'      => 'PURCHASED',
			),
		);

		$order->add_product( $p1 );
		$order->update_meta_data( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, $existing_wcshipping_labels );
		$order->update_meta_data( LegacyLabelMigrator::LEGACY_LABEL_META_KEY, $legacy_labels );
		$order->save();

		// Mock settings store to return our legacy label
		$this->settings_store_mock->method( 'get_label_order_meta_data' )
			->willReturn( $legacy_labels );

		// Run migration
		$this->migrator->convert_item_and_copy_label_data( $order );

		// Get final labels
		$final_labels = $order->get_meta( LegacyLabelMigrator::WCSHIPPING_LABEL_META_KEY, true );

		// Verify results
		$this->assertCount( 3, $final_labels );

		// Check existing labels are preserved
		$this->assertEquals( 0, $final_labels[0]['id'] );
		$this->assertEquals( '1234', $final_labels[0]['label_id'] );

		$this->assertEquals( 1, $final_labels[1]['id'] );
		$this->assertEquals( '5678', $final_labels[1]['label_id'] );

		// Check new migrated label has correct ID
		$this->assertEquals( 1, $final_labels[2]['id'] );
		$this->assertEquals( '9012', $final_labels[2]['label_id'] );
		$this->assertTrue( $final_labels[2]['is_legacy'] );
	}
}
