<?php

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\Migration\LegacySettingsMigrator;

class LegacySettingsMigratorTest extends WCShipping_Test_Case {

	private $migrator;

	function setUp(): void {
		parent::setUp();
		$this->migrator = new LegacySettingsMigrator();
	}

	public function test_settings_are_migrated_when_legacy_settings_exist_and_new_settings_do_not() {
		$test_settings = array(
			'foo' => 'bar',
		);
		update_option(
			LegacySettingsMigrator::OPTIONS['legacy'],
			$test_settings
		);

		$this->migrator->migrate_settings();

		$this->assertEquals(
			$test_settings,
			get_option( LegacySettingsMigrator::OPTIONS['wcshipping'] )
		);
	}

	public function test_settings_are_not_migrated_when_no_legacy_settings_exist() {
		delete_option( LegacySettingsMigrator::OPTIONS['legacy'] );

		$this->migrator->migrate_settings();

		$this->assertEmpty( get_option( LegacySettingsMigrator::OPTIONS['wcshipping'] ) );
	}

	public function test_settings_are_merged_when_new_settings_already_exist() {
		update_option( LegacySettingsMigrator::OPTIONS['legacy'], array( 'legacy_value' => 'legacy_value' ) );
		update_option( LegacySettingsMigrator::OPTIONS['wcshipping'], array( 'new_value' => 'new_value' ) );

		$this->migrator->migrate_settings();

		$options = get_option( LegacySettingsMigrator::OPTIONS['wcshipping'] );

		$this->assertEquals(
			array(
				'legacy_value' => 'legacy_value',
				'new_value'    => 'new_value',
			),
			$options
		);
	}

	/**
	 * This test case tests the situation where a user did not click the migration but navigated to the
	 * new WCShipping's settings page and saved some settings. This causes both `wc_connect_options` and `wcshipping_options`
	 * to not be null. This test checks if we merge the config properly.
	 */
	public function test_settings_merged_when_delayed_migration_but_chose_credit_card() {
		$payment_method_1                    = new stdClass();
		$payment_method_1->payment_method_id = 111;
		$payment_method_1->name              = '3DS card';
		$payment_method_1->card_type         = 'visa';
		$payment_method_1->card_digits       = '3155';
		$payment_method_1->expiry            = '2039-12-31';

		$payment_method_2                    = new stdClass();
		$payment_method_2->payment_method_id = 222;
		$payment_method_2->name              = 'New card';
		$payment_method_2->card_type         = 'visa';
		$payment_method_2->card_digits       = '3155';
		$payment_method_2->expiry            = '2040-12-31';

		$payment_method_3                    = new stdClass();
		$payment_method_3->payment_method_id = 333;
		$payment_method_3->name              = 'My third card';
		$payment_method_3->card_type         = 'visa';
		$payment_method_3->card_digits       = '3155';
		$payment_method_3->expiry            = '2040-12-31';

		$wcst_options = array(
			'tos_accepted'              => true,
			'store_guid'                => '111-222-3333',
			'shipping_methods_migrated' => true,
			'payment_methods'           => array(
				$payment_method_1,
				$payment_method_3,
			),
			'paper_size'                => 'label',
			'account_settings'          => array(
				'selected_payment_method_id' => 111,
				'enabled'                    => false,
				'wcc_debug_on'               => false,
				'wcc_logging_on'             => false,
				'email_receipts'             => false,
				'use_last_service'           => false,
				'use_last_package'           => false,
			),
			'packages'                  => array(
				array(
					'is_user_defined'  => true,
					'name'             => 'WCS&T 10x10x10',
					'inner_dimensions' => '10 x 10 x 10',
					'max_weight'       => 0,
					'box_weight'       => 0.3,
				),
				array(
					'is_user_defined'  => true,
					'is_letter'        => true,
					'name'             => 'WCS&T Letter',
					'inner_dimensions' => '6 x 6.125 x 0.25',
					'box_weight'       => 0,
					'max_weight'       => 0,
				),
			),
			'predefined_packages'       => array(
				'usps' => array( null, 'regional_a1' ),
			),
			'debug_logging_enabled'     => false,
			'debug_display_enabled'     => false,
			'add_payment_method_url'    => 'https://wordpress.com/me/purchases/add-credit-card',
		);

		$wcshipping_options = array(
			'should_display_nux_after_jp_cxn_banner' => true,
			'tos_accepted'                           => true,
			'store_guid'                             => '666-777-8888',
			'add_payment_method_url'                 => 'https://wordpress.com/me/purchases/add-credit-card',
			'payment_methods'                        => array( $payment_method_1, $payment_method_2 ),
			'paper_size'                             => 'letter',
			'account_settings'                       => array(
				'selected_payment_method_id' => 222,
				'enabled'                    => true,
				'email_receipts'             => true,
				'use_last_service'           => true,
				'use_last_package'           => true,
			),
		);

		// This is a merge between the $wcst_options and $wcshipping_options
		$expected_wcshipping_options = array(
			'tos_accepted'                           => true,
			'packages'                               => array(
				array(
					'is_user_defined'  => true,
					'name'             => 'WCS&T 10x10x10',
					'inner_dimensions' => '10 x 10 x 10',
					'max_weight'       => 0,
					'box_weight'       => 0.3,
				),
				array(
					'is_user_defined'  => true,
					'is_letter'        => true,
					'name'             => 'WCS&T Letter',
					'inner_dimensions' => '6 x 6.125 x 0.25',
					'box_weight'       => 0,
					'max_weight'       => 0,
				),
			),
			'predefined_packages'                    => array(
				'usps' => array( null, 'regional_a1' ),
			),
			'debug_logging_enabled'                  => false,
			'debug_display_enabled'                  => false,
			'add_payment_method_url'                 => 'https://wordpress.com/me/purchases/add-credit-card',
			'should_display_nux_after_jp_cxn_banner' => true,
			'store_guid'                             => '111-222-3333',
			'payment_methods'                        => array( $payment_method_1, $payment_method_2 ),
			'paper_size'                             => 'letter',
			'account_settings'                       => array(
				'selected_payment_method_id' => 222,
				'enabled'                    => true,
				'email_receipts'             => true,
				'use_last_service'           => true,
				'use_last_package'           => true,
			),
		);

		update_option( LegacySettingsMigrator::OPTIONS['legacy'], $wcst_options );
		update_option( LegacySettingsMigrator::OPTIONS['wcshipping'], $wcshipping_options );
		$this->migrator->migrate_settings();

		$new_wcst_options       = get_option( LegacySettingsMigrator::OPTIONS['legacy'] );
		$new_wcshipping_options = get_option( LegacySettingsMigrator::OPTIONS['wcshipping'] );

		$this->assertEquals( $wcst_options, $new_wcst_options ); // no change to the WCS&T's settings.
		$this->assertEquals( $expected_wcshipping_options, $new_wcshipping_options );
	}

	/**
	 * This tests the scenario where a user created a "saved template" before they migrate. In this case,
	 * both `wc_connect_options` and `wcshipping_options` will have "packages". This test tests the merge.
	 */
	public function test_settings_merged_when_delayed_migration_but_with_existing_saved_templates() {
		$payment_method_1                    = new stdClass();
		$payment_method_1->payment_method_id = 111;
		$payment_method_1->name              = '3DS card';
		$payment_method_1->card_type         = 'visa';
		$payment_method_1->card_digits       = '3155';
		$payment_method_1->expiry            = '2039-12-31';

		$payment_method_2                    = new stdClass();
		$payment_method_2->payment_method_id = 222;
		$payment_method_2->name              = 'New card';
		$payment_method_2->card_type         = 'visa';
		$payment_method_2->card_digits       = '3155';
		$payment_method_2->expiry            = '2040-12-31';

		$payment_method_3                    = new stdClass();
		$payment_method_3->payment_method_id = 333;
		$payment_method_3->name              = 'My third card';
		$payment_method_3->card_type         = 'visa';
		$payment_method_3->card_digits       = '3155';
		$payment_method_3->expiry            = '2040-12-31';

		$wcst_options = array(
			'tos_accepted'              => true,
			'store_guid'                => '111-222-3333',
			'shipping_methods_migrated' => true,
			'payment_methods'           => array(
				$payment_method_1,
				$payment_method_3,
			),
			'paper_size'                => 'label',
			'account_settings'          => array(
				'selected_payment_method_id' => 111,
				'enabled'                    => false,
				'wcc_debug_on'               => false,
				'wcc_logging_on'             => false,
				'email_receipts'             => false,
				'use_last_service'           => false,
				'use_last_package'           => false,
			),
			'packages'                  => array(
				array(
					'is_user_defined'  => true,
					'name'             => 'WCS&T 10x10x10',
					'inner_dimensions' => '10 x 10 x 10',
					'max_weight'       => 0,
					'box_weight'       => 0.3,
				),
				array(
					'is_user_defined'  => true,
					'is_letter'        => true,
					'name'             => 'WCS&T Letter',
					'inner_dimensions' => '6 x 6.125 x 0.25',
					'box_weight'       => 0,
					'max_weight'       => 0,
				),
			),
			'predefined_packages'       => array(
				'usps' => array( null, 'regional_a1' ),
			),
			'debug_logging_enabled'     => false,
			'debug_display_enabled'     => false,
			'add_payment_method_url'    => 'https://wordpress.com/me/purchases/add-credit-card',
		);

		$wcshipping_options = array(
			'should_display_nux_after_jp_cxn_banner' => true,
			'tos_accepted'                           => true,
			'store_guid'                             => '666-777-8888',
			'add_payment_method_url'                 => 'https://wordpress.com/me/purchases/add-credit-card',
			'payment_methods'                        => array( $payment_method_1, $payment_method_2 ),
			'paper_size'                             => 'letter',
			'account_settings'                       => array(
				'selected_payment_method_id' => 222,
				'enabled'                    => true,
				'email_receipts'             => true,
				'use_last_service'           => true,
				'use_last_package'           => true,
			),
			'packages'                               => array(
				array(
					'name'            => 'wcshipping UI 4x4x4',
					'boxWeight'       => 4,
					'id'              => '94c623d29e6faaf5438a5b03fbc02081',
					'type'            => 'box',
					'isLetter'        => false,
					'dimensions'      => '4 x 4 x 4',
					'is_user_defined' => true,
				),
			),
		);

		// This is a merge between the $wcst_options and $wcshipping_options
		$expected_wcshipping_options = array(
			'tos_accepted'                           => true,
			'packages'                               => array(
				array(
					'is_user_defined'  => true,
					'name'             => 'WCS&T 10x10x10',
					'inner_dimensions' => '10 x 10 x 10',
					'max_weight'       => 0,
					'box_weight'       => 0.3,
				),
				array(
					'is_user_defined'  => true,
					'is_letter'        => true,
					'name'             => 'WCS&T Letter',
					'inner_dimensions' => '6 x 6.125 x 0.25',
					'box_weight'       => 0,
					'max_weight'       => 0,
				),
				array(
					'name'            => 'wcshipping UI 4x4x4',
					'boxWeight'       => 4,
					'id'              => '94c623d29e6faaf5438a5b03fbc02081',
					'type'            => 'box',
					'isLetter'        => false,
					'dimensions'      => '4 x 4 x 4',
					'is_user_defined' => true,
				),
			),
			'predefined_packages'                    => array(
				'usps' => array( null, 'regional_a1' ),
			),
			'debug_logging_enabled'                  => false,
			'debug_display_enabled'                  => false,
			'add_payment_method_url'                 => 'https://wordpress.com/me/purchases/add-credit-card',
			'should_display_nux_after_jp_cxn_banner' => true,
			'store_guid'                             => '111-222-3333',
			'payment_methods'                        => array( $payment_method_1, $payment_method_2 ),
			'paper_size'                             => 'letter',
			'account_settings'                       => array(
				'selected_payment_method_id' => 222,
				'enabled'                    => true,
				'email_receipts'             => true,
				'use_last_service'           => true,
				'use_last_package'           => true,
			),
		);

		update_option( LegacySettingsMigrator::OPTIONS['legacy'], $wcst_options );
		update_option( LegacySettingsMigrator::OPTIONS['wcshipping'], $wcshipping_options );
		$this->migrator->migrate_settings();

		$new_wcst_options       = get_option( LegacySettingsMigrator::OPTIONS['legacy'] );
		$new_wcshipping_options = get_option( LegacySettingsMigrator::OPTIONS['wcshipping'] );

		$this->assertEquals( $wcst_options, $new_wcst_options ); // no change to the WCS&T's settings.
		$this->assertEquals( $expected_wcshipping_options, $new_wcshipping_options );
	}

	public function test_migrate_settings_even_if_non_migratable_settings_exist() {
		update_option(
			LegacySettingsMigrator::OPTIONS['legacy'],
			array(
				'store_guid'   => 'CORRECT',
				'tos_accepted' => true,
				'foo'          => true,
				'bar'          => false,
			)
		);

		update_option(
			LegacySettingsMigrator::OPTIONS['wcshipping'],
			array(
				'store_guid'                             => 'INCORRECT',
				'tos_accepted'                           => false,
				'should_display_nux_after_jp_cxn_banner' => true,
			)
		);

		$this->migrator->migrate_settings();
		$wcshipping_options = get_option( LegacySettingsMigrator::OPTIONS['wcshipping'] );

		// Verify that expected data was correctly migrated.
		$this->assertArrayHasKey( 'foo', $wcshipping_options );
		$this->assertSame( true, $wcshipping_options['foo'] );
		$this->assertArrayHasKey( 'bar', $wcshipping_options );
		$this->assertSame( false, $wcshipping_options['bar'] );

		// Verify that our legacy migration do not remove WC Shipping "non migratable" settings.
		$this->assertArrayHasKey( 'should_display_nux_after_jp_cxn_banner', $wcshipping_options );
		$this->assertSame( true, $wcshipping_options['should_display_nux_after_jp_cxn_banner'] );

		// Verify that our legacy settings migration did not override existing "non migratable" values.
		$this->assertArrayHasKey( 'tos_accepted', $wcshipping_options );
		$this->assertSame( false, $wcshipping_options['tos_accepted'] );

		// "store_guid" is a special case when it comes to "non migratable settings.
		// Because the Store GUID is unique, then we still want to allow settings migrations to happen
		// if it exists in WC Shipping, but it's also the only value we should carry with us.
		$this->assertArrayHasKey( 'store_guid', $wcshipping_options );
		$this->assertSame( 'CORRECT', $wcshipping_options['store_guid'] );
	}

	public function test_needs_migration_when_tos_is_already_accepted() {
		// Legacy settings exist, otherwise migration would not be pending
		update_option(
			LegacySettingsMigrator::OPTIONS['legacy'],
			array(
				'tos_accepted' => true,
				'foo'          => true,
				'bar'          => false,
			)
		);

		update_option(
			LegacySettingsMigrator::OPTIONS['wcshipping'],
			array(
				'store_guid'   => 'asdasds',
				'tos_accepted' => false,
			)
		);
		$this->assertTrue( $this->migrator->needs_migration() );
	}

	public function test_purchase_settings_are_migrated_when_legacy_settings_exist() {
		$user_ids = array(
			$this->create_user(),
			$this->create_user(),
			$this->create_user(),
			$this->create_user(),
			$this->create_user(),
		);
		$value    = 'legacy_value';
		// Set legacy settings
		foreach ( LegacySettingsMigrator::PURCHASE_SETTINGS as $versioned_setting ) {
			foreach ( $user_ids as $user_id ) {
				$this->assertNotFalse( add_user_meta( $user_id, $versioned_setting['legacy'], $value, true ) );
			}
		}

		$this->migrator->migrate_label_purchase_settings();

		// Check if new settings are migrated
		foreach ( LegacySettingsMigrator::PURCHASE_SETTINGS as $versioned_setting ) {
			foreach ( $user_ids as $user_id ) {
				$this->assertEquals( $value, get_user_meta( $user_id, $versioned_setting['wcshipping'], true ) );
			}
		}
	}

	public function test_purchase_settings_are_not_migrated_when_no_legacy_settings_exist() {
		$user_id = $this->create_user();

		// Ensure no legacy settings
		foreach ( LegacySettingsMigrator::PURCHASE_SETTINGS as $versioned_setting ) {
			delete_user_meta( $user_id, $versioned_setting['legacy'] );
		}

		$this->migrator->migrate_label_purchase_settings();

		// Check if new settings are not set
		foreach ( LegacySettingsMigrator::PURCHASE_SETTINGS as $versioned_setting ) {
			$this->assertEmpty( get_user_meta( $user_id, $versioned_setting['wcshipping'], true ) );
		}
	}

	public function test_purchase_settings_are_not_migrated_when_already_migrated() {
		$user_id = $this->create_user();

		// Set new settings
		foreach ( LegacySettingsMigrator::PURCHASE_SETTINGS as $versioned_setting ) {
			update_user_meta( $user_id, $versioned_setting['legacy'], 'old_value' );
			update_user_meta( $user_id, $versioned_setting['wcshipping'], 'new_value' );
		}

		$this->migrator->migrate_label_purchase_settings();

		// Check if new settings are not overwritten
		foreach ( LegacySettingsMigrator::PURCHASE_SETTINGS as $versioned_setting ) {
			$this->assertEquals( 'new_value', get_user_meta( $user_id, $versioned_setting['wcshipping'], true ) );
		}
	}

	public function test_migrate_origin_address_when_legacy_origin_exists_and_wcshipping_origins_does_not_exist() {
		// Mock the get_option function to return a legacy origin when called with the legacy key
		// and to return null when called with the wcshipping key
		$legacy_origin = array(
			'is_verified' => true,
			'id'          => 'legacy_id',
		);

		update_option( LegacySettingsMigrator::ORIGIN_ADDRESS['legacy'], $legacy_origin );
		delete_option( LegacySettingsMigrator::ORIGIN_ADDRESS['wcshipping'] );

		$this->migrator->migrate_origin_address();

		$migrated_origin = get_option( LegacySettingsMigrator::ORIGIN_ADDRESS['wcshipping'] );

		$this->assertNotNull( $migrated_origin );
		$this->assertFalse( $migrated_origin[0]['is_verified'] );
		$this->assertEquals( 'wcst_copy_over', $migrated_origin[0]['id'] );
	}

	public function test_migrate_origin_address_when_legacy_origin_does_not_exist() {
		delete_option( LegacySettingsMigrator::ORIGIN_ADDRESS['legacy'] );
		delete_option( LegacySettingsMigrator::ORIGIN_ADDRESS['wcshipping'] );

		$this->migrator->migrate_origin_address();

		$migrated_origin = get_option( LegacySettingsMigrator::ORIGIN_ADDRESS['wcshipping'], false );

		$this->assertFalse( $migrated_origin );
	}

	public function test_migrate_origin_address_when_wcshipping_origins_already_exist() {

		$legacy_origin      = array(
			'is_verified' => true,
			'id'          => 'legacy_id',
		);
		$wcshipping_origins = array(
			array(
				'is_verified' => true,
				'id'          => 'wcshipping_id',
			),
		);

		update_option( LegacySettingsMigrator::ORIGIN_ADDRESS['legacy'], $legacy_origin );
		update_option( LegacySettingsMigrator::ORIGIN_ADDRESS['wcshipping'], $wcshipping_origins );

		$this->migrator->migrate_origin_address();

		$wcshipping_origins_after_migration = get_option( LegacySettingsMigrator::ORIGIN_ADDRESS['wcshipping'] );

		$this->assertEquals( $wcshipping_origins, $wcshipping_origins_after_migration );
	}

	public function test_package_migration_deregisters_wcst_compatibility_filters_if_needed() {
		// Set up initial values of legacy and WCShipping options.
		update_option(
			LegacySettingsMigrator::OPTIONS['legacy'],
			array(
				'packages'            => array( 'foo' ),
				'predefined_packages' => array( 'bar' ),
			)
		);
		update_option(
			LegacySettingsMigrator::OPTIONS['wcshipping'],
			array(
				'packages'            => array( 'quux' ),
				'predefined_packages' => array( 'corge' ),
			)
		);

		// Mock the compatibility class and register its filters.
		require_once WCSHIPPING_PHP_TESTS_DIR . '/Double/WC_Connect_Compatibility_WCShipping_Packages.php';
		add_filter( 'option_wc_connect_options', array( 'WC_Connect_Compatibility_WCShipping_Packages', 'intercept_packages_read' ) );
		add_filter( 'option_wc_connect_options', array( 'WC_Connect_Compatibility_WCShipping_Packages', 'intercept_predefined_packages_read' ) );

		// Assert intercepted values are returned when used outside of the migrator.
		$this->assertEquals( array( 'intercepted package' ), get_option( 'wc_connect_options' )['packages'] );
		$this->assertEquals( array( 'intercepted predefined package' ), get_option( 'wc_connect_options' )['predefined_packages'] );

		/*
		 * Despite the interception working, the migrator should temporarily deregister it during migration, getting
		 * the unfiltered value of `wc_connect_options`.
		 */
		$this->migrator->migrate_settings();
		$this->assertEquals( array( 'foo', 'quux' ), get_option( 'wcshipping_options' )['packages'] );
		$this->assertEquals( array( 'bar', 'corge' ), get_option( 'wcshipping_options' )['predefined_packages'] );

		// Assert package interception is enabled again.
		$this->assertEquals( array( 'intercepted package' ), get_option( 'wc_connect_options' )['packages'] );
		$this->assertEquals( array( 'intercepted predefined package' ), get_option( 'wc_connect_options' )['predefined_packages'] );
	}

	function tearDown(): void {
		parent::tearDown();
		foreach ( LegacySettingsMigrator::ORIGIN_ADDRESS as $address_key ) {
			delete_option( $address_key );
		}
		foreach ( LegacySettingsMigrator::OPTIONS as $option_key ) {
			delete_option( $option_key );
		}

		foreach ( LegacySettingsMigrator::PURCHASE_SETTINGS as $versioned_settings ) {
			foreach ( $versioned_settings as $setting_key ) {
				delete_option( $setting_key );
			}
		}

		foreach ( get_users() as $user ) {
			self::delete_user( $user->ID );
		}
	}

	private function create_user(): int {
		return wp_create_user( uniqid( 'username' ), uniqid( 'password' ), uniqid( 'testuser' ) . '@password.com' );
	}
}
