<?php

namespace Automattic\WCShipping\Tests\Shipments\Models;

use Automattic\WCShipping\Shipments\Models\ShipmentItem;
use WP_UnitTestCase;

class ShipmentItemTest extends WP_UnitTestCase {

	/**
	 * Test that ShipmentItem requires an ID
	 */
	public function test_shipment_item_requires_id() {
		$this->expectException( \InvalidArgumentException::class );
		$this->expectExceptionMessage( 'ShipmentItem requires an id' );

		new ShipmentItem( array( 'subItems' => array() ) );
	}

	/**
	 * Test basic ShipmentItem creation
	 */
	public function test_create_basic_shipment_item() {
		$item = new ShipmentItem(
			array(
				'id' => '20',
			)
		);

		$this->assertEquals( '20', $item->id );
		$this->assertEmpty( $item->subItems );
	}

	/**
	 * Test that subItems with objects are normalized to strings
	 */
	public function test_subitems_objects_normalized_to_strings() {
		$item = new ShipmentItem(
			array(
				'id'       => '20',
				'subItems' => array(
					array(
						'id'       => '20-sub-0',
						'quantity' => 1,
						'parentId' => 20,
					),
					array(
						'id'       => '20-sub-1',
						'quantity' => 1,
						'parentId' => 20,
					),
					array(
						'id'       => '20-sub-2',
						'quantity' => 1,
						'parentId' => 20,
					),
				),
			)
		);

		// subItems should be normalized to string array
		$this->assertEquals( array( '20-sub-0', '20-sub-1', '20-sub-2' ), $item->subItems );
		$this->assertTrue( $item->has_valid_sub_items() );
	}

	/**
	 * Test that subItems already as strings remain unchanged
	 */
	public function test_subitems_strings_remain_unchanged() {
		$item = new ShipmentItem(
			array(
				'id'       => '20',
				'subItems' => array( '20-sub-0', '20-sub-1' ),
			)
		);

		$this->assertEquals( array( '20-sub-0', '20-sub-1' ), $item->subItems );
		$this->assertTrue( $item->has_valid_sub_items() );
	}

	/**
	 * Test mixed format subItems (objects as stdClass)
	 */
	public function test_subitems_stdclass_normalized() {
		$sub1           = new \stdClass();
		$sub1->id       = '20-sub-0';
		$sub1->quantity = 1;

		$item = new ShipmentItem(
			array(
				'id'       => '20',
				'subItems' => array( $sub1, '20-sub-1' ),
			)
		);

		$this->assertEquals( array( '20-sub-0', '20-sub-1' ), $item->subItems );
	}

	/**
	 * Test to_array only includes minimal data
	 */
	public function test_to_array_minimal_data() {
		$item = new ShipmentItem(
			array(
				'id'         => '20',
				'subItems'   => array( '20-sub-0', '20-sub-1' ),
				// These fields should be ignored
				'quantity'   => 2,
				'name'       => 'Test Product',
				'price'      => '45.00',
				'product_id' => 83,
				'meta'       => array( 'customs_info' => array( 'description' => 'Test' ) ),
			)
		);

		$array = $item->to_array();

		// Should only have id and subItems
		$this->assertArrayHasKey( 'id', $array );
		$this->assertArrayHasKey( 'subItems', $array );
		$this->assertCount( 2, $array ); // Only 2 fields

		$this->assertEquals( '20', $array['id'] );
		$this->assertEquals( array( '20-sub-0', '20-sub-1' ), $array['subItems'] );
	}

	/**
	 * Test that invalid subItems are skipped
	 */
	public function test_invalid_subitems_skipped() {
		$item = new ShipmentItem(
			array(
				'id'       => '20',
				'subItems' => array(
					'valid-string',
					array( 'no-id-field' => 'test' ),  // Invalid - no id
					array( 'id' => 'valid-object' ),   // Valid
					123,                           // Invalid - number
				),
			)
		);

		// Only valid items should be kept
		$this->assertEquals( array( 'valid-string', 'valid-object' ), $item->subItems );
	}

	/**
	 * Test that item's id remains an int for regular items
	 */
	public function test_item_id_remains_int_for_regular_items() {
		$item = new ShipmentItem(
			array(
				'id' => '123', // String input
			)
		);

		// Should be converted to int for regular items
		$this->assertIsInt( $item->id );
		$this->assertEquals( 123, $item->id );
	}

	/**
	 * Test that item's id remains a string for sub-items
	 */
	public function test_item_id_remains_string_for_sub_items() {
		$item = new ShipmentItem(
			array(
				'id'       => '20',
				'subItems' => array( '20-sub-0', '20-sub-1' ),
			)
		);

		$this->assertIsInt( $item->id );
		$this->assertEquals( 20, $item->id );
		$this->assertEquals( array( '20-sub-0', '20-sub-1' ), $item->subItems );
	}

	/**
	 * Test that numeric id input is converted to int
	 */
	public function test_numeric_id_input_converted_to_int() {
		$item = new ShipmentItem(
			array(
				'id' => 456, // Numeric input
			)
		);

		$this->assertIsInt( $item->id );
		$this->assertEquals( 456, $item->id );
	}

	public function test_string_id_input_converted_to_int() {
		$item = new ShipmentItem(
			array(
				'id' => '456', // String input
			)
		);

		$this->assertIsInt( $item->id );
		$this->assertEquals( 456, $item->id );
	}
}
