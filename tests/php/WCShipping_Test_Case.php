<?php

namespace Automattic\WCShipping\Tests\php;

use WC_Unit_Test_Case;
use WP_REST_Request;
use WP_REST_Server;

/**
 * Base class for all Automattic\WCShipping unit tests.
 *
 * Extends WC_Unit_Test_Case and PHPUnit\Framework\TestCase.
 */
abstract class WCShipping_Test_Case extends WC_Unit_Test_Case {

	public function setUp(): void {
		parent::setUp();
		add_filter( 'wcshipping_user_can_manage_labels', '__return_true' );
	}

	/**
	 *  Flushes the REST server.
	 *  This is needed because the REST server is a singleton and is not reset between tests.
	 *  Calling this method will ensure that the REST server is reset between tests and new routes are registered.
	 *
	 */
	protected function flushRestServer(): void {
		global $wp_rest_server;
		$wp_rest_server = new WP_REST_Server();
	}

	protected function create_request( string $endpoint, $method = 'POST', $id = null ): WP_REST_Request {
		$request = new WP_REST_Request( $method, $endpoint . ( $id ? '/' . $id : '' ) );
		$request->set_header( 'content-type', 'application/json' );
		$request->set_header( 'x_wp_nonce', wp_create_nonce( 'wp_rest' ) );

		return $request;
	}

	function tearDown(): void {
		parent::tearDown();
		$this->flushRestServer();
	}

	/**
	 * Helper method to mock WordPress functions.
	 *
	 * @param string $function_name The name of the function to mock.
	 * @param callable $callback The callback that should be executed when the function is called.
	 */
	protected function mockFunction( $function_name, $callback ) {
		$this->addFilter( "pre_{$function_name}", $callback );
	}

	/**
	 * Add a filter to simulate WordPress hooks for the purpose of mocking.
	 *
	 * @param string $hook The name of the hook.
	 * @param callable $callback The callback function.
	 */
	protected function addFilter( $hook, $callback ) {
		add_filter( $hook, $callback );
	}
}
