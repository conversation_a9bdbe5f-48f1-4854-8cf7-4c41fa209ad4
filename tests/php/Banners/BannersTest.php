<?php
/**
 * Class BannersTest
 * @package Automattic\WCShipping
 */

namespace Automattic\WCShipping\Tests\Banners;

use Automattic\WCShipping\Banners\Banners;
use Automattic\WCShipping\Connect\WC_Connect_Service_Schemas_Store;
use Automattic\WCShipping\Connect\WC_Connect_Logger;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\Tracks;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * Tests for Banners.
 */
class BannersTest extends WCShipping_Test_Case {

	protected $user_id = 0;
	private $mock_schemas_store;
	private $mock_logger;
	private $feature_banners;

	public function setUp(): void {
		parent::setUp();

		// Create test user
		$this->user_id = $this->factory->user->create(
			array(
				'role' => 'administrator',
			)
		);
		wp_set_current_user( $this->user_id );

		// Mock dependencies
		$this->mock_schemas_store = $this->createMock( WC_Connect_Service_Schemas_Store::class );
		$this->mock_logger        = $this->createMock( WC_Connect_Logger::class );

		// Create instance
		$this->feature_banners = new Banners( $this->mock_schemas_store, $this->mock_logger );

		// Add filters to override wp_send_json functions in test environment
		add_filter( 'wp_die_ajax_handler', array( $this, 'get_wp_die_handler' ), 1, 1 );
	}

	public function tearDown(): void {
		parent::tearDown();

		// Clean up user meta
		delete_user_meta( $this->user_id, Banners::DISMISSED_BANNERS_META_KEY );

		// Remove the filter
		remove_filter( 'wp_die_ajax_handler', array( $this, 'get_wp_die_handler' ), 1 );

		// Clean up any POST/SERVER variables and filters
		$_POST = array();
		if ( isset( $_SERVER['REQUEST_METHOD'] ) ) {
			unset( $_SERVER['REQUEST_METHOD'] );
		}
		remove_filter( 'wp_doing_ajax', '__return_true' );
	}

	/**
	 * Custom wp_die handler for AJAX tests that prevents actual termination
	 *
	 * @param string $name The name of the wp_die handler.
	 * @return callable The custom handler.
	 */
	public function get_wp_die_handler( $name ) {
		return array( $this, 'wp_die_handler' );
	}

	/**
	 * Handler that catches wp_die calls and prevents actual termination
	 *
	 * @param string $message The wp_die message.
	 * @param string $title   The wp_die title.
	 * @param array  $args    The wp_die args.
	 */
	public function wp_die_handler( $message, $title = '', $args = array() ) {
		// Do nothing - just prevent actual termination
	}

	/**
	 * Test that should_show_feature_banners returns true for admin users
	 */
	public function test_should_show_feature_banners_for_admin() {
		// Set up WordPress environment - set to a valid target screen
		set_current_screen( 'plugins' );

		// Add manage_woocommerce capability to the test user
		$user = wp_get_current_user();
		$user->add_cap( 'manage_woocommerce' );

		$this->assertTrue( $this->invoke_private_method( $this->feature_banners, 'should_show_feature_banners' ) );
	}

	/**
	 * Test that should_show_feature_banners returns false for non-admin users
	 */
	public function test_should_show_feature_banners_for_non_admin() {
		// Create non-admin user
		$subscriber_id = $this->factory->user->create( array( 'role' => 'subscriber' ) );
		wp_set_current_user( $subscriber_id );

		$this->assertFalse( $this->invoke_private_method( $this->feature_banners, 'should_show_feature_banners' ) );
	}

	/**
	 * Test get_active_banners returns empty array when no features available
	 */
	public function test_get_active_banners_with_no_features() {
		// Mock the get_features method to return null
		$mock_banners = $this->getMockBuilder( Banners::class )
			->setConstructorArgs( array( $this->mock_schemas_store, $this->mock_logger ) )
			->onlyMethods( array( 'get_features' ) )
			->getMock();

		$mock_banners->method( 'get_features' )->willReturn( null );

		$result = $mock_banners->get_active_banners();

		$this->assertIsArray( $result );
		$this->assertEmpty( $result );
	}

	/**
	 * Test get_active_banners filters non-announcement features
	 */
	public function test_get_active_banners_filters_non_announcements() {
		$features = (object) array(
			'feature1' => (object) array(
				'type'    => 'announcement',
				'content' => (object) array(
					'title' => 'Test Banner',
				),
			),
			'feature2' => (object) array(
				'type'    => 'other',
				'content' => (object) array(
					'title' => 'Not a Banner',
				),
			),
		);

		// Mock the get_features method
		$mock_banners = $this->getMockBuilder( Banners::class )
			->setConstructorArgs( array( $this->mock_schemas_store, $this->mock_logger ) )
			->onlyMethods( array( 'get_features' ) )
			->getMock();

		$mock_banners->method( 'get_features' )->willReturn( $features );

		$result = $mock_banners->get_active_banners();

		$this->assertArrayHasKey( 'feature1', $result );
		$this->assertArrayNotHasKey( 'feature2', $result );
	}

	/**
	 * Test get_active_banners filters dismissed banners
	 */
	public function test_get_active_banners_filters_dismissed() {
		$features = (object) array(
			'banner1' => (object) array(
				'type'    => 'announcement',
				'content' => (object) array(
					'title' => 'Banner 1',
				),
			),
			'banner2' => (object) array(
				'type'    => 'announcement',
				'content' => (object) array(
					'title' => 'Banner 2',
				),
			),
		);

		// Mock the get_features method
		$mock_banners = $this->getMockBuilder( Banners::class )
			->setConstructorArgs( array( $this->mock_schemas_store, $this->mock_logger ) )
			->onlyMethods( array( 'get_features' ) )
			->getMock();

		$mock_banners->method( 'get_features' )->willReturn( $features );

		// Dismiss banner1
		$mock_banners->on_banner_dismissed( 'banner1' );

		$result = $mock_banners->get_active_banners();

		$this->assertArrayNotHasKey( 'banner1', $result );
		$this->assertArrayHasKey( 'banner2', $result );
	}

	/**
	 * Test banner dismissal functionality
	 */
	public function test_on_banner_dismissed() {
		$banner_id = 'test_banner';

		// Initially not dismissed
		$this->assertFalse( $this->feature_banners->is_banner_dismissed( $banner_id ) );

		// Dismiss banner
		$result = $this->feature_banners->on_banner_dismissed( $banner_id );

		// update_user_meta returns meta ID on success, which evaluates to true
		$this->assertNotFalse( $result );
		$this->assertTrue( $this->feature_banners->is_banner_dismissed( $banner_id ) );
	}

	/**
	 * Test that dismissing same banner twice doesn't duplicate entries
	 */
	public function test_on_banner_dismissed_duplicate() {
		$banner_id = 'test_banner';

		// Dismiss banner twice
		$this->feature_banners->on_banner_dismissed( $banner_id );
		$this->feature_banners->on_banner_dismissed( $banner_id );

		$dismissed_banners = get_user_meta( $this->user_id, Banners::DISMISSED_BANNERS_META_KEY, true );

		// Should only appear once
		$this->assertEquals( array( $banner_id ), $dismissed_banners );
	}

	/**
	 * Test AJAX dismiss functionality
	 */
	public function test_dismiss_feature_banner_ajax() {
		$banner_id = 'test_banner';

		// Create a mock that simulates valid nonce
		$mock_banners = $this->getMockBuilder( Banners::class )
			->setConstructorArgs( array( $this->mock_schemas_store, $this->mock_logger ) )
			->onlyMethods( array( 'verify_dismiss_nonce' ) )
			->getMock();

		// Mock the nonce verification to return true (simulating valid nonce)
		$mock_banners->method( 'verify_dismiss_nonce' )->willReturn( true );

		// Set up AJAX request
		$_POST['banner_id'] = $banner_id;
		$_POST['nonce']     = wp_create_nonce( 'wcshipping_dismiss_feature_banner' );

		// Mock Tracks to avoid actual tracking
		$this->addFilter( 'wcshipping_feature_banner_dismissed', '__return_true' );

		// Set up AJAX environment
		$_SERVER['REQUEST_METHOD'] = 'POST';
		add_filter( 'wp_doing_ajax', '__return_true' );

		// Capture output
		ob_start();
		$mock_banners->dismiss_feature_banner();
		$output = ob_get_clean();

		// Verify the banner was dismissed
		$this->assertTrue( $mock_banners->is_banner_dismissed( $banner_id ) );

		// Clean up
		remove_filter( 'wp_doing_ajax', '__return_true' );
		unset( $_SERVER['REQUEST_METHOD'] );
	}

	/**
	 * Test AJAX dismiss with invalid nonce
	 */
	public function test_dismiss_feature_banner_ajax_invalid_nonce() {
		$banner_id = 'test_banner_invalid_nonce';

		// Create a mock of the Banners class that simulates nonce failure
		$mock_banners = $this->getMockBuilder( Banners::class )
			->setConstructorArgs( array( $this->mock_schemas_store, $this->mock_logger ) )
			->onlyMethods( array( 'verify_dismiss_nonce' ) )
			->getMock();

		// Mock the nonce verification to always return false (simulating invalid nonce)
		$mock_banners->method( 'verify_dismiss_nonce' )->willReturn( false );

		// Ensure banner is not already dismissed
		$this->assertFalse( $mock_banners->is_banner_dismissed( $banner_id ) );

		// Set up AJAX request
		$_POST['banner_id']        = $banner_id;
		$_POST['nonce']            = 'invalid_nonce_value';
		$_SERVER['REQUEST_METHOD'] = 'POST';
		add_filter( 'wp_doing_ajax', '__return_true' );

		// Capture output
		ob_start();
		$mock_banners->dismiss_feature_banner();
		ob_end_clean();

		// Banner should NOT be dismissed because nonce verification failed
		$this->assertFalse( $mock_banners->is_banner_dismissed( $banner_id ) );

		// Clean up
		remove_filter( 'wp_doing_ajax', '__return_true' );
		unset( $_SERVER['REQUEST_METHOD'] );
		unset( $_POST['banner_id'] );
		unset( $_POST['nonce'] );
	}

	/**
	 * Test nonce verification method directly
	 */
	public function test_verify_dismiss_nonce() {
		// Test with no nonce at all
		$_POST   = array();
		$result1 = $this->invoke_private_method( $this->feature_banners, 'verify_dismiss_nonce' );
		$this->assertFalse( $result1, 'Should return false when no nonce provided' );

		// Test with empty nonce
		$_POST['nonce'] = '';
		$result2        = $this->invoke_private_method( $this->feature_banners, 'verify_dismiss_nonce' );
		$this->assertFalse( $result2, 'Should return false when empty nonce provided' );

		// Test with obviously invalid nonce
		$_POST['nonce'] = 'clearly_invalid_nonce';
		$result3        = $this->invoke_private_method( $this->feature_banners, 'verify_dismiss_nonce' );
		$this->assertFalse( $result3, 'Should return false when invalid nonce provided' );

		// Clean up
		$_POST = array();
	}

	/**
	 * Test AJAX tracking functionality
	 */
	public function test_track_feature_banner_click_ajax() {
		$banner_id     = 'test_banner';
		$button_action = 'Learn More';

		// Set up AJAX request
		$_POST['banner_id']     = $banner_id;
		$_POST['button_action'] = $button_action;
		$_POST['nonce']         = wp_create_nonce( 'wcshipping_track_feature_banner_click' );

		// Mock Tracks to avoid actual tracking
		$this->addFilter( 'wcshipping_feature_banner_button_clicked', '__return_true' );

		// Set up AJAX environment
		$_SERVER['REQUEST_METHOD'] = 'POST';
		add_filter( 'wp_doing_ajax', '__return_true' );

		// Capture output
		ob_start();
		$this->feature_banners->track_feature_banner_click();
		$output = ob_get_clean();

		// We can't easily verify the tracking was called in the test environment
		// but if we get here without error, the method executed successfully
		$this->assertTrue( true, 'AJAX tracking completed successfully' );

		// Clean up
		remove_filter( 'wp_doing_ajax', '__return_true' );
		unset( $_SERVER['REQUEST_METHOD'] );
	}

	/**
	 * Test CTA button style property is used correctly
	 */
	public function test_cta_button_style_property() {
		$banner_data = (object) array(
			'content' => (object) array(
				'title'       => 'Test Banner',
				'description' => 'Test description',
				'cta'         => array(
					(object) array(
						'title' => 'Primary Button',
						'url'   => 'https://example.com/primary',
						'style' => 'primary',
					),
					(object) array(
						'title' => 'Secondary Button',
						'url'   => 'https://example.com/secondary',
						'style' => 'secondary',
					),
					(object) array(
						'title' => 'Default Button',
						'url'   => 'https://example.com/default',
						// No style property - should default to primary
					),
				),
			),
		);

		ob_start();
		$this->invoke_private_method( $this->feature_banners, 'render_banner', 'test_cta_styles', $banner_data );
		$output = ob_get_clean();

		// Test primary button class
		$this->assertStringContainsString( 'button button-primary', $output );
		$this->assertStringContainsString( 'Primary Button', $output );

		// Test secondary button class
		$this->assertStringContainsString( 'button button-secondary', $output );
		$this->assertStringContainsString( 'Secondary Button', $output );

		// Test default button (should be primary when style not specified)
		$this->assertStringContainsString( 'Default Button', $output );

		// Count button-primary vs button-secondary occurrences
		$primary_count   = substr_count( $output, 'button button-primary' );
		$secondary_count = substr_count( $output, 'button button-secondary' );

		$this->assertEquals( 2, $primary_count, 'Should have 2 primary buttons (explicit + default)' );
		$this->assertEquals( 1, $secondary_count, 'Should have 1 secondary button' );
	}

	/**
	 * Test get_features() extracts from service schemas correctly
	 */
	public function test_get_features_extracts_from_service_schemas() {
		$services = (object) array(
			'features'   => (object) array(
				'banner1' => (object) array( 'type' => 'announcement' ),
				'banner2' => (object) array( 'type' => 'announcement' ),
			),
			'other_data' => 'some_value',
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )->willReturn( $services );

		$result = $this->invoke_private_method( $this->feature_banners, 'get_features' );

		$this->assertIsObject( $result );
		$this->assertObjectHasProperty( 'banner1', $result );
		$this->assertObjectHasProperty( 'banner2', $result );
		$this->assertEquals( 'announcement', $result->banner1->type );
	}

	/**
	 * Test get_features() returns null when service schemas missing features
	 */
	public function test_get_features_returns_null_when_no_features_in_schemas() {
		$services = (object) array(
			'other_data' => 'value',
			'services'   => array( 'service1', 'service2' ),
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )->willReturn( $services );

		$result = $this->invoke_private_method( $this->feature_banners, 'get_features' );

		$this->assertNull( $result );
	}

	/**
	 * Test get_features() handles null service schemas
	 */
	public function test_get_features_handles_null_service_schemas() {
		$this->mock_schemas_store->method( 'get_service_schemas' )->willReturn( null );

		$result = $this->invoke_private_method( $this->feature_banners, 'get_features' );

		$this->assertNull( $result );
	}

	/**
	 * Test get_features() handles false service schemas
	 */
	public function test_get_features_handles_false_service_schemas() {
		$this->mock_schemas_store->method( 'get_service_schemas' )->willReturn( false );

		$result = $this->invoke_private_method( $this->feature_banners, 'get_features' );

		$this->assertNull( $result );
	}

	/**
	 * Test get_features() handles features property as non-object
	 */
	public function test_get_features_handles_features_as_non_object() {
		$services = (object) array(
			'features'   => 'not_an_object',
			'other_data' => 'value',
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )->willReturn( $services );

		$result = $this->invoke_private_method( $this->feature_banners, 'get_features' );

		$this->assertNull( $result );
	}

	/**
	 * Test get_features() handles features property as array
	 */
	public function test_get_features_handles_features_as_array() {
		$services = (object) array(
			'features'   => array( 'banner1', 'banner2' ),
			'other_data' => 'value',
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )->willReturn( $services );

		$result = $this->invoke_private_method( $this->feature_banners, 'get_features' );

		$this->assertNull( $result );
	}

	/**
	 * Test get_active_banners() works with new feature extraction
	 */
	public function test_get_active_banners_with_service_schemas_integration() {
		$services = (object) array(
			'features' => (object) array(
				'promo_banner'  => (object) array(
					'type'    => 'announcement',
					'content' => (object) array(
						'title'       => 'New Feature Available',
						'description' => 'Check out our latest feature',
					),
				),
				'update_banner' => (object) array(
					'type'    => 'announcement',
					'content' => (object) array(
						'title'       => 'System Update',
						'description' => 'Important system update',
					),
				),
			),
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )->willReturn( $services );

		$result = $this->feature_banners->get_active_banners();

		$this->assertIsArray( $result );
		$this->assertCount( 2, $result );
		$this->assertArrayHasKey( 'promo_banner', $result );
		$this->assertArrayHasKey( 'update_banner', $result );
	}

	/**
	 * Test screen targeting functionality for products list page
	 */
	public function test_should_display_on_products_list_page() {
		// Mock screen for products list
		$screen            = new \stdClass();
		$screen->post_type = 'product';
		$screen->base      = 'edit';

		$result = $this->invoke_private_method( $this->feature_banners, 'should_display_feature_banner_on_screen', $screen );

		$this->assertTrue( $result );
	}

	/**
	 * Test screen targeting functionality for orders list page
	 */
	public function test_should_display_on_orders_list_page() {
		// Mock screen for orders list
		$screen            = new \stdClass();
		$screen->post_type = 'shop_order';
		$screen->base      = 'edit';

		$result = $this->invoke_private_method( $this->feature_banners, 'should_display_feature_banner_on_screen', $screen );

		$this->assertTrue( $result );
	}

	/**
	 * Test screen targeting functionality for edit order page
	 */
	public function test_should_display_on_edit_order_page() {
		// Mock screen for edit order
		$screen            = new \stdClass();
		$screen->post_type = 'shop_order';
		$screen->base      = 'post';

		$result = $this->invoke_private_method( $this->feature_banners, 'should_display_feature_banner_on_screen', $screen );

		$this->assertTrue( $result );
	}

	/**
	 * Test screen targeting functionality for HPOS orders page
	 */
	public function test_should_display_on_hpos_orders_page() {
		// Mock screen for HPOS orders
		$screen       = new \stdClass();
		$screen->base = 'woocommerce_page_wc-orders';

		$result = $this->invoke_private_method( $this->feature_banners, 'should_display_feature_banner_on_screen', $screen );

		$this->assertTrue( $result );
	}

	/**
	 * Test screen targeting functionality for WooCommerce settings page
	 */
	public function test_should_display_on_wc_settings_page() {
		// Mock screen for WC settings
		$screen       = new \stdClass();
		$screen->base = 'woocommerce_page_wc-settings';

		$result = $this->invoke_private_method( $this->feature_banners, 'should_display_feature_banner_on_screen', $screen );

		$this->assertTrue( $result );
	}

	/**
	 * Test screen targeting functionality for plugins page
	 */
	public function test_should_display_on_plugins_page() {
		// Mock screen for plugins
		$screen       = new \stdClass();
		$screen->base = 'plugins';

		$result = $this->invoke_private_method( $this->feature_banners, 'should_display_feature_banner_on_screen', $screen );

		$this->assertTrue( $result );
	}

	/**
	 * Test screen targeting functionality for featured extensions page
	 */
	public function test_should_display_on_featured_extensions_page() {
		// Mock GET parameter
		$_GET['section'] = 'featured';

		// Mock screen for featured extensions
		$screen       = new \stdClass();
		$screen->base = 'woocommerce_page_wc-addons';

		$result = $this->invoke_private_method( $this->feature_banners, 'should_display_feature_banner_on_screen', $screen );

		$this->assertTrue( $result );

		// Clean up
		unset( $_GET['section'] );
	}

	/**
	 * Test screen targeting functionality for shipping extensions page
	 */
	public function test_should_display_on_shipping_extensions_page() {
		// Mock GET parameter
		$_GET['section'] = 'shipping_methods';

		// Mock screen for shipping extensions
		$screen       = new \stdClass();
		$screen->base = 'woocommerce_page_wc-addons';

		$result = $this->invoke_private_method( $this->feature_banners, 'should_display_feature_banner_on_screen', $screen );

		$this->assertTrue( $result );

		// Clean up
		unset( $_GET['section'] );
	}

	/**
	 * Test screen targeting should not display on non-targeted pages
	 */
	public function test_should_not_display_on_non_targeted_pages() {
		// Mock screen for dashboard
		$screen       = new \stdClass();
		$screen->base = 'dashboard';

		$result = $this->invoke_private_method( $this->feature_banners, 'should_display_feature_banner_on_screen', $screen );

		$this->assertFalse( $result );

		// Test posts page
		$screen->post_type = 'post';
		$screen->base      = 'edit';

		$result = $this->invoke_private_method( $this->feature_banners, 'should_display_feature_banner_on_screen', $screen );

		$this->assertFalse( $result );
	}

	/**
	 * Test screen targeting filter allows adding custom screens
	 */
	public function test_screen_targeting_filter_allows_custom_screens() {
		// Add custom screen via filter
		$filter_callback = function ( $screens ) {
			$screens['custom_page'] = array(
				'base' => 'custom_page_base',
			);
			return $screens;
		};
		add_filter( 'wcshipping_feature_banner_target_screens', $filter_callback );

		// Mock screen for custom page
		$screen       = new \stdClass();
		$screen->base = 'custom_page_base';

		$result = $this->invoke_private_method( $this->feature_banners, 'should_display_feature_banner_on_screen', $screen );

		$this->assertTrue( $result );

		// Clean up
		remove_filter( 'wcshipping_feature_banner_target_screens', $filter_callback );
	}

	/**
	 * Test screen_matches_target method with various configurations
	 */
	public function test_screen_matches_target() {
		// Test base matching
		$screen       = new \stdClass();
		$screen->base = 'plugins';

		$target = array( 'base' => 'plugins' );
		$result = $this->invoke_private_method( $this->feature_banners, 'screen_matches_target', $screen, $target );
		$this->assertTrue( $result );

		$target = array( 'base' => 'dashboard' );
		$result = $this->invoke_private_method( $this->feature_banners, 'screen_matches_target', $screen, $target );
		$this->assertFalse( $result );

		// Test post_type matching
		$screen->post_type = 'product';
		$screen->base      = 'edit';

		$target = array(
			'post_type' => 'product',
			'base'      => 'edit',
		);
		$result = $this->invoke_private_method( $this->feature_banners, 'screen_matches_target', $screen, $target );
		$this->assertTrue( $result );

		$target = array(
			'post_type' => 'post',
			'base'      => 'edit',
		);
		$result = $this->invoke_private_method( $this->feature_banners, 'screen_matches_target', $screen, $target );
		$this->assertFalse( $result );
	}

	/**
	 * Test section parameter matching
	 */
	public function test_screen_matches_target_with_section() {
		$screen       = new \stdClass();
		$screen->base = 'woocommerce_page_wc-addons';

		// Test with matching section
		$_GET['section'] = 'featured';
		$target          = array(
			'base'    => 'woocommerce_page_wc-addons',
			'section' => 'featured',
		);
		$result          = $this->invoke_private_method( $this->feature_banners, 'screen_matches_target', $screen, $target );
		$this->assertTrue( $result );

		// Test with non-matching section
		$target = array(
			'base'    => 'woocommerce_page_wc-addons',
			'section' => 'other',
		);
		$result = $this->invoke_private_method( $this->feature_banners, 'screen_matches_target', $screen, $target );
		$this->assertFalse( $result );

		// Clean up
		unset( $_GET['section'] );
	}

	/**
	 * Helper method to invoke private methods for testing
	 */
	private function invoke_private_method( $object, $method_name, ...$args ) {
		$reflection = new \ReflectionClass( $object );
		$method     = $reflection->getMethod( $method_name );
		$method->setAccessible( true );
		return $method->invoke( $object, ...$args );
	}
}
