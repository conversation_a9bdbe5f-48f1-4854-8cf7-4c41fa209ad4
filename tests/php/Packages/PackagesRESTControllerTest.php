<?php

namespace Automattic\WCShipping\Tests\php\Packages;

use Automattic\WCShipping\Connect\WC_Connect_API_Client;
use Automattic\WCShipping\Connect\WC_Connect_API_Client_Live;
use Automattic\WCShipping\Connect\WC_Connect_Package_Settings;
use Automattic\WCShipping\Connect\WC_Connect_Logger;
use Automattic\WCShipping\Connect\WC_Connect_Service_Schemas_Store;
use Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store;
use Automattic\WCShipping\Packages\PackagesRESTController;
use Automattic\WCShipping\Packages\Package;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use WP_REST_Request;

/**
 * Unit test for PackagesRESTController
 */
class PackagesRESTControllerTest extends WCShipping_Test_Case {

	const REST_ENDPOINT = '/wcshipping/v1/packages';

	protected WC_Connect_API_Client_Live $api_client_mock;

	protected WC_Connect_Logger $connect_logger_mock;

	protected WC_Connect_Service_Schemas_Store $service_schemas_store_mock;

	protected WC_Connect_Service_Settings_Store $settings_store;

	protected WC_Connect_Package_Settings $package_settings;
	/**
	 * @inherit
	 */
	public static function set_up_before_class() {
		require_once __DIR__ . '/../../../classes/legacy-api-controllers/class-wc-rest-connect-base-controller.php';
		require_once __DIR__ . '/../../../classes/legacy-api-controllers/class-wc-rest-connect-base-controller.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-service-schemas-store.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-logger.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-service-settings-store.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-api-client-live.php';
	}

	/**
	 * Setup the test case.
	 *
	 * @see WCShipping_Test_Case::setUp()
	 */
	public function setUp(): void {
		parent::setUp();

		$this->api_client_mock            = $this->createMock( WC_Connect_API_Client_Live::class );
		$this->connect_logger_mock        = $this->createMock( WC_Connect_Logger::class );
		$this->service_schemas_store_mock = $this->createMock( WC_Connect_Service_Schemas_Store::class );
		$this->package_settings           = $this->createMock( WC_Connect_Package_Settings::class );
		$this->settings_store             = new WC_Connect_Service_Settings_Store(
			$this->service_schemas_store_mock,
			$this->api_client_mock,
			$this->connect_logger_mock
		);
		$this->package_settings->method( 'get' )
			->willReturnCallback(
				function () {
					// Get the real package settings
					$real_settings = ( new WC_Connect_Package_Settings(
						$this->settings_store,
						$this->service_schemas_store_mock
					) )->get( null );

					// Override only the storeOptions
					return array_merge(
						$real_settings,
						array(
							'storeOptions' => array(
								'currency_symbol' => '$',
								'dimension_unit'  => 'in',
								'weight_unit'     => 'lbs',
								'origin_country'  => 'US',
							),
						)
					);
				}
			);

		$controller = new PackagesRESTController( $this->settings_store, $this->package_settings );
		add_action(
			'rest_api_init',
			function () use ( $controller ) {
				$controller->register_routes();
			}
		);
		do_action( 'rest_api_init' );
	}

	/**
	 * Test that creating custom packages updates the custom packages in settings store while predefined packages remain the same.
	 */
	public function test_creating_packages_with_only_custom_packages_updates_packages_in_settings_store() {
		$package_1 = array(
			'id'              => 'my_package_1',
			'is_user_defined' => true,
			'name'            => 'Fun box',
			'dimensions'      => '10 x 20 x 5',
			'boxWeight'       => 0.23,
			'maxWeight'       => 0,
			'type'            => Package::TYPE_BOX,
		);
		$package_2 = array(
			'id'              => 'my_package_2',
			'is_user_defined' => true,
			'name'            => 'Fun envelope',
			'dimensions'      => '12 x 16 x 11',
			'boxWeight'       => 0.5,
			'maxWeight'       => 0,
			'type'            => Package::TYPE_ENVELOPE,
		);

		$existing_packages = array( $package_1 );
		$new_packages      = array( $package_2 );
		$this->settings_store->update_packages( $existing_packages );

		$predefined_packages_before_creation = $this->settings_store->get_predefined_packages();

		// When.
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body(
			wp_json_encode(
				array(
					'custom' => $new_packages,
				)
			)
		);
		$response = rest_get_server()->dispatch( $request );

		// Then.
		$this->assertEquals( 200, $response->status );

		$actual_packages_after_creation   = $this->settings_store->get_packages();
		$expected_packages_after_creation = array( $package_1, $package_2 );
		$this->assertEquals( $expected_packages_after_creation, $actual_packages_after_creation );
		$this->assertEquals( $expected_packages_after_creation, $response->data['custom'] );

		// Predefined packages should not change from the creation request.
		$predefined_packages_after_creation = $this->settings_store->get_predefined_packages();
		$this->assertEquals( $predefined_packages_before_creation, $predefined_packages_after_creation );
		$this->assertEquals( $predefined_packages_before_creation, $response->data['predefined'] );
	}

	/**
	 * Test that creating predefined packages updates the predefined packages in settings store while custom packages remain the same.
	 */
	public function test_creating_packages_with_only_predefined_packages_updates_predefined_packages_in_settings_store() {

		$existing_predefined_packages = array(
			'usps' => array(
				'flat_envelope',
				'padded_flat_envelope',
			),
		);
		$new_predefined_packages      = array(
			'dhlexpress' => array(
				'SmallPaddedPouch',
				'Box2Cube',
			),
			'usps'       => array(
				'legal_flat_envelope',
			),
		);
		$this->settings_store->update_predefined_packages( $existing_predefined_packages );

		$custom_packages_before_creation = $this->settings_store->get_packages();

		// When.
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body(
			wp_json_encode(
				array(
					'predefined' => $new_predefined_packages,
				)
			)
		);

		$response = rest_do_request( $request );

		// Then.
		$this->assertEquals( 200, $response->status );

		$actual_predefined_packages_after_creation   = $this->settings_store->get_predefined_packages();
		$expected_predefined_packages_after_creation = array(
			'usps'       => array(
				'flat_envelope',
				'padded_flat_envelope',
				'legal_flat_envelope',
			),
			'dhlexpress' => array(
				'SmallPaddedPouch',
				'Box2Cube',
			),
		);
		$this->assertEquals( $expected_predefined_packages_after_creation, $actual_predefined_packages_after_creation );
		$this->assertEquals( $expected_predefined_packages_after_creation, $response->data['predefined'] );

		// Custom packages should not change from the creation request.
		$custom_packages_after_creation = $this->settings_store->get_packages();
		$this->assertEquals( $custom_packages_before_creation, $custom_packages_after_creation );
		$this->assertEquals( $custom_packages_before_creation, $response->data['custom'] );
	}

	/**
	 * Test that creating both custom and predefined packages updates the both package types in settings store.
	 */
	public function test_creating_packages_with_both_custom_and_predefined_packages_updates_both_types_of_packages_in_settings_store() {
		// Set up custom packages.
		$package_1 = array(
			'id'              => 'my_package_1',
			'is_user_defined' => true,
			'name'            => 'Fun box',
			'dimensions'      => '10 x 20 x 5',
			'boxWeight'       => 0.23,
			'maxWeight'       => 0,
		);
		$package_2 = array(
			'id'              => 'my_package_2',
			'is_user_defined' => true,
			'name'            => 'Fun envelope',
			'dimensions'      => '12 x 16 x 11',
			'boxWeight'       => 0.5,
			'maxWeight'       => 0,
		);
		$this->settings_store->update_packages( array( $package_1 ) );

		// Set up predefined packages.
		$existing_predefined_packages = array(
			'usps' => array(
				'flat_envelope',
				'padded_flat_envelope',
			),
		);
		$new_predefined_packages      = array(
			'dhlexpress' => array(
				'SmallPaddedPouch',
				'Box2Cube',
			),
			'usps'       => array(
				'legal_flat_envelope',
			),
		);
		$this->settings_store->update_predefined_packages( $existing_predefined_packages );

		// When.
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body(
			wp_json_encode(
				array(
					'predefined' => $new_predefined_packages,
					'custom'     => array( $package_2 ),
				)
			)
		);
		$response = rest_get_server()->dispatch( $request );

		// Then.
		$this->assertEquals( 200, $response->status );

		$actual_packages_after_creation = $this->settings_store->get_packages();
		// We expect the type to be set to 'box' for packages with missing type property.
		$expected_packages_after_creation = array(
			array_merge( $package_1, array( 'type' => Package::TYPE_BOX ) ),
			array_merge( $package_2, array( 'type' => Package::TYPE_BOX ) ),
		);
		$this->assertEquals( $expected_packages_after_creation, $actual_packages_after_creation );
		$this->assertEquals( $expected_packages_after_creation, $response->data['custom'] );

		$actual_predefined_packages_after_creation   = $this->settings_store->get_predefined_packages();
		$expected_predefined_packages_after_creation = array(
			'usps'       => array(
				'flat_envelope',
				'padded_flat_envelope',
				'legal_flat_envelope',
			),
			'dhlexpress' => array(
				'SmallPaddedPouch',
				'Box2Cube',
			),
		);
		$this->assertEquals( $expected_predefined_packages_after_creation, $actual_predefined_packages_after_creation );
		$this->assertEquals( $expected_predefined_packages_after_creation, $response->data['predefined'] );
	}

	/**
	 * Test that creating custom packages where one package has a duplicate name returns an error.
	 */
	public function test_creating_duplicate_custom_packages_returns_an_error() {
		// Set up custom packages.
		$existing_packages = array(
			array(
				'id'              => 'my_package_1',
				'is_user_defined' => true,
				'name'            => 'Fun box',
				'dimensions'      => '10 x 20 x 5',
				'boxWeight'       => 0.23,
				'maxWeight'       => 0,
				'type'            => Package::TYPE_BOX,
			),
		);
		$new_packages      = array_merge(
			array(
				array(
					'id'               => 'my_package_2',
					'is_user_defined'  => true,
					'name'             => 'Cool box',
					'inner_dimensions' => '10 x 20 x 5',
					'box_weight'       => 0.23,
					'max_weight'       => 0,
				),
			),
			$existing_packages
		);
		$this->settings_store->update_packages( $existing_packages );

		// When.
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body(
			wp_json_encode(
				array(
					'custom' => $new_packages,
				)
			)
		);
		$response = rest_get_server()->dispatch( $request );

		$error = $response->as_error();

		// Then.
		$this->assertEquals( 400, $response->get_status() );
		$this->assertEquals( 'duplicate_custom_package_names_of_existing_packages', $error->get_error_code() );
		$this->assertEquals( array( 'package_names' => array( 'Fun box' ) ), $error->get_error_data() );

		// Assert that custom packages remain the same after the invalid request.
		$actual_packages_after_creation   = $this->settings_store->get_packages();
		$expected_packages_after_creation = $existing_packages;
		$this->assertEquals( $expected_packages_after_creation, $actual_packages_after_creation );
	}

	/**
	 * Test that creating two of the same custom packages returns an error.
	 */
	public function test_creating_two_of_the_same_custom_packages_returns_an_error() {
		// Set up existing custom packages to be empty.
		$this->settings_store->update_packages( array() );

		$new_packages = array(
			array(
				'is_user_defined'  => true,
				'name'             => 'Cool box',
				'inner_dimensions' => '10 x 20 x 5',
				'box_weight'       => 0.23,
				'max_weight'       => 0,
			),
			array(
				'is_user_defined'  => true,
				'name'             => 'Cool box',
				'inner_dimensions' => '10 x 20 x 5',
				'box_weight'       => 0.23,
				'max_weight'       => 0,
			),
		);

		// When.
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body(
			wp_json_encode(
				array(
					'custom' => $new_packages,
				)
			)
		);
		$response = rest_get_server()->dispatch( $request );
		$error    = $response->as_error();

		// Then.
		$this->assertEquals( 400, $response->get_status() );
		$this->assertEquals( 'duplicate_custom_package_names', $error->get_error_code() );
		$this->assertEquals( array( 'package_names' => array( 'Cool box' ) ), $error->get_error_data() );

		// Assert that custom packages remain the same after the invalid request.
		$actual_packages_after_creation = $this->settings_store->get_packages();
		$this->assertEquals( array(), $actual_packages_after_creation );
	}

	/**
	 * Test that creating predefined packages where one package for the same carrier has a duplicate name returns an error.
	 */
	public function test_creating_duplicate_predefined_packages_returns_an_error() {
		// Set up predefined packages.
		$existing_predefined_packages = array(
			'usps' => array(
				'flat_envelope',
				'padded_flat_envelope',
			),
		);
		$new_predefined_packages      = array(
			'dhlexpress' => array(
				'SmallPaddedPouch',
				'Box2Cube',
			),
			'usps'       => array(
				'legal_flat_envelope',
				'flat_envelope', // 'flat_envelope' is a duplicate
			),
		);
		$this->settings_store->update_predefined_packages( $existing_predefined_packages );

		// When.
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body(
			wp_json_encode(
				array(
					'predefined' => $new_predefined_packages,
				)
			)
		);
		$response = rest_get_server()->dispatch( $request );

		$error = $response->as_error();

		// Then.
		$this->assertEquals( 400, $response->get_status() );
		$this->assertEquals( 'duplicate_predefined_package_names_of_existing_packages', $error->get_error_code() );
		$expected_error_data = array( 'package_names_by_carrier' => array( 'usps' => array( 'flat_envelope' ) ) );
		$this->assertEquals( $expected_error_data, $error->get_error_data() );

		// Assert that custom packages remain the same after the invalid request.
		$actual_packages_after_creation   = $this->settings_store->get_predefined_packages();
		$expected_packages_after_creation = $existing_predefined_packages;
		$this->assertEquals( $expected_packages_after_creation, $actual_packages_after_creation );
	}

	/**
	 * Test that creating two of the same predefined packages returns an error.
	 */
	public function test_creating_two_of_the_same_predefined_packages_returns_an_error() {

		// Set up existing custom packages to be empty.
		$this->settings_store->update_predefined_packages( array() );

		$new_predefined_packages = array(
			'dhlexpress' => array(
				'Box2Cube',
				'SmallPaddedPouch',
				'Box2Cube',
			),
			'usps'       => array(
				'legal_flat_envelope',
				'flat_envelope',
				'flat_envelope',
			),
		);

		// When.
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body(
			wp_json_encode(
				array(
					'predefined' => $new_predefined_packages,
				)
			)
		);
		$response = rest_get_server()->dispatch( $request );

		$error = $response->as_error();

		// Then.
		$this->assertEquals( 400, $response->get_status() );
		$this->assertEquals( 'duplicate_predefined_package_names', $error->get_error_code() );
		$expected_error_data = array(
			'package_names_by_carrier' => array(
				'dhlexpress' => array( 'Box2Cube' ),
				'usps'       => array( 'flat_envelope' ),
			),
		);
		$this->assertEquals( $expected_error_data, $error->get_error_data() );

		// Assert that custom packages remain the same after the invalid request.
		$actual_packages_after_creation = $this->settings_store->get_predefined_packages();
		$this->assertEquals( array(), $actual_packages_after_creation );
	}

	/**
	 * Test updating each package types
	 */
	public function test_updating_each_package_type_works() {

		$existing_custom_packages = array(
			array(
				'id'              => 'my_package_1',
				'is_user_defined' => true,
				'name'            => 'Cool box',
				'dimensions'      => '10 x 20 x 5',
				'boxWeight'       => 0.23,
				'maxWeight'       => 0,
				'type'            => 'box',
			),
			array(
				'id'              => 'my_package_2',
				'is_user_defined' => true,
				'name'            => 'Cool box',
				'dimensions'      => '10 x 20 x 5',
				'boxWeight'       => 0.23,
				'maxWeight'       => 0,
				'type'            => 'envelope',
			),
		);
		$this->settings_store->update_packages( $existing_custom_packages );

		$existing_predefined_packages = array(
			'dhlexpress' => array(
				'Box2Cube',
				'SmallPaddedPouch',
				'Box2Cube',
			),
			'usps'       => array(
				'legal_flat_envelope',
				'flat_envelope',
				'flat_envelope',
			),
		);

		$this->settings_store->update_predefined_packages( $existing_predefined_packages );
		$new_custom_packages = $existing_custom_packages; // make a copy
		unset( $new_custom_packages[1] );

		// When.
		$request = $this->create_request( self::REST_ENDPOINT, 'PUT' );
		$request->set_body(
			wp_json_encode(
				array(
					'custom' => $new_custom_packages,
				)
			)
		);
		$response = rest_get_server()->dispatch( $request );

		// Then.
		$this->assertEquals( 200, $response->get_status() );
		$this->assertEquals( null, $response->as_error() );
		$this->assertCount( 1, $response->get_data()['custom'] );
		$this->assertEquals(
			$new_custom_packages,
			$response->get_data()['custom']
		);

		$this->assertCount( 2, $response->get_data()['predefined'] );
		$this->assertEquals( $existing_predefined_packages, $response->get_data()['predefined'] );
	}

	/**
	 * Test deleting custom and predefined packages.
	 */
	public function test_deletion_works() {
		// Set up custom packages
		$custom_package_1 = array(
			'id'              => 'my_package_1',
			'is_user_defined' => true,
			'name'            => 'Cool box',
			'dimensions'      => '10 x 20 x 5',
			'boxWeight'       => 0.23,
			'maxWeight'       => 0,
			'type'            => Package::TYPE_BOX,
		);
		$custom_package_2 = array(
			'id'              => 'my_package_2',
			'is_user_defined' => true,
			'name'            => 'Cool envelope',
			'dimensions'      => '10 x 20 x 5',
			'boxWeight'       => 0.23,
			'maxWeight'       => 0,
			'type'            => Package::TYPE_ENVELOPE,
		);
		$this->settings_store->update_packages( array( $custom_package_1, $custom_package_2 ) );

		// Set up predefined packages
		$predefined_packages = array(
			'dhlexpress' => array(
				'Box2Cube',
				'SmallPaddedPouch',
			),
			'usps'       => array(
				'legal_flat_envelope',
				'flat_envelope',
			),
		);
		$this->settings_store->update_predefined_packages( $predefined_packages );

		// Test deleting a custom package
		$request  = $this->create_request( self::REST_ENDPOINT . '/custom/my_package_1', 'DELETE' );
		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->get_status() );
		$this->assertEquals( null, $response->as_error() );
		$this->assertCount( 1, $response->get_data()['custom'] );
		$this->assertEquals( $custom_package_2, $response->get_data()['custom'][0] );
		$this->assertEquals( $predefined_packages, $response->get_data()['predefined'] );

		// Test deleting a predefined package
		$request  = $this->create_request( self::REST_ENDPOINT . '/predefined/flat_envelope', 'DELETE' );
		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->get_status() );
		$this->assertEquals( null, $response->as_error() );
		$expected_predefined = array(
			'dhlexpress' => array(
				'Box2Cube',
				'SmallPaddedPouch',
			),
			'usps'       => array(
				'legal_flat_envelope',
			),
		);
		$this->assertEquals( $expected_predefined, $response->get_data()['predefined'] );
		$this->assertCount( 1, $response->get_data()['custom'] );
	}

	/**
	 * Test deleting a non-existent package returns an error.
	 */
	public function test_deleting_nonexistent_package_returns_error() {
		// Set up custom packages
		$custom_package = array(
			'id'              => 'my_package_1',
			'is_user_defined' => true,
			'name'            => 'Cool box',
			'dimensions'      => '10 x 20 x 5',
			'boxWeight'       => 0.23,
			'maxWeight'       => 0,
			'type'            => Package::TYPE_BOX,
		);
		$this->settings_store->update_packages( array( $custom_package ) );

		// Set up predefined packages
		$predefined_packages = array(
			'usps' => array(
				'flat_envelope',
			),
		);
		$this->settings_store->update_predefined_packages( $predefined_packages );

		// Test deleting non-existent custom package
		$request  = $this->create_request( self::REST_ENDPOINT . '/custom/nonexistent_package', 'DELETE' );
		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->get_status() );
		$this->assertEquals(
			array(
				'success' => false,
				'error'   => array(
					'code'    => 'package_not_found',
					'message' => 'Custom package not found.',
				),
			),
			$response->get_data()
		);

		// Test deleting non-existent predefined package
		$request  = $this->create_request( self::REST_ENDPOINT . '/predefined/nonexistent_package', 'DELETE' );
		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->get_status() );
		$this->assertEquals(
			array(
				'success' => false,
				'error'   => array(
					'code'    => 'package_not_found',
					'message' => 'Predefined package not found.',
				),
			),
			$response->get_data()
		);
	}

	/**
	 * Test deleting with invalid package type returns an error.
	 */
	public function test_deleting_with_invalid_type_returns_error() {
		// this will not match any route
		$request  = $this->create_request( self::REST_ENDPOINT . '/invalid_type/my_package', 'DELETE' );
		$response = rest_do_request( $request );
		$this->assertEquals( 404, $response->get_status() );
		$data = $response->get_data();
		$this->assertEquals( 'rest_no_route', $data['code'] );
		$this->assertStringContainsString( 'No route was found matching the URL and request method', $data['message'] );
	}

	/**
	 * Test deleting with invalid package ID format returns an error.
	 */
	public function test_deleting_with_invalid_id_format_returns_error() {
		// this will not match any route
		$request  = $this->create_request( self::REST_ENDPOINT . '/custom/invalid@id$format', 'DELETE' );
		$response = rest_do_request( $request );

		$this->assertEquals( 404, $response->get_status() );
		$data = $response->get_data();
		$this->assertEquals( 'rest_no_route', $data['code'] );
		$this->assertStringContainsString( 'No route was found matching the URL and request method', $data['message'] );
	}

	/**
	 * DataProvider: Set ID behavioural expectations.
	 *
	 * @return array[]
	 */
	public function dataprovider_id_expectations() {
		return array(
			// Expect to hash empty value based on the package name.
			array( 'My Package', '', md5( 'My Package' ) ),
			// Verify we're converting "custom_box" to a hashed value.
			array( 'My Package', 'custom_box', md5( 'My Package' ) ),
			// Any "non custom_box" value should be respected as they are.
			array( 'My Package', 'some-random-value', 'some-random-value' ),
		);
	}

	/**
	 * Test that new packages are created with the package name's hashed value.
	 *
	 * @dataProvider dataprovider_id_expectations
	 */
	public function test_creating_packages_hashes_package_name_as_id( $package_name, $id, $expected_id ) {
		$package_data = array(
			'id'               => $id,
			'name'             => $package_name,
			'is_user_defined'  => true,
			'inner_dimensions' => '10 x 20 x 5',
			'box_weight'       => 0.23,
			'max_weight'       => 0,
		);

		// Save and get the packages.
		$this->settings_store->create_packages( array( $package_data ) );
		$packages = $this->settings_store->get_packages();

		$this->assertCount( 1, $packages );
		$this->assertSame( $expected_id, $packages[0]['id'] );
	}

	/**
	 * Test that max_weight is always returned in the API response even if not persisted.
	 */
	public function test_max_weight_is_always_returned_in_api_response() {
		// Set up a package without max_weight
		$package_without_max_weight = array(
			'id'               => 'my_package_1',
			'is_user_defined'  => true,
			'name'             => 'Fun box',
			'inner_dimensions' => '10 x 20 x 5',
			'box_weight'       => 0.23,
			'type'             => Package::TYPE_BOX,
		);

		// Make the POST request
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body(
			wp_json_encode(
				array(
					'custom' => array( $package_without_max_weight ),
				)
			)
		);
		$response = rest_get_server()->dispatch( $request );

		// Then
		$this->assertEquals( 200, $response->status );
		$this->assertCount( 1, $response->data['custom'] );
		$this->assertArrayHasKey( 'maxWeight', $response->data['custom'][0] );
		$this->assertEquals( 0, $response->data['custom'][0]['maxWeight'] );
	}

	public function test_creation_works_using_wcship_format() {
		$this->settings_store->update_packages( array() );

		$package_to_create = array(
			'id'              => 'my_package_1',
			'is_user_defined' => true,
			'name'            => 'Fun box',
			'dimensions'      => '10 x 20 x 5',
			'boxWeight'       => 0.23,
			'type'            => 'envelope',
		);

		// Make the POST request
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body(
			wp_json_encode(
				array(
					'custom' => array( $package_to_create ),
				)
			)
		);
		$response = rest_get_server()->dispatch( $request );

		// Then
		$this->assertEquals( 200, $response->status );
		$this->assertEquals(
			array(
				array(
					'id'              => 'my_package_1',
					'name'            => 'Fun box',
					'dimensions'      => '10 x 20 x 5',
					'boxWeight'       => 0.23,
					'maxWeight'       => 0,
					'type'            => 'envelope',
					'is_user_defined' => true,
				),
			),
			$response->data['custom']
		);
	}

	public function test_creation_works_using_wcst_format() {
		$this->settings_store->update_packages( array() );

		$package_to_create = array(
			'id'               => 'my_package_1',
			'is_user_defined'  => true,
			'name'             => 'Fun box',
			'inner_dimensions' => '10 x 20 x 5',
			'box_weight'       => 0.23,
			'is_letter'        => true,
		);

		// Make the POST request
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body(
			wp_json_encode(
				array(
					'custom' => array( $package_to_create ),
				)
			)
		);
		$response = rest_get_server()->dispatch( $request );

		// Then
		$this->assertEquals( 200, $response->status );
		$this->assertEquals(
			array(
				array(
					'id'              => 'my_package_1',
					'name'            => 'Fun box',
					'dimensions'      => '10 x 20 x 5',
					'boxWeight'       => 0.23,
					'maxWeight'       => 0,
					'type'            => 'envelope',
					'is_user_defined' => true,
				),
			),
			$response->data['custom']
		);
	}

	public function test_updating_works_using_wcship_format() {
		$existing_custom_packages = array(
			array(
				'id'              => 'my_package_1',
				'is_user_defined' => true,
				'name'            => 'Cool box',
				'dimensions'      => '10 x 20 x 5',
				'boxWeight'       => 0.23,
				'maxWeight'       => 0,
				'type'            => 'box',
			),
		);

		$this->settings_store->update_packages( $existing_custom_packages );

		$new_custom_packages                 = $existing_custom_packages;
		$new_custom_packages[0]['boxWeight'] = 123;
		$new_custom_packages[0]['type']      = 'envelope';

		$request = $this->create_request( self::REST_ENDPOINT, 'PUT' );
		$request->set_body(
			wp_json_encode(
				array(
					'custom' => $new_custom_packages,
				)
			)
		);
		$response = rest_get_server()->dispatch( $request );

		$this->assertEquals( 200, $response->get_status() );
		$this->assertEquals(
			$new_custom_packages,
			$response->get_data()['custom']
		);
	}

	public function test_updating_works_using_wcst_format() {
		$existing_custom_packages = array(
			array(
				'id'              => 'my_package_1',
				'is_user_defined' => true,
				'name'            => 'Cool box',
				'dimensions'      => '10 x 20 x 5',
				'boxWeight'       => 0.23,
				'maxWeight'       => 0,
				'type'            => 'box',
			),
		);

		$this->settings_store->update_packages( $existing_custom_packages );

		$new_custom_packages = array(
			array(
				'id'               => 'my_package_1',
				'is_user_defined'  => true,
				'name'             => 'Cool box',
				'inner_dimensions' => '1 x 2 x 3',
				'box_weight'       => 123,
				'is_letter'        => true,
			),
		);

		$request = $this->create_request( self::REST_ENDPOINT, 'PUT' );
		$request->set_body(
			wp_json_encode(
				array(
					'custom' => $new_custom_packages,
				)
			)
		);
		$response = rest_get_server()->dispatch( $request );

		$this->assertEquals( 200, $response->get_status() );
		$this->assertEquals(
			array(
				array(
					'id'              => 'my_package_1',
					'is_user_defined' => true,
					'name'            => 'Cool box',
					'dimensions'      => '1 x 2 x 3',
					'boxWeight'       => 123,
					'maxWeight'       => 0,
					'type'            => 'envelope',
				),
			),
			$response->get_data()['custom']
		);
	}

	public function test_saved_and_predefined_packages_are_returned_in_separate_objects() {
		$existing_custom_package = array(
			array(
				'id'              => 'my_envelope',
				'is_user_defined' => true,
				'name'            => 'My Envelope',
				'dimensions'      => '12 x 12 x 0.75',
				'boxWeight'       => 0.01,
				'type'            => 'envelope',
			),
		);
		$this->settings_store->update_packages( $existing_custom_package );

		$request  = $this->create_request( self::REST_ENDPOINT, 'GET' );
		$response = rest_get_server()->dispatch( $request );

		$this->assertEquals( 200, $response->get_status() );
		$this->assertArrayHasKey( 'storeOptions', $response->get_data() );
		$this->assertArrayHasKey( 'packages', $response->get_data() );
		$this->assertArrayHasKey( 'saved', $response->get_data()['packages'] );
		$this->assertArrayHasKey( 'custom', $response->get_data()['packages']['saved'] );
		$this->assertArrayHasKey( 'predefined', $response->get_data()['packages']['saved'] );
		$this->assertArrayHasKey( 'predefined', $response->get_data()['packages'] );
	}

	public function test_it_gets_upsdap_packages_if_supported_by_client() {
		update_option(
			'wcshipping_services',
			$this->convert_to_object(
				array(
					'shipping' => array(
						array(
							'id'       => 'usps',
							'packages' => array( 'foo' ),
						),
						array(
							'id'       => 'upsdap',
							'packages' => array( 'bar' ),
						),
					),
				)
			)
		);

		$request = new WP_REST_Request();
		$request->set_param( 'features_supported_by_client', array( 'upsdap' ) );

		$controller = $this->create_packages_controller_with_actual_service_schemas_store();

		$this->assertArrayHasKey(
			'upsdap',
			$controller->get( $request )->get_data()['packages']['predefined']
		);
	}

	public function test_it_does_not_get_upsdap_packages_if_not_supported_by_client() {
		update_option(
			'wcshipping_services',
			$this->convert_to_object(
				array(
					'shipping' => array(
						array(
							'id'       => 'usps',
							'packages' => array( 'foo' ),
						),
						array(
							'id'       => 'upsdap',
							'packages' => array( 'bar' ),
						),
					),
				)
			)
		);

		$request = new WP_REST_Request();
		$request->set_param( 'features_supported_by_client', array() );

		$controller = $this->create_packages_controller_with_actual_service_schemas_store();

		$this->assertArrayNotHasKey(
			'upsdap',
			$controller->get( $request )->get_data()['packages']['predefined']
		);
	}

	public function test_it_does_not_get_upsdap_packages_if_support_unspecified_by_client() {
		update_option(
			'wcshipping_services',
			$this->convert_to_object(
				array(
					'shipping' => array(
						array(
							'id'       => 'usps',
							'packages' => array( 'foo' ),
						),
						array(
							'id'       => 'upsdap',
							'packages' => array( 'bar' ),
						),
					),
				)
			)
		);

		$request = new WP_REST_Request();

		$controller = $this->create_packages_controller_with_actual_service_schemas_store();

		$this->assertArrayNotHasKey(
			'upsdap',
			$controller->get( $request )->get_data()['packages']['predefined']
		);
	}

	private function create_packages_controller_with_actual_service_schemas_store(): PackagesRESTController {
		return new PackagesRESTController(
			$this->createMock( WC_Connect_Service_Settings_Store::class ),
			new WC_Connect_Package_Settings(
				$this->createMock( WC_Connect_Service_Settings_Store::class ),
				new WC_Connect_Service_Schemas_Store(
					$this->createMock( WC_Connect_API_Client::class ),
					$this->createMock( WC_Connect_Logger::class )
				)
			)
		);
	}

	private function convert_to_object( array $arr ) {
		return json_decode( json_encode( $arr ) );
	}
}
