<?php

namespace Automattic\WCShipping\Tests\Packages;

use Automattic\WCShipping\Packages\PackageRepository;
use Automattic\WCShipping\Connect\WC_Connect_Options;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;

class PackageRepositoryTest extends WCShipping_Test_Case {
	private PackageRepository $repository;

	public function setUp(): void {
		parent::setUp();
		$this->repository = new PackageRepository();
	}

	public function tearDown(): void {
		parent::tearDown();
		// Clean up the packages option after each test
		WC_Connect_Options::update_option( 'packages', array() );
	}

	public function test_get_custom_packages_returns_empty_array_when_no_packages_exist() {
		WC_Connect_Options::update_option( 'packages', array() );

		$result = $this->repository->get_custom_packages();

		$this->assertIsArray( $result );
		$this->assertEmpty( $result );
	}

	public function test_get_custom_packages_returns_sanitized_packages() {
		$mockPackages = array(
			array(
				'name'          => 'Test Package',
				'dimensions'    => '10 x 10 x 10',
				'boxWeight'     => 0.5,
				'type'          => 'box',
				'isUserDefined' => true,
				'id'            => 'test_package',
			),
		);

		WC_Connect_Options::update_option( 'packages', $mockPackages );
		$result = $this->repository->get_custom_packages();

		$expected = array(
			array(
				'name'            => 'Test Package',
				'dimensions'      => '10 x 10 x 10',
				'boxWeight'       => 0.5,
				'type'            => 'box',
				'is_user_defined' => true,
				'id'              => 'test_package',
				'maxWeight'       => 0,
			),
		);

		$this->assertEquals( $expected, $result );
	}

	public function test_get_custom_packages_skips_invalid_packages() {
		$mockPackages = array(
			array(
				'name'          => 'Test Package',
				'dimensions'    => '10 x 10 x 10',
				'boxWeight'     => 0.5,
				'type'          => 'box',
				'isUserDefined' => true,
				'id'            => 'test_package',
			),
			array(
				'name'          => new \stdClass(),
				'dimensions'    => array( 'foo' ),
				'boxWeight'     => true,
				'type'          => 'fffff',
				'isUserDefined' => false,
				'id'            => new \stdClass(),
			),
		);

		WC_Connect_Options::update_option( 'packages', $mockPackages );
		$result = $this->repository->get_custom_packages();

		$expected = array(
			array(
				'name'            => 'Test Package',
				'dimensions'      => '10 x 10 x 10',
				'boxWeight'       => 0.5,
				'type'            => 'box',
				'is_user_defined' => true,
				'id'              => 'test_package',
				'maxWeight'       => 0,
			),
		);

		$this->assertEquals( $expected, $result );
	}

	public function test_add_custom_packages_merges_with_existing_packages() {
		$existingPackages = array(
			array(
				'name'          => 'Existing Package',
				'dimensions'    => '10 x 10 x 10',
				'boxWeight'     => 1,
				'type'          => 'box',
				'isUserDefined' => true,
				'id'            => 'existing_package',
				'maxWeight'     => 1.0,
			),
		);
		$newPackages      = array(
			array(
				'name'          => 'Test Package',
				'dimensions'    => '10 x 10 x 10',
				'boxWeight'     => 0.5,
				'type'          => 'box',
				'isUserDefined' => true,
				'id'            => 'test_package',
			),
		);

			WC_Connect_Options::update_option( 'packages', $existingPackages );
			$this->repository->add_custom_packages( $newPackages );
			$result = $this->repository->get_custom_packages();

			$expected = array(
				array(
					'name'            => 'Existing Package',
					'dimensions'      => '10 x 10 x 10',
					'boxWeight'       => 1.0,
					'type'            => 'box',
					'is_user_defined' => true,
					'id'              => 'existing_package',
					'maxWeight'       => 1,
				),
				array(
					'name'            => 'Test Package',
					'dimensions'      => '10 x 10 x 10',
					'boxWeight'       => 0.5,
					'type'            => 'box',
					'is_user_defined' => true,
					'id'              => 'test_package',
					'maxWeight'       => 0,
				),
			);

			$this->assertEquals( $expected, $result );
	}

	public function test_replace_custom_packages_overwrites_existing_packages() {
		$existingPackages = array(
			array(
				'name'          => 'Existing Package',
				'dimensions'    => '10 x 10 x 10',
				'boxWeight'     => 1,
				'type'          => 'box',
				'isUserDefined' => false,
				'id'            => 'existing_package',
			),
		);

		WC_Connect_Options::update_option( 'packages', $existingPackages );

		$newPackages = array(
			array(
				'name'          => 'Test Package',
				'dimensions'    => '10 x 10 x 10',
				'boxWeight'     => 0.5,
				'type'          => 'box',
				'isUserDefined' => true,
				'id'            => 'test_package',
			),
		);

		$this->repository->replace_custom_packages( $newPackages );

		$result   = $this->repository->get_custom_packages();
		$expected = array(
			array(
				'name'            => 'Test Package',
				'dimensions'      => '10 x 10 x 10',
				'boxWeight'       => 0.5,
				'type'            => 'box',
				'is_user_defined' => true,
				'id'              => 'test_package',
				'maxWeight'       => 0,
			),
		);

		$this->assertEquals( $expected, $result );
	}
}
