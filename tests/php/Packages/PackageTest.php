<?php

use Automattic\WCShipping\Packages\Package;
use Automattic\WCShipping\Packages\PackageValidationException;
use PHPUnit\Framework\TestCase;

class PackageTest extends TestCase {
	private $package;

	protected function setUp(): void {
		$this->package = new Package(
			'1',
			'Test Package',
			Package::TYPE_BOX,
			'10 x 10 x 10',
			1.0,
			50.0
		);
	}

	public function test_from_array() {
		$data = array(
			'id'         => '2',
			'name'       => 'Array Package',
			'type'       => Package::TYPE_ENVELOPE,
			'dimensions' => '5 x 5 x 5',
			'boxWeight'  => 0.5,
			'maxWeight'  => 10.0,
		);

		$package = Package::from_array( $data );

		$this->assertEquals( '2', $package->id );
		$this->assertEquals( 'Array Package', $package->name );
		$this->assertEquals( Package::TYPE_ENVELOPE, $package->type );
		$this->assertEquals( '5 x 5 x 5', $package->dimensions );
		$this->assertEquals( 0.5, $package->box_weight );
		$this->assertEquals( 10.0, $package->max_weight );
	}

	public function test_to_wcst_array() {
		$expected = array(
			'id'               => '1',
			'name'             => 'Test Package',
			'inner_dimensions' => '10 x 10 x 10',
			'box_weight'       => 1.0,
			'max_weight'       => 50.0,
			'is_letter'        => false,
			'is_user_defined'  => true,
		);

		$this->assertEquals( $expected, $this->package->to_wcst_array() );
	}

	public function test_to_api_array() {
		$expected = array(
			'id'              => '1',
			'name'            => 'Test Package',
			'dimensions'      => '10 x 10 x 10',
			'length'          => 10,
			'width'           => 10,
			'height'          => 10,
			'box_weight'      => 1.0,
			'is_letter'       => false,
			'is_user_defined' => true,
			'type'            => Package::TYPE_BOX,
		);

		$this->assertEquals( $expected, $this->package->to_api_array() );
	}

	public function test_validate_throws_on_invalid_type() {
		$this->expectException( PackageValidationException::class );
		new Package( '3', 'Invalid Package', 'invalid_type', '10 x 10 x 10', 1.0, 50.0 );
	}

	public function test_validate_throws_on_invalid_dimensions() {
		$this->expectException( PackageValidationException::class );
		new Package( '3', 'Invalid Package', 'box', '10', 1.0, 50.0 );
	}

	public function test_validate_throws_on_invalid_box_weight() {
		$this->expectException( PackageValidationException::class );
		new Package( '3', 'Invalid Package', 'box', '10 x 10 x 10', -1.0, 50.0 );
	}

	public function test_validate_throws_on_invalid_max_weight() {
		$this->expectException( PackageValidationException::class );
		new Package( '3', 'Invalid Package', 'box', '10 x 10 x 10', 1.0, -50.0 );
	}

	public function test_from_array_with_wcst_keys() {
		$data = array(
			'id'               => '3',
			'name'             => 'Mapped Package',
			'inner_dimensions' => '15 x 15 x 15',
			'box_weight'       => 2.0,
			'max_weight'       => 20.0,
			'is_letter'        => true,
		);

		$package = Package::from_array( $data );

		$this->assertEquals( '3', $package->id );
		$this->assertEquals( 'Mapped Package', $package->name );
		$this->assertEquals( Package::TYPE_ENVELOPE, $package->type );
		$this->assertEquals( '15 x 15 x 15', $package->dimensions );
		$this->assertEquals( 2.0, $package->box_weight );
		$this->assertEquals( 20.0, $package->max_weight );
		$this->assertTrue( $package->is_user_defined );
	}

	public function test_from_array_prioritizes_wcship_keys_over_wcst() {
		$data = array(
			'id'               => '3',
			'name'             => 'Mapped Package',
			'inner_dimensions' => '15 x 15 x 15',
			'dimensions'       => '25 x 25 x 25',
			'box_weight'       => 2.0,
			'boxWeight'        => 3.0,
			'max_weight'       => 20.0,
			'maxWeight'        => 40.0,
			'is_letter'        => true,
			'type'             => 'box',
		);

		$package = Package::from_array( $data );

		$this->assertEquals( '3', $package->id );
		$this->assertEquals( 'Mapped Package', $package->name );
		$this->assertEquals( Package::TYPE_BOX, $package->type );
		$this->assertEquals( '25 x 25 x 25', $package->dimensions );
		$this->assertEquals( 3.0, $package->box_weight );
		$this->assertEquals( 40.0, $package->max_weight );
		$this->assertTrue( $package->is_user_defined );
	}

	/**
	 * @dataProvider required_keys_provider
	 */
	public function test_from_array_throws_key_missing_exception( $key_to_unset ) {
		$this->expectException( PackageValidationException::class );
		$this->expectExceptionMessage( 'Missing required key: ' . $key_to_unset );

		$package_data = array(
			'id'         => '4',
			'name'       => 'Missing Key Package',
			'dimensions' => '10 x 10 x 10',
			'boxWeight'  => '20',
			'maxWeight'  => '30',
			'type'       => Package::TYPE_BOX,
		);

		unset( $package_data[ $key_to_unset ] );

		Package::from_array( $package_data );
	}

	/**
	 * @dataProvider required_keys_provider
	 */
	public function test_from_array_does_not_throw_key_missing_exception_if_key_can_be_mapped_from_wcst_format( $key_to_unset ) {
		$package_data = array(
			'id'               => '4',
			'name'             => 'Missing Key Package',
			'inner_dimensions' => '10 x 10 x 10',
			'box_weight'       => '20',
			'max_weight'       => '30',
			'is_letter'        => true,
		);

		Package::from_array( $package_data );

		$this->expectNotToPerformAssertions(); // PackageValidationException is not thrown.
	}

	/**
	 * @dataProvider string_keys_provider
	 */
	public function test_from_array_throws_if_stringable_key_has_nonstringable_value( $key_to_invalidate ) {
		$this->expectException( PackageValidationException::class );
		$this->expectExceptionMessage( "Key $key_to_invalidate must be a string" );

		$package_data = array(
			'id'         => '4',
			'name'       => 'Missing Key Package',
			'dimensions' => '10 x 10 x 10',
			'boxWeight'  => '20',
			'maxWeight'  => '30',
			'type'       => Package::TYPE_BOX,
		);

		$package_data[ $key_to_invalidate ] = new stdClass();

		Package::from_array( $package_data );
	}

	/**
	 * @dataProvider numeric_keys_provider
	 */
	public function test_from_array_throws_if_numeric_key_has_nonnumeric_value( $key_to_invalidate ) {
		$this->expectException( PackageValidationException::class );
		$this->expectExceptionMessage( "Key $key_to_invalidate must be a number" );

		$package_data = array(
			'id'         => '4',
			'name'       => 'Missing Key Package',
			'dimensions' => '10 x 10 x 10',
			'boxWeight'  => '20',
			'maxWeight'  => '30',
			'type'       => Package::TYPE_BOX,
		);

		$package_data[ $key_to_invalidate ] = 'foo';

		Package::from_array( $package_data );
	}

	public function test_from_array_allows_coercible_types() {
		$package = Package::from_array(
			array(
				'id'         => 123, // stringable key is expected
				'name'       => 456, // stringable key is expected
				'type'       => 'envelope', // no coercion tested here
				'dimensions' => '1 x 2 x 3', // no coercion tested here
				'boxWeight'  => '   12.3 ', // extra whitespace
				'maxWeight'  => 45, // float is expected
			)
		);

		$this->assertEquals(
			array(
				'id'              => '123',
				'name'            => '456',
				'type'            => Package::TYPE_ENVELOPE,
				'dimensions'      => '1 x 2 x 3',
				'boxWeight'       => 12.3,
				'maxWeight'       => 45.0,
				'is_user_defined' => true,
			),
			$package->to_array()
		);
	}

	public function test_constructor_allows_coercible_types() {
		$package = new Package(
			123, // stringable key is expected
			'456', // stringable key is expected
			'envelope', // no coercion tested here
			'1 x 2 x 3', // no coercion tested here
			'   12.3 ', // extra whitespace
			45, // float is expected
		);

		$this->assertEquals(
			array(
				'id'              => '123',
				'name'            => '456',
				'type'            => Package::TYPE_ENVELOPE,
				'dimensions'      => '1 x 2 x 3',
				'boxWeight'       => 12.3,
				'maxWeight'       => 45.0,
				'is_user_defined' => true,
			),
			$package->to_array()
		);
	}

	public function test_from_array_with_null_type() {
		$data = array(
			'id'         => '1',
			'name'       => 'Test Package',
			'type'       => null,
			'dimensions' => '10 x 10 x 10',
			'boxWeight'  => 1.0,
			'maxWeight'  => 50.0,
		);

		$package = Package::from_array( $data );

		$this->assertEquals( Package::TYPE_BOX, $package->type );

		$wcst_array = $package->to_wcst_array();

		$expected = array(
			'id'               => '1',
			'name'             => 'Test Package',
			'inner_dimensions' => '10 x 10 x 10',
			'box_weight'       => 1.0,
			'max_weight'       => 50.0,
			'is_letter'        => false,
			'is_user_defined'  => true,
		);

		$this->assertEquals( $expected, $wcst_array );
		$this->assertArrayHasKey( 'is_letter', $wcst_array );
		$this->assertFalse( $wcst_array['is_letter'] );
	}

	public function test_it_generates_id_only_if_not_set_or_equal_custom_box() {
		$this->assertEquals(
			'foo',
			( new Package( 'foo', 'bar', 'box', '1 x 1 x 1', 1.0 ) )->id
		);

		$this->assertEquals(
			md5( 'bar' ),
			( new Package( null, 'bar', 'box', '1 x 1 x 1', 1.0 ) )->id
		);

		$this->assertEquals(
			md5( 'bar' ),
			( new Package( 'custom_box', 'bar', 'box', '1 x 1 x 1', 1.0 ) )->id
		);
	}

	public function test_package_dimensions_are_converted_to_length_width_height() {
		$package = new Package( 'test_package', 'Test Package', 'envelope', '10 x 10 x 0.75', 0.5 );

		$this->assertEquals( 10, $package->length );
		$this->assertEquals( 10, $package->width );
		$this->assertEquals( 0.75, $package->height );
	}

	public function required_keys_provider() {
		return array(
			array( 'name' ),
			array( 'dimensions' ),
			array( 'boxWeight' ),
		);
	}

	public function string_keys_provider() {
		return array(
			array( 'id' ),
			array( 'name' ),
			array( 'type' ),
			array( 'dimensions' ),
		);
	}

	public function numeric_keys_provider() {
		return array(
			array( 'boxWeight' ),
			array( 'maxWeight' ),
		);
	}
}
