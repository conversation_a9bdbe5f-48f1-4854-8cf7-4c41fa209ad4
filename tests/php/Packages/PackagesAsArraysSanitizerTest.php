<?php

namespace Automattic\WCShipping\Tests\Packages;

use Automattic\WCShipping\Packages\Package;
use Automattic\WCShipping\Packages\PackagesAsArraysSanitizer;
use Automattic\WCShipping\Packages\PackageValidationException;
use PHPUnit\Framework\TestCase;

class PackagesAsArraysSanitizerTest extends TestCase {
	public function test_empty_array_returns_empty_array() {
		$sanitizer = new PackagesAsArraysSanitizer( array() );

		$this->assertEmpty( $sanitizer->to_packages_as_arrays() );
		$this->assertEmpty( $sanitizer->to_packages_as_wcst_arrays() );
	}

	public function test_valid_package_is_sanitized() {
		$package = array(
			'id'         => 'test_box',
			'name'       => 'Test Box',
			'length'     => 10,
			'width'      => 10,
			'height'     => 10,
			'dimensions' => '10 x 10 x 10',
			'weight'     => 1,
			'boxWeight'  => 0.5,
		);

		$sanitizer = new PackagesAsArraysSanitizer( array( $package ) );

		$result = $sanitizer->to_packages_as_arrays();

		$this->assertCount( 1, $result );
		$this->assertEquals( 'test_box', $result[0]['id'] );
		$this->assertEquals( 'Test Box', $result[0]['name'] );
	}

	public function test_invalid_package_throws_exception() {
		$invalid_package = array(
			'id'     => 'test_box',
			'name'   => 'Test Box',
			// Missing required dimensions
			'weight' => 1,
		);

		$this->expectException( PackageValidationException::class );

		new PackagesAsArraysSanitizer( array( $invalid_package ) );
	}

	public function test_invalid_package_is_filtered_when_throw_on_failure_is_false() {
		$valid_package = array(
			'id'         => 'valid_box',
			'name'       => 'Valid Box',
			'length'     => 10,
			'width'      => 10,
			'height'     => 10,
			'dimensions' => '10 x 10 x 10',
			'weight'     => 1,
			'boxWeight'  => 0.5,
		);

		$invalid_package = array(
			'id'         => 'invalid_box',
			'name'       => 'Invalid Box',
			// Missing dimensions
			'weight'     => 1,
			'dimensions' => '10x10x10',
		);

		$package_missing_all_keys = array();

		$package_with_invalid_types = array(
			'id'         => new \stdClass(),
			'name'       => new \stdClass(),
			'type'       => new \stdClass(),
			'dimensions' => new \stdClass(),
			'boxWeight'  => new \stdClass(),
			'maxWeight'  => new \stdClass(),
		);

		$sanitizer = new PackagesAsArraysSanitizer(
			array( $valid_package, $invalid_package, $package_missing_all_keys ),
			false // Don't throw on failure
		);

		$result = $sanitizer->to_packages_as_arrays();

		$this->assertCount( 1, $result );
		$this->assertEquals( 'valid_box', $result[0]['id'] );
	}

	public function test_wcst_format_conversion() {
		$package = array(
			'id'         => 'test_box',
			'name'       => 'Test Box',
			'length'     => 10,
			'width'      => 10,
			'height'     => 10,
			'dimensions' => '10 x 10 x 10',
			'weight'     => 1,
			'boxWeight'  => 0.5,
		);

		$sanitizer = new PackagesAsArraysSanitizer( array( $package ) );

		$result = $sanitizer->to_packages_as_wcst_arrays();
		$this->assertCount( 1, $result );
		// Verify WCS&T specific format differences
		// Note: You'll need to adjust these assertions based on the actual
		// differences between WCShip and WCS&T formats
		$this->assertArrayHasKey( 'box_weight', $result[0] );
		$this->assertArrayNotHasKey( 'boxWeight', $result[0] );
	}

	public function test_type_error_is_handled_same_as_validation_error() {
		$package_with_type_error = array(
			'id'         => 'test_box',
			'name'       => 'Test Box',
			'length'     => 'not_a_number',
			'width'      => 10,
			'height'     => 10,
			'dimensions' => '10 x 10 x 10',
			'weight'     => 1,
			'boxWeight'  => 0.5,
			'type'       => 'not_a_type',
		);

		// Test that it throws with throw_on_failure = true
		$this->expectException( PackageValidationException::class );
		new PackagesAsArraysSanitizer( array( $package_with_type_error ) );

		// Test that it filters with throw_on_failure = false
		$sanitizer = new PackagesAsArraysSanitizer( array( $package_with_type_error ), false );
		$this->assertEmpty( $sanitizer->to_packages_as_arrays() );
	}
}
