<?php

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\OriginAddresses\OriginAddressService;
use Automattic\WCShipping\Connect\WC_Connect_Options;

class OriginAddressServiceTest extends WCShipping_Test_Case {

	/**
	 * @var OriginAddressService
	 */
	private $originAddressService;

	function setUp(): void {
		parent::setUp();
		$this->originAddressService = new OriginAddressService();
		$this->add_store_details();
	}


	// convert to kebab case

	public function test_get_origin_addresses_returns_default_address_when_no_addresses_exist() {
		WC_Connect_Options::delete_option( 'origin_addresses' );

		$addresses = $this->originAddressService->get_origin_addresses();

		$this->assertCount( 1, $addresses );
		$this->assertTrue( $addresses[0]['default_address'] );
	}

	public function test_update_origin_addresses_adds_new_address_when_no_matching_id_exists() {
		$newAddress = array(
			'id'      => 'new_address_id',
			'address' => 'New Address',
		);

		$updatedAddress = $this->originAddressService->update_origin_addresses( $newAddress );
		$this->assertEquals( $newAddress, $updatedAddress );
	}

	public function test_update_origin_addresses_updates_existing_address_when_matching_id_exists() {
		$existingAddress = array(
			'id'              => 'existing_address_id',
			'address'         => 'Existing Address',
			'default_address' => true,
		);
		WC_Connect_Options::update_option( 'origin_addresses', array( $existingAddress ) );

		$updatedAddress = array(
			'id'              => 'existing_address_id',
			'address'         => 'Updated Address',
			'default_address' => true,
		);
		$this->originAddressService->update_origin_addresses( $updatedAddress );

		$addresses = $this->originAddressService->get_origin_addresses();

		$this->assertCount( 1, $addresses );
		$this->assertEquals( $updatedAddress, $addresses[0] );
	}

	public function test_delete_origin_addresses_removes_address_with_matching_id() {
		$existingAddress = array(
			'id'      => 'existing_address_id',
			'address' => 'Existing Address',
		);
		$this->originAddressService->update_origin_addresses( $existingAddress );
		$addresses = $this->originAddressService->get_origin_addresses();
		$this->assertCount( 2, $addresses );

		$this->originAddressService->delete_origin_address( 'existing_address_id' );

		$addresses = $this->originAddressService->get_origin_addresses();

		$this->assertCount( 1, $addresses );
	}

	public function test_delete_origin_addresses_does_nothing_when_no_matching_id_exists() {
		$existingAddress = array(
			'id'              => 'existing_address_id',
			'address'         => 'Existing Address',
			'default_address' => false,
		);

		$this->originAddressService->update_origin_addresses( $existingAddress );
		$addresses = $this->originAddressService->get_origin_addresses();
		$this->assertCount( 2, $addresses );

		$this->originAddressService->delete_origin_address( 'no_existing_address_id' );

		$addresses = $this->originAddressService->get_origin_addresses();

		$this->assertCount( 2, $addresses );
		$this->assertEquals( $existingAddress, $addresses[1] );
	}

	public function test_update_origin_addresses_removes_default_from_previous_addresses_when_new_default_is_added() {
		$existingAddress1 = array(
			'id'              => 'existing_address_id_1',
			'address'         => 'Existing Address 1',
			'default_address' => true,
		);
		$existingAddress2 = array(
			'id'      => 'existing_address_id_2',
			'address' => 'Existing Address 2',
		);
		WC_Connect_Options::update_option( 'origin_addresses', array( $existingAddress1, $existingAddress2 ) );

		$newDefaultAddress = array(
			'id'              => 'new_default_address_id',
			'address'         => 'New Default Address',
			'default_address' => true,
		);

		$this->originAddressService->update_origin_addresses( $newDefaultAddress );

		$addresses = $this->originAddressService->get_origin_addresses();

		$this->assertCount( 3, $addresses );
		$this->assertFalse( isset( $addresses[0]['default_address'] ) );
		$this->assertTrue( $addresses[2]['default_address'] );
	}

	public function test_update_origin_addresses_removes_default_from_previous_addresses_when_existing_default_is_updated() {
		// Arrange
		$existingAddress1 = array(
			'id'              => 'existing_address_id_1',
			'address'         => 'Existing Address 1',
			'default_address' => true,
		);
		$existingAddress2 = array(
			'id'      => 'existing_address_id_2',
			'address' => 'Existing Address 2',
		);
		WC_Connect_Options::update_option( 'origin_addresses', array( $existingAddress1, $existingAddress2 ) );

		$updatedDefaultAddress = array(
			'id'              => 'existing_address_id_1',
			'address'         => 'Updated Default Address',
			'default_address' => true,
		);

		$this->originAddressService->update_origin_addresses( $updatedDefaultAddress );

		$addresses = $this->originAddressService->get_origin_addresses();

		$this->assertCount( 2, $addresses );
		$this->assertFalse( isset( $addresses[1]['default_address'] ) );
		$this->assertTrue( $addresses[0]['default_address'] );
	}

	public function test_delete_origin_addresses_does_not_delete_last_remaining_address() {
		$existingAddress = array(
			'id'              => 'existing_address_id',
			'address'         => 'Existing Address',
			'default_address' => true,
		);
		WC_Connect_Options::update_option( 'origin_addresses', array( $existingAddress ) );

		$this->originAddressService->delete_origin_address( 'existing_address_id' );

		$addresses = $this->originAddressService->get_origin_addresses();

		$this->assertCount( 1, $addresses );
		$this->assertEquals( $existingAddress, $addresses[0] );
	}

	public function test_sync_origin_addresses_with_woocommerce_store_address() {
		$this->add_store_details();
		update_option( 'woocommerce_store_address', 'New address' );
		$this->originAddressService->sync_origin_addresses_with_woocommerce_store_address();
		$storeDetails = $this->originAddressService->get_origin_addresses()[0];
		$this->assertEquals( 'New address Test Address 2', $storeDetails['address_1'] );
	}

	private function add_store_details() {
		update_option( 'woocommerce_store_address', 'Test address' );
		update_option( 'woocommerce_store_address_2', 'Test Address 2' );
		update_option( 'woocommerce_store_city', 'Test city' );
		update_option( 'woocommerce_store_postcode', '0000' );
		update_option( 'woocommerce_default_country', 'US' );
	}
}
