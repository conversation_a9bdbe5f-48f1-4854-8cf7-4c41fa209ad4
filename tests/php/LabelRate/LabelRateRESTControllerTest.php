<?php

namespace Automattic\WCShipping\Tests\php\LabelRate;

use WC_Unit_Test_Case;
use Automattic\WCShipping\LabelRate\LabelRateRESTController;
use WP_REST_Request;
use Automattic\WCShipping\LabelRate\LabelRateService;
use WP_Error;

class LabelRateRESTControllerTest extends WC_Unit_Test_Case {
	private const SINGLE_PACKAGE_SAMPLE = '{
        "order_id": 139,
        "origin": {
            "company": "Occasional Antlion",
            "name": "HARRIS W",
            "phone": "**********",
            "country": "US",
            "state": "CA",
            "address_1": "1600 AMPHITHEATRE",
            "address_1": "1600 AMPHITHEATRE",
            "address_2": " PKWY",
            "city": "MOUNTAIN VIEW",
            "postcode": "94043-1351"
        },
        "destination": {
            "company": "",
            "city": "MOUNTAIN VIEW",
            "state": "CA",
            "postcode": "94043-1351",
            "country": "US",
            "phone": "**********",
            "name": " ",
            "address": "1600 AMPHITHEATRE PKWY",
            "address_1": "1600 AMPHITHEATRE",
            "address_2": "PKWY"
        },
        "packages": [
            {
                "id": "default_box",
                "box_id": "small_flat_box",
                "length": 21.91,
                "width": 13.65,
                "height": 4.13,
                "weight": 0.2,
                "is_letter": false
            }
        ]
    }';

	private const RESPONSE_SAMPLE = '{
        "default_box": {
            "default": {
                "rates": [
                    {
                        "rate_id": "rate_c9e78aa7689840c6894e72832f7a11eb",
                        "service_id": "Priority",
                        "carrier_id": "usps",
                        "title": "USPS - Priority Mail",
                        "rate": 8.55,
                        "retail_rate": 10.2,
                        "list_rate": 8.55,
                        "is_selected": false,
                        "tracking": true,
                        "insurance": 100,
                        "free_pickup": true,
                        "shipment_id": "shp_8808bad2468e4e5c9c981fae5bb82a7f"
                    }
                ],
                "errors": []
            },
            "signature_required": {
                "rates": [
                    {
                        "rate_id": "rate_c4b3ed096e634e4b8d308d1b3991316d",
                        "service_id": "Priority",
                        "carrier_id": "usps",
                        "title": "USPS - Priority Mail",
                        "rate": 11.95,
                        "retail_rate": 14.25,
                        "list_rate": 11.95,
                        "is_selected": false,
                        "tracking": true,
                        "insurance": 100,
                        "free_pickup": true,
                        "shipment_id": "shp_ef041526403e41849f97bcb18515fcfc"
                    }
                ],
                "errors": []
            },
            "adult_signature_required": {
                "rates": [
                    {
                        "rate_id": "rate_ca22c98d0577413ea7bb70c3ccdc89a2",
                        "service_id": "Priority",
                        "carrier_id": "usps",
                        "title": "USPS - Priority Mail",
                        "rate": 17.6,
                        "retail_rate": 19.25,
                        "list_rate": 17.6,
                        "is_selected": false,
                        "tracking": true,
                        "insurance": 100,
                        "free_pickup": true,
                        "shipment_id": "shp_21799cf7fd7a458e92295d952877bdb7"
                    }
                ],
                "errors": []
            }
        }
    }';

	public function set_up() {
		parent::set_up();
		$this->label_rate_service = $this->createMock( LabelRateService::class );

		$this->controller = new LabelRateRESTController( $this->label_rate_service );
		$controller       = $this->controller;
		add_action(
			'rest_api_init',
			array( $controller, 'register_routes' )
		);
		do_action( 'rest_api_init' );

		add_filter( 'wcshipping_user_can_manage_labels', '__return_true' );
		$this->set_store_to_be_eligible_for_shipping_label_creation();
	}

	public function test_quote_rate() {
		$this->label_rate_service->expects( $this->once() )
			->method( 'get_all_rates' )
			->willReturn( self::RESPONSE_SAMPLE );

		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/label/rate' );
		$request->set_header( 'Content-Type', 'application/json' );
		$request->set_body( self::SINGLE_PACKAGE_SAMPLE );

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->status );
		$this->assertEquals( self::RESPONSE_SAMPLE, $response->get_data() );
	}

	public function test_invalid_payload() {
		$this->label_rate_service->expects( $this->never() )
			->method( 'get_all_rates' );

		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/label/rate' );
		$request->set_header( 'Content-Type', 'application/json' );
		$request->set_body( '{}' );

		$response = rest_do_request( $request );

		$this->assertEquals( 400, $response->status );
		$this->assertEquals( 'Missing parameter(s): order_id, origin, destination, packages', $response->get_data()['message'] );
	}

	public function test_error_response() {
		$expected = new WP_Error(
			500,
			'Something went wrong.',
			array( 'message' => 'A typo broke the server. :(' )
		);

		$this->label_rate_service->expects( $this->once() )
			->method( 'get_all_rates' )
			->willReturn( $expected );

		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/label/rate' );
		$request->set_header( 'Content-Type', 'application/json' );
		$request->set_body( self::SINGLE_PACKAGE_SAMPLE );

		$controller = new LabelRateRESTController( $this->label_rate_service );
		$response   = $controller->quote_rates( $request );

		$this->assertEquals( 500, $response->get_error_code() );
		$this->assertEquals( 'Something went wrong.', $response->get_error_message() );
	}

	public function test_invalid_address() {
		// invalid city in US.
		$invalid_payload = '{
            "order_id": 139,
            "origin": {
                "company": "Occasional Antlion",
                "name": "HARRIS W",
                "phone": "**********",
                "country": "INVALID",
                "state": "CA",
                "address_1": "1600 AMPHITHEATRE PKWY",
                "address_2": "",
                "city": "MOUNTAIN VIEW",
                "postcode": "94043-1351"
            },
            "destination": {
                "company": "",
                "address_2": "",
                "city": "MOUNTAIN VIEW",
                "state": "CA",
                "postcode": "94043-1351",
                "country": "US",
                "phone": "**********",
                "name": " ",
                "address_1": "1600 AMPHITHEATRE PKWY"
            },
            "packages": [
                {
                    "id": "default_box",
                    "box_id": "small_flat_box",
                    "length": 21.91,
                    "width": 13.65,
                    "height": 4.13,
                    "weight": 0.2,
                    "is_letter": false
                }
            ]
        }';

		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/label/rate' );
		$request->set_header( 'Content-Type', 'application/json' );
		$request->set_body( $invalid_payload );

		$response = rest_do_request( $request );

		$this->assertEquals( 400, $response->status );
		$this->assertEquals( 'Invalid parameter(s): origin', $response->get_data()['message'] );
	}

	/**
	 * @dataProvider measurement_key_data_provider
	 *
	 * @param string $measurement_to_make_invalid
	 *
	 * @return void
	 */
	public function test_invalid_package_measurement( $measurement_to_make_invalid ) {
		$payload = $this->create_valid_payload();
		$payload['packages'][0][ $measurement_to_make_invalid ] = 'hello';

		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/label/rate' );
		$request->set_header( 'Content-Type', 'application/json' );
		$request->set_body( json_encode( $payload ) );

		$response = rest_do_request( $request );

		$this->assertEquals( 400, $response->status );
		$this->assertEquals( 'Invalid parameter(s): packages', $response->get_data()['message'] );
	}

	/**
	 * @dataProvider measurement_key_data_provider
	 *
	 * @param string $measurement_to_make_invalid
	 *
	 * @return void
	 */
	public function test_empty_package_measurement( $measurement_to_make_invalid ) {
		$payload = $this->create_valid_payload();
		$payload['packages'][0][ $measurement_to_make_invalid ] = 0;

		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/label/rate' );
		$request->set_header( 'Content-Type', 'application/json' );
		$request->set_body( json_encode( $payload ) );

		$response = rest_do_request( $request );

		$this->assertEquals( 400, $response->status );
		$this->assertEquals( 'Invalid parameter(s): packages', $response->get_data()['message'] );
		$this->assertEquals( 'packages[0][' . $measurement_to_make_invalid . '] must be greater than 0', $response->get_data()['data']['params']['packages'] );
	}

	/**
	 * Test that the controller correctly processes a payload with shipment_options.
	 */
	public function test_label_date_as_shipment_option_in_payload() {
		// Create a payload with shipment_options
		$payload                     = $this->create_valid_payload();
		$payload['shipment_options'] = array(
			'label_date' => '2023-05-15T14:30:00Z',
		);

		// Mock the label_rate_service to expect the payload with shipment_options
		// but note that the presence of shipment_options doesn't change the response
		$label_rate_service = $this->createMock( LabelRateService::class );
		$label_rate_service->expects( $this->once() )
			->method( 'get_all_rates' )
			->with(
				$this->callback(
					function ( $actual_payload ) {
						// Verify that shipment_options is passed through to the service
						return isset( $actual_payload['shipment_options'] ) &&
						$actual_payload['shipment_options']['label_date'] === '2023-05-15T14:30:00Z';
					}
				)
			)
			->willReturn( self::RESPONSE_SAMPLE );

		// Create and send the request
		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/label/rate' );
		$request->set_header( 'Content-Type', 'application/json' );
		$request->set_body( json_encode( $payload ) );

		$controller = new LabelRateRESTController( $label_rate_service );
		$response   = $controller->quote_rates( $request );

		// Verify the response - the presence of shipment_options doesn't change the response
		$this->assertEquals( 200, $response->get_status() );
		$this->assertEquals( self::RESPONSE_SAMPLE, $response->get_data() );
	}

	/**
	 * Test that the controller correctly validates the label_date format in shipment_options.
	 */
	public function test_invalid_label_date_in_shipment_options() {
		// Create a payload with invalid label_date format in shipment_options
		$payload                     = $this->create_valid_payload();
		$payload['shipment_options'] = array(
			'label_date' => '2025-02-27 14:30:45', // Not ISO 8601 format
		);

		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/label/rate' );
		$request->set_header( 'Content-Type', 'application/json' );
		$request->set_body( json_encode( $payload ) );

		$response = rest_do_request( $request );

		// Verify the response indicates an error
		$this->assertEquals( 400, $response->status );
		$this->assertEquals( 'Invalid parameter(s): shipment_options', $response->get_data()['message'] );
		$this->assertStringContainsString( 'label_date', $response->get_data()['data']['params']['shipment_options'] );
	}

	/**
	 * The controller should throw a generic WP_Error if the address country is invalid.
	 */
	public function test_validate_address_with_invalid_address() {
		$params = array(
			'phone'     => '**********',
			'country'   => 'ABCDEFG',
			'state'     => 'CA',
			'address_1' => '1600 AMPHITHEATRE PKWY',
			'address_2' => '',
			'city'      => 'MOUNTAIN VIEW',
			'postcode'  => '94043-1351',
		);

		$controller = new LabelRateRESTController( $this->label_rate_service );
		$response   = $controller->validate_address( $params );

		$this->assertTrue( is_a( $response, WP_Error::class ) );
		$this->assertEquals( 'Invalid country code provided. Country code must be 2 characters.', $response->get_error_message() );
	}

	/**
	 * The controller should throw a WP_Error if the phone number is invalid.
	 */
	public function test_validate_address_with_invalid_phone() {
		$params = array(
			'phone'     => 'asd',
			'country'   => 'US',
			'state'     => 'CA',
			'address_1' => '1600 AMPHITHEATRE PKWY',
			'address_2' => '',
			'city'      => 'MOUNTAIN VIEW',
			'postcode'  => '94043-1351',
		);

		$controller = new LabelRateRESTController( $this->label_rate_service );
		$response   = $controller->validate_address( $params );

		$this->assertTrue( is_a( $response, WP_Error::class ) );
		$this->assertEquals( 'The provided phone number is not valid', $response->get_error_message() );
	}

	/**
	 * Set up a store to be eligible for shipping label creation.
	 */
	private function set_store_to_be_eligible_for_shipping_label_creation() {
		update_option( 'woocommerce_currency', 'USD' );
		update_option( 'woocommerce_default_country', 'US' );
	}

	/**
	 * Returns a valid payload for use in tests.
	 *
	 * @return array
	 */
	private function create_valid_payload() {
		return array(
			'order_id'    => 139,
			'origin'      => array(
				'company'   => 'Occasional Antlion',
				'name'      => 'HARRIS W',
				'phone'     => '**********',
				'country'   => 'US',
				'state'     => 'CA',
				'address_1' => '1600 AMPHITHEATRE PKWY',
				'address_2' => '',
				'city'      => 'MOUNTAIN VIEW',
				'postcode'  => '94043-1351',
			),
			'destination' => array(
				'company'   => '',
				'address_2' => '',
				'city'      => 'MOUNTAIN VIEW',
				'state'     => 'CA',
				'postcode'  => '94043-1351',
				'country'   => 'US',
				'phone'     => '**********',
				'name'      => ' ',
				'address_1' => '1600 AMPHITHEATRE PKWY',
			),
			'packages'    => array(
				array(
					'id'        => 'default_box',
					'box_id'    => 'small_flat_box',
					'length'    => 21.91,
					'width'     => 13.65,
					'height'    => 4.13,
					'weight'    => 0.2,
					'is_letter' => false,
				),
			),
		);
	}
	private function measurement_key_data_provider() {
		return array(
			array( 'length' ),
			array( 'width' ),
			array( 'height' ),
			array( 'weight' ),
		);
	}
}
