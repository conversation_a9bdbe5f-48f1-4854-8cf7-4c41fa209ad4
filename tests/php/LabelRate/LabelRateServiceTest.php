<?php

namespace Automattic\WCShipping\Tests\php\LabelRate;

use Automattic\WCShipping\Utils;
use WC_Unit_Test_Case;
use Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store;
use Automattic\WCShipping\Connect\WC_Connect_Logger;
use Automattic\WCShipping\Connect\WC_Connect_API_Client_Live;
use Automattic\WCShipping\LabelRate\LabelRateService;
use WP_Error;
use WC_Helper_Product;

class LabelRateServiceTest extends WC_Unit_Test_Case {
	/**
	 * This sample is micmicing the payload after validation and sanitization,
	 * that's why country and state are renamed to country_code and state_code.
	 */
	private const SINGLE_PACKAGE_SAMPLE = '{
        "order_id": 139,
        "origin": {
            "company": "Occasional Antlion",
            "name": "HARRIS W",
            "phone": "7801234567",
            "country_code": "US",
            "state_code": "CA",
            "address_1": "1600 AMPHITHEATRE PKWY",
            "address_2": "",
            "city": "MOUNTAIN VIEW",
            "postcode": "94043-1351"
        },
        "destination": {
            "company": "",
            "address_2": "",
            "city": "MOUNTAIN VIEW",
            "state_code": "CA",
            "postcode": "94043-1351",
            "country_code": "US",
            "phone": "7801234567",
            "name": " ",
            "address_1": "1600 AMPHITHEATRE PKWY"
        },
        "packages": [
            {
                "id": "default_box",
                "box_id": "small_flat_box",
                "length": 21.91,
                "width": 13.65,
                "height": 4.13,
                "weight": 0.2,
                "is_letter": false
            }
        ]
    }';

	private const MULTI_PACKAGES_SAMPLE = '{
        "order_id": 181,
        "origin": {
            "company": "Monetary Gibbon",
            "name": "HARRIS W",
            "country_code": "US",
            "state_code": "CA",
            "address_1": "1600 AMPHITHEATRE PKWY",
            "address_2": "",
            "city": "MOUNTAIN VIEW",
            "postcode": "94043-1351",
            "phone": "7801234567"
        },
        "destination": {
            "company": "",
            "address_2": "",
            "city": "MOUNTAIN VIEW",
            "state_code": "CA",
            "postcode": "94043-1351",
            "country_code": "US",
            "name": "HARRIS W",
            "address_1": "1600 AMPHITHEATRE PKWY",
            "phone": "7801234567"
        },
        "packages": [
            {
                "id": "default_box",
                "box_id": "small_flat_box",
                "length": 21.91,
                "width": 13.65,
                "height": 4.13,
                "weight": 0.2,
                "is_letter": false
            },
            {
                "id": "client_custom_0",
                "box_id": "medium_flat_box_top",
                "length": 28.57,
                "width": 22.22,
                "height": 15.24,
                "weight": 0.2,
                "is_letter": false
            }
        ]
    }';

	public function set_up() {
		parent::set_up();
		$this->api_client_mock = $this->getMockBuilder( WC_Connect_API_Client_Live::class )
			->disableOriginalConstructor()
			->getMock();

		$this->connect_logger_mock = $this->createMock( WC_Connect_Logger::class );
		$this->settings_store      = $this->createMock( WC_Connect_Service_Settings_Store::class );
	}

	public function test_get_package_id_from_single_package() {
		$payload = json_decode(
			self::SINGLE_PACKAGE_SAMPLE,
			true
		);

		$label_rate_service = new LabelRateService( $this->api_client_mock, $this->connect_logger_mock, $this->settings_store );
		$actual_package_ids = $label_rate_service->get_package_ids_from_payload( $payload );

		$this->assertEquals( array( 'default_box' ), $actual_package_ids );
	}

	public function test_get_package_id_from_multi_packages() {
		$payload = json_decode(
			self::MULTI_PACKAGES_SAMPLE,
			true
		);

		$label_rate_service = new LabelRateService( $this->api_client_mock, $this->connect_logger_mock, $this->settings_store );
		$actual_package_ids = $label_rate_service->get_package_ids_from_payload( $payload );

		$this->assertEquals( array( 'default_box', 'client_custom_0' ), $actual_package_ids );
	}

	public function test_get_request_payload_packages() {
		$payload  = json_decode(
			self::SINGLE_PACKAGE_SAMPLE,
			true
		);
		$expected = json_decode(
			'{
            "order_id": 139,
            "origin": {
                "company": "Occasional Antlion",
                "name": "HARRIS W",
                "phone": "7801234567",
                "country_code": "US",
                "state_code": "CA",
                "address_1": "1600 AMPHITHEATRE PKWY",
                "address_2": "",
                "city": "MOUNTAIN VIEW",
                "postcode": "94043-1351"
            },
            "destination": {
                "company": "",
                "address_2": "",
                "city": "MOUNTAIN VIEW",
                "state_code": "CA",
                "postcode": "94043-1351",
                "country_code": "US",
                "phone": "7801234567",
                "name": " ",
                "address_1": "1600 AMPHITHEATRE PKWY"
            },
            "packages": [
                {
                    "id": "default_box",
                    "box_id": "small_flat_box",
                    "length": 21.91,
                    "width": 13.65,
                    "height": 4.13,
                    "weight": 0.2,
                    "is_letter": false
                },
                {
                    "id": "default_box_wcshipping_rate_type_signature_required",
                    "box_id": "small_flat_box",
                    "length": 21.91,
                    "width": 13.65,
                    "height": 4.13,
                    "weight": 0.2,
                    "is_letter": false,
                    "signature": "yes"
                },
                {
                    "id": "default_box_wcshipping_rate_type_adult_signature_required",
                    "box_id": "small_flat_box",
                    "length": 21.91,
                    "width": 13.65,
                    "height": 4.13,
                    "weight": 0.2,
                    "is_letter": false,
                    "signature": "adult"
                },
                {
                    "id": "default_box_wcshipping_rate_type_carbon_neutral",
                    "box_id": "small_flat_box",
                    "length": 21.91,
                    "width": 13.65,
                    "height": 4.13,
                    "weight": 0.2,
                    "is_letter": false,
                    "carbon_neutral": true,
                    "carrier_ids": ["upsdap"]
                },
                {
                    "id": "default_box_wcshipping_rate_type_additional_handling",
                    "box_id": "small_flat_box",
                    "length": 21.91,
                    "width": 13.65,
                    "height": 4.13,
                    "weight": 0.2,
                    "is_letter": false,
                    "additional_handling": true,
                    "carrier_ids": ["upsdap"]
                },
                {
                    "id": "default_box_wcshipping_rate_type_saturday_delivery",
                    "box_id": "small_flat_box",
                    "length": 21.91,
                    "width": 13.65,
                    "height": 4.13,
                    "weight": 0.2,
                    "is_letter": false,
                    "saturday_delivery": true,
                    "carrier_ids": ["upsdap"]
                }
            ]
        }',
			true
		);

		$label_rate_service = new LabelRateService( $this->api_client_mock, $this->connect_logger_mock, $this->settings_store );
		$actual_packages    = $label_rate_service->get_request_payload_packages( $payload['packages'] );
		$this->assertEquals( $expected['packages'], $actual_packages );
	}

	/**
	 * There should be 6 packages in total. "signature" and "adult signature" is added
	 * to each of the multi-packages sample.
	 */
	public function test_get_request_payload_packages_multi_package() {
		$payload  = json_decode(
			self::MULTI_PACKAGES_SAMPLE,
			true
		);
		$expected = json_decode(
			'{
            "order_id": 181,
            "origin": {
                "company": "Monetary Gibbon",
                "name": "HARRIS W",
                "phone": "7801234567",
                "country_code": "US",
                "state_code": "CA",
                "address_1": "1600 AMPHITHEATRE PKWY",
                "address_2": "",
                "city": "MOUNTAIN VIEW",
                "postcode": "94043-1351"
            },
            "destination": {
                "company": "",
                "address_2": "",
                "city": "MOUNTAIN VIEW",
                "state_code": "CA",
                "postcode": "94043-1351",
                "country_code": "US",
                "phone": "7801234567",
                "name": "HARRIS W",
                "address_1": "1600 AMPHITHEATRE PKWY"
            },
            "packages": [
                {
                    "id": "default_box",
                    "box_id": "small_flat_box",
                    "length": 21.91,
                    "width": 13.65,
                    "height": 4.13,
                    "weight": 0.2,
                    "is_letter": false
                },
                {
                    "id": "client_custom_0",
                    "box_id": "medium_flat_box_top",
                    "length": 28.57,
                    "width": 22.22,
                    "height": 15.24,
                    "weight": 0.2,
                    "is_letter": false
                },
                {
                    "id": "default_box_wcshipping_rate_type_signature_required",
                    "box_id": "small_flat_box",
                    "length": 21.91,
                    "width": 13.65,
                    "height": 4.13,
                    "weight": 0.2,
                    "is_letter": false,
                    "signature": "yes"
                },
                {
                    "id": "client_custom_0_wcshipping_rate_type_signature_required",
                    "box_id": "medium_flat_box_top",
                    "length": 28.57,
                    "width": 22.22,
                    "height": 15.24,
                    "weight": 0.2,
                    "is_letter": false,
                    "signature": "yes"
                },
                {
                    "id": "default_box_wcshipping_rate_type_adult_signature_required",
                    "box_id": "small_flat_box",
                    "length": 21.91,
                    "width": 13.65,
                    "height": 4.13,
                    "weight": 0.2,
                    "is_letter": false,
                    "signature": "adult"
                },
                {
                    "id": "client_custom_0_wcshipping_rate_type_adult_signature_required",
                    "box_id": "medium_flat_box_top",
                    "length": 28.57,
                    "width": 22.22,
                    "height": 15.24,
                    "weight": 0.2,
                    "is_letter": false,
                    "signature": "adult"
                },
                {
                    "id": "default_box_wcshipping_rate_type_carbon_neutral",
                    "box_id": "small_flat_box",
                    "length": 21.91,
                    "width": 13.65,
                    "height": 4.13,
                    "weight": 0.2,
                    "is_letter": false,
                    "carbon_neutral": true,
                    "carrier_ids": ["upsdap"]
                },
                {
                    "id": "client_custom_0_wcshipping_rate_type_carbon_neutral",
                    "box_id": "medium_flat_box_top",
                    "length": 28.57,
                    "width": 22.22,
                    "height": 15.24,
                    "weight": 0.2,
                    "is_letter": false,
                    "carbon_neutral": true,
                    "carrier_ids": ["upsdap"]
                },
                {
                    "id": "default_box_wcshipping_rate_type_additional_handling",
                    "box_id": "small_flat_box",
                    "length": 21.91,
                    "width": 13.65,
                    "height": 4.13,
                    "weight": 0.2,
                    "is_letter": false,
                    "additional_handling": true,
                    "carrier_ids": ["upsdap"]
                },
                {
                    "id": "client_custom_0_wcshipping_rate_type_additional_handling",
                    "box_id": "medium_flat_box_top",
                    "length": 28.57,
                    "width": 22.22,
                    "height": 15.24,
                    "weight": 0.2,
                    "is_letter": false,
                    "additional_handling": true,
                    "carrier_ids": ["upsdap"]
                },
                {
                    "id": "default_box_wcshipping_rate_type_saturday_delivery",
                    "box_id": "small_flat_box",
                    "length": 21.91,
                    "width": 13.65,
                    "height": 4.13,
                    "weight": 0.2,
                    "is_letter": false,
                    "saturday_delivery": true,
                    "carrier_ids": ["upsdap"]
                },
                {
                    "id": "client_custom_0_wcshipping_rate_type_saturday_delivery",
                    "box_id": "medium_flat_box_top",
                    "length": 28.57,
                    "width": 22.22,
                    "height": 15.24,
                    "weight": 0.2,
                    "is_letter": false,
                    "saturday_delivery": true,
                    "carrier_ids": ["upsdap"]
                }
            ]
        }',
			true
		);

		$label_rate_service = new LabelRateService( $this->api_client_mock, $this->connect_logger_mock, $this->settings_store );
		$actual_packages    = $label_rate_service->get_request_payload_packages( $payload['packages'] );
		$this->assertEquals( $expected['packages'], $actual_packages );
	}

	/**
	 * Test the function that normalize the response. merge_all_rates()
	 * combines extra rates.
	 */
	public function test_merge_extra_rates() {
		$response_sample = json_decode(
			'{
            "default_box": {
                "rates": [{
                    "rate_id": "rate_111"
                }],
                "errors": []
            },
            "default_box_wcshipping_rate_type_signature_required": {
                "rates": [{
                    "rate_id": "rate_222"
                }],
                "errors": []
            },
            "default_box_wcshipping_rate_type_adult_signature_required": {
                "rates": [{
                    "rate_id": "rate_333"
                }],
                "errors": []
            },
            "default_box_wcshipping_rate_type_carbon_neutral": {
                "rates": [{
                    "rate_id": "rate_444"
                }],
                "errors": []
            },
            "default_box_wcshipping_rate_type_additional_handling": {
                "rates": [{
                    "rate_id": "rate_555"
                }],
                "errors": []
            },
            "default_box_wcshipping_rate_type_saturday_delivery": {
                "rates": [{
                    "rate_id": "rate_666"
                }],
                "errors": []
            }
        }'
		);

		/**
		 * The response should look like:
		 *
		 * {
		 *    "default_box": {
		 *        "default": {
		 *            "rates": [{
		 *                "rate_id": "rate_111"
		 *            }],
		 *            "errors": []
		 *        },
		 *        "signature_required": {
		 *            "rates": [{
		 *                "rate_id": "rate_222"
		 *            }],
		 *            "errors": []
		 *        },
		 *        "adult_signature_required": {
		 *            "rates": [{
		 *                "rate_id": "rate_333"
		 *            }],
		 *             "errors": []
		 *        },
		 *        "carbon_neutral": {
		 *            "rates": [{
		 *                "rate_id": "rate_444"
		 *            }],
		 *             "errors": []
		 *        },
		 *        "additional_handling": {
		 *            "rates": [{
		 *                "rate_id": "rate_555"
		 *            }],
		 *             "errors": []
		 *        },
		 *        "saturday_delivery": {
		 *            "rates": [{
		 *                "rate_id": "rate_666"
		 *            }],
		 *             "errors": []
		 *        }
		 *    }
		 * }
		 */

		$label_rate_service = new LabelRateService( $this->api_client_mock, $this->connect_logger_mock, $this->settings_store );
		$actual             = $label_rate_service->merge_extra_rates( $response_sample, array( 'default_box' ) );

		$this->assertEquals( 'rate_111', $actual->default_box->default->rates[0]->rate_id );
		$this->assertEquals( 'rate_222', $actual->default_box->signature_required->rates[0]->rate_id );
		$this->assertEquals( 'rate_333', $actual->default_box->adult_signature_required->rates[0]->rate_id );
		$this->assertEquals( 'rate_444', $actual->default_box->carbon_neutral->rates[0]->rate_id );
		$this->assertEquals( 'rate_555', $actual->default_box->additional_handling->rates[0]->rate_id );
		$this->assertEquals( 'rate_666', $actual->default_box->saturday_delivery->rates[0]->rate_id );
	}

	/**
	 * Test happy path for request_rates(). It should return whatever the client returns.
	 */
	public function test_request_rates_success() {
		$payload         = json_decode(
			self::SINGLE_PACKAGE_SAMPLE,
			true
		);
		$response_sample = 'Some response that should be an object';

		$this->api_client_mock->expects( $this->once() )
			->method( 'get_label_rates' )
			->willReturn( $response_sample );

		$label_rate_service = new LabelRateService( $this->api_client_mock, $this->connect_logger_mock, $this->settings_store );

		$actual = $label_rate_service->request_rates( $payload, array( 'default_box' ) );
		$this->assertEquals( 'Some response that should be an object', $actual );
	}

	/**
	 * This tests the scenario when the client returns a 500 error.
	 */
	public function test_request_rates_fail() {
		$payload         = json_decode(
			self::SINGLE_PACKAGE_SAMPLE,
			true
		);
		$response_sample = new WP_Error(
			500,
			'Something went wrong with the server'
		);
		$expectedError   = new WP_Error(
			500,
			'Something went wrong with the server',
			array( 'message' => 'Something went wrong with the server' )
		);

		$this->api_client_mock->expects( $this->once() )
			->method( 'get_label_rates' )
			->willReturn( $response_sample );

		$this->connect_logger_mock->expects( $this->once() )
			->method( 'log' )
			->with( $expectedError, LabelRateService::class );

		$label_rate_service = new LabelRateService( $this->api_client_mock, $this->connect_logger_mock, $this->settings_store );

		$actual = $label_rate_service->request_rates( $payload, array( 'default_box' ) );
		$this->assertEquals( $expectedError, $actual );
	}

	/**
	 * This tests if we can successfully update the custom form via product meta.
	 */
	public function test_update_product_and_payload_customs_information() {

		$product    = $this->create_simple_product();
		$product_id = $product->get_id();

		$payload = json_decode(
			'{
                "order_id": 139,
                "origin": {
                    "company": "Colorful Deer",
                    "name": "HARRIS W",
                    "country": "US",
                    "state": "CA",
                    "address_1": "1600 AMPHITHEATRE PKWY",
                    "address_2": "",
                    "city": "MOUNTAIN VIEW",
                    "postcode": "94043-1351",
                    "phone": "7801234567"
                },
                "destination": {
                    "company": "",
                    "address_2": "",
                    "city": "TORONTO",
                    "state": "ON",
                    "postcode": "M1M 1M1",
                    "country": "CA",
                    "name": "HARRIS W",
                    "address_1": "123 TORONTO ROAD",
                    "phone": "7801234567"
                },
                "packages": [
                    {
                        "id": "default_box",
                        "box_id": "small_flat_box",
                        "length": 21.91,
                        "width": 13.65,
                        "height": 4.13,
                        "weight": 0.2,
                        "is_letter": false,
                        "contents_type": "merchandise",
                        "restriction_type": "none",
                        "non_delivery_option": "return",
                        "itn": "",
                        "items": [
                            {
                                "description": "fasion-hat - Hat",
                                "quantity": 2,
                                "value": 24,
                                "weight": 0.4,
                                "hs_tariff_number": "98765",
                                "origin_country": "US",
                                "product_id": ' . $product_id . '
                            }
                        ]
                    }
                ]
            }',
			true
		);

		// Before updating custom form
		$this->assertEquals( '', $product->get_meta( 'wcshipping_customs_info' ) );
		$this->assertEquals( 24, $payload['packages'][0]['items'][0]['value'] ); // This "value" represents the individual item's value, not the total.
		$this->assertEquals( 0.4, $payload['packages'][0]['items'][0]['weight'] ); // This "weight" represents the individual item's weight, not the total.

		$label_rate_service = new LabelRateService( $this->api_client_mock, $this->connect_logger_mock, $this->settings_store );
		$label_rate_service->update_product_and_payload_customs_information( $payload );

		// Retrieve the product again to see if the meta is updated.
		$product = wc_get_product( $product_id );

		$this->assertEquals(
			array(
				'description'      => 'fasion-hat - Hat',
				'hs_tariff_number' => '98765',
				'origin_country'   => 'US',
			),
			Utils::get_product_customs_data( $product )
		);
		$this->assertEquals( 48, $payload['packages'][0]['items'][0]['value'] ); // This "value" is now the total value.
		$this->assertEquals( 0.8, $payload['packages'][0]['items'][0]['weight'] ); // This "weight" is now the total weight.
	}

	/**
	 * This tests multiple packages in 1 shipment.
	 */
	public function test_update_product_and_payload_customs_information_with_multiple_packages() {

		$product1    = $this->create_simple_product();
		$product1_id = $product1->get_id();
		$product2    = $this->create_simple_product();
		$product2_id = $product2->get_id();
		$product3    = $this->create_simple_product();
		$product3_id = $product3->get_id();

		$payload = json_decode(
			'{
                "order_id": 139,
                "origin": {
                    "company": "Colorful Deer",
                    "name": "HARRIS W",
                    "country": "US",
                    "state": "CA",
                    "address_1": "1600 AMPHITHEATRE PKWY",
                    "address_2": "",
                    "city": "MOUNTAIN VIEW",
                    "postcode": "94043-1351",
                    "phone": "7801234567"
                },
                "destination": {
                    "company": "",
                    "address_2": "",
                    "city": "TORONTO",
                    "state": "ON",
                    "postcode": "M1M 1M1",
                    "country": "CA",
                    "name": "HARRIS W",
                    "address_1": "123 TORONTO ROAD",
                    "phone": "7801234567"
                },
                "packages":[
                    {
                        "id": "0",
                        "box_id": "custom_box",
                        "length": 10,
                        "width": 6,
                        "height": 6,
                        "weight": 30,
                        "is_letter": false,
                        "contents_type": "merchandise",
                        "restriction_type": "none",
                        "non_delivery_option": "abandon",
                        "itn": "",
                        "items": [
                            {
                                "description": "Belt",
                                "quantity": 1,
                                "weight": 5,
                                "hs_tariff_number": "97020001",
                                "origin_country": "US",
                                "product_id": ' . $product1_id . ',
                                "value": 55
                            },
                            {
                                "description": "Beanie with Logo",
                                "quantity": 5,
                                "weight": 6,
                                "hs_tariff_number": "97020002",
                                "origin_country": "US",
                                "product_id": ' . $product2_id . ',
                                "value": 18
                            },
                            {
                                "description": "Cap",
                                "quantity": 1,
                                "weight": 7,
                                "hs_tariff_number": "97020003",
                                "origin_country": "US",
                                "product_id": ' . $product3_id . ',
                                "value": 16
                            }
                        ]
                    }
                ]
            }',
			true
		);

		// Before updating custom form
		$this->assertEquals( '', $product1->get_meta( 'wcshipping_customs_info' ) );
		$this->assertEquals( '', $product2->get_meta( 'wcshipping_customs_info' ) );
		$this->assertEquals( '', $product3->get_meta( 'wcshipping_customs_info' ) );

		// This "value" represents the individual item's value, not the total.
		$this->assertEquals( 55, $payload['packages'][0]['items'][0]['value'] );
		$this->assertEquals( 18, $payload['packages'][0]['items'][1]['value'] );
		$this->assertEquals( 16, $payload['packages'][0]['items'][2]['value'] );

		// This "weight" represents the individual item's weight, not the total.
		$this->assertEquals( 5, $payload['packages'][0]['items'][0]['weight'] );
		$this->assertEquals( 6, $payload['packages'][0]['items'][1]['weight'] );
		$this->assertEquals( 7, $payload['packages'][0]['items'][2]['weight'] );

		$label_rate_service = new LabelRateService( $this->api_client_mock, $this->connect_logger_mock, $this->settings_store );
		$label_rate_service->update_product_and_payload_customs_information( $payload );

		// Retrieve the product again to see if the meta is updated.
		$product = wc_get_product( $product1_id );
		$this->assertEquals(
			array(
				'description'      => 'Belt',
				'hs_tariff_number' => '97020001',
				'origin_country'   => 'US',
			),
			Utils::get_product_customs_data( $product )
		);
		$product = wc_get_product( $product2_id );
		$this->assertEquals(
			array(
				'description'      => 'Beanie with Logo',
				'hs_tariff_number' => '97020002',
				'origin_country'   => 'US',
			),
			Utils::get_product_customs_data( $product )
		);
		$product = wc_get_product( $product3_id );
		$this->assertEquals(
			array(
				'description'      => 'Cap',
				'hs_tariff_number' => '97020003',
				'origin_country'   => 'US',
			),
			Utils::get_product_customs_data( $product )
		);

		// Check if the total values are updated according to the quantity
		$this->assertEquals( 55 * 1, $payload['packages'][0]['items'][0]['value'] );
		$this->assertEquals( 18 * 5, $payload['packages'][0]['items'][1]['value'] );
		$this->assertEquals( 16 * 1, $payload['packages'][0]['items'][2]['value'] );

		// Check if the total weight are updated according to the quantity
		$this->assertEquals( 5 * 1, $payload['packages'][0]['items'][0]['weight'] );
		$this->assertEquals( 6 * 5, $payload['packages'][0]['items'][1]['weight'] );
		$this->assertEquals( 7 * 1, $payload['packages'][0]['items'][2]['weight'] );
	}

	public function test_normalize_api_rate_request() {
		$payload = json_decode(
			self::SINGLE_PACKAGE_SAMPLE,
			true
		);

		$label_rate_service = new LabelRateService( $this->api_client_mock, $this->connect_logger_mock, $this->settings_store );

		$actual = $label_rate_service->normalize_api_rate_request( $payload );
		$this->assertEquals( '1600 AMPHITHEATRE PKWY', $actual['origin']['address'] );
		$this->assertEquals( 'US', $actual['origin']['country'] );
		$this->assertEquals( 'CA', $actual['origin']['state'] );

		$this->assertEquals( '1600 AMPHITHEATRE PKWY', $actual['destination']['address'] );
		$this->assertEquals( 'US', $actual['destination']['country'] );
		$this->assertEquals( 'CA', $actual['destination']['state'] );
	}

	// Domestic shipment should not have any customs form.
	public function test_payload_is_not_updated_for_domestic_shipment() {
		$payload = json_decode(
			'{
                "order_id": 139,
                "origin": {
                    "company": "Colorful Deer",
                    "name": "HARRIS W",
                    "country": "US",
                    "state": "CA",
                    "address_1": "1600 AMPHITHEATRE PKWY",
                    "address_2": "",
                    "city": "MOUNTAIN VIEW",
                    "postcode": "94043-1351",
                    "phone": "7801234567"
                },
                "destination": {
                    "company": "",
                    "address_2": "",
                    "city": "TORONTO",
                    "state": "ON",
                    "postcode": "M1M 1M1",
                    "country": "CA",
                    "name": "HARRIS W",
                    "address_1": "123 TORONTO ROAD",
                    "phone": "7801234567"
                },
                "packages": [
                    {
                        "id": "0",
                        "box_id": "custom_box",
                        "length": 10,
                        "width": 6,
                        "height": 6,
                        "weight": 30,
                        "is_letter": false
                    }
                ]
            }',
			true
		);

		// Domestic shipment does not have a content type. "content type" is merchanidise, etc.
		$this->assertFalse( array_key_exists( 'contents_type', $payload ) );
		$original_payload = $payload; // make a copy because update_product_and_payload_customs_information() passes payload by reference.

		$label_rate_service = new LabelRateService( $this->api_client_mock, $this->connect_logger_mock, $this->settings_store );
		$label_rate_service->update_product_and_payload_customs_information( $payload );

		$this->assertEquals( $original_payload, $payload ); // Nothing should be modified.
	}

	/**
	 * Test that we correctly null out the origin name if we have a company name to display instead.
	 *
	 * @return void
	 */
	public function test_normalize_origin_sender_name_if_we_have_a_company_name() {
		$payload = json_decode(
			self::SINGLE_PACKAGE_SAMPLE,
			true
		);

		$payload['origin']['name']    = 'MY ORIGIN NAME';
		$payload['origin']['company'] = 'AUTOMATTIC INC';

		$label_rate_service = new LabelRateService( $this->api_client_mock, $this->connect_logger_mock, $this->settings_store );

		$normalized_payload = $label_rate_service->normalize_api_rate_request( $payload );
		$this->assertSame( 'AUTOMATTIC INC', $normalized_payload['origin']['company'] );
		$this->assertSame( '', $normalized_payload['origin']['name'] );
	}

	/**
	 * A helper to create a simple product that is default to be shippable.
	 *
	 * @return WC_Product_Simple|null|false
	 */
	private function create_simple_product() {
		$product = WC_Helper_Product::create_simple_product();
		$product->set_virtual( false );
		$product->add_meta_data( 'wcshipping_customs_info', '' );
		$product->save();
		return $product;
	}
}
