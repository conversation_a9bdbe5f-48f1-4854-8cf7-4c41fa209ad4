<?php

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\Checkout\StoreNotice;

class StoreNoticeTest extends WCShipping_Test_Case {

	private StoreNotice $storeNotice;

	public function setUp(): void {
		parent::setUp();
	}

	public function test_constructor_sets_properties_correctly() {
		$message = 'Test message';
		$type    = 'warning';
		$data    = array( 'key' => 'value' );

		$this->storeNotice = new StoreNotice( $message, $type, $data );

		$this->assertEquals( $message, $this->storeNotice->get_message() );
		$this->assertEquals( $type, $this->storeNotice->get_type() );
		$this->assertEquals( $data, $this->storeNotice->get_data() );
	}

	public function test_set_message() {
		$this->storeNotice = new StoreNotice( 'Initial message', 'info' );

		$newMessage = 'Updated message';
		$this->storeNotice->set_message( $newMessage );

		$this->assertEquals( $newMessage, $this->storeNotice->get_message() );
	}

	public function test_set_type() {
		$this->storeNotice = new StoreNotice( 'Message', 'info' );

		$newType = 'error';
		$this->storeNotice->set_type( $newType );

		$this->assertEquals( $newType, $this->storeNotice->get_type() );
	}

	public function test_set_data() {
		$this->storeNotice = new StoreNotice( 'Message', 'info' );

		$newData = array( 'key' => 'value' );
		$this->storeNotice->set_data( $newData );

		$this->assertEquals( $newData, $this->storeNotice->get_data() );
	}

	public function test_to_array() {
		$message = 'Test message';
		$type    = 'info';
		$data    = array( 'key' => 'value' );

		$this->storeNotice = new StoreNotice( $message, $type, $data );

		$expected = array(
			'message' => $message,
			'type'    => $type,
			'data'    => $data,
		);

		$this->assertEquals( $expected, $this->storeNotice->to_array() );
	}

	public function test_get_message() {
		$message           = 'Test message';
		$this->storeNotice = new StoreNotice( $message, 'info' );

		$this->assertEquals( $message, $this->storeNotice->get_message() );
	}

	public function test_get_type() {
		$type              = 'warning';
		$this->storeNotice = new StoreNotice( 'Message', $type );

		$this->assertEquals( $type, $this->storeNotice->get_type() );
	}

	public function test_get_data() {
		$data              = array( 'key' => 'value' );
		$this->storeNotice = new StoreNotice( 'Message', 'info', $data );

		$this->assertEquals( $data, $this->storeNotice->get_data() );
	}
}
