<?php

use Automattic\WCShipping\Checkout\StoreNotice;
use Automattic\WCShipping\StoreApi\StoreApiExtendSchema;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\Checkout\CheckoutService;
use Automattic\WCShipping\Connect\WC_Connect_Logger;
use Automattic\WCShipping\Connect\WC_Connect_Options;
use Automattic\WCShipping\LabelPurchase\AddressNormalizationService;
use Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store;

class CheckoutServiceTest extends WCShipping_Test_Case {

	private CheckoutService $checkoutService;
	private AddressNormalizationService $addressNormalizationService;

	public static function set_up_before_class() {
		require_once __DIR__ . '/../../../classes/class-wc-connect-service-settings-store.php';
	}

	public function setUp(): void {
		parent::setUp();

		$this->addressNormalizationService = $this->createMock( AddressNormalizationService::class );
		$this->checkoutService             = new CheckoutService( $this->addressNormalizationService );
	}

	private function get_base_normalization_response( $success = true ) {
		if ( ! $success ) {
			return array(
				'success'                => false,
				'errors'                 => array(),
				'isTrivialNormalization' => true,
				'address'                => array(),
			);
		}

		return array(
			'success'                => true,
			'normalizedAddress'      => array(),
			'isTrivialNormalization' => true,
			'address'                => array(),
		);
	}

	private function get_default_address_validation_response() {
		return array(
			'success'          => false,
			'notices'          => array(),
			'is_address_valid' => false,
		);
	}

	private function get_default_shipping_address() {
		return array(
			'address_1' => '123 Main St',
			'address_2' => 'Apt 1',
			'city'      => 'City',
			'state'     => 'State',
			'postcode'  => '12345-1234',
			'country'   => 'US',
		);
	}

	private function get_mock_checkout_service( $shipping_address = array() ) {

		$shipping_address = empty( $shipping_address ) ? $this->get_default_shipping_address() : $shipping_address;

		$mockCheckoutService = $this->getMockBuilder( CheckoutService::class )
			->setConstructorArgs( array( $this->addressNormalizationService ) )
			->onlyMethods( array( 'get_cart_shipping_address' ) )
			->getMock();

		$mockCheckoutService->method( 'get_cart_shipping_address' )->willReturn( $shipping_address );

		return $mockCheckoutService;
	}

	public function test_get_checkout_script_data() {
		$expected = array(
			'store_api_identifier' => StoreApiExtendSchema::IDENTIFIER,
			'is_blocks_checkout'   => has_block( 'woocommerce/checkout' ),
			'settings'             => CheckoutService::get_checkout_settings(),
		);

		$data = CheckoutService::get_checkout_script_data();
		$this->assertEquals( $expected, $data );
	}

	public function test_get_checkout_settings() {
		$mockSettings = array( 'checkout_address_validation' => true );
		WC_Connect_Options::update_option( 'account_settings', $mockSettings );

		$settings = CheckoutService::get_checkout_settings();
		$this->assertTrue( $settings['is_checkout_address_validation_enabled'] );
	}

	public function test_validate_shipping_address_with_valid_normalization_response() {
		$address           = $this->get_default_shipping_address();
		$normalizedAddress = array(
			'id' => '1',
		) + $address;

		$mockNormalizationResponse                      = $this->get_base_normalization_response();
		$mockNormalizationResponse['normalizedAddress'] = $normalizedAddress;
		$mockNormalizationResponse['address']           = $address;

		$mockCheckoutService = $this->get_mock_checkout_service( $address );

		$this->addressNormalizationService->method( 'get_normalization_response' )->willReturn( $mockNormalizationResponse );

		$response = $mockCheckoutService->validate_shipping_address();

		$this->assertTrue( $response['success'] );
		$this->assertEmpty( $response['notices'] );
		$this->assertTrue( $response['is_address_valid'] );
	}

	public function test_validate_shipping_address_with_wp_error_as_normalization_response() {
		$mockNormalizationResponse = new WP_Error( 'error', 'error message' );
		$default_response          = $this->get_default_address_validation_response();

		$mockCheckoutService = $this->get_mock_checkout_service();

		$this->addressNormalizationService->method( 'get_normalization_response' )->willReturn( $mockNormalizationResponse );

		$response = $mockCheckoutService->validate_shipping_address();

		$this->assertEquals( $default_response, $response );
	}

	public function test_validate_shipping_address_with_address_errors_in_normalization_response() {
		$mockNormalizationResponse           = $this->get_base_normalization_response( false );
		$mockNormalizationResponse['errors'] = array(
			'error message',
		);

		$mockCheckoutService = $this->get_mock_checkout_service();

		$this->addressNormalizationService->method( 'get_normalization_response' )->willReturn( $mockNormalizationResponse );

		$response = $mockCheckoutService->validate_shipping_address();

		$this->assertTrue( $response['success'] );
		$this->assertNotEmpty( $response['notices'][0] );
		$this->assertInstanceof( StoreNotice::class, $response['notices'][0] );
		$this->assertFalse( $response['is_address_valid'] );
	}

	public function test_validate_shipping_address_without_suggested_address_in_normalization_response() {
		$mockNormalizationResponse                      = $this->get_base_normalization_response();
		$mockNormalizationResponse['normalizedAddress'] = array();

		$mockCheckoutService = $this->get_mock_checkout_service();

		$this->addressNormalizationService->method( 'get_normalization_response' )->willReturn( $mockNormalizationResponse );

		$response = $mockCheckoutService->validate_shipping_address();

		$this->assertTrue( $response['success'] );
		$this->assertNotEmpty( $response['notices'][0] );
		$this->assertInstanceof( StoreNotice::class, $response['notices'][0] );
		$this->assertFalse( $response['is_address_valid'] );
	}

	public function test_validate_shipping_address_with_suggested_address_in_normalization_response() {
		$address           = $this->get_default_shipping_address();
		$normalizedAddress = array(
			'id' => '1',
		) + $address;

		$normalizedAddress['address_1'] = '123 First Avenue';

		$mockNormalizationResponse                      = $this->get_base_normalization_response();
		$mockNormalizationResponse['normalizedAddress'] = $normalizedAddress;
		$mockNormalizationResponse['address']           = $address;

		$mockCheckoutService = $this->get_mock_checkout_service();

		$this->addressNormalizationService->method( 'get_normalization_response' )->willReturn( $mockNormalizationResponse );

		$response = $mockCheckoutService->validate_shipping_address();

		$this->assertTrue( $response['success'] );
		$this->assertNotEmpty( $response['notices'][0] );
		$this->assertInstanceof( StoreNotice::class, $response['notices'][0] );
		$this->assertFalse( $response['is_address_valid'] );
	}
}
