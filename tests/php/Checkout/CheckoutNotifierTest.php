<?php

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\Checkout\CheckoutNotifier;

class CheckoutNotifierTest extends WCShipping_Test_Case {

	/**
	 * @var CheckoutNotifier
	 */
	private $notifier;

	public function setUp(): void {
		parent::setUp();

		// Instantiate the CheckoutNotifier with debug enabled
		$this->notifier = new CheckoutNotifier( true );
	}

	public function test_debug_calls_is_debug_enabled() {
		$mockNotifier = $this->getMockBuilder( CheckoutNotifier::class )
			->onlyMethods( array( 'is_debug_enabled' ) )
			->disableOriginalConstructor()
			->getMock();

		$mockNotifier->expects( $this->once() )->method( 'is_debug_enabled' );

		$mockNotifier->debug( 'Debug message', 'notice', array( 'key' => 'value' ), 'group' );
	}

	public function test_info_adds_notice() {
		$this->notifier->info( 'Info message', array( 'key' => 'value' ), 'group' );
		$this->assertTrue( $this->notifier->has_notice( 'Info message', 'notice', array( 'key' => 'value' ), 'group' ) );
	}

	public function test_error_adds_notice() {
		$this->notifier->error( 'Error message', array( 'key' => 'value' ), 'group' );
		$this->assertTrue( $this->notifier->has_notice( 'Error message', 'error', array( 'key' => 'value' ), 'group' ) );
	}

	public function test_success_adds_notice() {
		$this->notifier->success( 'Success message', array( 'key' => 'value' ), 'group' );
		$this->assertTrue( $this->notifier->has_notice( 'Success message', 'success', array( 'key' => 'value' ), 'group' ) );
	}
}
