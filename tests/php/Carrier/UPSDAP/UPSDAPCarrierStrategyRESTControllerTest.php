<?php
/**
 * Unit tests for UPSDAPCarrierStrategyRESTController.
 *
 * @package Automattic\WCShipping
 */

namespace Automattic\WCShipping\Tests\php\Carrier\UPSDAP;

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\Carrier\UPSDAP\UPSDAPCarrierStrategyService;
use Automattic\WCShipping\Carrier\UPSDAP\UPSDAPCarrierStrategyRESTController;
use Automattic\WCShipping\Connect\WC_Connect_API_Client_Live;
use Automattic\WCShipping\OriginAddresses\OriginAddressService;

/**
 * Unit test for UPSDAPCarrierStrategyRESTController.
 */
class UPSDAPCarrierStrategyRESTControllerTest extends WCShipping_Test_Case {

	const REST_ENDPOINT = '/wcshipping/v1/carrier-strategy/upsdap';

	/**
	 * Mocked UPSDAPCarrierStrategyService.
	 *
	 * @var UPSDAPCarrierStrategyService|\PHPUnit\Framework\MockObject\MockObject
	 */
	protected $ups_carrier_service_mock;

	/**
	 * Set up the test case.
	 *
	 * @see WC_Unit_Test_Case::setUp()
	 */
	public function setUp(): void {
		parent::setUp();

		$api_client_mock = $this->getMockBuilder( WC_Connect_API_Client_Live::class )
			->disableOriginalConstructor()
			->getMock();

		$origin_address_service_mock = $this->getMockBuilder( OriginAddressService::class )
			->disableOriginalConstructor()
			->getMock();

		// Create mock for the UPS carrier service.
		$this->ups_carrier_service_mock = $this->getMockBuilder( UPSDAPCarrierStrategyService::class )
			->setConstructorArgs( array( $origin_address_service_mock, $api_client_mock ) )
			->getMock();

		// Create the controller and register routes
		$controller = new UPSDAPCarrierStrategyRESTController( $this->ups_carrier_service_mock );
		add_action(
			'rest_api_init',
			function () use ( $controller ) {
				$controller->register_routes();
			}
		);

		// Trigger route registration
		do_action( 'rest_api_init' );
	}

	/**
	 * Test updating UPS carrier strategy with missing parameters (confirmed).
	 */
	public function test_update_carrier_strategy_with_missing_parameters(): void {
		// Create the request with missing 'confirmed' parameter
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body(
			json_encode(
				array(
					'origin' => array(),
				)
			)
		);

		// Execute the request
		$response = rest_do_request( $request );

		// Assert that the response has a 500 status code (internal error).
		$this->assertSame( 500, $response->get_status() );

		// Assert that the response includes an appropriate error message
		$response_data = $response->get_data();
		$this->assertSame( 400, $response_data['code'] );
		$this->assertSame( 'Required parameter is missing: confirmed', $response_data['message'] );
	}

	/**
	 * Test update method handling missing origin.
	 */
	public function test_update_carrier_strategy_with_missing_origin(): void {
		// Create the request without 'origin'.
		$request = $this->create_request( self::REST_ENDPOINT );
		$request->set_body(
			json_encode(
				array(
					'confirmed' => true,
				)
			)
		);

		// Execute the request
		$response = rest_do_request( $request );

		// Assert that the response has a 500 status code (internal error).
		$this->assertSame( 500, $response->get_status() );

		// Assert that the response includes an appropriate error message
		$response_data = $response->get_data();
		$this->assertSame( 400, $response_data['code'] );
		$this->assertSame( 'Required parameter is missing: origin', $response_data['message'] );
	}

	/**
	 * Test update method without body.
	 */
	public function test_update_carrier_strategy_without_body(): void {
		// Create the request without a body
		$request = $this->create_request( self::REST_ENDPOINT );

		// Execute the request
		$response = rest_do_request( $request );

		// Assert that the response has a 500 status code (internal error).
		$this->assertSame( 500, $response->get_status() );

		// Assert that the response includes an appropriate error message
		$response_data = $response->get_data();
		$this->assertSame( 400, $response_data['code'] );
		$this->assertSame( 'Required parameter is missing: origin', $response_data['message'] );
	}
}
