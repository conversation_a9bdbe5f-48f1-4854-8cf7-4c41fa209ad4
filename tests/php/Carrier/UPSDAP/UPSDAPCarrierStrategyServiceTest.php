<?php

use Automattic\WCShipping\Connect\WC_Connect_API_Client;
use Automattic\WCShipping\Connect\WC_Connect_API_Client_Live;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\OriginAddresses\OriginAddressService;
use Automattic\WCShipping\Connect\WC_Connect_Options;
use Automattic\WCShipping\Carrier\UPSDAP\UPSDAPCarrierStrategyService;

class UPSDAPCarrierStrategyServiceTest extends WCShipping_Test_Case {

	/**
	 * @var UPSDAPCarrierStrategyService
	 */
	private $upsCarrierStrategyService;

	/**
	 * @var OriginAddressService|\PHPUnit\Framework\MockObject\MockObject
	 */
	private $originAddressServiceMock;

	/**
	 * @var WC_Connect_API_Client
	 */
	private $api_client_mock;

	function setUp(): void {
		parent::setUp();
		$this->originAddressServiceMock  = $this->createMock( OriginAddressService::class );
		$this->api_client_mock           = $this->createMock( WC_Connect_API_Client_Live::class );
		$this->upsCarrierStrategyService = new UPSDAPCarrierStrategyService( $this->originAddressServiceMock, $this->api_client_mock );
	}

	public function test_get_strategies_returns_default_if_none_exist() {
		WC_Connect_Options::delete_option( UPSDAPCarrierStrategyService::UPSDAP_STRATEGIES_KEY );

		$mockedAddresses = array(
			array( 'id' => 'address_1' ),
			array( 'id' => 'address_2' ),
		);
		$this->originAddressServiceMock
			->expects( $this->once() )
			->method( 'get_origin_addresses' )
			->willReturn( $mockedAddresses );

		$strategies = $this->upsCarrierStrategyService->get_strategies();

		$this->assertCount( 2, $strategies['origin_address'] );
		$this->assertFalse( $strategies['origin_address']['address_1']['has_agreed_to_tos'] );
		$this->assertFalse( $strategies['origin_address']['address_2']['has_agreed_to_tos'] );
	}

	public function test_get_strategies_returns_existing_strategies() {
		$existingStrategies = array(
			'origin_address' => array(
				'address_1' => array( 'has_agreed_to_tos' => true ),
			),
		);
		WC_Connect_Options::update_option( UPSDAPCarrierStrategyService::UPSDAP_STRATEGIES_KEY, $existingStrategies );

		$strategies = $this->upsCarrierStrategyService->get_strategies();

		$this->assertCount( 1, $strategies['origin_address'] );
		$this->assertTrue( $strategies['origin_address']['address_1']['has_agreed_to_tos'] );
	}

	public function test_update_strategies_updates_strategies() {
		// Mock the API client to return success.
		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_tos_acceptance_for_origin_address' )
			->willReturn( true );

		$origin  = array(
			'name'      => 'Test Store 2',
			'company'   => 'Test Company 2',
			'address_1' => '456 Test Ave',
			'address_2' => 'Floor 2',
			'city'      => 'Test Town',
			'state'     => 'NY',
			'postcode'  => '12345',
			'country'   => 'US',
			'email'     => '<EMAIL>',
			'phone'     => '************',
		);
		$options = array( 'tos' => true );

		$result = $this->upsCarrierStrategyService->update_strategies( $origin, $options );

		$this->assertTrue( $result );
	}

	public function test_revoke_tos_for_address_returns_if_address_not_found() {
		$address_id = 'missing_address_id';

		// Mock the strategies to return an origin_address array that does not contain the address_id
		$existingStrategies = array(
			'origin_address' => array(
				'address_1' => array( 'has_agreed_to_tos' => true ),
			),
		);
		WC_Connect_Options::update_option( UPSDAPCarrierStrategyService::UPSDAP_STRATEGIES_KEY, $existingStrategies );

		// Expect that update_strategies will not be called since the address_id is missing
		$this->upsCarrierStrategyService->revoke_tos_for_address( $address_id );

		// Fetch the strategies after attempting to revoke TOS
		$updatedStrategies = $this->upsCarrierStrategyService->get_strategies();

		// Assert that the strategies remain unchanged since the address_id was not found
		$this->assertArrayNotHasKey( $address_id, $updatedStrategies['origin_address'] );
		$this->assertTrue( $updatedStrategies['origin_address']['address_1']['has_agreed_to_tos'] );
	}
}
