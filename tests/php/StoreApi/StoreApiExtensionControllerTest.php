<?php

use Automattic\WCShipping\StoreApi\StoreApiExtendSchema;
use PHPUnit\Framework\TestCase;
use Automattic\WCShipping\StoreApi\StoreApiExtensionController;
use Automattic\WCShipping\StoreApi\AbstractStoreApiExtension;

class StoreApiExtensionControllerTest extends TestCase {

	/**
	 * @var StoreApiExtensionController
	 */
	private $controller;

	public function setUp(): void {
		parent::setUp();

		// Instantiate the StoreApiExtensionController with mocks
		$this->controller = new StoreApiExtensionController( StoreApiExtendSchema::instance() );
	}

	public function test_register_extension_adds_extension() {
		$mockExtension = $this->createMock( AbstractStoreApiExtension::class );

		$this->controller->register_extension( $mockExtension );

		$registeredExtensions = $this->controller->get_registered_extensions();

		$this->assertCount( 1, $registeredExtensions );
		$this->assertSame( $mockExtension, $registeredExtensions[0] );
	}

	public function test_extend_store_returns_early() {
		// Create a partial mock for the controller to spy on the register_endpoint_data and register_update_callback methods
		$controllerMock = $this->getMockBuilder( StoreApiExtensionController::class )
			->setConstructorArgs( array( StoreApiExtendSchema::instance() ) )
			->onlyMethods( array( 'get_registered_extensions' ) )
			->getMock();

		$controllerMock->expects( $this->once() )
			->method( 'get_registered_extensions' )
			->willReturn( array() );

		// Call the method
		$controllerMock->extend_store();
	}

	public function test_extend_store_calls_register_methods() {
		// Create a mock for the extension
		$mockExtension = $this->createMock( AbstractStoreApiExtension::class );

		// Create a partial mock for the controller to spy on the register_endpoint_data and register_update_callback methods
		$controllerMock = $this->getMockBuilder( StoreApiExtensionController::class )
			->setConstructorArgs( array( StoreApiExtendSchema::instance() ) )
			->onlyMethods( array( 'register_endpoint_data', 'register_update_callback' ) )
			->getMock();

		// Add the mock extension to the controller
		$controllerMock->register_extension( $mockExtension );

		$controllerMock->expects( $this->once() )
			->method( 'register_endpoint_data' )
			->with( $mockExtension );

		$controllerMock->expects( $this->once() )
			->method( 'register_update_callback' )
			->with( $mockExtension );

		// Call the method
		$controllerMock->extend_store();
	}
}
