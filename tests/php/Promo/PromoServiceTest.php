<?php
/**
 * Class PromoServiceTest
 * @package Automattic\WCShipping
 */

namespace Automattic\WCShipping\Tests\Promo;

use Automattic\WCShipping\Connect\WC_Connect_Service_Schemas_Store;
use Automattic\WCShipping\Promo\PromoService;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * Tests for PromoService.
 */
class PromoServiceTest extends WCShipping_Test_Case {
	/**
	 * Test user Id
	 *
	 * @var integer
	 */
	protected $user_id = 0;

	/**
	 * Mocked WC_Connect_Service_Schemas_Store instance.
	 *
	 * @var WC_Connect_Service_Schemas_Store&MockObject
	 */
	private $mock_schemas_store;

	/**
	 * Mocked WC_Connect_Service_Settings_Store instance.
	 *
	 * @var \Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store&MockObject
	 */
	private $mock_settings_store;

	/**
	 * PromoService instance.
	 *
	 * @var PromoService
	 */
	private $promo_service;

	public function setUp(): void {
		parent::setUp();

		$this->mock_schemas_store  = $this->createMock( \Automattic\WCShipping\Connect\WC_Connect_Service_Schemas_Store::class );
		$this->mock_settings_store = $this->createMock( \Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store::class );
		$this->promo_service       = new PromoService( $this->mock_schemas_store, $this->mock_settings_store );

		$this->user_id = $this->login_as_administrator();
		wp_set_current_user( $this->user_id );
	}

	public function tearDown(): void {
		wp_set_current_user( 0 );
		wp_delete_user( $this->user_id );

		parent::tearDown();
	}

	public function test_get_promotion_returns_promotion_when_available() {
		$expected_promotion = (object) array(
			'id'        => 'promo123',
			'remaining' => 5,
		);

		$schemas = (object) array(
			'promotion' => $expected_promotion,
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )
			->willReturn( $schemas );

		$promotion = $this->promo_service->get_promotion();

		$this->assertEquals( $expected_promotion, $promotion );
	}

	public function test_get_promotion_sanitizes_html_properties() {
		$expected_promotion = (object) array(
			'id'        => 'promo123',
			'remaining' => 5,
			'notice'    => '<button>Click me</button>',
		);

		$schemas = (object) array(
			'promotion' => (object) array(
				'id'        => 'promo123',
				'remaining' => 5,
				'notice'    => '<button onclick="alert(\'XSS\')">Click me</button>',
			),
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )
			->willReturn( $schemas );

		$promotion = $this->promo_service->get_promotion();

		$this->assertEquals( $expected_promotion, $promotion );
	}

	public function test_get_promotion_returns_null_when_promotion_property_is_missing() {
		$schemas = (object) array();
		$this->mock_schemas_store->method( 'get_service_schemas' )
			->willReturn( $schemas );

		$promotion = $this->promo_service->get_promotion();

		$this->assertNull( $promotion );
	}

	public function test_get_promotion_returns_null_when_remaining_is_zero() {
		$schemas = (object) array(
			'promotion' => (object) array(
				'id'        => 'promo123',
				'remaining' => 0,
			),
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )
			->willReturn( $schemas );

		$promotion = $this->promo_service->get_promotion();

		$this->assertNull( $promotion );
	}

	public function test_get_promotion_returns_null_when_end_date_is_over() {
		$schemas = (object) array(
			'promotion' => (object) array(
				'id'        => 'promo123',
				'remaining' => 10,
				'endDate'   => gmdate( 'Y-m-d\TH:i:s.v\Z', strtotime( '-1 day' ) ),
			),
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )
			->willReturn( $schemas );

		$promotion = $this->promo_service->get_promotion();

		$this->assertNull( $promotion );
	}

	public function test_get_promotion_unsets_dismissed_notice() {
		$expected_promotion = (object) array(
			'id'        => 'promo123',
			'remaining' => 5,
			'notice'    => 'Test promotion notice',
			'banner'    => 'Test promotion banner',
		);

		$schemas = (object) array(
			'promotion' => $expected_promotion,
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )
			->willReturn( $schemas );

		// Mock the notice as dismissed.
		update_user_meta( $this->user_id, 'wcc-promo-notice-promo123-dismissed', true );

		$promotion = $this->promo_service->get_promotion();

		$this->assertEquals( 'promo123', $promotion->id );
		$this->assertObjectNotHasProperty( 'notice', $promotion );
		$this->assertObjectHasProperty( 'banner', $promotion );

		// Cleanup.
		delete_user_meta( $this->user_id, 'wcc-promo-notice-promo123-dismissed' );
	}

	public function test_get_promotion_unsets_dismissed_banner() {
		$expected_promotion = (object) array(
			'id'        => 'promo123',
			'remaining' => 5,
			'notice'    => 'Test promotion notice',
			'banner'    => 'Test promotion banner',
		);

		$schemas = (object) array(
			'promotion' => $expected_promotion,
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )
			->willReturn( $schemas );

		// Mock the banner as dismissed.
		update_user_meta( $this->user_id, 'wcc-promo-banner-promo123-dismissed', true );

		$promotion = $this->promo_service->get_promotion();

		$this->assertEquals( 'promo123', $promotion->id );
		$this->assertObjectHasProperty( 'notice', $promotion );
		$this->assertObjectNotHasProperty( 'banner', $promotion );

		// Cleanup.
		delete_user_meta( $this->user_id, 'wcc-promo-banner-promo123-dismissed' );
	}

	public function test_maybe_show_promotion_notice_does_nothing_when_no_promotion() {
		$schemas = (object) array();

		$this->mock_schemas_store->method( 'get_service_schemas' )
			->willReturn( $schemas );

		set_current_screen( 'woocommerce_page_wc-orders' );

		ob_start();
		$this->promo_service->maybe_show_promotion_notice();
		$output = ob_get_clean();

		$this->assertEmpty( $output );
	}

	public function test_maybe_show_promotion_notice_does_nothing_when_no_notice() {
		$expected_promotion = (object) array(
			'id'        => 'promo123',
			'remaining' => 5,
			'banner'    => 'Test promotion banner',
		);

		$schemas = (object) array(
			'promotion' => $expected_promotion,
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )
			->willReturn( $schemas );

		set_current_screen( 'woocommerce_page_wc-orders' );

		ob_start();
		$this->promo_service->maybe_show_promotion_notice();
		$output = ob_get_clean();

		$this->assertEmpty( $output );
	}

	public function test_maybe_show_promotion_notice_does_nothing_when_not_on_orders_page() {
		$expected_promotion = (object) array(
			'id'        => 'promo123',
			'remaining' => 5,
			'notice'    => 'Test promotion notice',
		);

		$schemas = (object) array(
			'promotion' => $expected_promotion,
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )
			->willReturn( $schemas );

		set_current_screen( 'dashboard' );

		ob_start();
		$this->promo_service->maybe_show_promotion_notice();
		$output = ob_get_clean();

		$this->assertEmpty( $output );
	}

	public function test_maybe_show_promotion_notice_does_nothing_when_id_parameter_present() {
		$expected_promotion = (object) array(
			'id'        => 'promo123',
			'remaining' => 5,
			'notice'    => 'Test promotion notice',
		);

		$schemas = (object) array(
			'promotion' => $expected_promotion,
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )
			->willReturn( $schemas );

		set_current_screen( 'woocommerce_page_wc-orders' );

		$_GET['id'] = '123';

		ob_start();
		$this->promo_service->maybe_show_promotion_notice();
		$output = ob_get_clean();

		unset( $_GET['id'] );

		$this->assertEmpty( $output );
	}

	public function test_maybe_show_promotion_notice_shows_notice_when_conditions_met() {
		$expected_promotion = (object) array(
			'id'        => 'promo123',
			'remaining' => 5,
			'notice'    => '<strong>Test promotion notice</strong>',
		);

		$schemas = (object) array(
			'promotion' => $expected_promotion,
		);

		$this->mock_schemas_store->method( 'get_service_schemas' )
			->willReturn( $schemas );

		set_current_screen( 'woocommerce_page_wc-orders' );

		ob_start();
		$this->promo_service->maybe_show_promotion_notice();
		$output = ob_get_clean();

		$this->assertStringContainsString( 'notice notice-info is-dismissible', $output );
		$this->assertStringContainsString( '<strong>Test promotion notice</strong>', $output );
		$this->assertStringContainsString( 'wcc-dismiss-promo-notice=promo123', $output );
		$this->assertStringContainsString( '_wpnonce=', $output );
	}

	public function test_is_promotion_dismissed_returns_true_when_dismissed() {
		update_user_meta( $this->user_id, 'wcc-promo-notice-promo123-dismissed', true );

		$result = $this->promo_service->is_promotion_dismissed( 'notice', 'promo123' );

		$this->assertTrue( $result );

		// Cleanup.
		delete_user_meta( $this->user_id, 'wcc-promo-notice-promo123-dismissed' );
	}

	public function test_is_promotion_dismissed_returns_false_when_not_dismissed() {
		$result = $this->promo_service->is_promotion_dismissed( 'notice', 'promo123' );

		$this->assertFalse( $result );
	}

	public function test_is_promotion_dismissed_handles_different_types() {
		update_user_meta( $this->user_id, 'wcc-promo-banner-promo123-dismissed', true );

		$notice_result = $this->promo_service->is_promotion_dismissed( 'notice', 'promo123' );
		$banner_result = $this->promo_service->is_promotion_dismissed( 'banner', 'promo123' );

		$this->assertFalse( $notice_result );
		$this->assertTrue( $banner_result );

		// Cleanup.
		delete_user_meta( $this->user_id, 'wcc-promo-banner-promo123-dismissed' );
	}

	public function test_dismiss_promotion_updates_user_meta() {
		$this->promo_service->dismiss_promotion( 'notice', 'promo123' );

		$meta_value = get_user_meta( $this->user_id, 'wcc-promo-notice-promo123-dismissed', true );
		$this->assertTrue( (bool) $meta_value );

		// Cleanup.
		delete_user_meta( $this->user_id, 'wcc-promo-notice-promo123-dismissed' );
	}

	public function test_dismiss_promotion_handles_different_types() {
		$this->promo_service->dismiss_promotion( 'banner', 'promo456' );

		$meta_value = get_user_meta( $this->user_id, 'wcc-promo-banner-promo456-dismissed', true );
		$this->assertTrue( (bool) $meta_value );

		// Cleanup.
		delete_user_meta( $this->user_id, 'wcc-promo-banner-promo456-dismissed' );
	}

	/**
	 * @dataProvider handle_promotion_notice_dismiss_data_provider
	 *
	 * @param object|null $promotion          The promotion object or null if no promotion.
	 * @param string|null $get_promo_id       The promo ID from $_GET parameter.
	 * @param bool|null   $valid_nonce        Whether a valid nonce should be generated for $_GET parameter.
	 * @param bool        $should_call_dismiss Whether dismiss_promotion should be called.
	 */
	public function test_handle_promotion_notice_dismiss( $promotion, $get_promo_id, $valid_nonce, $should_call_dismiss ) {
		$schemas = is_null( $promotion ) ? (object) array() : (object) array( 'promotion' => $promotion );

		$this->mock_schemas_store->method( 'get_service_schemas' )->willReturn( $schemas );

		if ( ! is_null( $get_promo_id ) ) {
			$_GET['wcc-dismiss-promo-notice']     = $get_promo_id;
			$_REQUEST['wcc-dismiss-promo-notice'] = $get_promo_id;
		}
		if ( ! is_null( $valid_nonce ) ) {
			$_GET['_wpnonce']     = $valid_nonce ? wp_create_nonce( 'wcc_dismiss_promo_notice_nonce' ) : 'invalid-nonce';
			$_REQUEST['_wpnonce'] = $valid_nonce ? wp_create_nonce( 'wcc_dismiss_promo_notice_nonce' ) : 'invalid-nonce';
		}

		// Create a partial mock to verify dismiss_promotion behavior.
		$promo_service_mock = $this->getMockBuilder( PromoService::class )
			->setConstructorArgs( array( $this->mock_schemas_store, $this->mock_settings_store ) )
			->onlyMethods( array( 'dismiss_promotion' ) )
			->getMock();

		$promo_service_mock->expects( $should_call_dismiss ? $this->once() : $this->never() )
			->method( 'dismiss_promotion' )
			->with( 'notice', 'promo123' );

		if ( false === $valid_nonce ) {
			$this->expectException( \WPDieException::class );
		}

		$promo_service_mock->handle_promotion_notice_dismiss();

		// Clean up $_GET and $_REQUEST parameters.
		unset( $_GET['wcc-dismiss-promo-notice'], $_GET['_wpnonce'] );
		unset( $_REQUEST['wcc-dismiss-promo-notice'], $_REQUEST['_wpnonce'] );
	}

	/**
	 * Data provider for test_handle_promotion_notice_dismiss.
	 *
	 * @return array[]
	 */
	public function handle_promotion_notice_dismiss_data_provider() {
		return array(
			'no promotion available' => array(
				'promotion'           => null,
				'get_promo_id'        => 'promo123',
				'valid_nonce'         => true,
				'should_call_dismiss' => false,
			),
			'no GET parameter'       => array(
				'promotion'           => (object) array(
					'id'        => 'promo123',
					'remaining' => 5,
				),
				'get_promo_id'        => null,
				'valid_nonce'         => null,
				'should_call_dismiss' => false,
			),
			'promo ID mismatch'      => array(
				'promotion'           => (object) array(
					'id'        => 'promo123',
					'remaining' => 5,
				),
				'get_promo_id'        => 'different_promo',
				'valid_nonce'         => true,
				'should_call_dismiss' => false,
			),
			'no nonce provided'      => array(
				'promotion'           => (object) array(
					'id'        => 'promo123',
					'remaining' => 5,
				),
				'get_promo_id'        => 'promo123',
				'valid_nonce'         => null,
				'should_call_dismiss' => false,
			),
			'invalid nonce'          => array(
				'promotion'           => (object) array(
					'id'        => 'promo123',
					'remaining' => 5,
				),
				'get_promo_id'        => 'promo123',
				'valid_nonce'         => false,
				'should_call_dismiss' => false,
			),
			'happy path'             => array(
				'promotion'           => (object) array(
					'id'        => 'promo123',
					'remaining' => 5,
				),
				'get_promo_id'        => 'promo123',
				'valid_nonce'         => true,
				'should_call_dismiss' => true,
				'expected_promo_id'   => 'promo123',
			),
		);
	}

	/**
	 * @dataProvider maybe_decrement_promotion_remaining_data_provider
	 *
	 * @param array       $label_data         Original label data.
	 * @param object      $new_label_data     New label data.
	 * @param object|null $promotion          Promotion object from schemas.
	 * @param bool        $should_update      Whether the promotion should be updated.
	 * @param int|null    $expected_remaining The expected remaining count after update.
	 */
	public function test_maybe_decrement_promotion_remaining( $label_data, $new_label_data, $promotion, $should_update, $expected_remaining = null ) {
		$schemas = (object) array( 'promotion' => $promotion );
		if ( is_null( $promotion ) ) {
			$schemas = (object) array();
		}

		$order_id = 123;

		// We need to create a partial mock to test the protected call.
		$promo_service_mock = $this->getMockBuilder( PromoService::class )
			->setConstructorArgs( array( $this->mock_schemas_store, $this->mock_settings_store ) )
			->onlyMethods( array( 'update_promotion_schema' ) )
			->getMock();

		$this->mock_schemas_store->method( 'get_service_schemas' )
			->willReturn( $schemas );

		$this->mock_settings_store->method( 'get_label_order_meta_data' )
			->with( $order_id )
			->willReturn( array( $label_data ) );

		$promo_service_mock->expects( $should_update ? $this->once() : $this->never() )
			->method( 'update_promotion_schema' )
			->with(
				$this->callback(
					function ( $promo ) use ( $expected_remaining ) {
						return $promo->remaining === $expected_remaining;
					}
				)
			);

		$promo_service_mock->maybe_decrement_promotion_remaining( $order_id, $new_label_data );
	}

	/**
	 * Data provider for test_maybe_decrement_promotion_remaining.
	 *
	 * @return array[]
	 */
	public function maybe_decrement_promotion_remaining_data_provider() {
		return array(
			'happy path'              => array(
				'label_data'         => array(
					'status'   => 'PURCHASE_IN_PROGRESS',
					'promo_id' => 'promo123',
					'label_id' => 'label_1',
				),
				'new_label_data'     => (object) array(
					'status'   => 'PURCHASED',
					'promo_id' => 'promo123',
					'label_id' => 'label_1',
				),
				'promotion'          => (object) array(
					'id'        => 'promo123',
					'remaining' => 5,
				),
				'should_update'      => true,
				'expected_remaining' => 4,
			),
			'label not purchased'     => array(
				'label_data'     => array(
					'status'   => 'PURCHASE_IN_PROGRESS',
					'promo_id' => 'promo123',
				),
				'new_label_data' => (object) array(
					'status'   => 'PURCHASE_ERROR',
					'promo_id' => 'promo123',
					'label_id' => 'label_1',
				),
				'promotion'      => (object) array(
					'id'        => 'promo123',
					'remaining' => 5,
				),
				'should_update'  => false,
			),
			'no promo available'      => array(
				'label_data'     => array(
					'status'   => 'PURCHASE_IN_PROGRESS',
					'promo_id' => 'promo123',
				),
				'new_label_data' => (object) array(
					'status'   => 'PURCHASED',
					'promo_id' => 'promo123',
					'label_id' => 'label_1',
				),
				'promotion'      => null,
				'should_update'  => false,
			),
			'promo id mismatch'       => array(
				'label_data'     => array(
					'status'   => 'PURCHASE_IN_PROGRESS',
					'promo_id' => 'other_promo',
				),
				'new_label_data' => (object) array(
					'status'   => 'PURCHASED',
					'promo_id' => 'other_promo',
					'label_id' => 'label_1',
				),
				'promotion'      => (object) array(
					'id'        => 'promo123',
					'remaining' => 5,
				),
				'should_update'  => false,
			),
			'remaining is zero'       => array(
				'label_data'     => array(
					'status'   => 'PURCHASE_IN_PROGRESS',
					'promo_id' => 'promo123',
				),
				'new_label_data' => (object) array(
					'status'   => 'PURCHASED',
					'promo_id' => 'promo123',
					'label_id' => 'label_1',
				),
				'promotion'      => (object) array(
					'id'        => 'promo123',
					'remaining' => 0,
				),
				'should_update'  => false,
			),
			'label already purchased' => array(
				'label_data'     => array(
					'status'   => 'PURCHASED',
					'promo_id' => 'promo123',
					'label_id' => 'label_1',
				),
				'new_label_data' => (object) array(
					'status'   => 'PURCHASED',
					'promo_id' => 'promo123',
					'label_id' => 'label_1',
				),
				'promotion'      => (object) array(
					'id'        => 'promo123',
					'remaining' => 5,
				),
				'should_update'  => false,
			),
		);
	}
}
