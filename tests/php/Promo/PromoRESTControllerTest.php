<?php
/**
 * Unit test for PromoRESTController
 *
 * @package Automattic\WCShipping
 */

namespace Automattic\WCShipping\Tests\php\Promo;

use Automattic\WCShipping\Promo\PromoRESTController;
use Automattic\WCShipping\Promo\PromoService;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use PHPUnit\Framework\MockObject\MockObject;
use WP_REST_Request;

/**
 * Unit test for PromoRESTController
 */
class PromoRESTControllerTest extends WCShipping_Test_Case {

	const REST_ENDPOINT_NOTICE = '/wcshipping/v1/promo/notice/test-promo-123';
	const REST_ENDPOINT_BANNER = '/wcshipping/v1/promo/banner/test-promo-456';

	/**
	 * Mock promo service
	 *
	 * @var PromoService&MockObject
	 */
	private $promo_service_mock;

	/**
	 * PromoRESTController instance
	 *
	 * @var PromoRESTController
	 */
	private $controller;

	/**
	 * Setup the test case.
	 *
	 * @see WCShipping_Test_Case::setUp()
	 */
	public function setUp(): void {
		parent::setUp();

		$this->promo_service_mock = $this->createMock( PromoService::class );
		$this->controller         = new PromoRESTController( $this->promo_service_mock );

		add_action(
			'rest_api_init',
			function () {
				$this->controller->register_routes();
			}
		);

		do_action( 'rest_api_init' );
	}

	public function test_dismiss_notice_promotion_success() {
		$promo_id = 'test-promo-123';
		$type     = 'notice';

		$mock_promotion = (object) array(
			'id' => $promo_id,
		);

		$this->promo_service_mock
			->expects( $this->once() )
			->method( 'get_promotion' )
			->willReturn( $mock_promotion );

		$this->promo_service_mock
			->expects( $this->once() )
			->method( 'dismiss_promotion' )
			->with( $type, $promo_id );

		$request = new WP_REST_Request( 'DELETE', self::REST_ENDPOINT_NOTICE );

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->get_status() );
		$this->assertTrue( $response->get_data()['success'] );
	}

	public function test_dismiss_banner_promotion_success() {
		$promo_id = 'test-promo-456';
		$type     = 'banner';

		$mock_promotion = (object) array(
			'id' => $promo_id,
		);

		$this->promo_service_mock
			->expects( $this->once() )
			->method( 'get_promotion' )
			->willReturn( $mock_promotion );

		$this->promo_service_mock
			->expects( $this->once() )
			->method( 'dismiss_promotion' )
			->with( $type, $promo_id );

		$request = new WP_REST_Request( 'DELETE', self::REST_ENDPOINT_BANNER );

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->get_status() );
		$this->assertTrue( $response->get_data()['success'] );
	}

	public function test_dismiss_promotion_when_no_promotion_exists() {
		$this->promo_service_mock
			->expects( $this->once() )
			->method( 'get_promotion' )
			->willReturn( null );

		$this->promo_service_mock
			->expects( $this->never() )
			->method( 'dismiss_promotion' );

		$request = new WP_REST_Request( 'DELETE', self::REST_ENDPOINT_NOTICE );

		$response = rest_do_request( $request );

		$this->assertEquals( 500, $response->get_status() );
		$this->assertStringContainsString( 'Promotion does not exist', $response->get_data()['message'] );
	}

	public function test_dismiss_promotion_when_id_mismatch() {
		$mock_promotion = (object) array(
			'id' => 'different-promo-id',
		);

		$this->promo_service_mock
			->expects( $this->once() )
			->method( 'get_promotion' )
			->willReturn( $mock_promotion );

		$this->promo_service_mock
			->expects( $this->never() )
			->method( 'dismiss_promotion' );

		$request = new WP_REST_Request( 'DELETE', self::REST_ENDPOINT_NOTICE );

		$response = rest_do_request( $request );

		$this->assertEquals( 500, $response->get_status() );
		$this->assertStringContainsString( 'Promotion does not exist', $response->get_data()['message'] );
	}

	public function test_invalid_promotion_type() {
		$request = new WP_REST_Request( 'DELETE', '/wcshipping/v1/promo/invalid-type/test-promo' );

		$response = rest_do_request( $request );

		$this->assertEquals( 404, $response->get_status() );
		$data = $response->get_data();
		$this->assertEquals( 'rest_no_route', $data['code'] );
	}

	public function test_invalid_promotion_id_format() {
		$request = new WP_REST_Request( 'DELETE', '/wcshipping/v1/promo/notice/invalid id format!' );

		$response = rest_do_request( $request );

		$this->assertEquals( 404, $response->get_status() );
		$data = $response->get_data();
		$this->assertEquals( 'rest_no_route', $data['code'] );
	}
}
