<?php
namespace Automattic\WCShipping\Tests\php;

class DummyServiceSchemasStore extends \Automattic\WCShipping\Connect\WC_Connect_Service_Schemas_Store {
	public function __construct() {
		// Override constructor to prevent errors from parent constructor.
	}

	public function get_service_schemas() {
		return (object) array( 'dummy' => true );
	}
}

use PHPUnit\Framework\TestCase;
use Automattic\WCShipping\Loader;

/**
 * Extend the Loader class to track if queue_service_schema_refresh is called.
 */
class TestLoader extends Loader {
	public $queue_called = false;

	public function queue_service_schema_refresh() {
		// Set flag instead of actual behavior.
		$this->queue_called = true;
	}

	public function test_non_dependent_option_does_not_trigger_queue_refresh(): void {
		// Create a new instance so that even without any dependent option action, the refresh is not triggered.
		$loader = new TestLoader();
		$loader->set_service_schemas_store( new DummyServiceSchemasStore() );
		$loader->attach_hooks();
		$loader->queue_called = false;
		// Trigger an action for an option that is not in the dependent options list.
		do_action( 'update_option_non_dependent_option', 'value', 'old', 'new' );
		$this->assertFalse( $loader->queue_called, 'queue_service_schema_refresh should not be triggered by non-dependent option.' );
	}
}

class Loader_Test extends TestCase {
	protected $loader;

	protected function setUp(): void {
		// Instantiate our tracking loader and assign a dummy service schemas store.
		$this->loader = new TestLoader();
		$this->loader->set_service_schemas_store( new DummyServiceSchemasStore() );
		$this->loader->attach_hooks();
	}

	public function test_update_option_triggers_queue_refresh(): void {
		// Initially, queue_called should be false.
		$this->assertFalse( $this->loader->queue_called, 'queue_service_schema_refresh should not have been called yet.' );
		// Trigger the action for one of the default dependent options.
		do_action( 'update_option_woocommerce_currency', 'new_value', 'old_value', 'new_value' );
		// Now, the queue_service_schema_refresh method should have been called.
		$this->assertTrue( $this->loader->queue_called, 'queue_service_schema_refresh was not triggered by the update_option action.' );
	}

	public function test_filter_custom_options(): void {
		$custom_filter = function ( $options ) {
			return array( 'custom_option' );
		};
		add_filter( 'wcshipping_schema_dependent_options', $custom_filter );

		try {
			// Create a new instance so that the filter takes effect.
			$loader = new TestLoader();
			$loader->set_service_schemas_store( new DummyServiceSchemasStore() );
			$loader->attach_hooks();
			$loader->queue_called = false;
			// Trigger the action for our custom option.
			do_action( 'update_option_custom_option', 'value', 'old', 'new' );
			$this->assertTrue( $loader->queue_called, 'queue_service_schema_refresh was not triggered for the custom option.' );
		} finally {
			remove_filter( 'wcshipping_schema_dependent_options', $custom_filter );
		}
	}
}
