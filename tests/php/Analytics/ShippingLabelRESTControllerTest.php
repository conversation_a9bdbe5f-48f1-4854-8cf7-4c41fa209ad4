<?php

namespace Automattic\WCShipping\Tests\Analytics;

use Automattic\WCShipping\Analytics\ShippingLabelRESTController;
use Automattic\WCShipping\Analytics\LabelsService;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use WP_REST_Request;

class ShippingLabelRESTControllerTest extends WCShipping_Test_Case {
	const REST_ENDPOINT = '/wcshipping/v1/reports/labels';

	private $mock_labels_service;

	public function setUp(): void {
		parent::setUp();

		// Set up the mock LabelsService
		$this->mock_labels_service = $this->createMock( LabelsService::class );

		// Pass the mock to the controller
		$controller = new ShippingLabelRESTController( $this->mock_labels_service );
		add_action(
			'rest_api_init',
			function () use ( $controller ) {
				$controller->register_routes();
			}
		);

		do_action( 'rest_api_init' );
	}

	public function test_get_labels() {
		// Adjust the return value of the mock service
		$this->mock_labels_service->method( 'get_labels_for_period' )
			->willReturn(
				array(
					'rows' => array(
						array(
							'created_date' => '2023-01-01',
							'order_id'     => 123,
							'rate'         => '10.00',
							'service_name' => 'Standard Shipping',
							'refund'       => false,
						),
					),
					'meta' => array(
						'pages'         => 1,
						'total_count'   => 1,
						'total_cost'    => 10.00,
						'total_refunds' => 0.00,
					),
				)
			);

		// Create a mock request using the method from WCShipping_Test_Case
		$request = $this->create_request( self::REST_ENDPOINT, 'GET' );
		$request->set_query_params(
			array(
				'before'   => '2023-01-31',
				'after'    => '2023-01-01',
				'offset'   => 0,
				'per_page' => 5,
				'fields'   => array( 'created_date', 'order_id', 'rate', 'service_name', 'refund' ),
			)
		);

		// Dispatch the request
		$response = rest_do_request( $request );
		// Assert the response
		$this->assertEquals( 200, $response->get_status() );
		$this->assertNotEmpty( $response->get_data() );
		$this->assertEquals( '2023-01-01', $response->get_data()['rows'][0]['created_date'] );
		$this->assertEquals( 123, $response->get_data()['rows'][0]['order_id'] );
	}

	public function test_get_labels_with_null_or_negative_per_page() {
		// Adjust the return value of the mock service to return multiple items
		$this->mock_labels_service->method( 'get_labels_for_period' )
			->willReturn(
				array(
					'rows' => array(
						array(
							'created_date' => '2023-01-01',
							'order_id'     => 123,
							'rate'         => '10.00',
							'service_name' => 'Standard Shipping',
							'refund'       => false,
						),
						array(
							'created_date' => '2023-01-02',
							'order_id'     => 124,
							'rate'         => '15.00',
							'service_name' => 'Express Shipping',
							'refund'       => false,
						),
					),
					'meta' => array(
						'pages'         => 1,
						'total_count'   => 2,
						'total_cost'    => 25.00,
						'total_refunds' => 0.00,
					),
				)
			);

		// Test with per_page set to null
		$request = $this->create_request( self::REST_ENDPOINT, 'GET' );
		$request->set_query_params(
			array(
				'before'   => '2023-01-31',
				'after'    => '2023-01-01',
				'offset'   => 0,
				'per_page' => null,
				'fields'   => array( 'created_date', 'order_id', 'rate', 'service_name', 'refund' ),
			)
		);

		$response = rest_do_request( $request );
		$this->assertEquals( 200, $response->get_status() );
		$this->assertCount( 2, $response->get_data()['rows'] );

		// // Test with per_page set to a negative number
		$request->set_query_params(
			array(
				'before'   => '2023-01-31',
				'after'    => '2023-01-01',
				'offset'   => 0,
				'per_page' => -1,
				'fields'   => array( 'created_date', 'order_id', 'rate', 'service_name', 'refund' ),
			)
		);

		$response = rest_do_request( $request );
		$this->assertEquals( 200, $response->get_status() );
		$this->assertCount( 2, $response->get_data()['rows'] );
	}

	public function test_get_labels_filtered_by_dates() {
		// Adjust the return value of the mock service to return multiple items
		$this->mock_labels_service->method( 'get_labels_for_period' )
			->willReturn(
				array(
					'rows' => array(
						array(
							'created_date' => strtotime( '2023-01-01T00:00:00' ) * 1000,
							'order_id'     => 123,
							'rate'         => '10.00',
							'service_name' => 'Standard Shipping',
							'refund'       => false,
						),
						array(
							'created_date' => strtotime( '2023-01-15T12:30:00' ) * 1000,
							'order_id'     => 124,
							'rate'         => '15.00',
							'service_name' => 'Express Shipping',
							'refund'       => false,
						),
						array(
							'created_date' => strtotime( '2023-02-01T23:59:59' ) * 1000,
							'order_id'     => 125,
							'rate'         => '20.00',
							'service_name' => 'Overnight Shipping',
							'refund'       => false,
						),
					),
					'meta' => array(
						'pages'         => 1,
						'total_count'   => 3,
						'total_cost'    => 45.00,
						'total_refunds' => 0.00,
					),
				)
			);

		// Create a request with specific before and after dates
		$request = $this->create_request( self::REST_ENDPOINT, 'GET' );
		$request->set_query_params(
			array(
				'before'   => '2023-01-31T23:59:59',
				'after'    => '2023-01-01T00:00:00',
				'offset'   => 0,
				'per_page' => 5,
				'fields'   => array( 'created_date', 'order_id', 'rate', 'service_name', 'refund' ),
			)
		);

		// Dispatch the request
		$response = rest_do_request( $request );

		// Assert the response
		$this->assertEquals( 200, $response->get_status() );
		$data = $response->get_data();
		$this->assertCount( 3, $data['rows'] );
		$this->assertEquals( 1, $data['meta']['pages'] );
		$this->assertEquals( 3, $data['meta']['total_count'] );
		$this->assertEquals( 45.00, $data['meta']['total_cost'] );
		$this->assertEquals( 0.00, $data['meta']['total_refunds'] );

		$this->assertEquals( 1672531200000, $data['rows'][0]['created_date'] );
		$this->assertEquals( 123, $data['rows'][0]['order_id'] );
		$this->assertEquals( '10.00', $data['rows'][0]['rate'] );
		$this->assertEquals( 'Standard Shipping', $data['rows'][0]['service_name'] );
		$this->assertFalse( $data['rows'][0]['refund'] );

		$this->assertEquals( 1673785800000, $data['rows'][1]['created_date'] );
		$this->assertEquals( 124, $data['rows'][1]['order_id'] );
		$this->assertEquals( '15.00', $data['rows'][1]['rate'] );
		$this->assertEquals( 'Express Shipping', $data['rows'][1]['service_name'] );
		$this->assertFalse( $data['rows'][1]['refund'] );

		$this->assertEquals( 1675295999000, $data['rows'][2]['created_date'] );
		$this->assertEquals( 125, $data['rows'][2]['order_id'] );
		$this->assertEquals( '20.00', $data['rows'][2]['rate'] );
		$this->assertEquals( 'Overnight Shipping', $data['rows'][2]['service_name'] );
		$this->assertFalse( $data['rows'][2]['refund'] );
	}
}
