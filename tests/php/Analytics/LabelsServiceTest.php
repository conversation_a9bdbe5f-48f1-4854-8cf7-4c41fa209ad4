<?php

namespace Automattic\WCShipping\Tests\Analytics;

use Automattic\WCShipping\Analytics\LabelsService;
use Automattic\WooCommerce\Utilities\OrderUtil;
use WC_Unit_Test_Case;

class LabelsServiceTest extends WC_Unit_Test_Case {

	protected $labels_service;

	public function set_up() {
		parent::set_up();
		$this->labels_service = new LabelsService();
	}

	public function tear_down() {
		parent::tear_down();
		// Remove the transient value before each test
		delete_transient( LabelsService::LABELS_TRANSIENT_KEY );
	}

	public function test_get_all_labels_returns_array() {
		// Set the transient with a predefined value
		$expected_labels = array(
			array(
				'order_id'     => 1,
				'created_date' => strtotime( '2023-01-01' ) * 1000,
				'rate'         => 10.0,
				'refund'       => array( 'status' => 'complete' ),
			),
		);

		// Set the transient directly
		set_transient( LabelsService::LABELS_TRANSIENT_KEY, $expected_labels );

		// Retrieve the labels using the service
		$labels = $this->labels_service->get_all_labels();
		$this->assertIsArray( $labels );
		$this->assertEquals( $expected_labels, $labels );
	}

	public function test_get_labels_for_period_returns_correct_structure() {
		$query = array(
			'before'   => '2023-12-31 23:59:59',
			'after'    => '2023-01-01 00:00:00',
			'offset'   => 0,
			'per_page' => 10,
		);

		$fields = array( 'order_id', 'created_date', 'rate', 'refund' );

		$expected_labels = array(
			array(
				'order_id'     => 1,
				'created_date' => strtotime( '2023-01-01' ) * 1000,
				'rate'         => 10.0,
				'refund'       => array( 'status' => 'complete' ),
			),
			array(
				'order_id'     => 2,
				'created_date' => strtotime( '2023-06-01' ) * 1000,
				'rate'         => 15.0,
				'refund'       => array( 'status' => 'requested' ),
			),
		);

		set_transient( LabelsService::LABELS_TRANSIENT_KEY, $expected_labels );

		$result = $this->labels_service->get_labels_for_period( $query, $fields );

		$this->assertArrayHasKey( 'rows', $result );
		$this->assertArrayHasKey( 'meta', $result );
		$this->assertIsArray( $result['rows'] );
		$this->assertIsArray( $result['meta'] );
		$this->assertArrayHasKey( 'pages', $result['meta'] );
		$this->assertArrayHasKey( 'total_count', $result['meta'] );
		$this->assertArrayHasKey( 'total_cost', $result['meta'] );
		$this->assertArrayHasKey( 'total_refunds', $result['meta'] );

		// New test case for before/after query arguments with time
		$query_variant = array(
			'before'   => '2023-06-30 23:59:59',
			'after'    => '2023-01-01 00:00:00',
			'offset'   => 0,
			'per_page' => 10,
		);

		$result_variant = $this->labels_service->get_labels_for_period( $query_variant, $fields );

		// Assuming the expected result is known for this date and time range
		$expected_variant_rows = array(
			array(
				'order_id'     => 1,
				'created_date' => strtotime( '2023-01-01' ) * 1000,
				'rate'         => 10.0,
				'refund'       => array( 'status' => 'Complete' ),
			),
			array(
				'order_id'     => 2,
				'created_date' => strtotime( '2023-06-01' ) * 1000,
				'rate'         => 15.0,
				'refund'       => array( 'status' => 'Requested' ),
			),
		);

		$this->assertEquals( $expected_variant_rows, $result_variant['rows'] );
	}

	public function test_get_label_refund_status() {
		$label  = array( 'refund' => array( 'status' => 'complete' ) );
		$status = $this->labels_service->get_label_refund_status( $label );
		$this->assertEquals( 'Complete', $status );

		$label  = array( 'refund' => array( 'status' => 'rejected' ) );
		$status = $this->labels_service->get_label_refund_status( $label );
		$this->assertEquals( 'Rejected', $status );

		$label  = array( 'refund' => array( 'status' => 'pending' ) );
		$status = $this->labels_service->get_label_refund_status( $label );
		$this->assertEquals( 'Requested', $status );

		$label  = array();
		$status = $this->labels_service->get_label_refund_status( $label );
		$this->assertEquals( '', $status );
	}

	public function test_get_labels_for_period_with_negative_per_page() {
		$query = array(
			'before'   => '2023-12-31 23:59:59',
			'after'    => '2023-01-01 00:00:00',
			'offset'   => 0,
			'per_page' => -10, // Negative per_page value
		);

		$fields = array( 'order_id', 'created_date', 'rate', 'refund' );

		// Insert a large dataset into the database
		$this->insert_labels_with_dates();

		$result = $this->labels_service->get_labels_for_period( $query, $fields );

		// Define expected behavior for negative per_page
		// Assuming it defaults to returning all items
		$expected_result = array(
			'rows' => array(
				// Sorted in descending order based on created_date
				array(
					'order_id'     => 3,
					'created_date' => strtotime( '2023-12-01 09:45:00' ) * 1000,
					'rate'         => 20.0,
					'refund'       => array( 'status' => '' ),
				),
				array(
					'order_id'     => 2,
					'created_date' => strtotime( '2023-06-01 15:30:00' ) * 1000,
					'rate'         => 15.0,
					'refund'       => array( 'status' => 'Requested' ),
				),
				array(
					'order_id'     => 1,
					'created_date' => strtotime( '2023-01-01 12:00:00' ) * 1000,
					'rate'         => 10.0,
					'refund'       => array( 'status' => 'Complete' ),
				),
			),
			'meta' => array(
				'pages'         => 1,
				'total_count'   => 3, // Adjusted to match the expected number of rows
				'total_cost'    => 45.0,
				'total_refunds' => 1,
			),
		);

		$this->assertEquals( $expected_result, $result );
	}

	public function test_fetch_labels_from_database_with_no_data() {
		// Ensure the database is empty
		$this->clear_database();

		// Fetch labels from an empty database
		$labels = $this->labels_service->fetch_labels_from_database();

		// Assert that the result is an empty array
		$this->assertIsArray( $labels );
		$this->assertEmpty( $labels );
	}

	public function test_fetch_labels_from_database_with_specific_date_range() {
		// Insert labels with specific dates into the database
		$this->insert_labels_with_dates();

		// Define the date range for the query
		$query = array(
			'before' => '2023-12-31 23:59:59',
			'after'  => '2023-01-01 00:00:00',
		);

		// Fetch labels from the database within the date range
		$labels = $this->labels_service->fetch_labels_from_database( $query );

		// Assert that the result contains only labels within the specified date range
		$this->assertIsArray( $labels );
		foreach ( $labels as $label ) {
			$this->assertGreaterThanOrEqual( strtotime( '2023-01-01 00:00:00' ) * 1000, $label['created_date'] );
			$this->assertLessThanOrEqual( strtotime( '2023-12-31 23:59:59' ) * 1000, $label['created_date'] );
		}
	}

	// Helper methods to set up the database for tests
	private function clear_database() {
		global $wpdb;
		$table_name = OrderUtil::get_table_for_order_meta();
		$wpdb->query( "DELETE FROM $table_name WHERE meta_key = 'wcshipping_labels'" );
	}


	private function insert_labels_with_dates() {
		global $wpdb;
		$table_name  = OrderUtil::get_table_for_order_meta();
		$column_name = OrderUtil::custom_orders_table_usage_is_enabled() ? 'order_id' : 'post_id';

		$labels = array(
			array(
				'created_date' => strtotime( '2023-01-01 12:00:00' ) * 1000,
				'rate'         => 10.0,
				'refund'       => array( 'status' => 'complete' ),
			),
			array(
				'created_date' => strtotime( '2023-06-01 15:30:00' ) * 1000,
				'rate'         => 15.0,
				'refund'       => array( 'status' => 'requested' ),
			),
			array(
				'created_date' => strtotime( '2023-12-01 09:45:00' ) * 1000,
				'rate'         => 20.0,
				'refund'       => null,
			),
		);

		foreach ( $labels as $index => $label ) {
			$wpdb->insert(
				$table_name,
				array(
					$column_name => $index + 1,
					'meta_key'   => 'wcshipping_labels',
					'meta_value' => maybe_serialize( array( $label ) ),
				)
			);
		}
	}
}
