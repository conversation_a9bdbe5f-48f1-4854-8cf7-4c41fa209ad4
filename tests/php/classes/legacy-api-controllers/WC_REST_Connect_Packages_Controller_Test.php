<?php

require_once __DIR__ . '/../../../../classes/legacy-api-controllers/class-wc-rest-connect-base-controller.php';
require_once __DIR__ . '/../../../../classes/legacy-api-controllers/class-wc-rest-connect-packages-controller.php';

use Automattic\WCShipping\LegacyAPIControllers\WC_REST_Connect_Packages_Controller;
use Automattic\WCShipping\Connect\WC_Connect_Package_Settings;
use Automattic\WCShipping\Connect\WC_Connect_API_Client;
use Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store;
use Automattic\WCShipping\Connect\WC_Connect_Logger;
use Automattic\WCShipping\Connect\WC_Connect_Service_Schemas_Store;
use Automattic\WCShipping\Packages\Package;

class WC_REST_Connect_Packages_Controller_Test extends WP_UnitTestCase {
	private $controller;
	private $package_settings_mock;

	public function setUp(): void {
		parent::setUp();

		$this->package_settings_mock = $this->createMock( WC_Connect_Package_Settings::class );

		$api_client_mock            = $this->createMock( WC_Connect_API_Client::class );
		$settings_store_mock        = $this->createMock( WC_Connect_Service_Settings_Store::class );
		$logger_mock                = $this->createMock( WC_Connect_Logger::class );
		$service_schemas_store_mock = $this->createMock( WC_Connect_Service_Schemas_Store::class );

		$this->controller = new WC_REST_Connect_Packages_Controller(
			$api_client_mock,
			$settings_store_mock,
			$logger_mock,
			$service_schemas_store_mock
		);

		$reflection = new ReflectionClass( $this->controller );
		$property   = $reflection->getProperty( 'package_settings' );
		$property->setAccessible( true );
		$property->setValue( $this->controller, $this->package_settings_mock );
	}

	public function test_get_method_returns_correct_response() {
		$mock_package_data = array(
			'id'         => '1',
			'name'       => 'Test Package',
			'dimensions' => '10 x 10 x 10',
			'box_weight' => 1,
			'max_weight' => 50,
			'type'       => Package::TYPE_BOX,
		);

		$mock_result = array(
			'formData' => array(
				'custom' => array( $mock_package_data ),
			),
		);

		$this->package_settings_mock->expects( $this->once() )
			->method( 'get' )
			->willReturn( $mock_result );

		$expected_wcst_array = array(
			'id'               => '1',
			'name'             => 'Test Package',
			'inner_dimensions' => '10 x 10 x 10',
			'is_user_defined'  => true,
			'box_weight'       => 1,
			'max_weight'       => 50,
			'is_letter'        => false,
		);

		$expected_response_data = array(
			'success'  => true,
			'formData' => array(
				'custom' => array( $expected_wcst_array ),
			),
		);

		$actual_response = $this->controller->get( new WP_REST_Request() );

		$this->assertInstanceOf( WP_REST_Response::class, $actual_response );
		$this->assertEquals( 200, $actual_response->get_status() );
		$this->assertEquals( $expected_response_data, $actual_response->get_data() );
	}

	public function test_get_method_returns_default_max_weight() {
		$mock_package_data = array(
			'id'         => '1',
			'name'       => 'Test Package',
			'dimensions' => '10 x 10 x 10',
			'box_weight' => 1,
			'type'       => Package::TYPE_BOX,
			// max_weight intentionally omitted
		);

		$mock_result = array(
			'formData' => array(
				'custom' => array( $mock_package_data ),
			),
		);

		$this->package_settings_mock->expects( $this->once() )
			->method( 'get' )
			->willReturn( $mock_result );

		$expected_wcst_array = array(
			'id'               => '1',
			'name'             => 'Test Package',
			'inner_dimensions' => '10 x 10 x 10',
			'is_user_defined'  => true,
			'box_weight'       => 1,
			'max_weight'       => 0, // Should default to 0 when not set
			'is_letter'        => false,
		);

		$expected_response_data = array(
			'success'  => true,
			'formData' => array(
				'custom' => array( $expected_wcst_array ),
			),
		);

		$actual_response = $this->controller->get( new WP_REST_Request() );

		$this->assertInstanceOf( WP_REST_Response::class, $actual_response );
		$this->assertEquals( 200, $actual_response->get_status() );
		$this->assertEquals( $expected_response_data, $actual_response->get_data() );
	}

	public function test_it_gets_upsdap_packages_if_supported_by_client() {
		update_option(
			'wcshipping_services',
			$this->convert_to_object(
				array(
					'shipping' => array(
						array(
							'id'       => 'usps',
							'packages' => array( 'foo' ),
						),
						array(
							'id'       => 'upsdap',
							'packages' => array( 'bar' ),
						),
					),
				)
			)
		);

		$request = new WP_REST_Request();
		$request->set_param( 'features_supported_by_client', array( 'upsdap' ) );

		$controller = $this->create_packages_controller_with_actual_service_schemas_store();

		$this->assertArrayHasKey(
			'upsdap',
			$controller->get( $request )->get_data()['formSchema']['predefined']
		);
	}

	public function test_it_does_not_get_upsdap_packages_if_not_supported_by_client() {
		update_option(
			'wcshipping_services',
			$this->convert_to_object(
				array(
					'shipping' => array(
						array(
							'id'       => 'usps',
							'packages' => array( 'foo' ),
						),
						array(
							'id'       => 'upsdap',
							'packages' => array( 'bar' ),
						),
					),
				)
			)
		);

		$request = new WP_REST_Request();
		$request->set_param( 'features_supported_by_client', array() );

		$controller = $this->create_packages_controller_with_actual_service_schemas_store();

		$this->assertArrayNotHasKey(
			'upsdap',
			$controller->get( $request )->get_data()['formSchema']['predefined']
		);
	}

	public function test_it_does_not_get_upsdap_packages_if_support_unspecified_by_client() {
		update_option(
			'wcshipping_services',
			$this->convert_to_object(
				array(
					'shipping' => array(
						array(
							'id'       => 'usps',
							'packages' => array( 'foo' ),
						),
						array(
							'id'       => 'upsdap',
							'packages' => array( 'bar' ),
						),
					),
				)
			)
		);

		$request = new WP_REST_Request();

		$controller = $this->create_packages_controller_with_actual_service_schemas_store();

		$this->assertArrayNotHasKey(
			'upsdap',
			$controller->get( $request )->get_data()['formSchema']['predefined']
		);
	}

	private function create_packages_controller_with_actual_service_schemas_store(): WC_REST_Connect_Packages_Controller {
		return new WC_REST_Connect_Packages_Controller(
			$this->createMock( WC_Connect_API_Client::class ),
			$this->createMock( WC_Connect_Service_Settings_Store::class ),
			$this->createMock( WC_Connect_Logger::class ),
			new WC_Connect_Service_Schemas_Store(
				$this->createMock( WC_Connect_API_Client::class ),
				$this->createMock( WC_Connect_Logger::class )
			)
		);
	}

	private function convert_to_object( array $arr ) {
		return json_decode( json_encode( $arr ) );
	}
}
