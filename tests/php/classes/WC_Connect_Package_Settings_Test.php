<?php

use Automattic\WCShipping\Connect\WC_Connect_Logger;
use Automattic\WCShipping\Connect\WC_Connect_API_Client;
use Automattic\WCShipping\Connect\WC_Connect_Package_Settings;
use Automattic\WCShipping\Connect\WC_Connect_Service_Schemas_Store;
use Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;

class WC_Connect_Package_Settings_Test extends WCShipping_Test_Case {

	public function setUp(): void {
		parent::setUp();

		$settings_store_dummy = $this->createMock( WC_Connect_Service_Settings_Store::class );

		$service_schemas_store_stub = $this->createMock( WC_Connect_Service_Schemas_Store::class );
		$service_schemas_store_stub
			->method( 'get_predefined_packages_schema' )
			->willReturn(
				array(
					'usps'   => array( 'packages' => 'foo' ),
					'upsdap' => array( 'packages' => 'bar' ),
				)
			);

		$this->package_settings = new WC_Connect_Package_Settings( $settings_store_dummy, $service_schemas_store_stub );
	}

	public function test_it_returns_upsdap_schema_if_supported_by_client() {
		$this->assertEquals(
			array(
				'usps'   => array( 'packages' => 'foo' ),
				'upsdap' => array( 'packages' => 'bar' ),
			),
			$this->package_settings->get( array( 'upsdap' ) )['formSchema']['predefined']
		);
	}

	public function test_it_does_not_return_upsdap_schema_if_not_supported_by_client() {
		$this->assertEquals(
			array(
				'usps' => array( 'packages' => 'foo' ),
			),
			$this->package_settings->get( array() )['formSchema']['predefined']
		);
	}

	public function test_it_does_not_return_upsdap_schema_if_features_supported_by_client_are_missing() {
		$this->assertEquals(
			array(
				'usps' => array( 'packages' => 'foo' ),
			),
			$this->package_settings->get( null )['formSchema']['predefined']
		);
	}
}
