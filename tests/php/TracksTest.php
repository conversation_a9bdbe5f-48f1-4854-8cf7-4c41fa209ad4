<?php
/**
 * Class TracksTest
 * @package Automattic\WCShipping
 */

namespace Automattic\WCShipping\Tests;

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\Tracks;

/**
 * Tests for Tracks analytics integration with feature banners.
 */
class TracksTest extends WCShipping_Test_Case {

	protected $user_id = 0;

	public function setUp(): void {
		parent::setUp();

		// Create test user
		$this->user_id = $this->factory->user->create(
			array(
				'role' => 'administrator',
			)
		);
		wp_set_current_user( $this->user_id );
	}


	/**
	 * Test that Tracks methods handle special characters in parameters
	 */
	public function test_tracks_methods_with_special_characters() {
		$banner_id     = 'test_banner_with_special_chars_@#$%';
		$button_action = 'Button with spaces & symbols!';

		// These should not throw errors
		Tracks::feature_banner_viewed( $banner_id );
		Tracks::feature_banner_dismissed( $banner_id );
		Tracks::feature_banner_button_clicked( $banner_id, $button_action );

		// Test passes if no exceptions are thrown
		$this->assertTrue( true );
	}

	/**
	 * Test event name generation follows conventions
	 */
	public function test_event_naming_conventions() {
		// Use reflection to test the record_event method behavior
		$reflection      = new \ReflectionClass( Tracks::class );
		$prefix_constant = $reflection->getConstant( 'PREFIX' );

		$this->assertEquals( 'wcadmin_wcshipping_', $prefix_constant );

		// Test that our banner events would generate correct names
		$expected_events = array(
			'banner_view',
			'banner_dismiss',
			'banner_button_click',
		);

		foreach ( $expected_events as $event ) {
			$expected_full_name = $prefix_constant . $event;
			// The full event name should start with the prefix
			$this->assertStringStartsWith( 'wcadmin_wcshipping_', $expected_full_name );
		}
	}

	/**
	 * Test PREFIX constant exists and has correct value
	 */
	public function test_prefix_constant() {
		$this->assertTrue( defined( 'Automattic\WCShipping\Tracks::PREFIX' ) );
		$this->assertEquals( 'wcadmin_wcshipping_', Tracks::PREFIX );
	}

	/**
	 * Integration test: Test full banner lifecycle tracking
	 */
	public function test_banner_lifecycle_tracking() {
		$banner_id     = 'lifecycle_test_banner';
		$button_action = 'Get Started';

		// Simulate full banner lifecycle
		try {
			Tracks::feature_banner_viewed( $banner_id );
			Tracks::feature_banner_button_clicked( $banner_id, $button_action );
			Tracks::feature_banner_dismissed( $banner_id );

			// If we get here without exceptions, the integration works
			$this->assertTrue( true );
		} catch ( \Exception $e ) {
			$this->fail( 'Banner lifecycle tracking failed: ' . $e->getMessage() );
		}
	}
}
