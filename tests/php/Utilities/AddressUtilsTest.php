<?php

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\Utilities\AddressUtils;

class AddressUtilsTest extends WCShipping_Test_Case {

	public function test_get_standardized_address() {
		$address = array(
			'address_2' => 'Apartment 1',
			'city'      => 'City',
			'postcode'  => '12345000',
			'state'     => 'Goiás',
			'country'   => 'BR',
			'address_1' => '123 First Street',
		);

		$standardizedAddress = AddressUtils::get_standardized_address( $address );
		$expected            = array(
			'address_1' => '123 FIRST ST',
			'address_2' => 'APT 1',
			'city'      => 'CITY',
			'state'     => 'GO',
			'postcode'  => '12345-000',
			'country'   => 'BR',
		);

		$this->assertEquals( $expected, $standardizedAddress );
	}

	public function test_are_addresses_equal_same() {
		$address1 = array(
			'address_1' => '123 Main St',
			'address_2' => 'Apt 1',
			'city'      => 'City',
			'state'     => 'State',
			'postcode'  => '12345',
			'country'   => 'US',
		);

		$address2 = array(
			'address_1' => '123 MAIN ST',
			'address_2' => 'APT 1',
			'city'      => 'CITY',
			'state'     => 'STATE',
			'postcode'  => '12345',
			'country'   => 'US',
		);

		$this->assertTrue( AddressUtils::are_addresses_equal( $address1, $address2 ) );
	}

	public function test_are_addresses_equal_different() {
		$address1 = array(
			'address_1' => '123 Main St',
			'address_2' => 'Apt 1',
			'city'      => 'City',
			'state'     => 'State',
			'postcode'  => '12345',
			'country'   => 'US',
		);

		$address2 = array(
			'address_1' => '124 Main St',
			'address_2' => 'Apt 2',
			'city'      => 'City',
			'state'     => 'State',
			'postcode'  => '12345',
			'country'   => 'US',
		);

		$this->assertFalse( AddressUtils::are_addresses_equal( $address1, $address2 ) );
	}

	public function test_convert_state_to_wc_format_valid_state() {
		$state   = 'CA';
		$country = 'US';

		$converted = AddressUtils::convert_state_to_wc_format( $state, $country );

		$this->assertEquals( 'CA', $converted );
	}

	public function test_convert_state_to_wc_format_valid_combo_state() {
		$state   = 'BY';
		$country = 'DE';

		$converted = AddressUtils::convert_state_to_wc_format( $state, $country );

		$this->assertEquals( 'DE-BY', $converted );
	}

	public function test_convert_state_to_wc_format_invalid_state() {
		$state   = 'CALIFORNIA';
		$country = 'US';

		$converted = AddressUtils::convert_state_to_wc_format( $state, $country );

		$this->assertEquals( 'CA', $converted );
	}

	public function test_are_addresses_close() {
		$address1 = array(
			'address_1' => '123 Main St',
			'address_2' => 'Apt 1',
			'city'      => 'City',
			'state'     => 'State',
			'postcode'  => '12345',
			'country'   => 'US',
		);

		$address2 = array(
			'address_1' => '123 Main Street',
			'address_2' => 'Apartment 1',
			'city'      => 'City',
			'state'     => 'State',
			'postcode'  => '12345',
			'country'   => 'US',
		);

		$this->assertTrue( AddressUtils::are_addresses_close( $address1, $address2 ) );
	}

	/**
	 * Test the address_array_to_formatted_html_string method
	 *
	 * @dataProvider addressDataProvider
	 */
	public function test_address_array_to_formatted_html_string( $address_data, $expected_output ) {
		$formatted_address = AddressUtils::address_array_to_formatted_html_string( $address_data );
		$this->assertEquals( $expected_output, $formatted_address );
	}

	/**
	 * Data provider for test_address_array_to_formatted_html_string
	 */
	public function addressDataProvider() {
		return array(
			'complete_address'  => array(
				array(
					'company'  => 'Automattic Inc',
					'name'     => 'John Doe',
					'address'  => '60 29th Street #343',
					'city'     => 'San Francisco',
					'state'    => 'CA',
					'postcode' => '94110',
					'country'  => 'United States',
					'phone'    => '************',
				),
				'Automattic Inc<br/>John Doe<br/>60 29th Street #343<br/>San Francisco, CA, 94110<br/>United States<br/>Phone: ************',
			),
			'no_company'        => array(
				array(
					'name'     => 'John Doe',
					'address'  => '60 29th Street #343',
					'city'     => 'San Francisco',
					'state'    => 'CA',
					'postcode' => '94110',
					'country'  => 'United States',
				),
				'John Doe<br/>60 29th Street #343<br/>San Francisco, CA, 94110<br/>United States',
			),
			'no_phone'          => array(
				array(
					'company'  => 'Automattic Inc',
					'name'     => 'John Doe',
					'address'  => '60 29th Street #343',
					'city'     => 'San Francisco',
					'state'    => 'CA',
					'postcode' => '94110',
					'country'  => 'United States',
				),
				'Automattic Inc<br/>John Doe<br/>60 29th Street #343<br/>San Francisco, CA, 94110<br/>United States',
			),
			'minimal_address'   => array(
				array(
					'name'    => 'John Doe',
					'address' => '60 29th Street #343',
					'city'    => 'San Francisco',
				),
				'John Doe<br/>60 29th Street #343<br/>San Francisco',
			),
			'empty_array'       => array(
				array(),
				'',
			),
			'partial_city_line' => array(
				array(
					'name'     => 'John Doe',
					'address'  => '60 29th Street #343',
					'city'     => 'San Francisco',
					'postcode' => '94110',
				),
				'John Doe<br/>60 29th Street #343<br/>San Francisco, 94110',
			),
		);
	}
}
