<?php

namespace Automattic\WCShipping\Tests\php\Utilities;

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\Utilities\BaseModel;

class BaseModelTest extends WCShipping_Test_Case {

	/**
	 * Test model for testing BaseModel functionality
	 */
	private function get_test_model() {
		return new class() extends BaseModel {
			public string $name           = 'Test Item';
			public int $quantity          = 5;
			public array $tags            = array( 'tag1', 'tag2' );
			public bool $active           = true;
			private string $private_field = 'hidden'; // This should not appear in to_array()
		};
	}

	/**
	 * Test model with nested BaseModel for testing recursive serialization
	 */
	private function get_nested_test_model() {
		return new class() extends BaseModel {
			public string $parent_name = 'Parent';
			public $child;
			public array $children = array();

			public function __construct() {
				// Create a child model
				$this->child = new class() extends BaseModel {
					public string $child_name = 'Child';
					public int $child_id      = 123;
				};

				// Create array of children
				$this->children = array(
					new class() extends BaseModel {
						public string $name = 'Child 1';
						public int $id = 1;
					},
					new class() extends BaseModel {
						public string $name = 'Child 2';
						public int $id = 2;
					},
				);
			}
		};
	}

	public function test_to_array_returns_public_properties_only() {
		$model  = $this->get_test_model();
		$result = $model->to_array();

		// Should include public properties
		$this->assertArrayHasKey( 'name', $result );
		$this->assertArrayHasKey( 'quantity', $result );
		$this->assertArrayHasKey( 'tags', $result );
		$this->assertArrayHasKey( 'active', $result );

		// Should not include private properties
		$this->assertArrayNotHasKey( 'private_field', $result );
	}

	public function test_to_array_returns_correct_values() {
		$model  = $this->get_test_model();
		$result = $model->to_array();

		$this->assertEquals( 'Test Item', $result['name'] );
		$this->assertEquals( 5, $result['quantity'] );
		$this->assertEquals( array( 'tag1', 'tag2' ), $result['tags'] );
		$this->assertTrue( $result['active'] );
	}

	public function test_to_array_handles_nested_models_recursively() {
		$model  = $this->get_nested_test_model();
		$result = $model->to_array();

		// Check parent properties
		$this->assertEquals( 'Parent', $result['parent_name'] );

		// Check nested child model is converted to array
		$this->assertIsArray( $result['child'] );
		$this->assertEquals( 'Child', $result['child']['child_name'] );
		$this->assertEquals( 123, $result['child']['child_id'] );

		// Check array of nested models
		$this->assertIsArray( $result['children'] );
		$this->assertCount( 2, $result['children'] );

		$this->assertIsArray( $result['children'][0] );
		$this->assertEquals( 'Child 1', $result['children'][0]['name'] );
		$this->assertEquals( 1, $result['children'][0]['id'] );

		$this->assertIsArray( $result['children'][1] );
		$this->assertEquals( 'Child 2', $result['children'][1]['name'] );
		$this->assertEquals( 2, $result['children'][1]['id'] );
	}

	public function test_to_array_handles_mixed_arrays() {
		$model = new class() extends BaseModel {
			public array $mixed_array = array(
				'string_value',
				42,
				true,
				array( 'nested' => 'array' ),
			);
		};

		$result = $model->to_array();

		$this->assertIsArray( $result['mixed_array'] );
		$this->assertEquals( 'string_value', $result['mixed_array'][0] );
		$this->assertEquals( 42, $result['mixed_array'][1] );
		$this->assertTrue( $result['mixed_array'][2] );
		$this->assertEquals( array( 'nested' => 'array' ), $result['mixed_array'][3] );
	}

	public function test_to_json_returns_valid_json_string() {
		$model       = $this->get_test_model();
		$json_result = $model->to_json();

		// Should be valid JSON
		$this->assertJson( $json_result );

		// Decode and verify content
		$decoded = json_decode( $json_result, true );
		$this->assertEquals( 'Test Item', $decoded['name'] );
		$this->assertEquals( 5, $decoded['quantity'] );
		$this->assertEquals( array( 'tag1', 'tag2' ), $decoded['tags'] );
		$this->assertTrue( $decoded['active'] );
	}

	public function test_to_json_handles_nested_models() {
		$model       = $this->get_nested_test_model();
		$json_result = $model->to_json();

		// Should be valid JSON
		$this->assertJson( $json_result );

		// Decode and verify nested structure
		$decoded = json_decode( $json_result, true );
		$this->assertEquals( 'Parent', $decoded['parent_name'] );
		$this->assertEquals( 'Child', $decoded['child']['child_name'] );
		$this->assertEquals( 123, $decoded['child']['child_id'] );

		$this->assertCount( 2, $decoded['children'] );
		$this->assertEquals( 'Child 1', $decoded['children'][0]['name'] );
		$this->assertEquals( 'Child 2', $decoded['children'][1]['name'] );
	}

	public function test_to_json_with_empty_model() {
		$model = new class() extends BaseModel {
			// No public properties
			private string $private_only = 'hidden';
		};

		$json_result = $model->to_json();
		$this->assertEquals( '[]', $json_result ); // Empty JSON object as array
	}

	public function test_serialize_value_with_null_values() {
		$model = new class() extends BaseModel {
			public ?string $nullable_string = null;
			public ?int $nullable_int       = null;
			public ?array $nullable_array   = null;
		};

		$result = $model->to_array();

		$this->assertArrayHasKey( 'nullable_string', $result );
		$this->assertArrayHasKey( 'nullable_int', $result );
		$this->assertArrayHasKey( 'nullable_array', $result );

		$this->assertNull( $result['nullable_string'] );
		$this->assertNull( $result['nullable_int'] );
		$this->assertNull( $result['nullable_array'] );
	}

	public function test_to_array_with_specific_key() {
		$model = $this->get_test_model();

		// Test getting specific properties
		$this->assertEquals( 'Test Item', $model->to_array( 'name' ) );
		$this->assertEquals( 5, $model->to_array( 'quantity' ) );
		$this->assertEquals( array( 'tag1', 'tag2' ), $model->to_array( 'tags' ) );
		$this->assertTrue( $model->to_array( 'active' ) );
	}

	public function test_to_array_with_nested_model_key() {
		$model = $this->get_nested_test_model();

		// Test getting nested model property
		$child_result = $model->to_array( 'child' );
		$this->assertIsArray( $child_result );
		$this->assertEquals( 'Child', $child_result['child_name'] );
		$this->assertEquals( 123, $child_result['child_id'] );

		// Test getting array of nested models
		$children_result = $model->to_array( 'children' );
		$this->assertIsArray( $children_result );
		$this->assertCount( 2, $children_result );
		$this->assertEquals( 'Child 1', $children_result[0]['name'] );
		$this->assertEquals( 'Child 2', $children_result[1]['name'] );
	}

	public function test_to_array_with_invalid_key_throws_exception() {
		$model = $this->get_test_model();

		$this->expectException( \InvalidArgumentException::class );
		$this->expectExceptionMessageMatches( '/Property "invalid_key" does not exist/' );

		$model->to_array( 'invalid_key' );
	}

	public function test_to_array_key_parameter_is_case_sensitive() {
		$model = $this->get_test_model();

		// These should work
		$this->assertEquals( 'Test Item', $model->to_array( 'name' ) );

		// These should throw exceptions
		$this->expectException( \InvalidArgumentException::class );
		$model->to_array( 'Name' ); // Capital N
	}

	public function test_nested_models_with_key_parameter() {
		$parent = new class() extends BaseModel {
			public string $parent_name = 'Parent';
			public $child;

			public function __construct() {
				$this->child = new class() extends BaseModel {
					public string $child_name = 'Child';
					public array $items       = array( 'item1', 'item2' );
				};
			}
		};

		// Get specific property from nested model
		$child_items = $parent->to_array( 'child' );
		$this->assertEquals( 'Child', $child_items['child_name'] );
		$this->assertEquals( array( 'item1', 'item2' ), $child_items['items'] );
	}
}
