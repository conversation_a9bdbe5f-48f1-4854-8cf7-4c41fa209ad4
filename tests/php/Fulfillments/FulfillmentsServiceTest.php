<?php

namespace Automattic\WCShipping\Tests\php\Fulfillments;

use Automattic\WCShipping\Fulfillments\FulfillmentsService;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;

class FulfillmentsServiceTest extends WCShipping_Test_Case {

	/**
	 * @var FulfillmentsService
	 */
	private $service;

	public function setUp(): void {
		parent::setUp();
		$this->service = new FulfillmentsService();
	}

	/**
	 * Test the shipment items to fulfillment items conversion.
	 */
	public function test_convert_shipment_items_to_fulfillment_items() {
		$shipment_items = array(
			array(
				'id'       => 100,
				'subItems' => array( 1, 2, 3 ),
			),
			array(
				'id'       => 101,
				'subItems' => array(),
			),
			array(
				'id'       => 102,
				'subItems' => array( 4 ),
			),
		);

		$result = $this->service->convert_shipment_items_to_fulfillment_items( $shipment_items );

		$expected = array(
			array(
				'item_id' => 100,
				'qty'     => 3,
			),
			array(
				'item_id' => 101,
				'qty'     => 1,
			),
			array(
				'item_id' => 102,
				'qty'     => 1,
			),
		);

		$this->assertEquals( $expected, $result );
	}

	/**
	 * Test conversion with empty subItems should return qty of 1.
	 */
	public function test_convert_shipment_items_empty_subitems() {
		$shipment_items = array(
			array(
				'id'       => 100,
				'subItems' => array(),
			),
		);

		$result = $this->service->convert_shipment_items_to_fulfillment_items( $shipment_items );

		$expected = array(
			array(
				'item_id' => 100,
				'qty'     => 1,
			),
		);

		$this->assertEquals( $expected, $result );
	}

	/**
	 * Test conversion with empty items array.
	 */
	public function test_convert_shipment_items_empty_array() {
		$result = $this->service->convert_shipment_items_to_fulfillment_items( array() );
		$this->assertEquals( array(), $result );
	}

	/**
	 * Test that update_order_fulfillments throws exception for invalid order.
	 */
	public function test_update_order_fulfillments_throws_exception_for_invalid_order() {
		$this->expectException( \Exception::class );
		$this->expectExceptionMessage( 'Order not found' );

		$fulfillments_data = array( 'shipments' => array() );
		$this->service->update_order_fulfillments( 99999, $fulfillments_data );
	}

	/**
	 * Test the private method to check if items are in shipment format.
	 * This tests the logic by invoking it through a public method.
	 */
	public function test_shipment_items_format_detection() {
		// Test valid shipment format
		$valid_shipment_items = array(
			array(
				'id'       => 100,
				'subItems' => array( 1, 2 ),
			),
		);

		$result = $this->service->convert_shipment_items_to_fulfillment_items( $valid_shipment_items );
		$this->assertNotEmpty( $result );
		$this->assertEquals( 100, $result[0]['item_id'] );
		$this->assertEquals( 2, $result[0]['qty'] );
	}

	// ==================================================
	// SCENARIO TESTS FOR NEW FULFILLMENT-ONLY ARCHITECTURE
	// ==================================================

	/**
	 * Test Scenario 1: Initial Creation
	 * When no existing fulfillments exist, create new ones from shipment data.
	 */
	public function test_scenario_1_initial_creation() {
		$shipments = array(
			array(
				array(
					'id'             => 12,
					'fulfillment_id' => 100,
					'subItems'       => array( '12-sub-0', '12-sub-1' ),
				),
				array(
					'id'             => 5,
					'fulfillment_id' => 100,
					'subItems'       => array( '5-sub-0' ),
				),
			),
			array(
				array(
					'id'             => 6,
					'fulfillment_id' => 101,
					'subItems'       => array(),
				),
				array(
					'id'             => 50,
					'fulfillment_id' => 101,
					'subItems'       => array( '50-sub-0', '50-sub-1', '50-sub-2' ),
				),
			),
		);

		// Test conversion for first shipment
		$first_shipment_items = $this->service->convert_shipment_items_to_fulfillment_items( $shipments[0] );
		$expected_first       = array(
			array(
				'item_id' => 12,
				'qty'     => 2,
			), // 2 subItems
			array(
				'item_id' => 5,
				'qty'     => 1,
			),  // 1 subItem
		);
		$this->assertEquals( $expected_first, $first_shipment_items );

		// Test conversion for second shipment
		$second_shipment_items = $this->service->convert_shipment_items_to_fulfillment_items( $shipments[1] );
		$expected_second       = array(
			array(
				'item_id' => 6,
				'qty'     => 1,
			),  // No subItems = qty 1
			array(
				'item_id' => 50,
				'qty'     => 3,
			), // 3 subItems
		);
		$this->assertEquals( $expected_second, $second_shipment_items );
	}

	/**
	 * Test Scenario 2: Item Movement Between Fulfillments
	 * Simulates moving items between fulfillments with same total count.
	 */
	public function test_scenario_2_item_movement() {
		// Simulate moving item 5 from first fulfillment to second
		$original_first_shipment = array(
			array(
				'id'             => 12,
				'fulfillment_id' => 100,
				'subItems'       => array( '12-sub-0', '12-sub-1' ),
			),
			array(
				'id'             => 5,
				'fulfillment_id' => 100,
				'subItems'       => array( '5-sub-0' ),
			),
		);

		$moved_first_shipment = array(
			array(
				'id'             => 12,
				'fulfillment_id' => 100,
				'subItems'       => array( '12-sub-0' ),
			), // Reduced subItems
		);

		$moved_second_shipment = array(
			array(
				'id'             => 5,
				'fulfillment_id' => 100,
				'subItems'       => array( '5-sub-0' ),
			), // Moved here
			array(
				'id'             => 6,
				'fulfillment_id' => 101,
				'subItems'       => array(),
			),
			array(
				'id'             => 50,
				'fulfillment_id' => 101,
				'subItems'       => array( '50-sub-0', '50-sub-1' ),
			),
		);

		// Test original conversion
		$original_items = $this->service->convert_shipment_items_to_fulfillment_items( $original_first_shipment );
		$this->assertCount( 2, $original_items );
		$this->assertEquals( 12, $original_items[0]['item_id'] );
		$this->assertEquals( 2, $original_items[0]['qty'] );

		// Test after movement
		$moved_first_items = $this->service->convert_shipment_items_to_fulfillment_items( $moved_first_shipment );
		$this->assertCount( 1, $moved_first_items );
		$this->assertEquals( 12, $moved_first_items[0]['item_id'] );
		$this->assertEquals( 1, $moved_first_items[0]['qty'] ); // Reduced from 2 to 1

		$moved_second_items = $this->service->convert_shipment_items_to_fulfillment_items( $moved_second_shipment );
		$this->assertCount( 3, $moved_second_items );
		$this->assertEquals( 5, $moved_second_items[0]['item_id'] ); // Item moved here
	}

	/**
	 * Test Scenario 3: Fulfillment Splitting (Addition)
	 * When one large fulfillment is split into multiple smaller ones.
	 */
	public function test_scenario_3_fulfillment_splitting() {
		// Original large fulfillment
		$large_shipment = array(
			array(
				'id'             => 12,
				'fulfillment_id' => 100,
				'subItems'       => array( '12-sub-0', '12-sub-1' ),
			),
			array(
				'id'             => 5,
				'fulfillment_id' => 100,
				'subItems'       => array( '5-sub-0' ),
			),
			array(
				'id'             => 6,
				'fulfillment_id' => 101,
				'subItems'       => array(),
			),
			array(
				'id'             => 50,
				'fulfillment_id' => 101,
				'subItems'       => array( '50-sub-0', '50-sub-1', '50-sub-2' ),
			),
		);

		// Split into multiple fulfillments
		$split_shipment_1 = array(
			array(
				'id'             => 12,
				'fulfillment_id' => 100,
				'subItems'       => array( '12-sub-0', '12-sub-1' ),
			),
		);
		$split_shipment_2 = array(
			array(
				'id'             => 5,
				'fulfillment_id' => 100,
				'subItems'       => array( '5-sub-0' ),
			),
			array(
				'id'             => 6,
				'fulfillment_id' => 101,
				'subItems'       => array(),
			),
		);
		$split_shipment_3 = array(
			array(
				'id'             => 50,
				'fulfillment_id' => 101,
				'subItems'       => array( '50-sub-0', '50-sub-1', '50-sub-2' ),
			),
		);

		// Test original
		$large_items = $this->service->convert_shipment_items_to_fulfillment_items( $large_shipment );
		$this->assertCount( 4, $large_items );

		// Test split results
		$split_1_items = $this->service->convert_shipment_items_to_fulfillment_items( $split_shipment_1 );
		$this->assertCount( 1, $split_1_items );
		$this->assertEquals( 12, $split_1_items[0]['item_id'] );

		$split_2_items = $this->service->convert_shipment_items_to_fulfillment_items( $split_shipment_2 );
		$this->assertCount( 2, $split_2_items );

		$split_3_items = $this->service->convert_shipment_items_to_fulfillment_items( $split_shipment_3 );
		$this->assertCount( 1, $split_3_items );
		$this->assertEquals( 50, $split_3_items[0]['item_id'] );
		$this->assertEquals( 3, $split_3_items[0]['qty'] );
	}

	/**
	 * Test Scenario 4: Fulfillment Consolidation (Removal)
	 * When multiple fulfillments are merged into fewer ones.
	 */
	public function test_scenario_4_fulfillment_consolidation() {
		// Multiple separate fulfillments
		$shipment_1 = array(
			array(
				'id'             => 12,
				'fulfillment_id' => 100,
				'subItems'       => array( '12-sub-0', '12-sub-1' ),
			),
		);
		$shipment_2 = array(
			array(
				'id'             => 5,
				'fulfillment_id' => 100,
				'subItems'       => array( '5-sub-0' ),
			),
		);
		$shipment_3 = array(
			array(
				'id'             => 6,
				'fulfillment_id' => 101,
				'subItems'       => array(),
			),
			array(
				'id'             => 50,
				'fulfillment_id' => 101,
				'subItems'       => array( '50-sub-0', '50-sub-1' ),
			),
		);

		// Consolidated into one
		$consolidated_shipment = array(
			array(
				'id'             => 12,
				'fulfillment_id' => 100,
				'subItems'       => array( '12-sub-0', '12-sub-1' ),
			),
			array(
				'id'             => 5,
				'fulfillment_id' => 100,
				'subItems'       => array( '5-sub-0' ),
			),
			array(
				'id'             => 6,
				'fulfillment_id' => 101,
				'subItems'       => array(),
			),
			array(
				'id'             => 50,
				'fulfillment_id' => 101,
				'subItems'       => array( '50-sub-0', '50-sub-1' ),
			),
		);

		// Test separate conversions
		$items_1 = $this->service->convert_shipment_items_to_fulfillment_items( $shipment_1 );
		$items_2 = $this->service->convert_shipment_items_to_fulfillment_items( $shipment_2 );
		$items_3 = $this->service->convert_shipment_items_to_fulfillment_items( $shipment_3 );

		$total_separate_items = count( $items_1 ) + count( $items_2 ) + count( $items_3 );

		// Test consolidated conversion
		$consolidated_items = $this->service->convert_shipment_items_to_fulfillment_items( $consolidated_shipment );

		// Should have same number of items but in one fulfillment
		$this->assertEquals( $total_separate_items, count( $consolidated_items ) );
		$this->assertCount( 4, $consolidated_items );
	}

	/**
	 * Test Complex Combined Scenario: All Operations Together
	 * Simulates a realistic complex change with additions, removals, and movements.
	 */
	public function test_complex_combined_scenario() {
		// This tests the most complex scenario with:
		// - Quantity changes (subItems added/removed)
		// - Item movement between fulfillments
		// - Different fulfillment_id values (should be ignored)
		// - Empty subItems arrays

		$complex_shipment_before = array(
			array(
				'id'             => 12,
				'fulfillment_id' => 100,
				'subItems'       => array( '12-sub-0', '12-sub-1', '12-sub-2' ),
			),
			array(
				'id'             => 5,
				'fulfillment_id' => 200,
				'subItems'       => array( '5-sub-0', '5-sub-1' ),
			),
		);

		$complex_shipment_after = array(
			array(
				'id'             => 12,
				'fulfillment_id' => 999,
				'subItems'       => array( '12-sub-0' ),
			), // Reduced from 3 to 1, different fulfillment_id
			array(
				'id'             => 6,
				'fulfillment_id' => 888,
				'subItems'       => array(),
			), // New item, no subItems
			array(
				'id'             => 5,
				'fulfillment_id' => 777,
				'subItems'       => array( '5-sub-0', '5-sub-1', '5-sub-2', '5-sub-3' ),
			), // Increased from 2 to 4
		);

		$before_items = $this->service->convert_shipment_items_to_fulfillment_items( $complex_shipment_before );
		$after_items  = $this->service->convert_shipment_items_to_fulfillment_items( $complex_shipment_after );

		// Verify before state
		$this->assertCount( 2, $before_items );
		$this->assertEquals( 12, $before_items[0]['item_id'] );
		$this->assertEquals( 3, $before_items[0]['qty'] );
		$this->assertEquals( 5, $before_items[1]['item_id'] );
		$this->assertEquals( 2, $before_items[1]['qty'] );

		// Verify after state
		$this->assertCount( 3, $after_items );
		$this->assertEquals( 12, $after_items[0]['item_id'] );
		$this->assertEquals( 1, $after_items[0]['qty'] ); // Reduced
		$this->assertEquals( 6, $after_items[1]['item_id'] );
		$this->assertEquals( 1, $after_items[1]['qty'] ); // New item
		$this->assertEquals( 5, $after_items[2]['item_id'] );
		$this->assertEquals( 4, $after_items[2]['qty'] ); // Increased
	}

	/**
	 * Test Edge Case: fulfillment_id Variations
	 * Verify that different fulfillment_id values don't affect item processing.
	 */
	public function test_edge_case_fulfillment_id_variations() {
		$shipments_with_fulfillment_id = array(
			array(
				'id'             => 12,
				'fulfillment_id' => 100,
				'subItems'       => array( '12-sub-0' ),
			),
			array(
				'id'             => 5,
				'fulfillment_id' => 999,
				'subItems'       => array(),
			), // Different fulfillment_id
			array(
				'id'       => 6,
				'subItems' => array(),
			), // Missing fulfillment_id
		);

		$items = $this->service->convert_shipment_items_to_fulfillment_items( $shipments_with_fulfillment_id );

		// Should work normally regardless of fulfillment_id presence/values
		$this->assertCount( 3, $items );
		$this->assertEquals( 12, $items[0]['item_id'] );
		$this->assertEquals( 1, $items[0]['qty'] );
		$this->assertEquals( 5, $items[1]['item_id'] );
		$this->assertEquals( 1, $items[1]['qty'] );
		$this->assertEquals( 6, $items[2]['item_id'] );
		$this->assertEquals( 1, $items[2]['qty'] );
	}

	/**
	 * Test Protection Against Updating Fulfilled Fulfillments
	 * Verify that the logic correctly identifies fulfillment status scenarios.
	 */
	public function test_fulfilled_fulfillments_protection() {
		// Since we can't mock the actual Fulfillment class in the test environment,
		// this test focuses on verifying the logic and data transformation
		// The actual protection is implemented in the update_order_fulfillments method

		$shipments = array(
			array(
				array(
					'id'             => 12,
					'fulfillment_id' => 100,
					'subItems'       => array( '12-sub-0' ),
				), // Would try to update fulfilled fulfillment
			),
			array(
				array(
					'id'             => 5,
					'fulfillment_id' => 101,
					'subItems'       => array( '5-sub-0', '5-sub-1' ),
				), // Would update unfulfilled fulfillment
			),
			// Note: fulfillment 102 (fulfilled) is intentionally missing - should not be deleted
		);

		// Test that the item conversion logic still works correctly
		// even when dealing with fulfillment protection scenarios

		// Verify first shipment conversion
		$first_shipment_items = $this->service->convert_shipment_items_to_fulfillment_items( $shipments[0] );
		$this->assertCount( 1, $first_shipment_items );
		$this->assertEquals( 12, $first_shipment_items[0]['item_id'] );
		$this->assertEquals( 1, $first_shipment_items[0]['qty'] );

		// Verify second shipment conversion
		$second_shipment_items = $this->service->convert_shipment_items_to_fulfillment_items( $shipments[1] );
		$this->assertCount( 1, $second_shipment_items );
		$this->assertEquals( 5, $second_shipment_items[0]['item_id'] );
		$this->assertEquals( 2, $second_shipment_items[0]['qty'] );

		// Test fulfillment_id extraction for both scenarios
		$reflection = new \ReflectionClass( $this->service );
		$method     = $reflection->getMethod( 'extract_fulfillment_id_from_shipment' );
		$method->setAccessible( true );

		$extracted_id_1 = $method->invoke( $this->service, $shipments[0] );
		$this->assertEquals( 100, $extracted_id_1 );

		$extracted_id_2 = $method->invoke( $this->service, $shipments[1] );
		$this->assertEquals( 101, $extracted_id_2 );
	}

	/**
	 * Test Extract Fulfillment ID Method
	 * Verify that fulfillment_id extraction works correctly.
	 */
	public function test_extract_fulfillment_id_from_shipment() {
		// Test shipment with fulfillment_id
		$shipment_with_id = array(
			array(
				'id'             => 12,
				'fulfillment_id' => 100,
				'subItems'       => array(),
			),
			array(
				'id'             => 5,
				'fulfillment_id' => 100,
				'subItems'       => array(),
			), // Same fulfillment_id
		);

		// Test shipment without fulfillment_id
		$shipment_without_id = array(
			array(
				'id'       => 12,
				'subItems' => array(),
			),
			array(
				'id'       => 5,
				'subItems' => array(),
			),
		);

		// Test empty shipment
		$empty_shipment = array();

		// Use reflection to test the private method
		$reflection = new \ReflectionClass( $this->service );
		$method     = $reflection->getMethod( 'extract_fulfillment_id_from_shipment' );
		$method->setAccessible( true );

		// Test extraction with fulfillment_id
		$extracted_id = $method->invoke( $this->service, $shipment_with_id );
		$this->assertEquals( 100, $extracted_id );

		// Test extraction without fulfillment_id
		$extracted_null = $method->invoke( $this->service, $shipment_without_id );
		$this->assertNull( $extracted_null );

		// Test extraction from empty shipment
		$extracted_empty = $method->invoke( $this->service, $empty_shipment );
		$this->assertNull( $extracted_empty );
	}

	/**
	 * Test convert_fulfillments_to_shipments_format with single fulfillment and single item
	 */
	public function test_convert_fulfillments_to_shipments_format_single_item() {
		// Create mock fulfillment with single item (qty = 1)
		$fulfillment = $this->createMockFulfillment(
			100,
			array(
				array(
					'item_id' => 12,
					'qty'     => 1,
				),
			)
		);

		$result = $this->service->convert_fulfillments_to_shipments_format( array( $fulfillment ) );

		$expected = array(
			'0' => array(
				array(
					'id'             => 12,
					'subItems'       => array(), // Empty array when qty = 1
					'fulfillment_id' => 100,
				),
			),
		);

		$this->assertEquals( $expected, $result );
	}

	/**
	 * Test convert_fulfillments_to_shipments_format with single fulfillment and multiple items with different quantities
	 */
	public function test_convert_fulfillments_to_shipments_format_multiple_items() {
		// Create mock fulfillment with multiple items
		$fulfillment = $this->createMockFulfillment(
			101,
			array(
				array(
					'item_id' => 12,
					'qty'     => 2,
				),
				array(
					'item_id' => 5,
					'qty'     => 1,
				),
				array(
					'item_id' => 50,
					'qty'     => 3,
				),
			)
		);

		$result = $this->service->convert_fulfillments_to_shipments_format( array( $fulfillment ) );

		$expected = array(
			'0' => array(
				array(
					'id'             => 12,
					'subItems'       => array( '12-sub-0', '12-sub-1' ), // 2 subItems for qty = 2
					'fulfillment_id' => 101,
				),
				array(
					'id'             => 5,
					'subItems'       => array(), // Empty array for qty = 1
					'fulfillment_id' => 101,
				),
				array(
					'id'             => 50,
					'subItems'       => array( '50-sub-0', '50-sub-1', '50-sub-2' ), // 3 subItems for qty = 3
					'fulfillment_id' => 101,
				),
			),
		);

		$this->assertEquals( $expected, $result );
	}

	/**
	 * Test convert_fulfillments_to_shipments_format with multiple fulfillments
	 */
	public function test_convert_fulfillments_to_shipments_format_multiple_fulfillments() {
		// Create two mock fulfillments
		$fulfillment1 = $this->createMockFulfillment(
			100,
			array(
				array(
					'item_id' => 12,
					'qty'     => 2,
				),
				array(
					'item_id' => 5,
					'qty'     => 1,
				),
			)
		);

		$fulfillment2 = $this->createMockFulfillment(
			101,
			array(
				array(
					'item_id' => 6,
					'qty'     => 1,
				),
				array(
					'item_id' => 50,
					'qty'     => 3,
				),
			)
		);

		$result = $this->service->convert_fulfillments_to_shipments_format( array( $fulfillment1, $fulfillment2 ) );

		$expected = array(
			'0' => array(
				array(
					'id'             => 12,
					'subItems'       => array( '12-sub-0', '12-sub-1' ),
					'fulfillment_id' => 100,
				),
				array(
					'id'             => 5,
					'subItems'       => array(),
					'fulfillment_id' => 100,
				),
			),
			'1' => array(
				array(
					'id'             => 6,
					'subItems'       => array(),
					'fulfillment_id' => 101,
				),
				array(
					'id'             => 50,
					'subItems'       => array( '50-sub-0', '50-sub-1', '50-sub-2' ),
					'fulfillment_id' => 101,
				),
			),
		);

		$this->assertEquals( $expected, $result );
	}

	/**
	 * Test convert_fulfillments_to_shipments_format with empty fulfillments array
	 */
	public function test_convert_fulfillments_to_shipments_format_empty_array() {
		$result = $this->service->convert_fulfillments_to_shipments_format( array() );
		$this->assertEquals( array(), $result );
	}

	/**
	 * Test convert_fulfillments_to_shipments_format with fulfillment containing no items
	 */
	public function test_convert_fulfillments_to_shipments_format_fulfillment_with_no_items() {
		$fulfillment = $this->createMockFulfillment( 100, array() );

		$result = $this->service->convert_fulfillments_to_shipments_format( array( $fulfillment ) );

		// Should return empty array since fulfillment has no items
		$this->assertEquals( array(), $result );
	}

	/**
	 * Test convert_fulfillments_to_shipments_format with quantity = 0 (edge case)
	 */
	public function test_convert_fulfillments_to_shipments_format_zero_quantity() {
		$fulfillment = $this->createMockFulfillment(
			100,
			array(
				array(
					'item_id' => 12,
					'qty'     => 0,
				),
			)
		);

		$result = $this->service->convert_fulfillments_to_shipments_format( array( $fulfillment ) );

		$expected = array(
			'0' => array(
				array(
					'id'             => 12,
					'subItems'       => array(), // Empty array for qty = 0
					'fulfillment_id' => 100,
				),
			),
		);

		$this->assertEquals( $expected, $result );
	}

	/**
	 * Test convert_fulfillments_to_shipments_format with large quantity
	 */
	public function test_convert_fulfillments_to_shipments_format_large_quantity() {
		$fulfillment = $this->createMockFulfillment(
			100,
			array(
				array(
					'item_id' => 12,
					'qty'     => 5,
				),
			)
		);

		$result = $this->service->convert_fulfillments_to_shipments_format( array( $fulfillment ) );

		$expected = array(
			'0' => array(
				array(
					'id'             => 12,
					'subItems'       => array( '12-sub-0', '12-sub-1', '12-sub-2', '12-sub-3', '12-sub-4' ),
					'fulfillment_id' => 100,
				),
			),
		);

		$this->assertEquals( $expected, $result );
	}

	/**
	 * Helper method to create mock fulfillment objects
	 *
	 * @param int   $fulfillment_id The fulfillment ID
	 * @param array $items Array of items with item_id and qty
	 * @return \PHPUnit\Framework\MockObject\MockObject
	 */
	private function createMockFulfillment( int $fulfillment_id, array $items ) {
		$fulfillment = $this->getMockBuilder( 'stdClass' )
			->addMethods( array( 'get_id', 'get_items' ) )
			->getMock();

		$fulfillment->method( 'get_id' )->willReturn( $fulfillment_id );
		$fulfillment->method( 'get_items' )->willReturn( $items );

		return $fulfillment;
	}

	/**
	 * Test get_order_fulfillments_as_shipments exception handling
	 * Note: The actual order validation and fulfillment datastore integration
	 * are tested in integration tests. Here we focus on the conversion logic.
	 */
	public function test_get_order_fulfillments_as_shipments_conversion_logic() {
		// This test verifies that the method correctly calls the conversion function
		// The conversion logic itself is tested separately in convert_fulfillments_to_shipments_format tests
		// Full integration testing would require a real WooCommerce environment with fulfillments
		$this->assertTrue( method_exists( $this->service, 'get_order_fulfillments_as_shipments' ) );
		$this->assertTrue( method_exists( $this->service, 'convert_fulfillments_to_shipments_format' ) );
	}

	/**
	 * Test roundtrip conversion: shipments -> fulfillments -> shipments
	 * This verifies that our conversion is symmetrical with the existing convert_shipment_items_to_fulfillment_items method
	 */
	public function test_roundtrip_conversion_shipments_to_fulfillments_to_shipments() {
		// Original shipment format (as it would come from UI)
		$original_shipments = array(
			'0' => array(
				array(
					'id'             => 12,
					'subItems'       => array( '12-sub-0', '12-sub-1' ),
					'fulfillment_id' => 100,
				),
				array(
					'id'             => 5,
					'subItems'       => array(),
					'fulfillment_id' => 100,
				),
			),
			'1' => array(
				array(
					'id'             => 6,
					'subItems'       => array(),
					'fulfillment_id' => 101,
				),
				array(
					'id'             => 50,
					'subItems'       => array( '50-sub-0', '50-sub-1', '50-sub-2' ),
					'fulfillment_id' => 101,
				),
			),
		);

		// Step 1: Convert shipments to fulfillment items (existing functionality)
		$fulfillment_items_0 = $this->service->convert_shipment_items_to_fulfillment_items( $original_shipments['0'] );
		$fulfillment_items_1 = $this->service->convert_shipment_items_to_fulfillment_items( $original_shipments['1'] );

		// Verify intermediate conversion is correct
		$expected_fulfillment_items_0 = array(
			array(
				'item_id' => 12,
				'qty'     => 2,
			),
			array(
				'item_id' => 5,
				'qty'     => 1,
			),
		);
		$expected_fulfillment_items_1 = array(
			array(
				'item_id' => 6,
				'qty'     => 1,
			),
			array(
				'item_id' => 50,
				'qty'     => 3,
			),
		);

		$this->assertEquals( $expected_fulfillment_items_0, $fulfillment_items_0 );
		$this->assertEquals( $expected_fulfillment_items_1, $fulfillment_items_1 );

		// Step 2: Create mock fulfillments using the converted items
		$fulfillment_0 = $this->createMockFulfillment( 100, $fulfillment_items_0 );
		$fulfillment_1 = $this->createMockFulfillment( 101, $fulfillment_items_1 );

		// Step 3: Convert fulfillments back to shipments format (our new functionality)
		$converted_back_shipments = $this->service->convert_fulfillments_to_shipments_format( array( $fulfillment_0, $fulfillment_1 ) );

		// Step 4: Verify the result matches the original shipment format
		$this->assertEquals( $original_shipments, $converted_back_shipments );
	}

	/**
	 * Test that ensure_order_has_fulfillment creates a fulfillment when none exist.
	 * Note: This is a basic test - full integration testing would require WooCommerce test infrastructure.
	 */
	public function test_ensure_order_has_fulfillment_public_method_exists() {
		// Verify the public method exists and is callable
		$this->assertTrue( method_exists( $this->service, 'ensure_order_has_fulfillment' ) );
		$this->assertTrue( is_callable( array( $this->service, 'ensure_order_has_fulfillment' ) ) );
	}
}
