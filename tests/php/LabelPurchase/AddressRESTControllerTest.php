<?php

namespace Automattic\WCShipping\Tests\php\LabelPurchase;

use Automattic\WCShipping\Carrier\UPSDAP\UPSDAPCarrierStrategyService;
use Automattic\WCShipping\Connect\WC_Connect_API_Client_Live;
use Automattic\WCShipping\Connect\WC_Connect_Logger;
use Automattic\WCShipping\Connect\WC_Connect_Service_Schemas_Store;
use Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store;
use Automattic\WCShipping\LabelPurchase\AddressNormalizationService;
use Automattic\WCShipping\LabelPurchase\AddressRESTController;
use Automattic\WCShipping\OriginAddresses\OriginAddressService;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use WC_Helper_Order;
use WC_Helper_Product;
use WC_Product_Simple;
use WP_Error;

class AddressRESTControllerTest extends WCShipping_Test_Case {

	/**
	 * REST controller under test.
	 *
	 * @var AddressRESTController
	 */
	private $controller;

	/**
	 * Address normalization service under test.
	 *
	 * @var AddressNormalizationService
	 */
	private $normalization_service;

	/**
	 * Mocked API client class.
	 *
	 * @var WC_Connect_API_Client_Live
	 */
	private $api_client_mock;

	/**
	 * Mocked logger class.
	 *
	 * @var WC_Connect_Logger
	 */
	private $logger_mock;



	/**
	 * Mocked OriginAddressService class.
	 *
	 * @var OriginAddressService
	 */
	private $origin_address_service;

	/**
	 * Test address fields.
	 *
	 * @var array
	 */
	const TEST_ADDRESS = array(
		'id'         => 'random_id',
		'first_name' => 'John',
		'last_name'  => 'Doe',
		'address'    => '123 Main St',
		'address_2'  => 'Apt 1',
		'city'       => 'San Francisco',
		'state'      => 'CA',
		'postcode'   => '94105',
		'country'    => 'US',
		'phone'      => '************',
		'company'    => 'WooCommerce Shipping',
		'email'      => '<EMAIL>',
	);

	/**
	 * @inherit
	 */
	public static function set_up_before_class() {
		require_once __DIR__ . '/../../../classes/class-wc-connect-api-client-live.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-service-settings-store.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-logger.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-service-schemas-store.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-account-settings.php';
	}

	public function setUp(): void {
		parent::setUp();
		$this->api_client_mock = $this->getMockBuilder( WC_Connect_API_Client_Live::class )
			->disableOriginalConstructor()
			->setMethods( array( 'send_address_normalization_request' ) )
			->getMock();

		$settings_store = new WC_Connect_Service_Settings_Store(
			$this->createMock( WC_Connect_Service_Schemas_Store::class ),
			$this->api_client_mock,
			$this->createMock( WC_Connect_Logger::class )
		);

		$this->logger_mock = $this->getMockBuilder( WC_Connect_Logger::class )
			->disableOriginalConstructor()
			->setMethods( array( 'log' ) )
			->getMock();

		$this->origin_address_service = $this->getMockBuilder( OriginAddressService::class )
			->disableOriginalConstructor()
			->setMethods( array( 'update_origin_addresses', 'delete_origin_address', 'get_origin_addresses' ) )
			->getMock();

		$this->normalization_service = new AddressNormalizationService(
			$settings_store,
			$this->api_client_mock,
			$this->logger_mock,
			$this->origin_address_service
		);

		$upsdap_carrier_strategy_service = new UPSDAPCarrierStrategyService( $this->origin_address_service, $this->api_client_mock );
		$this->controller                = new AddressRESTController(
			$this->normalization_service,
			$this->origin_address_service,
			$upsdap_carrier_strategy_service
		);

		add_action( 'rest_api_init', array( $this->controller, 'register_routes' ) );
		do_action( 'rest_api_init' );
		$this->set_store_to_be_eligible_for_shipping_label_creation();
	}

	public function test_update_origin() {
			$origin  = self::TEST_ADDRESS;
			$request = $this->create_request( '/wcshipping/v1/address/update_origin' );
			$request->set_body(
				wp_json_encode(
					array(
						'address'    => $origin,
						'isVerified' => false,
					)
				)
			);
			$this->origin_address_service->expects( $this->once() )->method( 'update_origin_addresses' )->willReturn( $origin );
			$response                     = rest_do_request( $request );
			$expected_origin              = $origin;
			$expected_origin['address_1'] = $origin['address'];
			unset( $expected_origin['address'] );

			$this->assertEquals( 200, $response->status );
			$this->assertTrue( $response->get_data()['success'] );
			$this->assertEquals( $expected_origin, $response->get_data()['address'] );
			$this->assertTrue( $response->get_data()['isVerified'] );
	}

	public function test_update_destination() {
		$product             = $this->create_simple_product();
		$order               = WC_Helper_Order::create_order( 1, $product );
		$destination_address = self::TEST_ADDRESS;
		unset( $destination_address['id'] );
		$request = $this->create_request( '/wcshipping/v1/address/' . $order->get_id() . '/update_destination' );
		$request->set_body(
			wp_json_encode(
				array(
					'address'    => $destination_address,
					'isVerified' => false,
				)
			)
		);

		$response = rest_do_request( $request );

		$expected_shipping              = $destination_address;
		$expected_shipping['address_1'] = $expected_shipping['address'];
		unset( $expected_shipping['address'] );
		unset( $expected_shipping['email'] );
		$order = wc_get_order( $order->get_id() );

		$this->assertEquals( 200, $response->status );
		$this->assertTrue( $response->get_data()['success'] );
		$this->assertEquals( $expected_shipping, $order->get_address( 'shipping' ) );
		$this->assertFalse( $response->get_data()['isVerified'] );
		$this->assertEquals( self::TEST_ADDRESS['email'], $order->get_billing_email() );
	}

	public function test_verify_order_destination() {
		$product = $this->create_simple_product();
		$order   = WC_Helper_Order::create_order( 1, $product );

		$request = $this->create_request( '/wcshipping/v1/address/' . $order->get_id() . '/verify_order', 'GET' );
		$request->set_param( 'order_id', $order->get_id() );

		$result                           = new \stdClass();
		$result->normalized               = (object) self::TEST_ADDRESS;
		$result->is_trivial_normalization = true;

		$this->api_client_mock->expects( $this->once() )
			->method( 'send_address_normalization_request' )
			->willReturn( $result );

		$response = $this->controller->verify_order_shipping_address( $request );

		$expected_address = self::TEST_ADDRESS;
		unset( $expected_address['address'] );

		$this->assertTrue( $response->get_data()['success'] );
		$this->assertFalse( $response->get_data()['isVerified'] );
		$this->assertTrue( $response->get_data()['isTrivialNormalization'] );

		$this->normalization_service->set_is_destination_address_normalized( $order->get_id(), true );

		$verified_response = $this->controller->verify_order_shipping_address( $request );

		$this->assertTrue( $verified_response->get_data()['success'] );
		$this->assertTrue( $verified_response->get_data()['isVerified'] );
	}

	public function test_normalize() {
		$address = self::TEST_ADDRESS;

		$result             = new \stdClass();
		$result->normalized = (object) self::TEST_ADDRESS;
		$this->api_client_mock->expects( $this->once() )
			->method( 'send_address_normalization_request' )
			->willReturn( $result );

		$request = $this->create_request( '/wcshipping/v1/address/normalize' );
		$request->set_body( wp_json_encode( array( 'address' => $address ) ) );
		$response = $this->controller->normalize_address( $request );

		$expected_address              = $address;
		$expected_address['address_1'] = $expected_address['address'];
		unset( $expected_address['address'] );

		$this->assertEquals( 200, $response->status );
		$this->assertTrue( $response->get_data()['success'] );
		$this->assertEquals( $expected_address, $response->get_data()['normalizedAddress'] );
		$this->assertEquals( false, $response->get_data()['isTrivialNormalization'] );
		$this->assertEquals( $address, $response->get_data()['address'] );
	}

	public function test_normalize_with_errors() {
		$this->set_store_to_be_eligible_for_shipping_label_creation();
		$address = self::TEST_ADDRESS;

		$request = $this->create_request( '/wcshipping/v1/address/normalize' );
		$request->set_body( wp_json_encode( array( 'address' => $address ) ) );

		$wp_error                   = new WP_Error( 400, 'Something broke.' );
		$result_error               = new \stdClass();
		$result_error->field_errors = (object) array( 'general' => 'Something broke again.' );
		$this->api_client_mock->expects( $this->exactly( 2 ) )
			->method( 'send_address_normalization_request' )
			->willReturnOnConsecutiveCalls( $wp_error, $result_error );
		$this->logger_mock->expects( $this->exactly( 2 ) )
			->method( 'log' );

		$response = $this->controller->normalize_address( $request );

		$this->assertEquals( 400, $response->get_error_code() );
		$this->assertEquals( 'Something broke.', $response->get_error_message() );

		$response = $this->controller->normalize_address( $request );

		$this->assertEquals( 200, $response->status );
		$this->assertFalse( $response->get_data()['success'] );
		$this->assertEquals( 'Something broke again.', $response->get_data()['errors']->general );
	}

	public function test_update_origin_returns_error_if_update_fails() {
		$origin  = self::TEST_ADDRESS;
		$request = $this->create_request( '/wcshipping/v1/address/update_origin' );
		$request->set_body(
			wp_json_encode(
				array(
					'address'    => $origin,
					'isVerified' => false,
				)
			)
		);
		$this->origin_address_service->expects( $this->once() )->method( 'update_origin_addresses' )->willReturn( false );
		$response = rest_do_request( $request );

		$this->assertEquals( 500, $response->status );
		$this->assertEquals( 'origin_address_update_failed', $response->get_data()['code'] );
	}

	/**
	 * Test that an origin address can be deleted
	 */
	public function test_deleting_origin_address_in_settings_store() {
		$request = $this->create_request( '/wcshipping/v1/address/' . self::TEST_ADDRESS['id'], 'DELETE' );
		$request->set_header( 'Content-Type', 'application/json' );

		$this->origin_address_service->expects( $this->once() )
			->method( 'delete_origin_address' )
			->with( self::TEST_ADDRESS['id'] )
			->willReturn(
				array(
					'success'    => true,
					'deleted_id' => self::TEST_ADDRESS['id'],
				)
			);

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->status );
	}

	/**
	 * Set up a store to be eligible for shipping label creation.
	 */
	private function set_store_to_be_eligible_for_shipping_label_creation() {
		update_option( 'woocommerce_currency', 'USD' );
		update_option( 'woocommerce_default_country', 'US' );
	}


	/**
	 * A helper to create a simple product that is default to be shippable.
	 *
	 * @param bool $is_shippable Whether the simple product is shippable or not.
	 * @return WC_Product_Simple|null|false
	 */
	private function create_simple_product( $is_shippable = true ) {
		$product = WC_Helper_Product::create_simple_product();
		$product->set_virtual( ! $is_shippable );
		$product->save();
		return $product;
	}

	public function test_get_origin_addresses() {
		// Mock the expected response from the origin_address_service
		$expected_addresses = array(
			array(
				'id'         => 'origin_1',
				'first_name' => 'Alice',
				'last_name'  => 'Smith',
				'address'    => '456 Elm St',
				'city'       => 'Los Angeles',
				'state'      => 'CA',
				'postcode'   => '90001',
				'country'    => 'US',
			),
			// Add more mock addresses if needed
		);

		$this->origin_address_service->expects( $this->once() )
			->method( 'get_origin_addresses' )
			->willReturn( $expected_addresses );

		// Create a GET request to the /origins endpoint
		$request = $this->create_request( '/wcshipping/v1/address/origins', 'GET' );

		// Execute the request
		$response = rest_do_request( $request );

		// Assert the response status and data
		$this->assertEquals( 200, $response->status );
		$this->assertEquals( $expected_addresses, $response->get_data() );
	}
}
