<?php

namespace Automattic\WCShipping\Tests\php\LabelPurchase;

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\LabelPurchase\LabelPrintController;
use Automattic\WCShipping\LabelPurchase\LabelPrintService;
use Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store;
use Automattic\WCShipping\Connect\WC_Connect_API_Client;
use Automattic\WCShipping\Connect\WC_Connect_Logger;
use PHPUnit\Framework\MockObject\MockObject;
use WP_REST_Request;

class LabelPrintControllerTest extends WCShipping_Test_Case {
	/**
	 * @var WC_Connect_Service_Settings_Store|MockObject
	 */
	private $settings_store;

	/**
	 * @var WC_Connect_API_Client|MockObject
	 */
	private $api_client;

	/**
	 * @var WC_Connect_Logger|MockObject
	 */
	private $logger;

	/**
	 * @var LabelPrintService|MockObject
	 */
	private $label_print_service;

	/**
	 * @var LabelPrintController
	 */
	private $controller;

	public static function set_up_before_class(): void {
		require_once __DIR__ . '/../../../classes/class-wc-connect-service-settings-store.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-api-client.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-logger.php';
	}

	public function setUp(): void {
		parent::setUp();

		$this->settings_store = $this->getMockBuilder( WC_Connect_Service_Settings_Store::class )
			->disableOriginalConstructor()
			->getMock();

		$this->api_client = $this->getMockBuilder( WC_Connect_API_Client::class )
			->disableOriginalConstructor()
			->getMock();

		$this->logger = $this->getMockBuilder( WC_Connect_Logger::class )
			->disableOriginalConstructor()
			->getMock();

		$this->label_print_service = $this->getMockBuilder( LabelPrintService::class )
			->disableOriginalConstructor()
			->getMock();

		$this->controller = new LabelPrintController(
			$this->settings_store,
			$this->api_client,
			$this->logger,
			$this->label_print_service
		);

		add_action( 'rest_api_init', array( $this->controller, 'register_routes' ) );
		do_action( 'rest_api_init' );
	}

	public function test_print_label_does_not_update_default_paper_size(): void {
		// Set up test data
		$label_id    = '123';
		$paper_size  = 'legal';
		$pdf_content = 'mock_pdf_content';

		// Create a request with label_id and paper_size
		$request = $this->create_request( '/wcshipping/v1/label/print', 'GET' );
		$request->set_query_params(
			array(
				'label_id_csv' => $label_id,
				'paper_size'   => $paper_size,
			)
		);

		// Mock the API client response
		$this->api_client
			->expects( $this->once() )
			->method( 'get_labels_print_pdf' )
			->with(
				array(
					'paper_size' => $paper_size,
					'labels'     => array( array( 'label_id' => (int) $label_id ) ),
				)
			)
			->willReturn(
				array(
					'headers' => array( 'content-type' => 'application/pdf' ),
					'body'    => $pdf_content,
				)
			);

		// Verify that settings_store is never called to update the paper size
		$this->settings_store
			->expects( $this->never() )
			->method( 'set_preferred_paper_size' );

		// Make the request
		$response = $this->controller->print_label( $request );

		// Assert the response
		$this->assertEquals(
			array(
				'mimeType'   => 'application/pdf',
				'b64Content' => base64_encode( $pdf_content ),
				'success'    => true,
			),
			$response
		);
	}
}
