<?php

namespace Automattic\WCShipping\Tests\php\LabelPurchase;

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use PHPUnit\Framework\MockObject\MockObject;
use Automattic\WCShipping\LabelPurchase\LabelPurchaseRESTController;
use WP_REST_Request;
use Automattic\WCShipping\LabelPurchase\LabelPurchaseService;

class LabelPurchaseRESTControllerTest extends WCShipping_Test_Case {
	private const SAMPLE_CUSTOMS_REQUEST          = '
	{
			"shipment_0": {
				"field1": "field1 val",
				"field2": "field2 val",
				"field3": "field3 val",
				"items": [
					{
						"hs_code": "123456",
						"quantity": 1,
						"value": 10,
						"weight": 0.5,
						"description": "item description"
					}
				]
			}
		}';
	private const SAMPLE_USER_META_REQUEST        = '
		,"user_meta":
			{"last_order_completed": true }
	';
	private const TEMPLATE_PURCHASE_LABEL_REQUEST = '{
		"origin": {
			"company": "WooCommerce",
			"name": "Sven Sender",
			"country": "US",
			"state": "CA",
			"address": "1600 AMPHITHEATRE PKWY",
			"address_2": "",
			"city": "MOUNTAIN VIEW",
			"postcode": "94043-1351",
			"phone": "1234567890"
		},
		"destination": {
			"company": "BooCommerce",
			"address_2": "",
			"city": "MOUNTAIN VIEW",
			"state": "CA",
			"postcode": "94043-1351",
			"country": "US",
			"name": "Ross Receiver",
			"address": "1600 AMPHITHEATRE PKWY",
			"phone": "9876543210"
		},
		"packages": [
			{
				"id": "default_box",
				"box_id": "medium_flat_box_top",
				"carrier_id": "usps",
				"height": 6,
				"weight": 0.6,
				"width": 8.75,
				"length": 11.25,
				"products": [ 1 ],
				"service_id": "Priority",
				"service_name": "USPS - Priority Mail",
				"shipment_id": "shp_b1478db37fae4bb082ec4c44e3ac1b90",
				"is_letter": false
			}
		],
		"selected_rate": {
			"rate": 10,
			"title": "USPS - Priority Mail",
			"carrier_id": "usps",
			"service_id": "Priority",
			"shipment_id": "shp_b1478db37fae4bb082ec4c44e3ac1b90",
			"is_letter": false,
			"parent": null,
			"type": "signatureRequiredRate"
		},
		"selected_rate_options": {
			"carbon_neutral": {
				"value": true,
				"rate": 10
			},
			"signature": {
				"value": "yes",
				"rate": 11
			}
		},
		"hazmat": {
			"shipment_0": {
				"isHazmat": true,
				"category": "ORM-D"
			}
		},
		"customs": ' . self::SAMPLE_CUSTOMS_REQUEST . '
		<USER_META_PLACEHOLDER>
	}';

	const SAMPLE_LABEL_DATA = array(
		'label_id'                                       => 1,
		'tracking_id'                                    => null,
		'refundable_amount'                              => 0,
		'created'                                        => 1699056000000,
		'carrier_id'                                     => 'usps',
		'service_name'                                   => 'USPS - Priority Mail',
		'status'                                         => 'PURCHASE_IN_PROGRESS',
		'commercial_invoice_url'                         => '',
		'is_commercial_invoice_submitted_electronically' => '',
		'package_name'                                   => 'Individual packaging',
		'is_letter'                                      => false,
	);

	/**
	 * Variable containing sample purchase label request without user meta.
	 *
	 * @var string
	 */
	private $sample_purchase_label_request;

	/**
	 * Variable containing sample purchase label request with user meta.
	 *
	 * @var string
	 */
	private $sample_purchase_label_request_with_user_meta;

	/**
	 * Mock label purchase service
	 *
	 * @var LabelPurchaseService
	 */
	private MockObject $label_purchase_service_mock;

	public function setUp(): void {
		parent::setUp();
		$this->label_purchase_service_mock = $this->getMockBuilder( LabelPurchaseService::class )
			->disableOriginalConstructor()
			->setMethods(
				array(
					'purchase_labels',
					'update_user_meta',
				)
			)
			->getMock();

		// Reinitialize other necessary components
		$label_purchase_controller = new LabelPurchaseRESTController( $this->label_purchase_service_mock );
		add_action(
			'rest_api_init',
			array( $label_purchase_controller, 'register_routes' )
		);
		do_action( 'rest_api_init' );

		$this->set_store_to_be_eligible_for_shipping_label_creation();

		// Get sample data ready.
		$this->sample_purchase_label_request = str_replace(
			'<USER_META_PLACEHOLDER>',
			'',
			self::TEMPLATE_PURCHASE_LABEL_REQUEST
		);

		$this->sample_purchase_label_request_with_user_meta = str_replace(
			'<USER_META_PLACEHOLDER>',
			self::SAMPLE_USER_META_REQUEST,
			self::TEMPLATE_PURCHASE_LABEL_REQUEST
		);
	}

	public function test_purchase_label() {
		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/label/purchase/1' );
		$request->set_header( 'Content-Type', 'application/json' );

		$request->set_body( $this->sample_purchase_label_request );

		$this->label_purchase_service_mock
			->expects( $this->any() )
			->method( 'purchase_labels' )
			->willReturn(
				array(
					'labels'              => array( self::SAMPLE_LABEL_DATA ),
					'customs_information' => json_decode( self::SAMPLE_CUSTOMS_REQUEST ),
					'success'             => true,
				)
			);

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->get_status() );
		$this->assertTrue( $response->get_data()['success'] );
		$this->assertEquals( self::SAMPLE_LABEL_DATA, $response->get_data()['labels'][0] );
		$this->assertEquals( json_decode( self::SAMPLE_CUSTOMS_REQUEST ), $response->get_data()['customs_information'] );
	}

	public function test_purchase_label_invalid_parameters() {
		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/label/purchase/1' );
		$request->set_header( 'Content-Type', 'application/json' );
		$request->set_body( '{}' );

		$response = rest_do_request( $request );

		$this->assertEquals( 500, $response->status );
		$this->assertStringContainsString( 'Required body parameter is missing', $response->get_data()['message'] );
	}

	public function test_purchase_user_meta() {
		$request = new WP_REST_Request( 'POST', '/wcshipping/v1/label/purchase/1' );
		$request->set_header( 'Content-Type', 'application/json' );

		$request->set_body( $this->sample_purchase_label_request_with_user_meta );

		$this->label_purchase_service_mock
			->expects( $this->any() )
			->method( 'purchase_labels' )
			->willReturn(
				array(
					'labels'              => array( self::SAMPLE_LABEL_DATA ),
					'customs_information' => json_decode( self::SAMPLE_CUSTOMS_REQUEST ),
					'success'             => true,
				)
			);

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->get_status() );
		$this->assertEquals( 1, $request->get_json_params()['user_meta']['last_order_completed'] );
	}

	public function test_purchase_label_with_features_supported_by_client() {
		$request = $this->create_request( '/wcshipping/v1/label/purchase/1' );
		$request->set_header( 'Content-Type', 'application/json' );

		$sample_request_with_features                                 = json_decode( $this->sample_purchase_label_request, true );
		$sample_request_with_features['features_supported_by_client'] = array( 'feature1', 'feature2' );
		$request->set_body( json_encode( $sample_request_with_features ) );

		$this->label_purchase_service_mock
			->expects( $this->any() )
			->method( 'purchase_labels' )
			->with(
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->equalTo( array( 'feature1', 'feature2' ) )
			)
			->willReturn(
				array(
					'labels'              => array( self::SAMPLE_LABEL_DATA ),
					'customs_information' => json_decode( self::SAMPLE_CUSTOMS_REQUEST ),
					'success'             => true,
				)
			);

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->get_status() );
		$this->assertTrue( $response->get_data()['success'] );
	}

	/**
	 * Test purchasing a label with shipment_options parameter.
	 */
	public function test_purchase_label_with_shipment_options() {
		$request = $this->create_request( '/wcshipping/v1/label/purchase/1' );
		$request->set_header( 'Content-Type', 'application/json' );

		$sample_request_with_shipment_options                     = json_decode( $this->sample_purchase_label_request, true );
		$sample_request_with_shipment_options['shipment_options'] = array(
			'option1' => 'value1',
			'option2' => 'value2',
		);
		$request->set_body( json_encode( $sample_request_with_shipment_options ) );

		$this->label_purchase_service_mock
			->expects( $this->any() )
			->method( 'purchase_labels' )
			->with(
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->anything(),
				$this->equalTo(
					array(
						'option1' => 'value1',
						'option2' => 'value2',
					)
				)
			)
			->willReturn(
				array(
					'labels'              => array( self::SAMPLE_LABEL_DATA ),
					'customs_information' => json_decode( self::SAMPLE_CUSTOMS_REQUEST ),
					'success'             => true,
				)
			);

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->get_status() );
		$this->assertTrue( $response->get_data()['success'] );
	}

	/**
	 * Test purchasing a label with an invalid label_date in shipment_options.
	 */
	public function test_purchase_label_with_invalid_label_date() {
		$request = $this->create_request( '/wcshipping/v1/label/purchase/1' );
		$request->set_header( 'Content-Type', 'application/json' );

		$sample_request_with_invalid_date                     = json_decode( $this->sample_purchase_label_request, true );
		$sample_request_with_invalid_date['shipment_options'] = array(
			'label_date' => '2023-13-45', // Invalid date format, should be ISO 8601
		);
		$request->set_body( json_encode( $sample_request_with_invalid_date ) );

		// The label_purchase_service_mock should not be called because validation should fail
		$this->label_purchase_service_mock
			->expects( $this->never() )
			->method( 'purchase_labels' );

		$response = rest_do_request( $request );

		// Check response status code
		$this->assertEquals( 400, $response->get_status() );

		// Check error code and message
		$this->assertEquals( 'rest_invalid_param', $response->get_data()['code'] );
		$this->assertEquals( 'Invalid parameter(s): shipment_options', $response->get_data()['message'] );

		// Check data structure
		$this->assertEquals( 400, $response->get_data()['data']['status'] );
		$this->assertArrayHasKey( 'shipment_options', $response->get_data()['data']['params'] );
		$this->assertStringContainsString( 'does not match pattern', $response->get_data()['data']['params']['shipment_options'] );
		$this->assertStringContainsString( 'label_date', $response->get_data()['data']['params']['shipment_options'] );

		// Check details structure
		$this->assertArrayHasKey( 'shipment_options', $response->get_data()['data']['details'] );
		$this->assertEquals( 'rest_invalid_pattern', $response->get_data()['data']['details']['shipment_options']['code'] );
		$this->assertStringContainsString(
			'shipment_options[label_date] does not match pattern',
			$response->get_data()['data']['details']['shipment_options']['message']
		);
	}

	/**
	 * Set up a store to be eligible for shipping label creation.
	 */
	private function set_store_to_be_eligible_for_shipping_label_creation() {
		update_option( 'woocommerce_currency', 'USD' );
		update_option( 'woocommerce_default_country', 'US' );
	}
}
