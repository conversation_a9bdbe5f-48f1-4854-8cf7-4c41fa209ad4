<?php

namespace Automattic\WCShipping\Tests\php\LabelPurchase;

use WC_Unit_Test_Case;
use WC_Helper_Product;
use WC_Helper_Order;
use WC_Product_Simple;
use Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store;
use Automattic\WCShipping\Connect\WC_Connect_Logger;
use Automattic\WCShipping\Connect\WC_Connect_API_Client_Live;
use Automattic\WCShipping\LabelPurchase\View;
use Automattic\WCShipping\LabelPurchase\LabelPurchaseService;
use Automattic\WCShipping\Promo\PromoService;
use Automattic\WCShipping\Fulfillments\FulfillmentsService;
use Automattic\WCShipping\Shipments\ShipmentsService;
use WP_Error;
use WC_Order;
use WC_Order_Item_Product;
use WC_Product;

class LabelPurchaseServiceTest extends WC_Unit_Test_Case {

	/**
	 * System under test.
	 *
	 * @var LabelPurchaseService
	 */
	private $label_purchase_service;

	/**
	 * Mock settings store.
	 *
	 * @var WC_Connect_Service_Settings_Store
	 */
	private $settings_store_mock;

	/**
	 * Mock API client.
	 *
	 * @var WC_Connect_API_Client
	 */
	private $api_client_mock;

	/**
	 * Mock Connect label service/view.
	 *
	 * @var View
	 */
	private $connect_label_service_mock;

	/**
	 * Mock Connect Logger.
	 *
	 * @var WC_Connect_Logger
	 */
	private $logger_mock;

	/**
	 * Mock Promo Service.
	 *
	 * @var PromoService
	 */
	private $promo_service;

	/**
	 * Mock fulfillments service.
	 *
	 * @var FulfillmentsService
	 */
	private $fulfillments_service_mock;

	/**
	 * Reusable shippable simple product.
	 *
	 * @var WC_Product_Simple
	 */
	private $sample_product;

	const SAMPLE_LABEL_DATA = array(
		'label_id'                                       => 1,
		'tracking_id'                                    => null,
		'refundable_amount'                              => 0,
		'created'                                        => 1699056000000,
		'carrier_id'                                     => 'usps',
		'service_name'                                   => 'USPS - Priority Mail',
		'status'                                         => 'PURCHASE_IN_PROGRESS',
		'commercial_invoice_url'                         => '',
		'is_commercial_invoice_submitted_electronically' => '',
		'package_name'                                   => 'Small Flat Rate Box',
		'is_letter'                                      => false,
	);

	const ORIGIN_ADDRESS = array(
		'company'   => 'WooCommerce',
		'name'      => 'Sven Sender',
		'phone'     => 1234567890,
		'country'   => 'US',
		'state'     => 'CA',
		'address'   => '123 Fake Street',
		'address_2' => 'Flat 45',
		'city'      => 'San Francisco',
		'postcode'  => '12345',
	);

	const DESTINATION_ADDRESS = array(
		'company'   => 'BooCommerce',
		'name'      => 'Ross Receiver',
		'phone'     => 9876543210,
		'country'   => 'US',
		'state'     => 'CA',
		'address'   => '543 Fake Street',
		'address_2' => 'Flat 21',
		'city'      => 'San Francisco',
		'postcode'  => '54321',
	);

	const SAMPLE_PACKAGE = array(
		'id'           => 0,
		'box_id'       => 'small_flat_box',
		'carrier_id'   => 'usps',
		'height'       => 6,
		'weight'       => 0.6,
		'width'        => 8.75,
		'length'       => 11.25,
		'service_id'   => 'Priority',
		'service_name' => 'USPS - Priority Mail',
		'shipment_id'  => 'shp_123',
		'is_letter'    => false,
	);

	const SAMPLE_SAVED_TEMPLATE_PACKAGE = array(
		'id'           => 0,
		'box_id'       => '********************************',
		'height'       => 2,
		'width'        => 2,
		'length'       => 2,
		'weight'       => 1,
		'carrier_id'   => 'usps',
		'service_id'   => 'Priority',
		'service_name' => 'USPS - Priority Mail',
		'shipment_id'  => 'shp_123',
		'is_letter'    => false,
	);

	const SAMPLE_CUSTOM_BOX_PACKAGE = array(
		'id'           => 0,
		'box_id'       => 'custom_box',
		'height'       => 3,
		'width'        => 3,
		'length'       => 3,
		'weight'       => 2,
		'carrier_id'   => 'usps',
		'service_id'   => 'Priority',
		'service_name' => 'USPS - Priority Mail',
		'shipment_id'  => 'shp_123',
		'is_letter'    => false,
	);

	const SAMPLE_RATE = array(
		'rate'   => array(
			'carrier_id'   => 'dhl',
			'service_name' => 'dhl sample service',
			'type'         => 'signatureRequired',
		),
		'parent' => null,
	);

	const SAMPLE_HAZMAT = array(
		'shipment_1' => array(
			'isHazmat' => true,
			'category' => 'some-cat',
		),
	);

	const SAMPLE_CUSTOMS = array(
		'shipment_0' => array(
			'field1' => 'field1 value',
			'field2' => 'field2 value',
			'items'  => array(
				array(
					'item1' => 'item1 value',
					'item2' => 'item2 value',
				),
			),
		),
		'shipment_1' => array(
			'field1' => 'field1 value',
			'field2' => 'field2 value',
			'items'  => array(
				array(
					'item1' => 'item1 value',
					'item2' => 'item2 value',
				),
			),
		),
	);

	const SAMPLE_RATE_OPTIONS = array(
		'carbon_neutral'      => true,
		'signature'           => 'adult',
		'additional_handling' => true,
		'saturday_delivery'   => true,
	);

	const SAMPLE_SHIPMENT_OPTIONS = array(
		'label_date' => '2023-11-15',
	);

	/**
	 * Setup prior to running tests, mainly to test functionality from View label service.
	 *
	 * @return void
	 */
	public static function set_up_before_class() {
		require_once __DIR__ . '/../../../classes/class-wc-connect-account-settings.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-package-settings.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-continents.php';
	}

	/**
	 * Set up prior to running tests.
	 *
	 * @return void
	 */
	public function set_up() {
		parent::set_up();

		$this->settings_store_mock = $this->getMockBuilder( WC_Connect_Service_Settings_Store::class )
			->disableOriginalConstructor()
			->setMethods(
				array(
					'get_preferred_paper_size',
					'get_origin_address',
					'get_selected_payment_method_id',
					'get_account_settings',
					'get_package_lookup',
					'add_labels_to_order',
				)
			)
			->getMock();
		$this->settings_store_mock
			->expects( $this->any() )
			->method( 'get_preferred_paper_size' )
			->willReturn( 'letter' );
		$this->settings_store_mock
			->expects( $this->any() )
			->method( 'get_origin_address' )
			->willReturn( self::ORIGIN_ADDRESS );
		$this->settings_store_mock
			->expects( $this->any() )
			->method( 'get_selected_payment_method_id' )
			->willReturn( 'pm_123' );
		$this->settings_store_mock
			->expects( $this->any() )
			->method( 'get_account_settings' )
			->willReturn(
				array(
					'email_receipts' => true,
				)
			);
		$this->settings_store_mock
			->expects( $this->any() )
			->method( 'get_package_lookup' )
			->willReturn(
				array(
					'********************************' => array(
						'id'               => '********************************',
						'name'             => 'My Package',
						'inner_dimensions' => '2 x 2 x 2',
						'is_user_defined'  => 1,
						'box_weight'       => 2,
						'max_weight'       => 0,
					),
					'small_flat_box'                   => array(
						'inner_dimensions'       => '8.63 x 5.38 x 1.63',
						'outer_dimensions'       => '8.63 x 5.38 x 1.63',
						'box_weight'             => 0,
						'is_flat_rate'           => true,
						'id'                     => 'small_flat_box',
						'name'                   => 'Small Flat Rate Box',
						'dimensions'             => '8.63 x 5.38 x 1.63',
						'max_weight'             => 70,
						'is_letter'              => false,
						'group_id'               => 'pri_flat_boxes',
						'can_ship_international' => true,
					),
				)
			);

		$this->api_client_mock = $this->getMockBuilder( WC_Connect_API_Client_Live::class )
			->disableOriginalConstructor()
			->setMethods(
				array(
					'send_shipping_label_request',
				)
			)
			->getMock();

		$this->connect_label_service_mock = $this->getMockBuilder( View::class )
			->disableOriginalConstructor()
			->setMethods( array( 'get_label_payload' ) )
			->getMock();

		$this->logger_mock = $this->getMockBuilder( WC_Connect_Logger::class )
			->disableOriginalConstructor()
			->setMethods( array( 'log' ) )
			->getMock();

		$this->promo_service = $this->getMockBuilder( PromoService::class )
			->disableOriginalConstructor()
			->getMock();

		$this->fulfillments_service_mock = $this->getMockBuilder( FulfillmentsService::class )
			->disableOriginalConstructor()
			->getMock();

		$this->label_purchase_service = new LabelPurchaseService(
			$this->settings_store_mock,
			$this->api_client_mock,
			$this->connect_label_service_mock,
			$this->logger_mock,
			$this->promo_service,
			$this->fulfillments_service_mock
		);

		$product = WC_Helper_Product::create_simple_product();
		$product->set_virtual( false );
		$product->save();
		$this->sample_product = $product;
	}

	/**
	 * Successfully retrieve stored labels for order.
	 */
	public function test_get_stored_labels_for_order() {
		$sample_label_data                  = self::SAMPLE_LABEL_DATA;
		$sample_label_data['product_names'] = array( $this->sample_product->get_name() );
		$sample_label_data['product_ids']   = array( $this->sample_product->get_id() );
		$purchased_labels                   = array( $sample_label_data );

		$order = WC_Helper_Order::create_order( 1, $this->sample_product );
		$order->update_meta_data( 'wcshipping_labels', $purchased_labels );
		$order->save();

		$this->connect_label_service_mock
			->expects( $this->once() )
			->method( 'get_label_payload' )
			->with( $order->get_id() )
			->willReturn(
				array(
					'orderId'            => $order->get_id(),
					'paperSize'          => 'letter',
					'storedData'         => array(
						'is_packed'   => true,
						'packages'    => array(),
						'origin'      => self::ORIGIN_ADDRESS,
						'destination' => self::DESTINATION_ADDRESS,
					),
					'currentOrderLabels' => $purchased_labels,
				)
			);

		$response = $this->label_purchase_service->get_labels( $order->get_id() );
		$this->assertTrue( $response['success'] );
		$this->assertEquals( $purchased_labels, $response['labels'] );
	}

	/**
	 * Failure to retrieve stored data for unknown order.
	 */
	public function test_get_stored_labels_unknown_order() {
		$this->connect_label_service_mock
			->expects( $this->once() )
			->method( 'get_label_payload' )
			->with( 0 )
			->willReturn( array() );

		$response = $this->label_purchase_service->get_labels( 0 );

		$this->assertInstanceOf( WP_Error::class, $response );
	}

	/**
	 * Successfully purchase label from Connect Server.
	 */
	public function test_purchase_label() {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		$sample_package             = self::SAMPLE_PACKAGE;
		$sample_package['products'] = array( $this->sample_product->get_id() );

		$origin      = self::ORIGIN_ADDRESS;
		$destination = self::DESTINATION_ADDRESS;
		$packages    = array( $sample_package );
		$order_id    = $order->get_id();

		$unset_service_name = function ( $package ) {
			unset( $package['service_name'] );
			return $package;
		};

		$label_response               = (object) array( 'label' => (object) self::SAMPLE_LABEL_DATA );
		$purchase_labels_api_response = array(
			'labels' => array( $label_response ),
			'rates'  => array(
				self::SAMPLE_RATE,
			),
		);

		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_shipping_label_request' )
			->with(
				array(
					'async'                        => true,
					'email_receipt'                => true,
					'origin'                       => $origin,
					'destination'                  => $destination,
					'payment_method_id'            => 'pm_123',
					'order_id'                     => $order_id,
					'packages'                     => array_map( $unset_service_name, $packages ),
					'features_supported_by_client' => array(),
					'shipment_options'             => array(
						'label_date' => self::SAMPLE_SHIPMENT_OPTIONS['label_date'],
					),
				)
			)
			->willReturn( (object) $purchase_labels_api_response );

		$this->settings_store_mock
			->expects( $this->once() )
			->method( 'add_labels_to_order' );

		$this->store_existing_rate( $order_id );

		$response = $this->label_purchase_service->purchase_labels(
			$origin,
			$destination,
			$packages,
			$order_id,
			self::SAMPLE_RATE,
			self::SAMPLE_RATE_OPTIONS,
			self::SAMPLE_HAZMAT,
			self::SAMPLE_CUSTOMS,
			array(), // user_meta
			array(), // features_supported_by_client
			self::SAMPLE_SHIPMENT_OPTIONS // shipment_options
		);

		$storedRates = $order->get_meta( LabelPurchaseService::SELECTED_RATES_KEY );
		$this->assertTrue( $response['success'] );
		$this->assertEquals( self::SAMPLE_LABEL_DATA['label_id'], $response['labels'][0]['label_id'] );
		$this->assertEquals( self::SAMPLE_LABEL_DATA['status'], $response['labels'][0]['status'] );
		$this->assertEquals( self::SAMPLE_LABEL_DATA['carrier_id'], $response['labels'][0]['carrier_id'] );
		$this->assertEquals( self::SAMPLE_LABEL_DATA['package_name'], $response['labels'][0]['package_name'] );
		$this->assertEquals( array( $this->sample_product->get_id() ), $response['labels'][0]['product_ids'] );
		$this->assertEquals( 2, count( $storedRates ) );
		$this->assertTrue( array_key_exists( 'shipment_0', $storedRates ) );
		$this->assertTrue( array_key_exists( 'shipment_1', $storedRates ) );
		$this->assertEquals( self::SAMPLE_RATE_OPTIONS, $storedRates['shipment_1']['shipment_options'] );

		$this->assertEquals( 'signatureRequired', $response['selected_rates']['shipment_1']['rate']['type'] );

		$customs_info = $order->get_meta( LabelPurchaseService::CUSTOMS_INFORMATION );
		$this->assertTrue( array_key_exists( 'shipment_0', $customs_info ) );
		$this->assertTrue( array_key_exists( 'shipment_1', $customs_info ) );

		$shipment_dates = $order->get_meta( LabelPurchaseService::SHIPMENT_DATES );
		$this->assertTrue( array_key_exists( 'shipment_1', $shipment_dates ) );
		$this->assertEquals( self::SAMPLE_SHIPMENT_OPTIONS['label_date'], $shipment_dates['shipment_1']['shipping_date'] );
		$this->assertNull( $shipment_dates['shipment_1']['estimated_delivery_date'] );
	}

	/**
	 * Connect server returns error response during label purchase
	 */
	public function test_purchase_label_api_response_error() {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		$sample_package             = self::SAMPLE_PACKAGE;
		$sample_package['products'] = array( $this->sample_product->get_id() );

		$origin      = self::ORIGIN_ADDRESS;
		$destination = self::DESTINATION_ADDRESS;
		$packages    = array( $sample_package );
		$order_id    = $order->get_id();

		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_shipping_label_request' )
			->willReturn( new WP_Error( 501, 'Something went wrong' ) );

		$this->settings_store_mock
			->expects( $this->never() )
			->method( 'add_labels_to_order' );

		$this->logger_mock
			->expects( $this->once() )
			->method( 'log' );

		$response = $this->label_purchase_service->purchase_labels(
			$origin,
			$destination,
			$packages,
			$order_id,
			self::SAMPLE_RATE,
			self::SAMPLE_RATE_OPTIONS,
			self::SAMPLE_HAZMAT,
			self::SAMPLE_CUSTOMS,
			array(), // user_meta
			array(), // features_supported_by_client
			self::SAMPLE_SHIPMENT_OPTIONS // shipment_options
		);

		$this->assertInstanceOf( WP_Error::class, $response );
		$this->assertEquals( 501, $response->get_error_code() );
		$this->assertEquals( 'Something went wrong', $response->get_error_message() );
	}

	/**
	 * Label service (e.g. EasyPost) returns error during label purchase.
	 */
	public function test_purchase_label_error_from_label_service() {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		$sample_package             = self::SAMPLE_PACKAGE;
		$sample_package['products'] = array( $this->sample_product->get_id() );

		$origin      = self::ORIGIN_ADDRESS;
		$destination = self::DESTINATION_ADDRESS;
		$packages    = array( $sample_package );
		$order_id    = $order->get_id();

		$label_response               = (object) array(
			'error' => (object) array(
				'code'    => 500,
				'message' => 'Not so EasyPost',
			),
		);
		$purchase_labels_api_response = array(
			'labels' => array( $label_response ),
			'rates'  => array(
				self::SAMPLE_RATE,
			),
		);

		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_shipping_label_request' )
			->willReturn( (object) $purchase_labels_api_response );

		$this->settings_store_mock
			->expects( $this->never() )
			->method( 'add_labels_to_order' );

		$this->logger_mock
			->expects( $this->once() )
			->method( 'log' );

		$response = $this->label_purchase_service->purchase_labels(
			$origin,
			$destination,
			$packages,
			$order_id,
			self::SAMPLE_RATE,
			self::SAMPLE_RATE_OPTIONS,
			self::SAMPLE_HAZMAT,
			self::SAMPLE_CUSTOMS,
			array(), // user_meta
			array(), // features_supported_by_client
			self::SAMPLE_SHIPMENT_OPTIONS // shipment_options
		);

		$this->assertInstanceOf( WP_Error::class, $response );
		$this->assertEquals( 500, $response->get_error_code() );
		$this->assertEquals( 'Not so EasyPost', $response->get_error_message() );
	}

	public function dataprovider_package_names() {
		return array(
			array( self::SAMPLE_CUSTOM_BOX_PACKAGE, 'Individual packaging' ),
			array( self::SAMPLE_PACKAGE, 'Small Flat Rate Box' ),
			array( self::SAMPLE_SAVED_TEMPLATE_PACKAGE, 'My Package' ),
		);
	}

	/**
	 * * Test that we store expected package name, based on the package type (custom, saved template, pre-defined).
	 *
	 * @dataProvider dataprovider_package_names
	 *
	 * @param array  $sample_package The package data.
	 * @param string $expected_package_name The expected package name.
	 */
	public function test_package_name_values( $sample_package, $expected_package_name ) {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		$sample_package['products'] = array( $this->sample_product->get_id() );

		$origin      = self::ORIGIN_ADDRESS;
		$destination = self::DESTINATION_ADDRESS;
		$packages    = array( $sample_package );
		$order_id    = $order->get_id();

		$unset_service_name = function ( $package ) {
			unset( $package['service_name'] );
			return $package;
		};

		$label_response               = (object) array( 'label' => (object) self::SAMPLE_LABEL_DATA );
		$purchase_labels_api_response = array(
			'labels' => array( $label_response ),
			'rates'  => array(
				self::SAMPLE_RATE,
			),
		);

		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_shipping_label_request' )
			->with(
				array(
					'async'                        => true,
					'email_receipt'                => true,
					'origin'                       => $origin,
					'destination'                  => $destination,
					'payment_method_id'            => 'pm_123',
					'order_id'                     => $order_id,
					'packages'                     => array_map( $unset_service_name, $packages ),
					'features_supported_by_client' => array(),
					'shipment_options'             => array(
						'label_date' => null,
					),
				)
			)
			->willReturn( (object) $purchase_labels_api_response );

		$this->settings_store_mock
			->expects( $this->once() )
			->method( 'add_labels_to_order' );

		$this->store_existing_rate( $order_id );

		$shipment_options_with_null_label_date = array(
			'label_date' => null,
		);

		$response = $this->label_purchase_service->purchase_labels(
			$origin,
			$destination,
			$packages,
			$order_id,
			self::SAMPLE_RATE,
			self::SAMPLE_RATE_OPTIONS,
			self::SAMPLE_HAZMAT,
			self::SAMPLE_CUSTOMS,
			array(), // user_meta
			array(), // features_supported_by_client
			$shipment_options_with_null_label_date // shipment_options with null label_date
		);

		$this->assertEquals( $expected_package_name, $response['labels'][0]['package_name'] );
	}

	private function store_existing_rate( $order_id ) {
		$order = wc_get_order( $order_id );
		$order->update_meta_data(
			LabelPurchaseService::SELECTED_RATES_KEY,
			array(
				'shipment_0' => array(
					'carrier_id'   => 'Existing usps',
					'service_name' => 'Existing USPS - Priority Mail',
				),
			)
		);

		$order->save();
	}

	/**
	 * Test that null features_supported_by_client is handled properly
	 */
	public function test_purchase_label_with_null_features() {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		$sample_package             = self::SAMPLE_PACKAGE;
		$sample_package['products'] = array( $this->sample_product->get_id() );

		$origin      = self::ORIGIN_ADDRESS;
		$destination = self::DESTINATION_ADDRESS;
		$packages    = array( $sample_package );
		$order_id    = $order->get_id();

		$unset_service_name = function ( $package ) {
			unset( $package['service_name'] );
			return $package;
		};

		$label_response               = (object) array( 'label' => (object) self::SAMPLE_LABEL_DATA );
		$purchase_labels_api_response = array(
			'labels' => array( $label_response ),
			'rates'  => array(
				self::SAMPLE_RATE,
			),
		);

		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_shipping_label_request' )
			->with(
				array(
					'async'                        => true,
					'email_receipt'                => true,
					'origin'                       => $origin,
					'destination'                  => $destination,
					'payment_method_id'            => 'pm_123',
					'order_id'                     => $order_id,
					'packages'                     => array_map( $unset_service_name, $packages ),
					'features_supported_by_client' => array(), // Verify empty array is passed when null
					'shipment_options'             => array(
						'label_date' => null,
					),
				)
			)
			->willReturn( (object) $purchase_labels_api_response );

		$this->settings_store_mock
			->expects( $this->once() )
			->method( 'add_labels_to_order' );

		$this->store_existing_rate( $order_id );

		$shipment_options_with_null_label_date = array(
			'label_date' => null,
		);

		$response = $this->label_purchase_service->purchase_labels(
			$origin,
			$destination,
			$packages,
			$order_id,
			self::SAMPLE_RATE,
			self::SAMPLE_RATE_OPTIONS,
			self::SAMPLE_HAZMAT,
			self::SAMPLE_CUSTOMS,
			array(), // user_meta
			null, // Explicitly pass null features_supported_by_client
			$shipment_options_with_null_label_date // shipment_options with null label_date
		);

		$this->assertTrue( $response['success'] );
	}

	/**
	 * Test that product name is retrieved correctly when product doesn't exist
	 */
	public function test_get_labels_meta_with_nonexistent_product() {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		// Use a product ID that definitely doesn't exist
		$nonexistent_product_id = 999999;

		$sample_package             = self::SAMPLE_PACKAGE;
		$sample_package['products'] = array( $nonexistent_product_id );

		$origin      = self::ORIGIN_ADDRESS;
		$destination = self::DESTINATION_ADDRESS;
		$packages    = array( $sample_package );
		$order_id    = $order->get_id();

		$label_response               = (object) array( 'label' => (object) self::SAMPLE_LABEL_DATA );
		$purchase_labels_api_response = array(
			'labels' => array( $label_response ),
			'rates'  => array(
				self::SAMPLE_RATE,
			),
		);

		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_shipping_label_request' )
			->willReturn( (object) $purchase_labels_api_response );

		$response = $this->label_purchase_service->purchase_labels(
			$origin,
			$destination,
			$packages,
			$order_id,
			self::SAMPLE_RATE_OPTIONS,
			9,
			self::SAMPLE_HAZMAT,
			self::SAMPLE_CUSTOMS,
			array(), // user_meta
			array(), // features_supported_by_client
			self::SAMPLE_SHIPMENT_OPTIONS // shipment_options
		);

		$this->assertTrue( $response['success'] );

		$label_meta = $response['labels'][0];
		$this->assertArrayHasKey( 'product_names', $label_meta );
		$this->assertArrayHasKey( 'product_ids', $label_meta );
		$this->assertEquals( $nonexistent_product_id, $label_meta['product_ids'][0] );
	}

	/**
	 * Test error handling in get_labels_meta_from_response for both label-level and individual label object errors
	 */
	public function test_get_labels_meta_from_response_errors() {
		$packages      = array(
			array(
				'box_id'   => 'small_flat_box',
				'products' => array( 1 ),
				'id'       => 0,
			),
		);
		$service_names = array( 'USPS - Priority Mail' );
		$order_id      = 123;

		// Test top level error
		$response_with_label_error = (object) array(
			'labels' => array(
				(object) array(
					'error' => (object) array(
						'code'    => 'label_error',
						'message' => 'Label creation failed',
					),
				),
			),
		);

		$result = $this->call_private_method(
			$this->label_purchase_service,
			'get_labels_meta_from_response',
			array( $response_with_label_error, $packages, $service_names, $order_id )
		);

		$this->assertInstanceOf( WP_Error::class, $result );
		$this->assertEquals( 'label_error', $result->get_error_code() );
		$this->assertEquals( 'Label creation failed', $result->get_error_message() );

		// Test individual label object error
		$response_with_label_object_error = (object) array(
			'labels' => array(
				(object) array(
					'label' => (object) array(
						'error' => 'Individual label error message',
					),
				),
			),
		);

		$result = $this->call_private_method(
			$this->label_purchase_service,
			'get_labels_meta_from_response',
			array( $response_with_label_object_error, $packages, $service_names, $order_id )
		);

		$this->assertInstanceOf( WP_Error::class, $result );
		$this->assertEquals( 'purchase_error', $result->get_error_code() );
		$this->assertEquals( 'Individual label error message', $result->get_error_message() );
	}

	/**
	 * Helper method to call private methods
	 */
	private function call_private_method( $object, $method_name, $parameters = array() ) {
		$reflection = new \ReflectionClass( get_class( $object ) );
		$method     = $reflection->getMethod( $method_name );
		$method->setAccessible( true );
		return $method->invokeArgs( $object, $parameters );
	}

	/**
	 * Test retrieving existing shipments from order meta.
	 */
	public function test_get_shipments_returns_existing_shipments() {
		$order              = WC_Helper_Order::create_order( 1, $this->sample_product );
		$existing_shipments = array(
			array(
				array(
					'id'       => 1,
					'name'     => 'Test Product',
					'quantity' => 1,
				),
			),
		);

		$order->update_meta_data( LabelPurchaseService::ORDER_SHIPMENTS, $existing_shipments );
		$order->save();

		$result = $this->label_purchase_service->get_shipments( $order->get_id() );

		$this->assertEquals( $existing_shipments, $result );
	}

	/**
	 * Test building shipment from order items when no shipments exist.
	 */
	public function test_get_shipments_builds_from_order_items() {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );
		$this->sample_product->set_weight( 1.5 );
		$this->sample_product->set_length( 10 );
		$this->sample_product->set_width( 5 );
		$this->sample_product->set_height( 2 );
		$this->sample_product->save();

		$result = $this->label_purchase_service->get_shipments( $order->get_id() );

		$this->assertIsArray( $result );
		$this->assertCount( 1, $result ); // One shipment
		$this->assertCount( 1, $result[0] ); // One item in shipment

		$line_item = $result[0][0];
		$this->assertArrayHasKey( 'id', $line_item );
		$this->assertArrayHasKey( 'subItems', $line_item );
		$this->assertIsArray( $line_item['subItems'] );
		// The quantity is 4 (from WC_Helper_Order::create_order), so we should have 4 sub-items
		$this->assertCount( 4, $line_item['subItems'] );

		// Check sub-item structure - should be array of strings
		foreach ( $line_item['subItems'] as $index => $subItem ) {
			$this->assertIsString( $subItem );
			$this->assertEquals( $line_item['id'] . '-sub-' . $index, $subItem );
		}
	}

	/**
	 * Test that virtual products are skipped when building shipments.
	 */
	public function test_get_shipments_skips_virtual_products() {
		$virtual_product = WC_Helper_Product::create_simple_product();
		$virtual_product->set_virtual( true );
		$virtual_product->save();

		$order = WC_Helper_Order::create_order( 1, $virtual_product );

		$result = $this->label_purchase_service->get_shipments( $order->get_id() );

		$this->assertIsArray( $result );
		$this->assertCount( 1, $result );
		$this->assertEmpty( $result[0] ); // Should be empty as product was virtual
	}

	/**
	 * Test handling of invalid/deleted products.
	 */
	public function test_get_shipments_handles_invalid_products() {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		// Delete the product to simulate an invalid product
		\wp_delete_post( $this->sample_product->get_id(), true );

		$result = $this->label_purchase_service->get_shipments( $order->get_id() );

		$this->assertIsArray( $result );
		$this->assertCount( 1, $result );
		$this->assertEmpty( $result[0] ); // Should be empty as product was invalid
	}

	/**
	 * Test that multiple products in an order are handled correctly.
	 */
	public function test_get_shipments_handles_multiple_products() {
		// This helper function sets the product qty to 4
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		$product2 = WC_Helper_Product::create_simple_product();
		$product2->set_virtual( false );
		$product2->save();

		$item2 = new \WC_Order_Item_Product();
		$item2->set_product( $product2 );
		$item2->set_quantity( 3 );
		$order->add_item( $item2 );
		$order->save();

		$result = $this->label_purchase_service->get_shipments( $order->get_id() );

		$this->assertIsArray( $result );
		$this->assertCount( 1, $result ); // One shipment
		$this->assertCount( 2, $result[0] ); // Two items in shipment

		// Verify first product structure
		$first_item = $result[0][0];
		$this->assertArrayHasKey( 'id', $first_item );
		$this->assertArrayHasKey( 'subItems', $first_item );
		$this->assertCount( 4, $first_item['subItems'] ); // quantity = 4

		// Verify second product structure
		$second_item = $result[0][1];
		$this->assertArrayHasKey( 'id', $second_item );
		$this->assertArrayHasKey( 'subItems', $second_item );
		$this->assertCount( 3, $second_item['subItems'] ); // quantity = 3
	}


	/**
	 * Test that null label_date in shipment_options is handled properly
	 */
	public function test_purchase_label_with_null_label_date() {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		$sample_package             = self::SAMPLE_PACKAGE;
		$sample_package['products'] = array( $this->sample_product->get_id() );

		$origin      = self::ORIGIN_ADDRESS;
		$destination = self::DESTINATION_ADDRESS;
		$packages    = array( $sample_package );
		$order_id    = $order->get_id();

		$unset_service_name = function ( $package ) {
			unset( $package['service_name'] );
			return $package;
		};

		$label_response               = (object) array( 'label' => (object) self::SAMPLE_LABEL_DATA );
		$purchase_labels_api_response = array(
			'labels' => array( $label_response ),
			'rates'  => array(
				self::SAMPLE_RATE,
			),
		);

		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_shipping_label_request' )
			->with(
				array(
					'async'                        => true,
					'email_receipt'                => true,
					'origin'                       => $origin,
					'destination'                  => $destination,
					'payment_method_id'            => 'pm_123',
					'order_id'                     => $order_id,
					'packages'                     => array_map( $unset_service_name, $packages ),
					'features_supported_by_client' => array(),
					'shipment_options'             => array(
						'label_date' => null,
					),
				)
			)
			->willReturn( (object) $purchase_labels_api_response );

		$this->settings_store_mock
			->expects( $this->once() )
			->method( 'add_labels_to_order' );

		$this->store_existing_rate( $order_id );

		$shipment_options_with_null_label_date = array(
			'label_date' => null,
		);

		$response = $this->label_purchase_service->purchase_labels(
			$origin,
			$destination,
			$packages,
			$order_id,
			self::SAMPLE_RATE,
			self::SAMPLE_RATE_OPTIONS,
			self::SAMPLE_HAZMAT,
			self::SAMPLE_CUSTOMS,
			array(), // user_meta
			array(), // features_supported_by_client
			$shipment_options_with_null_label_date // shipment_options with null label_date
		);

		$this->assertTrue( $response['success'] );

		$shipment_dates = $order->get_meta( LabelPurchaseService::SHIPMENT_DATES );
		$this->assertTrue( array_key_exists( 'shipment_1', $shipment_dates ) );
		$this->assertNull( $shipment_dates['shipment_1']['shipping_date'] );
		$this->assertNull( $shipment_dates['shipment_1']['estimated_delivery_date'] );
	}

	/**
	 * Test that missing shipment_options is handled properly
	 */
	public function test_purchase_label_without_shipment_options() {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		$sample_package             = self::SAMPLE_PACKAGE;
		$sample_package['products'] = array( $this->sample_product->get_id() );

		$origin      = self::ORIGIN_ADDRESS;
		$destination = self::DESTINATION_ADDRESS;
		$packages    = array( $sample_package );
		$order_id    = $order->get_id();

		$unset_service_name = function ( $package ) {
			unset( $package['service_name'] );
			return $package;
		};

		$label_response               = (object) array( 'label' => (object) self::SAMPLE_LABEL_DATA );
		$purchase_labels_api_response = array(
			'labels' => array( $label_response ),
			'rates'  => array(
				self::SAMPLE_RATE,
			),
		);

		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_shipping_label_request' )
			->with(
				array(
					'async'                        => true,
					'email_receipt'                => true,
					'origin'                       => $origin,
					'destination'                  => $destination,
					'payment_method_id'            => 'pm_123',
					'order_id'                     => $order_id,
					'packages'                     => array_map( $unset_service_name, $packages ),
					'features_supported_by_client' => array(),
					'shipment_options'             => array(
						'label_date' => null,
					),
				)
			)
			->willReturn( (object) $purchase_labels_api_response );

		$this->settings_store_mock
			->expects( $this->once() )
			->method( 'add_labels_to_order' );

		$this->store_existing_rate( $order_id );

		// Don't pass shipment_options parameter
		$response = $this->label_purchase_service->purchase_labels(
			$origin,
			$destination,
			$packages,
			$order_id,
			self::SAMPLE_RATE,
			self::SAMPLE_RATE_OPTIONS,
			self::SAMPLE_HAZMAT,
			self::SAMPLE_CUSTOMS,
			array(), // user_meta
			array() // features_supported_by_client
		);

		$this->assertTrue( $response['success'] );

		$shipment_dates = $order->get_meta( LabelPurchaseService::SHIPMENT_DATES );
		$this->assertTrue( array_key_exists( 'shipment_1', $shipment_dates ) );
		$this->assertNull( $shipment_dates['shipment_1']['shipping_date'] );
		$this->assertNull( $shipment_dates['shipment_1']['estimated_delivery_date'] );
	}

	/**
	 * Test that fulfillment is created when purchasing label for order without existing fulfillment
	 */
	public function test_purchase_label_creates_fulfillment_when_order_has_none() {
		// Enable fulfillment API via filter
		add_filter( 'wcshipping_should_use_fulfillment_api', '__return_true' );

		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		$sample_package             = self::SAMPLE_PACKAGE;
		$sample_package['products'] = array( $this->sample_product->get_id() );

		$origin      = self::ORIGIN_ADDRESS;
		$destination = self::DESTINATION_ADDRESS;
		$packages    = array( $sample_package );
		$order_id    = $order->get_id();

		$label_response               = (object) array( 'label' => (object) self::SAMPLE_LABEL_DATA );
		$purchase_labels_api_response = array(
			'labels' => array( $label_response ),
			'rates'  => array(
				self::SAMPLE_RATE,
			),
		);

		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_shipping_label_request' )
			->willReturn( (object) $purchase_labels_api_response );

		$this->settings_store_mock
			->expects( $this->once() )
			->method( 'add_labels_to_order' );

		// Expect that ensure_order_has_fulfillment is called once
		$this->fulfillments_service_mock
			->expects( $this->once() )
			->method( 'ensure_order_has_fulfillment' )
			->with( $order_id );

		$this->store_existing_rate( $order_id );

		$response = $this->label_purchase_service->purchase_labels(
			$origin,
			$destination,
			$packages,
			$order_id,
			self::SAMPLE_RATE,
			self::SAMPLE_RATE_OPTIONS,
			self::SAMPLE_HAZMAT,
			self::SAMPLE_CUSTOMS,
			array(), // user_meta
			array(), // features_supported_by_client
			self::SAMPLE_SHIPMENT_OPTIONS // shipment_options
		);

		$this->assertTrue( $response['success'] );

		// Clean up filter
		remove_filter( 'wcshipping_should_use_fulfillment_api', '__return_true' );
	}

	/**
	 * Test that fulfillment creation is skipped when fulfillment API is disabled
	 */
	public function test_purchase_label_skips_fulfillment_when_api_disabled() {
		// Disable fulfillment API via filter
		add_filter( 'wcshipping_should_use_fulfillment_api', '__return_false' );

		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		$sample_package             = self::SAMPLE_PACKAGE;
		$sample_package['products'] = array( $this->sample_product->get_id() );

		$origin      = self::ORIGIN_ADDRESS;
		$destination = self::DESTINATION_ADDRESS;
		$packages    = array( $sample_package );
		$order_id    = $order->get_id();

		$label_response               = (object) array( 'label' => (object) self::SAMPLE_LABEL_DATA );
		$purchase_labels_api_response = array(
			'labels' => array( $label_response ),
			'rates'  => array(
				self::SAMPLE_RATE,
			),
		);

		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_shipping_label_request' )
			->willReturn( (object) $purchase_labels_api_response );

		$this->settings_store_mock
			->expects( $this->once() )
			->method( 'add_labels_to_order' );

		// Expect that ensure_order_has_fulfillment is NOT called
		$this->fulfillments_service_mock
			->expects( $this->never() )
			->method( 'ensure_order_has_fulfillment' );

		$this->store_existing_rate( $order_id );

		$response = $this->label_purchase_service->purchase_labels(
			$origin,
			$destination,
			$packages,
			$order_id,
			self::SAMPLE_RATE,
			self::SAMPLE_RATE_OPTIONS,
			self::SAMPLE_HAZMAT,
			self::SAMPLE_CUSTOMS,
			array(), // user_meta
			array(), // features_supported_by_client
			self::SAMPLE_SHIPMENT_OPTIONS // shipment_options
		);

		$this->assertTrue( $response['success'] );

		// Clean up filter
		remove_filter( 'wcshipping_should_use_fulfillment_api', '__return_false' );
	}

	/**
	 * Test that shipments are created during label purchase when they don't exist
	 */
	public function test_purchase_label_creates_shipments_when_none_exist() {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		$sample_package             = self::SAMPLE_PACKAGE;
		$sample_package['products'] = array( $this->sample_product->get_id() );

		$origin      = self::ORIGIN_ADDRESS;
		$destination = self::DESTINATION_ADDRESS;
		$packages    = array( $sample_package );
		$order_id    = $order->get_id();

		// Verify order has no shipments initially
		$initial_shipments = $order->get_meta( LabelPurchaseService::ORDER_SHIPMENTS );
		$this->assertEmpty( $initial_shipments );

		$label_response               = (object) array( 'label' => (object) self::SAMPLE_LABEL_DATA );
		$purchase_labels_api_response = array(
			'labels' => array( $label_response ),
			'rates'  => array(
				self::SAMPLE_RATE,
			),
		);

		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_shipping_label_request' )
			->willReturn( (object) $purchase_labels_api_response );

		$this->settings_store_mock
			->expects( $this->once() )
			->method( 'add_labels_to_order' );

		$this->store_existing_rate( $order_id );

		$response = $this->label_purchase_service->purchase_labels(
			$origin,
			$destination,
			$packages,
			$order_id,
			self::SAMPLE_RATE,
			self::SAMPLE_RATE_OPTIONS,
			self::SAMPLE_HAZMAT,
			self::SAMPLE_CUSTOMS,
			array(), // user_meta
			array(), // features_supported_by_client
			self::SAMPLE_SHIPMENT_OPTIONS // shipment_options
		);

		$this->assertTrue( $response['success'] );

		// Verify shipments were created after purchase
		$order->read_meta_data( true ); // Refresh meta data
		$created_shipments = $order->get_meta( LabelPurchaseService::ORDER_SHIPMENTS );
		$this->assertNotEmpty( $created_shipments );
		$this->assertIsArray( $created_shipments );
		$this->assertCount( 1, $created_shipments ); // One shipment

		// Verify shipment structure
		$shipment = $created_shipments[0];
		$this->assertIsArray( $shipment );
		$this->assertCount( 1, $shipment ); // One line item

		$line_item = $shipment[0];
		$this->assertArrayHasKey( 'id', $line_item );
		$this->assertArrayHasKey( 'subItems', $line_item );
		$this->assertCount( 4, $line_item['subItems'] ); // Quantity is 4 from WC_Helper_Order
	}

	/**
	 * Test that existing shipments are not overwritten during label purchase
	 */
	public function test_purchase_label_preserves_existing_shipments() {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		// Set up existing shipments
		$existing_shipments = array(
			array(
				array(
					'id'       => 'existing_item_1',
					'subItems' => array(
						array(
							'id'       => 'existing_item_1-sub-0',
							'parentId' => 'existing_item_1',
						),
						array(
							'id'       => 'existing_item_1-sub-1',
							'parentId' => 'existing_item_1',
						),
					),
				),
			),
		);

		$order->update_meta_data( LabelPurchaseService::ORDER_SHIPMENTS, $existing_shipments );
		$order->save();

		$sample_package             = self::SAMPLE_PACKAGE;
		$sample_package['products'] = array( $this->sample_product->get_id() );

		$origin      = self::ORIGIN_ADDRESS;
		$destination = self::DESTINATION_ADDRESS;
		$packages    = array( $sample_package );
		$order_id    = $order->get_id();

		$label_response               = (object) array( 'label' => (object) self::SAMPLE_LABEL_DATA );
		$purchase_labels_api_response = array(
			'labels' => array( $label_response ),
			'rates'  => array(
				self::SAMPLE_RATE,
			),
		);

		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_shipping_label_request' )
			->willReturn( (object) $purchase_labels_api_response );

		$this->settings_store_mock
			->expects( $this->once() )
			->method( 'add_labels_to_order' );

		$this->store_existing_rate( $order_id );

		$response = $this->label_purchase_service->purchase_labels(
			$origin,
			$destination,
			$packages,
			$order_id,
			self::SAMPLE_RATE,
			self::SAMPLE_RATE_OPTIONS,
			self::SAMPLE_HAZMAT,
			self::SAMPLE_CUSTOMS,
			array(), // user_meta
			array(), // features_supported_by_client
			self::SAMPLE_SHIPMENT_OPTIONS // shipment_options
		);

		$this->assertTrue( $response['success'] );

		// Verify existing shipments were preserved
		$order->read_meta_data( true ); // Refresh meta data
		$final_shipments = $order->get_meta( LabelPurchaseService::ORDER_SHIPMENTS );
		$this->assertEquals( $existing_shipments, $final_shipments );
	}

	/**
	 * Test that ensure_order_has_shipments method creates shipments only when needed
	 */
	public function test_ensure_order_has_shipments_creates_when_missing() {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		// Verify no shipments initially
		$initial_shipments = $order->get_meta( LabelPurchaseService::ORDER_SHIPMENTS );
		$this->assertEmpty( $initial_shipments );

		// Call the private method directly
		$this->call_private_method(
			$this->label_purchase_service,
			'ensure_order_has_shipments',
			array( $order->get_id() )
		);

		// Verify shipments were created
		$order->read_meta_data( true ); // Refresh meta data
		$created_shipments = $order->get_meta( LabelPurchaseService::ORDER_SHIPMENTS );
		$this->assertNotEmpty( $created_shipments );
		$this->assertIsArray( $created_shipments );
		$this->assertCount( 1, $created_shipments );

		$shipment = $created_shipments[0];
		$this->assertIsArray( $shipment );
		$this->assertCount( 1, $shipment ); // One line item
	}

	/**
	 * Test that ensure_order_has_shipments method doesn't overwrite existing shipments
	 */
	public function test_ensure_order_has_shipments_preserves_existing() {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		// Set up existing shipments
		$existing_shipments = array(
			array(
				array(
					'id'       => 'custom_item',
					'subItems' => array(
						array(
							'id'       => 'custom_item-sub-0',
							'parentId' => 'custom_item',
						),
					),
				),
			),
		);

		$order->update_meta_data( LabelPurchaseService::ORDER_SHIPMENTS, $existing_shipments );
		$order->save();

		// Call the private method
		$this->call_private_method(
			$this->label_purchase_service,
			'ensure_order_has_shipments',
			array( $order->get_id() )
		);

		// Verify existing shipments were preserved
		$order->read_meta_data( true ); // Refresh meta data
		$final_shipments = $order->get_meta( LabelPurchaseService::ORDER_SHIPMENTS );
		$this->assertEquals( $existing_shipments, $final_shipments );
	}

	/**
	 * Test that shipments are created correctly for orders with multiple items
	 */
	public function test_purchase_label_creates_shipments_with_multiple_items() {
		$order = WC_Helper_Order::create_order( 1, $this->sample_product );

		// Add a second product
		$product2 = WC_Helper_Product::create_simple_product();
		$product2->set_virtual( false );
		$product2->save();

		$item2 = new \WC_Order_Item_Product();
		$item2->set_product( $product2 );
		$item2->set_quantity( 2 );
		$order->add_item( $item2 );
		$order->save();

		$sample_package             = self::SAMPLE_PACKAGE;
		$sample_package['products'] = array( $this->sample_product->get_id(), $product2->get_id() );

		$origin      = self::ORIGIN_ADDRESS;
		$destination = self::DESTINATION_ADDRESS;
		$packages    = array( $sample_package );
		$order_id    = $order->get_id();

		$label_response               = (object) array( 'label' => (object) self::SAMPLE_LABEL_DATA );
		$purchase_labels_api_response = array(
			'labels' => array( $label_response ),
			'rates'  => array(
				self::SAMPLE_RATE,
			),
		);

		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_shipping_label_request' )
			->willReturn( (object) $purchase_labels_api_response );

		$this->settings_store_mock
			->expects( $this->once() )
			->method( 'add_labels_to_order' );

		$this->store_existing_rate( $order_id );

		$response = $this->label_purchase_service->purchase_labels(
			$origin,
			$destination,
			$packages,
			$order_id,
			self::SAMPLE_RATE,
			self::SAMPLE_RATE_OPTIONS,
			self::SAMPLE_HAZMAT,
			self::SAMPLE_CUSTOMS,
			array(), // user_meta
			array(), // features_supported_by_client
			self::SAMPLE_SHIPMENT_OPTIONS // shipment_options
		);

		$this->assertTrue( $response['success'] );

		// Verify shipments were created with multiple items
		$order->read_meta_data( true ); // Refresh meta data
		$created_shipments = $order->get_meta( LabelPurchaseService::ORDER_SHIPMENTS );
		$this->assertNotEmpty( $created_shipments );
		$this->assertIsArray( $created_shipments );
		$this->assertCount( 1, $created_shipments ); // One shipment

		$shipment = $created_shipments[0];
		$this->assertIsArray( $shipment );
		$this->assertCount( 2, $shipment ); // Two line items

		// Verify first item structure
		$first_item = $shipment[0];
		$this->assertArrayHasKey( 'id', $first_item );
		$this->assertArrayHasKey( 'subItems', $first_item );
		$this->assertCount( 4, $first_item['subItems'] ); // Quantity from helper

		// Verify second item structure
		$second_item = $shipment[1];
		$this->assertArrayHasKey( 'id', $second_item );
		$this->assertArrayHasKey( 'subItems', $second_item );
		$this->assertCount( 2, $second_item['subItems'] ); // Quantity we set
	}

	/**
	 * Test that virtual products are excluded from created shipments during purchase
	 */
	public function test_purchase_label_excludes_virtual_products_from_shipments() {
		// Create a virtual product
		$virtual_product = WC_Helper_Product::create_simple_product();
		$virtual_product->set_virtual( true );
		$virtual_product->save();

		$order = WC_Helper_Order::create_order( 1, $virtual_product );

		$sample_package             = self::SAMPLE_PACKAGE;
		$sample_package['products'] = array( $virtual_product->get_id() );

		$origin      = self::ORIGIN_ADDRESS;
		$destination = self::DESTINATION_ADDRESS;
		$packages    = array( $sample_package );
		$order_id    = $order->get_id();

		$label_response               = (object) array( 'label' => (object) self::SAMPLE_LABEL_DATA );
		$purchase_labels_api_response = array(
			'labels' => array( $label_response ),
			'rates'  => array(
				self::SAMPLE_RATE,
			),
		);

		$this->api_client_mock
			->expects( $this->once() )
			->method( 'send_shipping_label_request' )
			->willReturn( (object) $purchase_labels_api_response );

		$this->settings_store_mock
			->expects( $this->once() )
			->method( 'add_labels_to_order' );

		$this->store_existing_rate( $order_id );

		$response = $this->label_purchase_service->purchase_labels(
			$origin,
			$destination,
			$packages,
			$order_id,
			self::SAMPLE_RATE,
			self::SAMPLE_RATE_OPTIONS,
			self::SAMPLE_HAZMAT,
			self::SAMPLE_CUSTOMS,
			array(), // user_meta
			array(), // features_supported_by_client
			self::SAMPLE_SHIPMENT_OPTIONS // shipment_options
		);

		$this->assertTrue( $response['success'] );

		// Verify shipments were created but are empty (virtual products excluded)
		$order->read_meta_data( true ); // Refresh meta data
		$created_shipments = $order->get_meta( LabelPurchaseService::ORDER_SHIPMENTS );
		$this->assertNotEmpty( $created_shipments );
		$this->assertIsArray( $created_shipments );
		$this->assertCount( 1, $created_shipments ); // One shipment

		$shipment = $created_shipments[0];
		$this->assertIsArray( $shipment );
		$this->assertEmpty( $shipment ); // Empty because virtual product was excluded
	}
}
