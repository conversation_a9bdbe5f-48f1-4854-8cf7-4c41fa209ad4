<?php
use Automattic\WCShipping\LabelPurchase\LabelPreviewRESTController;
use Automattic\WCShipping\LabelPurchase\LabelPrintService;
use Automattic\WCShipping\Connect\WC_Connect_Logger;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;

/**
 * Test cases for LabelPreviewRESTController.
 */
class TestLabelPreviewRESTController extends WCShipping_Test_Case {

	/**
	 * @var LabelPrintService|\PHPUnit\Framework\MockObject\MockObject
	 */
	protected $label_print_service;

	/**
	 * @var WC_Connect_Logger|\PHPUnit\Framework\MockObject\MockObject
	 */
	protected $logger;

	/**
	 * @var LabelPreviewRESTController
	 */
	protected $controller;

	public function setUp(): void {
		parent::setUp();
		$this->label_print_service = $this->createMock( LabelPrintService::class );
		$this->logger              = $this->createMock( WC_Connect_Logger::class );
		$this->controller          = new LabelPreviewRESTController( $this->label_print_service, $this->logger );
	}

	public function test_missing_paper_size() {
		$request = $this->create_request( '/' . $this->controller->get_rest_base(), 'GET' );
		$this->label_print_service->expects( $this->once() )
			->method( 'get_label_preview_content' )
			->with( null )
			->willReturn( new WP_Error( 'rest_missing_param', 'Missing paper size', array( 'status' => WP_Http::BAD_REQUEST ) ) );
		$response = $this->controller->label_preview( $request );

		$this->assertInstanceOf( 'WP_Error', $response );
		$this->assertEquals( 'rest_missing_param', $response->get_error_code() );
		$error_data = $response->get_error_data();
		$this->assertEquals( WP_Http::BAD_REQUEST, $error_data['status'] );
	}

	public function test_empty_paper_size() {
		$request = $this->create_request( '/' . $this->controller->get_rest_base(), 'GET' );
		$request->set_param( 'paper_size', '   ' );
		$this->label_print_service->expects( $this->once() )
			->method( 'get_label_preview_content' )
			->with( '   ' )
			->willReturn( new WP_Error( 'rest_missing_param', 'Missing paper size', array( 'status' => WP_Http::BAD_REQUEST ) ) );
		$response = $this->controller->label_preview( $request );

		$this->assertInstanceOf( 'WP_Error', $response );
		$this->assertEquals( 'rest_missing_param', $response->get_error_code() );
		$error_data = $response->get_error_data();
		$this->assertEquals( WP_Http::BAD_REQUEST, $error_data['status'] );
	}

	public function test_valid_paper_size() {
		$request = $this->create_request( '/' . $this->controller->get_rest_base(), 'GET' );
		$request->set_param( 'paper_size', 'A4' );

		$expectedB64 = base64_encode( 'dummy pdf content' );
		$this->label_print_service->expects( $this->once() )
			->method( 'get_label_preview_content' )
			->with( 'A4' )
			->willReturn( $expectedB64 );

		$response = $this->controller->label_preview( $request );
		$this->assertIsArray( $response );
		$this->assertArrayHasKey( 'mimeType', $response );
		$this->assertArrayHasKey( 'b64Content', $response );
		$this->assertArrayHasKey( 'success', $response );
		$this->assertEquals( 'application/pdf', $response['mimeType'] );
		$this->assertEquals( $expectedB64, $response['b64Content'] );
		$this->assertTrue( $response['success'] );
	}
}
