<?php

namespace Automattic\WCShipping\Tests\php\LabelPurchase;

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\LabelPurchase\EligibilityRESTController;
use Automattic\WCShipping\LabelPurchase\ViewService;
use Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store;
use Automattic\WCShipping\Connect\WC_Connect_Payment_Methods_Store;

use WC_Helper_Order;
use WC_Helper_Product;
use WC_Product_Simple;

class EligibilityRESTControllerTest extends WCShipping_Test_Case {
	/**
	 * @var ViewService|\PHPUnit\Framework\MockObject\MockObject
	 */
	private $view_service;

	/**
	 * @var WC_Connect_Service_Settings_Store|\PHPUnit\Framework\MockObject\MockObject
	 */
	private $settings_store;

	/**
	 * @var EligibilityRESTController
	 */
	private $controller;

	public static function set_up_before_class() {
		parent::set_up_before_class();
		require_once __DIR__ . '/../../../classes/class-wc-connect-service-settings-store.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-payment-methods-store.php';
	}

	function setUp(): void {
		parent::setUp();

		$this->view_service          = $this->createMock( ViewService::class );
		$this->settings_store        = $this->createMock( WC_Connect_Service_Settings_Store::class );
		$this->payment_methods_store = $this->createMock( WC_Connect_Payment_Methods_Store::class );
		$this->controller            = new EligibilityRESTController( $this->view_service, $this->settings_store, $this->payment_methods_store );

		add_action( 'rest_api_init', array( $this->controller, 'register_routes' ) );
		do_action( 'rest_api_init' );
		$this->set_store_to_be_eligible_for_shipping_label_creation();
	}

	public function test_get_eligibility_returns_false_for_invalid_order_id() {
		$request = $this->create_request( '/wcshipping/v1/eligibility/999999', 'GET' );

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->status );
		$this->assertFalse( $response->get_data()['is_eligible'] );
		$this->assertEquals( 'order_id_is_not_valid', $response->get_data()['reason'] );
	}

	public function test_get_eligibility_returns_false_when_shipping_labels_disabled() {
		$product = $this->create_simple_product();
		$order   = WC_Helper_Order::create_order( 1, $product );

		$request = $this->create_request( '/wcshipping/v1/eligibility/' . $order->get_id(), 'GET' );

		$this->settings_store->method( 'get_account_settings' )
			->willReturn( array( 'enabled' => false ) );

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->status );
		$this->assertFalse( $response->get_data()['is_eligible'] );
		$this->assertEquals( 'account_settings_disabled', $response->get_data()['reason'] );
	}

	public function test_get_eligibility_returns_true_when_all_conditions_met() {
		$product = $this->create_simple_product();
		$order   = WC_Helper_Order::create_order( 1, $product );

		$request = $this->create_request( '/wcshipping/v1/eligibility/' . $order->get_id(), 'GET' );

		$this->settings_store->method( 'get_account_settings' )
			->willReturn( array( 'enabled' => true ) );

		$this->view_service->method( 'is_store_eligible_for_shipping_label_creation' )
			->willReturn( true );

		$this->view_service->method( 'is_order_eligible_for_shipping_label_creation' )
			->willReturn( true );

		$this->settings_store->method( 'get_selected_payment_method_id' )
			->willReturn( 'payment_method_1' );

		$this->settings_store->method( 'can_user_manage_payment_methods' )
			->willReturn( true );

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->status );
		$this->assertTrue( $response->get_data()['is_eligible'] );
	}

	public function test_get_eligibility_fails_for_invalid_store_country() {
		$this->settings_store->method( 'get_account_settings' )
			->willReturn( array( 'enabled' => true ) );

		$this->view_service->method( 'is_store_eligible_for_shipping_label_creation' )
			->willReturn( true );

		$product = $this->create_simple_product();
		$order   = WC_Helper_Order::create_order( 1, $product );

		$request = $this->create_request( '/wcshipping/v1/eligibility/' . $order->get_id(), 'GET' );
		$request->set_param( 'can_create_customs_form', false );

		update_option( 'woocommerce_default_country', 'CA' ); // Set store country to Canada

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->status );
		$this->assertFalse( $response->get_data()['is_eligible'] );
		$this->assertEquals( 'store_country_not_supported_when_customs_form_is_not_supported_by_client', $response->get_data()['reason'] );
	}

	public function test_get_eligibility_fails_for_invalid_origin_or_destination_country() {
		$this->settings_store->method( 'get_account_settings' )
			->willReturn( array( 'enabled' => true ) );

		$this->view_service->method( 'is_store_eligible_for_shipping_label_creation' )
			->willReturn( true );

		$this->settings_store->method( 'get_origin_address' )
			->willReturn( array( 'country' => 'CA' ) ); // Set origin country to Canada

		$product = $this->create_simple_product();
		$order   = WC_Helper_Order::create_order( 1, $product );

		$request = $this->create_request( '/wcshipping/v1/eligibility/' . $order->get_id(), 'GET' );
		$request->set_param( 'can_create_customs_form', false );

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->status );
		$this->assertFalse( $response->get_data()['is_eligible'] );
		$this->assertEquals( 'origin_or_destination_country_not_supported_when_customs_form_is_not_supported_by_client', $response->get_data()['reason'] );
	}

	public function test_get_eligibility_fails_when_no_packages_available() {
		$this->settings_store->method( 'get_account_settings' )
			->willReturn( array( 'enabled' => true ) );

		$this->view_service->method( 'is_store_eligible_for_shipping_label_creation' )
			->willReturn( true );

		$product = $this->create_simple_product();
		$order   = WC_Helper_Order::create_order( 1, $product );

		$request = $this->create_request( '/wcshipping/v1/eligibility/' . $order->get_id(), 'GET' );
		$request->set_param( 'can_create_package', false );

		$this->settings_store->method( 'get_packages' )
			->willReturn( array() );
		$this->settings_store->method( 'get_predefined_packages' )
			->willReturn( array() );

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->status );
		$this->assertFalse( $response->get_data()['is_eligible'] );
		$this->assertEquals( 'no_packages_when_client_cannot_create_package', $response->get_data()['reason'] );
	}

	public function test_get_eligibility_fails_when_no_payment_methods_available() {
		$this->settings_store->method( 'get_account_settings' )
			->willReturn( array( 'enabled' => true ) );

		$this->view_service->method( 'is_store_eligible_for_shipping_label_creation' )
			->willReturn( true );

			$this->view_service->method( 'is_order_eligible_for_shipping_label_creation' )
				->willReturn( true );

		$product = $this->create_simple_product();
		$order   = WC_Helper_Order::create_order( 1, $product );

		$request = $this->create_request( '/wcshipping/v1/eligibility/' . $order->get_id(), 'GET' );
		$request->set_param( 'can_create_payment_method', false );

		$this->payment_methods_store->method( 'get_payment_methods' )
			->willReturn( array() );

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->status );
		$this->assertFalse( $response->get_data()['is_eligible'] );
		$this->assertEquals( 'no_payment_methods_and_client_cannot_create_one', $response->get_data()['reason'] );
	}

	public function test_get_eligibility_fails_when_no_selected_payment_method() {
		$this->settings_store->method( 'get_account_settings' )
			->willReturn( array( 'enabled' => true ) );

		$this->view_service->method( 'is_store_eligible_for_shipping_label_creation' )
			->willReturn( true );
			$this->view_service->method( 'is_order_eligible_for_shipping_label_creation' )
				->willReturn( true );
		$product = $this->create_simple_product();
		$order   = WC_Helper_Order::create_order( 1, $product );

		$request = $this->create_request( '/wcshipping/v1/eligibility/' . $order->get_id(), 'GET' );

		$this->settings_store->method( 'get_selected_payment_method_id' )
			->willReturn( null );
		$this->settings_store->method( 'can_user_manage_payment_methods' )
			->willReturn( false );

		$response = rest_do_request( $request );

		$this->assertEquals( 200, $response->status );
		$this->assertFalse( $response->get_data()['is_eligible'] );
		$this->assertEquals( 'no_selected_payment_method_and_user_cannot_manage_payment_methods', $response->get_data()['reason'] );
	}

	// ... other test methods with similar updates ...

	/**
	 * Set up a store to be eligible for shipping label creation.
	 */
	private function set_store_to_be_eligible_for_shipping_label_creation() {
		update_option( 'woocommerce_currency', 'USD' );
		update_option( 'woocommerce_default_country', 'US' );
	}

	/**
	 * A helper to create a simple product that is default to be shippable.
	 *
	 * @param bool $is_shippable Whether the simple product is shippable or not.
	 * @return WC_Product_Simple|null|false
	 */
	private function create_simple_product( $is_shippable = true ) {
		$product = WC_Helper_Product::create_simple_product();
		$product->set_virtual( ! $is_shippable );
		$product->save();
		return $product;
	}
}
