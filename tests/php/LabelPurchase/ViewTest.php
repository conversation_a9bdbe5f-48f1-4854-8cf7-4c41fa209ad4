<?php

namespace Automattic\WCShipping\Tests\php\LabelPurchase;

class TestableView extends \Automattic\WCShipping\LabelPurchase\View {

	// Dummy properties for testing.
	public $dhl_available = true;
	public $origin_country;
	public $destination_country;

	public function __construct() {
		// Create dummy dependencies using basic mocks.
		$dummyApiClient            = \Mockery::mock( \Automattic\WCShipping\Connect\WC_Connect_API_Client::class );
		$dummySettingsStore        = \Mockery::mock( \Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store::class );
		$dummySchemasStore         = \Mockery::mock( \Automattic\WCShipping\Connect\WC_Connect_Service_Schemas_Store::class );
		$dummyPaymentMethodsStore  = \Mockery::mock( \Automattic\WCShipping\Connect\WC_Connect_Payment_Methods_Store::class );
		$dummyShipmentsService     = \Mockery::mock( \Automattic\WCShipping\Shipments\ShipmentsService::class );
		$dummyOriginAddressService = \Mockery::mock( \Automattic\WCShipping\OriginAddresses\OriginAddressService::class );
		$dummyViewService          = \Mockery::mock( \Automattic\WCShipping\LabelPurchase\ViewService::class );
		$dummyCarrierService       = \Mockery::mock( \Automattic\WCShipping\Carrier\CarrierStrategyService::class );
		$dummyAccountSettings      = \Mockery::mock( \Automattic\WCShipping\Connect\WC_Connect_Account_Settings::class );
		$dummyPromoService         = \Mockery::mock( \Automattic\WCShipping\Promo\PromoService::class );

		// For methods called within get_label_payload(), stub out expected return values.
		$dummySettingsStore->shouldReceive( 'get_label_order_meta_data' )->andReturn( array() );
		$dummySettingsStore->shouldReceive( 'get_preferred_paper_size' )->andReturn( 'A4' );
		$dummySettingsStore->shouldReceive( 'get_store_options' )->andReturn( array() );

		// Call parent constructor.
		parent::__construct(
			$dummyApiClient,
			$dummySettingsStore,
			$dummySchemasStore,
			$dummyPaymentMethodsStore,
			$dummyShipmentsService,
			$dummyOriginAddressService,
			$dummyViewService,
			$dummyCarrierService,
			$dummyAccountSettings,
			$dummyPromoService
		);

		// Set default values.
		$this->origin_country      = 'US';
		$this->destination_country = 'US';
	}

	// Override to control DHL availability.
	public function is_dhl_express_available() {
		return $this->dhl_available;
	}

	// Override to return a dummy origin address.
	public function get_origin_address(): array {
		return array(
			'country' => $this->origin_country,
			'address' => '123 Origin St',
		);
	}

	// Override to return a dummy destination address.
	public function get_destination_address( $order ): array {
		return array(
			'country' => $this->destination_country,
			'address' => '456 Destination Ave',
		);
	}

	// Override WC_Connect_Compatibility dependency by bypassing the global and returning a dummy order.
	public function is_order_dhl_express_eligible(): bool {
		// Instead of using global $post and WC_Connect_Compatibility,
		// simply simulate the logic using the overridden origin and destination.
		if ( ! $this->is_dhl_express_available() ) {
			return false;
		}
		$origin      = $this->get_origin_address();
		$destination = $this->get_destination_address( null );
		return $origin['country'] !== $destination['country'];
	}

	public function test_is_order_dhl_express_eligible_different_countries() {
		$view                      = new TestableView();
		$view->dhl_available       = true;
		$view->origin_country      = 'US';
		$view->destination_country = 'CA';
		$this->assertTrue( $view->is_order_dhl_express_eligible() );
	}

	public function test_is_order_dhl_express_eligible_same_country() {
		$view                      = new TestableView();
		$view->dhl_available       = true;
		$view->origin_country      = 'US';
		$view->destination_country = 'US';
		$this->assertFalse( $view->is_order_dhl_express_eligible() );
	}

	public function test_get_label_payload_invalid_order() {
		$view   = new TestableView();
		$result = $view->get_label_payload( 0 );
		$this->assertSame( array(), $result );
	}

	public function test_throw_error_or_show_order_meta_box_invalid_order() {
		$view   = new TestableView();
		$result = $view->throw_error_or_show_order_meta_box( 'not an order' );
		$this->assertInstanceOf( \WP_Error::class, $result );
		$this->assertEquals( 'wcshipping_banner_order_not_found', $result->get_error_code() );
	}

	// New test to check shipments are included properly in meta boxes payload.
	public function test_get_meta_boxes_payload_includes_shipments_data() {
		// Prepare a dummy shipments data to be returned by the shipments service.
		$dummyShipmentsData   = array(
			'shipments'                 => array( array( 'dummy_key' => 'dummy_value' ) ),
			'autogenerated_from_labels' => true,
		);
		$shipmentsServiceMock = \Mockery::mock( \Automattic\WCShipping\Shipments\ShipmentsService::class );
		$shipmentsServiceMock->shouldReceive( 'get_order_shipments_data' )->andReturn( $dummyShipmentsData );

		$view = new TestableView();
		// Inject our mock shipments service.
		$view->shipments_service = $shipmentsServiceMock;

		// Create a dummy order mock.
		$orderMock = $this->getMockBuilder( 'WC_Order' )
			->disableOriginalConstructor()
			->getMock();
		$orderMock->method( 'get_id' )->willReturn( 123 );
		$orderMock->method( 'get_items' )->willReturn( array() );

		$payload = $view->get_meta_boxes_payload( $orderMock, array() );
		$this->assertArrayHasKey( 'shipments', $payload, 'Payload should have shipments key.' );
		$this->assertArrayHasKey( 'shipments_autogenerated_from_labels', $payload, 'Payload should have shipments_autogenerated_from_labels key.' );
		$this->assertEquals( json_encode( (object) $dummyShipmentsData['shipments'] ), $payload['shipments'], 'Shipments JSON should match the dummy data.' );
		$this->assertTrue( $payload['shipments_autogenerated_from_labels'], 'The shipments_autogenerated_from_labels flag should be true.' );
	}

	public function test_get_selected_rates_regular_json() {
		$json = json_encode( $this->wcshipping_packages );

		$shipping_method = array(
			array(
				'wcshipping_packages' => $json,
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$expected = array(
			'weight_0_.' => 'pri',
		);

		$shipping_label = $this->get_shipping_label();
		$actual         = $this->run_private_method( 'get_selected_rates', array( $mock_order ) );
		$this->assertEquals( $actual, $expected );
	}

	public function test_get_selected_rates_unescaped_json() {
		// create a json and ensure that quotes are unescaped
		$json = json_encode( $this->wcshipping_packages );
		$json = str_replace( '"box_id":"\""', '"box_id":"""', $json );

		$shipping_method = array(
			array(
				'wcshipping_packages' => $json,
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$expected = array(
			'weight_0_.' => 'pri',
		);

		$actual = $this->run_private_method( 'get_selected_rates', array( $mock_order ) );
		$this->assertEquals( $actual, $expected );
	}

	public function test_get_selected_rates_serialized_array() {
		$shipping_method = array(
			array(
				'wcshipping_packages' => maybe_serialize( $this->wcshipping_packages ),
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$expected = array(
			'weight_0_.' => 'pri',
		);

		$actual = $this->run_private_method( 'get_selected_rates', array( $mock_order ) );
		$this->assertEquals( $actual, $expected );
	}

	public function test_get_selected_rates_unserialized_array() {
		$shipping_method = array(
			array(
				'wcshipping_packages' => $this->wcshipping_packages,
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$expected = array(
			'weight_0_.' => 'pri',
		);

		$actual = $this->run_private_method( 'get_selected_rates', array( $mock_order ) );
		$this->assertEquals( $actual, $expected );
	}

	public function test_get_selected_packages_regular_json() {
		$json = json_encode( $this->wcshipping_packages );

		$shipping_method = array(
			array(
				'wcshipping_packages' => $json,
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$actual = $this->run_private_method( 'get_selected_packages', array( $mock_order ) );
		$this->assertEquals( $actual, $this->expected_selected_packages );
	}

	public function test_get_selected_packages_unescaped_json() {
		// create a json and ensure that quotes are unescaped
		$json = json_encode( $this->wcshipping_packages );
		$json = str_replace( '"box_id":"\""', '"box_id":"""', $json );

		$shipping_method = array(
			array(
				'wcshipping_packages' => $json,
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$actual = $this->run_private_method( 'get_selected_packages', array( $mock_order ) );
		$this->assertEquals( $actual, $this->expected_selected_packages );
	}

	public function test_get_selected_packages_serialized_array() {
		$shipping_method = array(
			array(
				'wcshipping_packages' => maybe_serialize( $this->wcshipping_packages ),
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$actual = $this->run_private_method( 'get_selected_packages', array( $mock_order ) );
		$this->assertEquals( $actual, $this->expected_selected_packages );
	}

	public function test_get_selected_packages_unserialized_array() {
		$shipping_method = array(
			array(
				'wcshipping_packages' => $this->wcshipping_packages,
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$actual = $this->run_private_method( 'get_selected_packages', array( $mock_order ) );
		$this->assertEquals( $actual, $this->expected_selected_packages );
	}

	// WC 3.1.0 shipping rate meta_data save bug
	// See: https://github.com/Automattic/woocommerce-services/issues/1075
	public function test_get_selected_packages_null_in_array() {
		$shipping_method = array(
			array(
				'wcshipping_packages' => array( null ), // Bug causes data to be `a:1:{i:0;N;}` in database
			),
		);

		$expected = array(
			'default_box' => array(
				'id'     => 'default_box',
				'box_id' => 'not_selected',
				'height' => 0,
				'length' => 0,
				'weight' => 0,
				'width'  => 0,
				'items'  => array(),
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$actual = $this->run_private_method( 'get_selected_packages', array( $mock_order ) );
		$this->assertEquals( $actual, $expected );
	}

	public function test_get_selected_rates_multiple_packages_regular_json() {
		$json = json_encode( $this->wcshipping_packages_multiple );

		$shipping_method = array(
			array(
				'wcshipping_packages' => $json,
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$expected = array(
			'weight_0_.' => 'pri',
			'weight_1_.' => 'pri',
		);

		$actual = $this->run_private_method( 'get_selected_rates', array( $mock_order ) );
		$this->assertEquals( $actual, $expected );
	}

	public function test_get_selected_rates_multiple_packages_unescaped_json() {
		// create a json and ensure that quotes are unescaped
		$json = json_encode( $this->wcshipping_packages_multiple );
		$json = str_replace( '"box_id":"\""', '"box_id":"""', $json );

		$shipping_method = array(
			array(
				'wcshipping_packages' => $json,
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$expected = array(
			'weight_0_.' => 'pri',
			'weight_1_.' => 'pri',
		);

		$actual = $this->run_private_method( 'get_selected_rates', array( $mock_order ) );
		$this->assertEquals( $actual, $expected );
	}

	public function test_get_selected_rates_multiple_packages_serialized_array() {
		$shipping_method = array(
			array(
				'wcshipping_packages' => maybe_serialize( $this->wcshipping_packages_multiple ),
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$expected = array(
			'weight_0_.' => 'pri',
			'weight_1_.' => 'pri',
		);

		$actual = $this->run_private_method( 'get_selected_rates', array( $mock_order ) );

		$this->assertEquals( $actual, $expected );
	}

	public function test_get_selected_rates_multiple_packages_unserialized_array() {
		$shipping_method = array(
			array(
				'wcshipping_packages' => $this->wcshipping_packages_multiple,
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$expected = array(
			'weight_0_.' => 'pri',
			'weight_1_.' => 'pri',
		);

		$actual = $this->run_private_method( 'get_selected_rates', array( $mock_order ) );
		$this->assertEquals( $actual, $expected );
	}

	public function test_get_selected_packages_multiple_packages_regular_json() {
		$json = json_encode( $this->wcshipping_packages_multiple );

		$shipping_method = array(
			array(
				'wcshipping_packages' => $json,
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$actual = $this->run_private_method( 'get_selected_packages', array( $mock_order ) );
		$this->assertEquals( $actual, $this->expected_selected_packages_multiple );
	}

	public function test_get_selected_packages_multiple_packages_unescaped_json() {
		// create a json and ensure that quotes are unescaped
		$json = json_encode( $this->wcshipping_packages_multiple );
		$json = str_replace( '"box_id":"\""', '"box_id":"""', $json );

		$shipping_method = array(
			array(
				'wcshipping_packages' => $json,
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$actual = $this->run_private_method( 'get_selected_packages', array( $mock_order ) );
		$this->assertEquals( $actual, $this->expected_selected_packages_multiple );
	}

	public function test_get_selected_packages_multiple_packages_serialized_array() {
		$shipping_method = array(
			array(
				'wcshipping_packages' => maybe_serialize( $this->wcshipping_packages_multiple ),
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$actual = $this->run_private_method( 'get_selected_packages', array( $mock_order ) );
		$this->assertEquals( $actual, $this->expected_selected_packages_multiple );
	}

	public function test_get_selected_packages_multiple_packages_unserialized_array() {
		$shipping_method = array(
			array(
				'wcshipping_packages' => $this->wcshipping_packages_multiple,
			),
		);

		$mock_order = $this->create_mock_order();
		$mock_order->expects( $this->any() )->method( 'get_shipping_methods' )->will( $this->returnValue( $shipping_method ) );

		$actual = $this->run_private_method( 'get_selected_packages', array( $mock_order ) );
		$this->assertEquals( $actual, $this->expected_selected_packages_multiple );
	}

	/**
	 * Test that get_all_items() returns only order items that have not been refunded.
	 */
	public function test_get_all_items_method_only_retrieves_non_refunded_order_items() {
		// Given.
		$product = $this->create_simple_product( false );

		$order = WC_Helper_Order::create_order(); // Note: this helper method already adds one product with a qty of 4.
		$order->set_status( 'on-hold' );
		$order->add_product( $product, 6 );
		$order->save();

		$order_items       = $order->get_items();
		$refund_line_items = array();
		foreach ( $order_items as $order_item ) {
			$refund_line_items[ $order_item->get_id() ] = array(
				'qty'          => $order_item->get_quantity() - 3,
				'refund_total' => 20,
			);
		}
		$refund_args = array(
			'order_id'   => $order->get_id(),
			'line_items' => $refund_line_items,
		);
		wc_create_refund( $refund_args );

		// When.
		$all_items       = $this->run_private_method( 'get_all_items', array( $order ) );
		$all_items_count = count( $all_items );

		// Then.
		$this->assertEquals( 6, $all_items_count );
	}

	public function test_get_meta_boxes_payload_custom_fulfillment_summary() {
		// Test the custom filter hooks and check if the fulfillment summary is updated.
		add_filter(
			'wcshipping_meta_box_payload',
			function ( $payload ) {
				return $payload['custom_fulfillment_summary'];
			}
		);
		add_filter(
			'wcshipping_fulfillment_summary',
			function ( $default_fulfillment_summary, $order_id, $label_data ) {
				return $order_id . 'hello';
			},
			10,
			3
		);

		$api_client_mock     = $this->getMockBuilder( \WC_Connect_API_Client_Live::class )
			->disableOriginalConstructor()
			->getMock();
		$settings_store_mock = $this->getMockBuilder( \WC_Connect_Service_Settings_Store::class )
			->disableOriginalConstructor()
			->getMock();
		$settings_store_mock->method( 'get_packages' )->willReturn( array() );

		$service_schemas_store_mock = $this->getMockBuilder( \WC_Connect_Service_Schemas_Store::class )
			->disableOriginalConstructor()
			->getMock();

		$payment_methods_store_mock = $this->getMockBuilder( \WC_Connect_Payment_Methods_Store::class )
			->disableOriginalConstructor()
			->getMock();

		$shipments_service_mock = $this->getMockBuilder( \Automattic\WCShipping\Shipments\ShipmentsService::class )
			->disableOriginalConstructor()
			->getMock();

		$origin_address_service_mock = $this->getMockBuilder( \Automattic\WCShipping\OriginAddresses\OriginAddressService::class )
			->disableOriginalConstructor()
			->getMock();

		$view_service_mock = $this->getMockBuilder( \Automattic\WCShipping\LabelPurchase\ViewService::class )
			->disableOriginalConstructor()
			->getMock();
		$view_service_mock->method( 'get_order_data' )->willReturn( array() );

		$carrier_strategy_service_mock = $this->getMockBuilder( \Automattic\WCShipping\Carrier\CarrierStrategyService::class )
			->disableOriginalConstructor()
			->getMock();

		$account_settings_mock = $this->getMockBuilder( \WC_Connect_Account_Settings::class )
			->disableOriginalConstructor()
			->getMock();

		$mock_order = $this->create_mock_order();
		$expected   = $mock_order->get_id() . 'hello';

		$view = new \Automattic\WCShipping\LabelPurchase\View(
			$api_client_mock,
			$settings_store_mock,
			$service_schemas_store_mock,
			$payment_methods_store_mock,
			$shipments_service_mock,
			$origin_address_service_mock,
			$view_service_mock,
			$carrier_strategy_service_mock,
			$account_settings_mock
		);

		$actual = $view->get_meta_boxes_payload( $mock_order, array() );

		$this->assertEquals( $actual, $expected );

		// Clean up
		remove_filter( 'wcshipping_meta_box_payload', function () {} );
		remove_filter( 'wcshipping_fulfillment_summary', function () {}, 10, 3 );
	}

	public function test_sort_and_reindex_label_data() {
		// Create test data with unsorted creation timestamps
		$unsorted_label_data = array(
			array(
				'id'         => 5,
				'created'    => **********, // May 3, 2021
				'carrier_id' => 'usps',
			),
			array(
				'id'         => 2,
				'created'    => **********, // Jan 7, 2021
				'carrier_id' => 'fedex',
			),
			array(
				'id'         => 8,
				'created'    => **********, // Mar 6, 2021
				'carrier_id' => 'dhl',
			),
		);

		// Expected result after sorting and reindexing
		$expected_label_data = array(
			array(
				'id'         => 0,
				'created'    => **********, // Jan 7, 2021 - earliest, should be first
				'carrier_id' => 'fedex',
			),
			array(
				'id'         => 1,
				'created'    => **********, // Mar 6, 2021
				'carrier_id' => 'dhl',
			),
			array(
				'id'         => 2,
				'created'    => **********, // May 3, 2021 - latest, should be last
				'carrier_id' => 'usps',
			),
		);

		$view              = new TestableView();
		$actual_label_data = $this->run_private_method( 'sort_and_reindex_label_data', array( $unsorted_label_data ) );

		$this->assertEquals( $expected_label_data, $actual_label_data );
	}

	/**
	 * A helper to create a simple product.
	 *
	 * @param bool $virtual Whether the simple product is virtual or not.
	 * @return \WC_Product_Simple
	 */
	private function create_simple_product( $virtual = false ): \WC_Product_Simple {
		$product = \WC_Helper_Product::create_simple_product();
		$product->set_virtual( $virtual );
		$product->save();
		return $product;
	}

	/**
	 * A helper to add a shipping label to an order.
	 *
	 * @param \WC_Connect_Service_Settings_Store $settings_store Settings store that coordinates the shipping labels.
	 * @param \WC_Order                          $order The order for the shipping label to be added to.
	 * @param array                             $product_ids A list of product IDs that the shipping label contains.
	 */
	private function add_shipping_label_to_order( $settings_store, $order, $product_ids = array() ) {
		$labels_meta = array(
			array(
				'label_id'    => 17,
				'product_ids' => $product_ids,
			),
		);
		$settings_store->add_labels_to_order( $order->get_id(), $labels_meta );
	}

	private function run_private_method( string $methodName, array $parameters ) {
		$object     = $object ?? $this->get_shipping_label();
		$reflection = new \ReflectionClass( get_class( $object ) );
		$method     = $reflection->getMethod( $methodName );
		$method->setAccessible( true );
		return $method->invokeArgs( $object, $parameters );
	}
}
