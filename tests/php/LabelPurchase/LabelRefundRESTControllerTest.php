<?php
namespace Automattic\WCShipping\Tests\php\LabelPurchase;

use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\LabelPurchase\LabelRefundRESTController;
use Automattic\WCShipping\LabelPurchase\LabelPurchaseService;
use WP_Error;
use WP_REST_Response;

class LabelRefundRESTControllerTest extends WCShipping_Test_Case {

	/**
	 * @var LabelPurchaseService
	 */
	private $purchase_service_mock;

	private const REST_PATH = '/wcshipping/v1/label/refund';

	function setUp(): void {
		parent::setUp();
		$this->purchase_service_mock = $this->getMockBuilder( LabelPurchaseService::class )
			->disableOriginalConstructor()
			->onlyMethods(
				array(
					'refund_label',
					'get_status',
				)
			)
			->getMock();

		$controller = new LabelRefundRESTController( $this->purchase_service_mock );
		add_action(
			'rest_api_init',
			array( $controller, 'register_routes' )
		);

		do_action( 'rest_api_init' );
	}

	private function get_api_path( $order_id, $label_id ): string {
		return trailingslashit( self::REST_PATH ) . $order_id . '/' . $label_id;
	}

	public function test_post_returns_error_response_when_request_params_are_invalid() {
		$request = $this->create_request( $this->get_api_path( 1, 'abc' ) );
		$this->purchase_service_mock
			->expects( $this->never() )
			->method( 'refund_label' )
			->willReturn( 'should-never-be-called' );

		$this->purchase_service_mock
			->expects( $this->never() )
			->method( 'get_status' )
			->willReturn( 'should-never-be-called' );

		$response = rest_do_request( $request );
		$this->assertInstanceOf( WP_REST_Response::class, $response );
		$this->assertEquals( 404, $response->get_status() );
	}

	public function test_request_returns_error_response_when_refund_fails() {
		$request = $this->create_request( $this->get_api_path( 444, 333 ) );

		$this->purchase_service_mock
			->expects( $this->once() )
			->method( 'refund_label' )
			->with( 444, 333 )
			->willReturn( new WP_Error( 'refund_failed', 'Refund failed' ) );

		$this->purchase_service_mock
			->expects( $this->never() )
			->method( 'get_status' )
			->with( 333 )
			->willReturn( 'should-never-be-called' );

		$response = rest_do_request( $request );
		$this->assertInstanceOf( WP_REST_Response::class, $response );
		$this->assertEquals( 500, $response->get_status() );
	}

	public function test_request_returns_success_response_when_refund_succeeds() {
		$request = $this->create_request( $this->get_api_path( 123, 456 ) );

		$this->purchase_service_mock
			->expects( $this->once() )
			->method( 'refund_label' )
			->with( 123, 456 )
			->willReturn( (object) array( 'refund' => 10 ) );

		$label = array(
			'label_id' => 123,
		);

		$this->purchase_service_mock
			->expects( $this->once() )
			->method( 'get_status' )
			->with( 456 )
			->willReturn(
				(object) array(
					'label' => $label,
				)
			);

		$response = rest_do_request( $request );

		$this->assertInstanceOf( WP_REST_Response::class, $response );
		$this->assertEquals( 200, $response->get_status() );
		$this->assertEquals(
			array(
				'success' => true,
				'refund'  => 10,
				'label'   => $label,
			),
			$response->get_data()
		);
	}

	public function test_request_returns_error_response_when_getting_status_fails() {
		$request = $this->create_request( $this->get_api_path( 444, 333 ) );

		$this->purchase_service_mock
			->expects( $this->once() )
			->method( 'refund_label' )
			->with( 444, 333 )
			->willReturn( (object) array( 'refund' => 10 ) );

		$this->purchase_service_mock
			->expects( $this->once() )
			->method( 'get_status' )
			->with( 333 )
			->willReturn( new WP_Error( 'status_failed', 'Fetching status failed' ) );

		$response = rest_do_request( $request );
		$this->assertInstanceOf( WP_REST_Response::class, $response );
		$this->assertEquals( 500, $response->get_status() );
		$this->assertEquals( 'Fetching status failed', $response->get_data()['message'] );
	}
}
