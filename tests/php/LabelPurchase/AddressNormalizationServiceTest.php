<?php
/**
 * Class AddressNormalizationServiceTest
 * @package Automattic\WCShipping
 */

namespace Automattic\WCShipping\Tests\LabelPurchase;

use Automattic\WCShipping\LabelPurchase\AddressNormalizationService;
use Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store;
use Automattic\WCShipping\Connect\WC_Connect_API_Client;
use Automattic\WCShipping\Connect\WC_Connect_Logger;
use Automattic\WCShipping\OriginAddresses\OriginAddressService;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use WC_Helper_Order;
use WC_Order;

/**
 * Tests for AddressNormalizationService.
 */
class AddressNormalizationServiceTest extends WCShipping_Test_Case {
	/**
	 * Mocked Settings Store.
	 * @var WC_Connect_Service_Settings_Store&\PHPUnit\Framework\MockObject\MockObject
	 */
	protected $mocked_settings_store;

	/**
	 * Mocked API Client.
	 * @var WC_Connect_API_Client&\PHPUnit\Framework\MockObject\MockObject
	 */
	protected $mocked_api_client;

	/**
	 * Mocked Logger.
	 * @var WC_Connect_Logger&\PHPUnit\Framework\MockObject\MockObject
	 */
	protected $mocked_logger;

	/**
	 * Mocked Origin Address Service.
	 * @var OriginAddressService&\PHPUnit\Framework\MockObject\MockObject
	 */
	protected $mocked_origin_address_service;

	/**
	 * Address Normalization Service instance.
	 * @var AddressNormalizationService
	 */
	protected $address_normalization_service;

	/**
	 * Default address for tests.
	 *
	 * @var array
	 */
	private $default_address = array(
		'address_1' => '123 Main St',
		'address_2' => '',
		'city'      => 'Anytown',
		'state'     => 'CA',
		'postcode'  => '12345',
		'country'   => 'US',
	);

	/**
	 * Pre-calculated hash for the default address.
	 *
	 * @var string
	 */
	private $default_address_hash = '441897db42eab70d43fa9c67a283ef07';

	public function setUp(): void {
		parent::setUp();
		$this->mocked_settings_store         = $this->createMock( WC_Connect_Service_Settings_Store::class );
		$this->mocked_api_client             = $this->createMock( WC_Connect_API_Client::class );
		$this->mocked_logger                 = $this->createMock( WC_Connect_Logger::class );
		$this->mocked_origin_address_service = $this->createMock( OriginAddressService::class );
		$this->address_normalization_service = new AddressNormalizationService(
			$this->mocked_settings_store,
			$this->mocked_api_client,
			$this->mocked_logger,
			$this->mocked_origin_address_service
		);
	}

	public function test_is_destination_address_normalized_returns_true_when_hash_matches() {
		$order = $this->create_order_with_shipping_address();
		$order->update_meta_data( AddressNormalizationService::DESTINATION_NORMALIZED_HASH_KEY, $this->default_address_hash );
		$order->save();

		$this->assertTrue( $this->address_normalization_service->is_destination_address_normalized( $order->get_id() ) );
	}

	public function test_is_destination_address_normalized_returns_false_when_hash_mismatches() {
		$order = $this->create_order_with_shipping_address();
		$order->update_meta_data( AddressNormalizationService::DESTINATION_NORMALIZED_HASH_KEY, 'different-hash' );
		$order->save();

		$this->assertFalse( $this->address_normalization_service->is_destination_address_normalized( $order->get_id() ) );
	}

	public function test_set_is_destination_address_normalized_updates_meta_when_verified() {
		$order = $this->create_order_with_shipping_address();
		$this->address_normalization_service->set_is_destination_address_normalized( $order->get_id(), true );

		$updated_order = wc_get_order( $order->get_id() );
		$this->assertEquals( $this->default_address_hash, $updated_order->get_meta( AddressNormalizationService::DESTINATION_NORMALIZED_HASH_KEY, true ) );
	}

	public function test_set_is_destination_address_normalized_deletes_meta_when_not_verified() {
		$order = $this->create_order_with_shipping_address();
		$order->update_meta_data( AddressNormalizationService::DESTINATION_NORMALIZED_HASH_KEY, 'some-hash' );
		$order->save();

		$this->address_normalization_service->set_is_destination_address_normalized( $order->get_id(), false );

		$updated_order = wc_get_order( $order->get_id() );
		$this->assertEmpty( $updated_order->get_meta( AddressNormalizationService::DESTINATION_NORMALIZED_HASH_KEY, true ) );
	}

	/**
	 * Helper to create an order with a shipping address.
	 *
	 * @param array $address_overrides Address fields to override.
	 * @return WC_Order
	 */
	private function create_order_with_shipping_address( array $address_overrides = array() ): WC_Order {
		$address = array_merge( $this->default_address, $address_overrides );
		$order   = WC_Helper_Order::create_order();
		$order->set_address( $address, 'shipping' );
		$order->save();
		return $order;
	}
}
