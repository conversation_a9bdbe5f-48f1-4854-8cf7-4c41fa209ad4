<?php

use Automattic\WCShipping\LabelPurchase\ViewService;
use Automattic\WCShipping\Tests\php\WCShipping_Test_Case;
use Automattic\WCShipping\Connect\WC_Connect_Account_Settings;
use Automattic\WCShipping\Connect\WC_Connect_Service_Settings_Store;
use Automattic\WCShipping\Connect\WC_Connect_Payment_Methods_Store;
use Automattic\WCShipping\Connect\WC_Connect_Service_Schemas_Store;
use Automattic\WCShipping\Connect\WC_Connect_API_Client_Live;
use Automattic\WCShipping\Connect\WC_Connect_Logger;
use Automattic\WCShipping\Connect\WC_Connect_Service_Schemas_Validator;
class ViewServiceTest extends WCShipping_Test_Case {

	/**
	 * @var WC_Connect_Service_Schemas_Store
	 */
	private $service_schemas_store;

	/**
	 * @var WC_Connect_Account_Settings
	 */
	private $account_settings;

	function setUp(): void {
		parent::setUp();
		require_once __DIR__ . '/../../../classes/class-wc-connect-account-settings.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-service-settings-store.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-payment-methods-store.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-service-schemas-store.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-api-client-live.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-logger.php';
		require_once __DIR__ . '/../../../classes/class-wc-connect-service-schemas-validator.php';

		$validator  = new WC_Connect_Service_Schemas_Validator();
		$api_client = $this->getMockBuilder( WC_Connect_API_Client_Live::class )
			->disableOriginalConstructor()
			->getMock();

		$this->service_schemas_store = $this->getMockBuilder( WC_Connect_Service_Schemas_Store::class )
			->disableOriginalConstructor()
			->getMock();
		$logger                      = $this->getMockBuilder( WC_Connect_Logger::class )
			->disableOriginalConstructor()
			->getMock();
		$settings_store              = new WC_Connect_Service_Settings_Store( $this->service_schemas_store, $api_client, $logger );
		$payment_methods_store       = new WC_Connect_Payment_Methods_Store( $settings_store, $api_client, $logger );
		$this->account_settings      = new WC_Connect_Account_Settings( $settings_store, $payment_methods_store );
	}

	public function test_removes_all_meta_for_purchase_error_status(): void {
		$purchased_labels = array(
			array(
				'id'     => '0',
				'status' => 'PURCHASE_ERROR',
			),
			array(
				'id'     => '1',
				'status' => 'ANY',
			),
			array(
				'id'     => '2',
				'status' => 'PURCHASED',
			),
		);
		$shipment_meta    = array(
			'shipment_0' => 'meta_1',
			'shipment_1' => 'meta_2',
			'shipment_2' => 'meta_3',
		);

		$viewService = new ViewService( $this->account_settings, $this->service_schemas_store );
		$result      = $viewService->remove_meta_for_purchase_error( $purchased_labels, $shipment_meta );

		$this->assertEquals(
			array(
				'shipment_1' => 'meta_2',
				'shipment_2' => 'meta_3',
			),
			$result,
			'Expected to remove all meta for purchase error status'
		);

		$purchased_labels = array(
			array(
				'id'     => '0',
				'status' => 'PURCHASED',
			),
			array(
				'id'     => '1',
				'status' => 'ANY',
			),
			array(
				'id'     => '2',
				'status' => 'PURCHASED',
			),
		);
		$shipment_meta    = array(
			'shipment_0' => 'meta_1',
			'shipment_1' => 'meta_2',
			'shipment_2' => 'meta_3',
		);

		$viewService = new ViewService( $this->account_settings, $this->service_schemas_store );
		$result      = $viewService->remove_meta_for_purchase_error( $purchased_labels, $shipment_meta );

		$this->assertEquals(
			$shipment_meta,
			$result,
			'Expected to keep the meta for all shipments when no purchase error status is found'
		);
	}

	/**
	 * Test: Remove all meta for refunded labels.
	 *
	 * @return void
	 */
	public function test_removes_all_meta_for_refunded_labels(): void {
		$purchased_labels = array(
			array( 'id' => '0' ),
			array(
				'id'     => '1',
				'refund' => ( new stdClass() ),
			),
			array(
				'id'     => '2',
				'refund' => ( new stdClass() ),
			),
			array( 'id' => '3' ),
		);
		$shipment_meta    = array(
			'shipment_0' => array(),
			'shipment_1' => array(),
			'shipment_2' => array(),
			'shipment_3' => array(),
		);

		$viewService    = new ViewService( $this->account_settings, $this->service_schemas_store );
		$selected_rates = $viewService->remove_meta_for_refunds( $purchased_labels, $shipment_meta );

		$this->assertEquals(
			array(
				'shipment_0' => array(),
				'shipment_3' => array(),
			),
			$selected_rates,
			'Expected to remove all meta for all refunded labels'
		);
	}

	/**
	 * Test: Keep meta for refunded labels when there is a non-refunded label with the same ID.
	 *
	 * @return void
	 */
	public function test_keeps_meta_for_refunded_labels_with_active_label(): void {
		$purchased_labels = array(
			array(
				'id'     => '1',
				'refund' => ( new stdClass() ),
			),
			array(
				'id' => '1', // Same ID as the refunded label
			),
			array(
				'id'     => '2',
				'refund' => ( new stdClass() ),
			),
		);
		$shipment_meta    = array(
			'shipment_1' => array( 'some' => 'data' ),
			'shipment_2' => array( 'other' => 'data' ),
		);

		$viewService = new ViewService( $this->account_settings, $this->service_schemas_store );
		$result      = $viewService->remove_meta_for_refunds( $purchased_labels, $shipment_meta );

		$this->assertEquals(
			array(
				'shipment_1' => array( 'some' => 'data' ), // Should be kept because there's an active label with ID 1
			),
			$result,
			'Expected to keep meta for refunded label that has an active label with same ID'
		);
	}

	/**
	 * Test: Remove meta for refunded labels when all labels with same ID are refunded.
	 *
	 * @return void
	 */
	public function test_removes_meta_when_all_labels_with_same_id_are_refunded(): void {
		$purchased_labels = array(
			array(
				'id'     => '1',
				'refund' => ( new stdClass() ),
			),
			array(
				'id'     => '1',
				'refund' => ( new stdClass() ), // Another refunded label with same ID
			),
			array(
				'id' => '2',
			),
		);
		$shipment_meta    = array(
			'shipment_1' => array( 'some' => 'data' ),
			'shipment_2' => array( 'other' => 'data' ),
		);

		$viewService = new ViewService( $this->account_settings, $this->service_schemas_store );
		$result      = $viewService->remove_meta_for_refunds( $purchased_labels, $shipment_meta );

		$this->assertEquals(
			array(
				'shipment_2' => array( 'other' => 'data' ),
			),
			$result,
			'Expected to remove meta when all labels with same ID are refunded'
		);
	}

	/**
	 * Test: Handle empty refund objects and missing shipment meta.
	 *
	 * @return void
	 */
	public function test_handles_edge_cases_for_refunded_labels(): void {
		$purchased_labels = array(
			array(
				'id'     => '1',
				'refund' => null, // Empty refund
			),
			array(
				'id'     => '2',
				'refund' => ( new stdClass() ),
			),
			array(
				'id' => '3', // Missing refund key
			),
		);
		$shipment_meta    = array(
			'shipment_1' => array( 'data' => 'one' ),
			// shipment_2 intentionally missing
			'shipment_3' => array( 'data' => 'three' ),
		);

		$viewService = new ViewService( $this->account_settings, $this->service_schemas_store );
		$result      = $viewService->remove_meta_for_refunds( $purchased_labels, $shipment_meta );

		$this->assertEquals(
			array(
				'shipment_1' => array( 'data' => 'one' ),
				'shipment_3' => array( 'data' => 'three' ),
			),
			$result,
			'Expected to handle edge cases gracefully'
		);
	}

	public function test_order_with_a_virtual_product_is_not_eligible_for_shipping_label_creation() {
		// Given.
		$product = $this->create_simple_product( true );
		$order   = WC_Helper_Order::create_order( 1, $product );

		// When.
		$view_service = new ViewService( $this->account_settings, $this->service_schemas_store );
		$is_eligible  = $view_service->is_order_eligible_for_shipping_label_creation( $order );

		// Then.
		$this->assertFalse( $is_eligible );
	}

	public function test_order_with_a_shippable_product_is_eligible_for_shipping_label_creation() {
		// Given.
		$product = $this->create_simple_product( false );
		$order   = WC_Helper_Order::create_order( 1, $product );

		// When.
		$view_service = new ViewService( $this->account_settings, $this->service_schemas_store );
		$is_eligible  = $view_service->is_order_eligible_for_shipping_label_creation( $order );

		// Then.
		$this->assertTrue( $is_eligible );
	}

	public function test_order_with_a_fully_refunded_shippable_product_is_not_eligible_for_shipping_label_creation() {
		// Given.
		$product = $this->create_simple_product( false );
		$order   = WC_Helper_Order::create_order( 1, $product );

		$order_items       = $order->get_items();
		$refund_line_items = array();
		foreach ( $order_items as $order_item ) {
			$refund_line_items[ $order_item->get_id() ] = array(
				'qty'          => $order_item->get_quantity(),
				'refund_total' => 20,
			);
		}
		$refund_args = array(
			'order_id'   => $order->get_id(),
			'line_items' => $refund_line_items,
		);
		wc_create_refund( $refund_args );

		// When.
		$view_service = new ViewService( $this->account_settings, $this->service_schemas_store );
		$is_eligible  = $view_service->is_order_eligible_for_shipping_label_creation( $order );

		// Then.
		$this->assertFalse( $is_eligible );
	}

	public function test_order_with_a_partially_refunded_shippable_product_is_eligible_for_shipping_label_creation() {
		// Given.
		$product = $this->create_simple_product( false );
		$order   = WC_Helper_Order::create_order( 1, $product );

		$order_items       = $order->get_items();
		$refund_line_items = array();
		foreach ( $order_items as $order_item ) {
			$refund_line_items[ $order_item->get_id() ] = array(
				'qty'          => $order_item->get_quantity() - 1,
				'refund_total' => 20,
			);
		}
		$refund_args = array(
			'order_id'   => $order->get_id(),
			'line_items' => $refund_line_items,
		);
		wc_create_refund( $refund_args );

		// When.
		$view_service = new ViewService( $this->account_settings, $this->service_schemas_store );
		$is_eligible  = $view_service->is_order_eligible_for_shipping_label_creation( $order );

		// Then.
		$this->assertTrue( $is_eligible );
	}

	public function test_keeps_meta_for_purchase_error_with_active_label(): void {
		$purchased_labels = array(
			array(
				'id'     => '1',
				'status' => 'PURCHASE_ERROR',
			),
			array(
				'id'     => '1', // Same ID as the error label
				'status' => 'PURCHASED',
			),
			array(
				'id'     => '2',
				'status' => 'PURCHASE_ERROR',
			),
		);
		$shipment_meta    = array(
			'shipment_1' => array( 'some' => 'data' ),
			'shipment_2' => array( 'other' => 'data' ),
		);

		$viewService = new ViewService( $this->account_settings, $this->service_schemas_store );
		$result      = $viewService->remove_meta_for_purchase_error( $purchased_labels, $shipment_meta );

		$this->assertEquals(
			array(
				'shipment_1' => array( 'some' => 'data' ), // Should be kept because there's a purchased label with ID 1
			),
			$result,
			'Expected to keep meta for error label that has a purchased label with same ID'
		);
	}

	public function test_removes_meta_when_all_labels_with_same_id_have_errors(): void {
		$purchased_labels = array(
			array(
				'id'     => '1',
				'status' => 'PURCHASE_ERROR',
			),
			array(
				'id'     => '1', // Another error label with same ID
				'status' => 'PURCHASE_ERROR',
			),
			array(
				'id'     => '2',
				'status' => 'PURCHASED',
			),
		);
		$shipment_meta    = array(
			'shipment_1' => array( 'some' => 'data' ),
			'shipment_2' => array( 'other' => 'data' ),
		);

		$viewService = new ViewService( $this->account_settings, $this->service_schemas_store );
		$result      = $viewService->remove_meta_for_purchase_error( $purchased_labels, $shipment_meta );

		$this->assertEquals(
			array(
				'shipment_2' => array( 'other' => 'data' ),
			),
			$result,
			'Expected to remove meta when all labels with same ID have purchase errors'
		);
	}

	/**
	 * A helper to create a simple product.
	 *
	 * @param bool $virtual Whether the simple product is virtual or not.
	 * @return WC_Product_Simple
	 */
	private function create_simple_product( $virtual = false ): WC_Product_Simple {
		$product = WC_Helper_Product::create_simple_product();
		$product->set_virtual( $virtual );
		$product->save();
		return $product;
	}
}
