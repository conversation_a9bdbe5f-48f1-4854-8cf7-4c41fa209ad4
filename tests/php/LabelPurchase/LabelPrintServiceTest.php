<?php

namespace Automattic\WCShipping\Tests\php\LabelPurchase;

use WC_Unit_Test_Case;
use WC_Helper_Product;
use WC_Helper_Order;
use Automattic\WCShipping\Connect\WC_Connect_Logger;
use Automattic\WCShipping\Connect\WC_Connect_API_Client_Live;
use Automattic\WCShipping\LabelPurchase\LabelPrintService;
use Automattic\WCShipping\LabelPurchase\LabelPurchaseService;
use WP_Error;

class LabelPrintServiceTest extends WC_Unit_Test_Case {

	public function set_up() {
		parent::set_up();
		$this->api_client_mock = $this->getMockBuilder( WC_Connect_API_Client_Live::class )
			->disableOriginalConstructor()
			->getMock();

		$this->connect_logger_mock         = $this->createMock( WC_Connect_Logger::class );
		$this->label_purchase_service_mock = $this->createMock( LabelPurchaseService::class );
	}

	public function test_get_label_preview_content() {
		$pdfInBytes = 'binary:pdf_bytes_xyz123123';
		$this->api_client_mock->expects( $this->once() )
			->method( 'get_labels_preview_pdf' )
			->with(
				array(
					'paper_size' => 'a4',
					'carrier'    => 'usps',
					'labels'     => array(
						array( 'caption' => 'Test label 1' ),
						array( 'caption' => 'Test label 2' ),
					),
				)
			)
			->willReturn(
				array(
					'body' => $pdfInBytes,
				)
			);

		$label_print_service = new LabelPrintService( $this->api_client_mock, $this->connect_logger_mock, $this->label_purchase_service_mock );
		$actual              = $label_print_service->get_label_preview_content( 'a4' );

		$this->assertEquals( base64_encode( $pdfInBytes ), $actual );
	}

	public function test_get_label_preview_content_500_error() {
		$expectedError = new WP_Error(
			500,
			'Test label somehow failed to print, something is wrong.',
			array( 'message' => 'Test label somehow failed to print, something is wrong.' )
		);

		$this->api_client_mock->expects( $this->once() )
			->method( 'get_labels_preview_pdf' )
			->with(
				array(
					'paper_size' => 'a4',
					'carrier'    => 'usps',
					'labels'     => array(
						array( 'caption' => 'Test label 1' ),
						array( 'caption' => 'Test label 2' ),
					),
				)
			)
			->willReturn( $expectedError );

		$this->connect_logger_mock->expects( $this->once() )
			->method( 'log' )
			->with( $expectedError, LabelPrintService::class );

		$label_print_service = new LabelPrintService( $this->api_client_mock, $this->connect_logger_mock, $this->label_purchase_service_mock );
		$actual              = $label_print_service->get_label_preview_content( 'a4' );

		$this->assertEquals( $expectedError, $actual );
	}

	/**
	 * Test get_packing_list returns error when order doesn't exist.
	 */
	public function test_get_packing_list_order_not_found() {
		$label_print_service = new LabelPrintService( $this->api_client_mock, $this->connect_logger_mock, $this->label_purchase_service_mock );

		$result = $label_print_service->get_packing_list( 999999, 123 );

		$this->assertInstanceOf( WP_Error::class, $result );
		$this->assertEquals( 401, $result->get_error_code() );
		$this->assertStringContainsString( 'Order not found', $result->get_error_message() );
	}

	/**
	 * Test get_packing_list returns error when label service fails.
	 */
	public function test_get_packing_list_label_service_error() {
		$product = WC_Helper_Product::create_simple_product();
		$order   = WC_Helper_Order::create_order( 1, $product );

		$expected_error = new WP_Error( 'service_error', 'Label service failed' );

		$this->label_purchase_service_mock->expects( $this->once() )
			->method( 'get_labels' )
			->with( $order->get_id() )
			->willReturn( $expected_error );

		$label_print_service = new LabelPrintService( $this->api_client_mock, $this->connect_logger_mock, $this->label_purchase_service_mock );

		$result = $label_print_service->get_packing_list( $order->get_id(), 123 );

		$this->assertEquals( $expected_error, $result );
	}

	/**
	 * Test get_packing_list returns error when label is not found.
	 */
	public function test_get_packing_list_label_not_found() {
		$product = WC_Helper_Product::create_simple_product();
		$order   = WC_Helper_Order::create_order( 1, $product );

		$labels_response = array(
			'labels' => array(
				array(
					'label_id'     => 456,
					'id'           => 1,
					'service_name' => 'USPS Priority',
					'tracking'     => 'US1234567890',
					'package_name' => 'Small Box',
				),
			),
		);

		$this->label_purchase_service_mock->expects( $this->once() )
			->method( 'get_labels' )
			->with( $order->get_id() )
			->willReturn( $labels_response );

		$label_print_service = new LabelPrintService( $this->api_client_mock, $this->connect_logger_mock, $this->label_purchase_service_mock );

		$result = $label_print_service->get_packing_list( $order->get_id(), 123 ); // Non-existent label ID

		$this->assertInstanceOf( WP_Error::class, $result );
		$this->assertEquals( 404, $result->get_error_code() );
		$this->assertStringContainsString( 'Label not found', $result->get_error_message() );
	}

	/**
	 * Test successful packing list generation.
	 */
	public function test_get_packing_list_success() {
		// Create test products including a variation
		$simple_product = WC_Helper_Product::create_simple_product();
		$simple_product->set_sku( 'SIMPLE-001' );
		$simple_product->set_weight( 1.5 );
		$simple_product->set_length( 10 );
		$simple_product->set_width( 8 );
		$simple_product->set_height( 2 );
		$simple_product->save();

		$variable_product = WC_Helper_Product::create_variation_product();
		$variations       = $variable_product->get_available_variations();
		$variation_id     = $variations[0]['variation_id'];

		$order = WC_Helper_Order::create_order( 1, $simple_product );
		// Add variation to order
		$order->add_product( wc_get_product( $variation_id ), 2 );
		$order->save();

		$label_id    = 123;
		$shipment_id = 1;

		// Mock label data
		$labels_response = array(
			'labels' => array(
				array(
					'label_id'     => $label_id,
					'id'           => $shipment_id,
					'service_name' => 'USPS Priority Mail',
					'tracking'     => 'US1234567890123',
					'package_name' => 'Medium Box',
				),
			),
		);

		// Mock destinations and origins
		$destinations = array(
			'shipment_1' => array(
				'address'  => '123 Test St',
				'city'     => 'Test City',
				'state'    => 'CA',
				'postcode' => '90210',
				'country'  => 'US',
			),
		);

		$origins = array(
			'shipment_1' => array(
				'address'  => '456 Store St',
				'city'     => 'Store City',
				'state'    => 'NY',
				'postcode' => '10001',
				'country'  => 'US',
			),
		);

		// Mock shipments data
		$order_items = array_values( $order->get_items() ); // Convert to indexed array
		$shipments   = array(
			$shipment_id => array(
				array( 'id' => $order_items[0]->get_id() ),
				array( 'id' => $order_items[1]->get_id() ),
			),
		);

		// Set up mocks
		$this->label_purchase_service_mock->expects( $this->once() )
			->method( 'get_labels' )
			->with( $order->get_id() )
			->willReturn( $labels_response );

		$this->label_purchase_service_mock->expects( $this->once() )
			->method( 'get_shipments_destinations' )
			->with( $order->get_id() )
			->willReturn( $destinations );

		$this->label_purchase_service_mock->expects( $this->once() )
			->method( 'get_shipments_origins' )
			->with( $order->get_id() )
			->willReturn( $origins );

		$this->label_purchase_service_mock->expects( $this->once() )
			->method( 'get_shipments' )
			->with( $order->get_id() )
			->willReturn( $shipments );

		$label_print_service = new LabelPrintService( $this->api_client_mock, $this->connect_logger_mock, $this->label_purchase_service_mock );

		$result = $label_print_service->get_packing_list( $order->get_id(), $label_id );

		// Assertions
		$this->assertIsArray( $result );
		$this->assertTrue( $result['success'] );
		$this->assertArrayHasKey( 'html', $result );

		$html = $result['html'];

		// Check HTML contains required elements
		$this->assertStringContainsString( 'Packing Slip', $html );
		$this->assertStringContainsString( 'Order #' . $order->get_order_number(), $html );
		$this->assertStringContainsString( 'USPS Priority Mail', $html );
		$this->assertStringContainsString( 'US1234567890123', $html );
		$this->assertStringContainsString( 'Medium Box', $html );

		// Check table headers
		$this->assertStringContainsString( 'Item', $html );
		$this->assertStringContainsString( 'SKU', $html );
		$this->assertStringContainsString( 'Variant', $html );
		$this->assertStringContainsString( 'Quantity', $html );
		$this->assertStringContainsString( 'Weight', $html );
		$this->assertStringContainsString( 'Dimensions', $html );
		$this->assertStringContainsString( 'Picked', $html );

		// Check product data appears
		$this->assertStringContainsString( 'SIMPLE-001', $html );
		$this->assertStringContainsString( '10 &times; 8 &times; 2', $html ); // Dimensions (HTML entities)

		// Check addresses
		$this->assertStringContainsString( '123 Test St', $html );
		$this->assertStringContainsString( '456 Store St', $html );
	}

	/**
	 * Test create_rows_from_shipment_items when subItems don't exist and quantity comes from order item.
	 */
	public function test_create_rows_from_shipment_items_without_subitems() {
		// Create a product with specific quantity
		$product = WC_Helper_Product::create_simple_product();
		$product->set_sku( 'TEST-SKU-001' );
		$product->set_weight( 2.5 );
		$product->set_length( 15 );
		$product->set_width( 10 );
		$product->set_height( 5 );
		$product->save();

		// Create order with specific quantity (3 items)
		$order       = WC_Helper_Order::create_order( 1, $product );
		$order_items = $order->get_items();
		$order_item  = reset( $order_items );
		$order_item->set_quantity( 3 ); // Set specific quantity
		$order_item->save();
		$order->save();

		$label_id    = 123;
		$shipment_id = 1;

		// Mock data for get_packing_list dependencies
		$labels_response = array(
			'labels' => array(
				array(
					'label_id'     => $label_id,
					'id'           => $shipment_id,
					'service_name' => 'Test Service',
					'tracking'     => 'TEST123',
					'package_name' => 'Test Package',
				),
			),
		);

		$destinations = array( 'shipment_1' => array() );
		$origins      = array( 'shipment_1' => array() );

		// Create shipment items WITHOUT subItems - this should use $order_item->get_quantity()
		$shipments = array(
			$shipment_id => array(
				array( 'id' => $order_item->get_id() ), // No 'subItems' key
			),
		);

		// Set up mocks
		$this->label_purchase_service_mock->method( 'get_labels' )->willReturn( $labels_response );
		$this->label_purchase_service_mock->method( 'get_shipments_destinations' )->willReturn( $destinations );
		$this->label_purchase_service_mock->method( 'get_shipments_origins' )->willReturn( $origins );
		$this->label_purchase_service_mock->method( 'get_shipments' )->willReturn( $shipments );

		$label_print_service = new LabelPrintService( $this->api_client_mock, $this->connect_logger_mock, $this->label_purchase_service_mock );
		$result              = $label_print_service->get_packing_list( $order->get_id(), $label_id );

		// Verify the result is successful
		$this->assertIsArray( $result );
		$this->assertTrue( $result['success'] );
		$this->assertArrayHasKey( 'html', $result );

		$html = $result['html'];

		// Verify that the quantity from order item (3) is used in the HTML
		// The HTML should show the order item quantity since subItems don't exist
		$this->assertStringContainsString( '>3<', $html ); // Quantity should be 3

		// Verify other product details are present
		$this->assertStringContainsString( 'TEST-SKU-001', $html );
		$this->assertStringContainsString( '15 &times; 10 &times; 5', $html ); // Dimensions

		// Verify the weight is displayed correctly
		$weight_unit = get_option( 'woocommerce_weight_unit' );
		$this->assertStringContainsString( '2.5 ' . $weight_unit, $html );
	}

	/**
	 * Test create_rows_from_shipment_items when subItems exist and should override order quantity.
	 */
	public function test_create_rows_from_shipment_items_with_subitems() {
		// Create a product
		$product = WC_Helper_Product::create_simple_product();
		$product->set_sku( 'TEST-SKU-002' );
		$product->save();

		// Create order with quantity 5
		$order       = WC_Helper_Order::create_order( 1, $product );
		$order_items = $order->get_items();
		$order_item  = reset( $order_items );
		$order_item->set_quantity( 5 ); // Order has 5 items
		$order_item->save();
		$order->save();

		$label_id    = 123;
		$shipment_id = 1;

		// Mock data for get_packing_list dependencies
		$labels_response = array(
			'labels' => array(
				array(
					'label_id'     => $label_id,
					'id'           => $shipment_id,
					'service_name' => 'Test Service',
					'tracking'     => 'TEST123',
					'package_name' => 'Test Package',
				),
			),
		);

		$destinations = array( 'shipment_1' => array() );
		$origins      = array( 'shipment_1' => array() );

		// Create shipment items WITH subItems - this should use max(subItems, 1)
		$shipments = array(
			$shipment_id => array(
				array(
					'id'       => $order_item->get_id(),
					'subItems' => array( 'item1', 'item2' ), // 2 subItems, should use count of 2
				),
			),
		);

		// Set up mocks
		$this->label_purchase_service_mock->method( 'get_labels' )->willReturn( $labels_response );
		$this->label_purchase_service_mock->method( 'get_shipments_destinations' )->willReturn( $destinations );
		$this->label_purchase_service_mock->method( 'get_shipments_origins' )->willReturn( $origins );
		$this->label_purchase_service_mock->method( 'get_shipments' )->willReturn( $shipments );

		$label_print_service = new LabelPrintService( $this->api_client_mock, $this->connect_logger_mock, $this->label_purchase_service_mock );
		$result              = $label_print_service->get_packing_list( $order->get_id(), $label_id );

		// Verify the result is successful
		$this->assertIsArray( $result );
		$this->assertTrue( $result['success'] );
		$this->assertArrayHasKey( 'html', $result );

		$html = $result['html'];

		// Verify that the quantity from subItems count (2) is used, not the order quantity (5)
		$this->assertStringContainsString( '>2<', $html ); // Quantity should be 2 (count of subItems)
		$this->assertStringNotContainsString( '>5<', $html ); // Should not show order quantity

		// Verify product details are present
		$this->assertStringContainsString( 'TEST-SKU-002', $html );
	}

	/**
	 * Test packing list HTML structure and security.
	 */
	public function test_get_packing_list_html_structure() {
		$product = WC_Helper_Product::create_simple_product();
		$order   = WC_Helper_Order::create_order( 1, $product );

		$label_id    = 123;
		$shipment_id = 1;

		$labels_response = array(
			'labels' => array(
				array(
					'label_id'     => $label_id,
					'id'           => $shipment_id,
					'service_name' => 'Test Service',
					'tracking'     => 'TEST123',
					'package_name' => 'Test Package',
				),
			),
		);

		$destinations = array( 'shipment_1' => array() );
		$origins      = array( 'shipment_1' => array() );
		$order_items  = array_values( $order->get_items() ); // Convert to indexed array
		$shipments    = array( $shipment_id => array( array( 'id' => $order_items[0]->get_id() ) ) );

		$this->label_purchase_service_mock->method( 'get_labels' )->willReturn( $labels_response );
		$this->label_purchase_service_mock->method( 'get_shipments_destinations' )->willReturn( $destinations );
		$this->label_purchase_service_mock->method( 'get_shipments_origins' )->willReturn( $origins );
		$this->label_purchase_service_mock->method( 'get_shipments' )->willReturn( $shipments );

		$label_print_service = new LabelPrintService( $this->api_client_mock, $this->connect_logger_mock, $this->label_purchase_service_mock );
		$result              = $label_print_service->get_packing_list( $order->get_id(), $label_id );

		$html = $result['html'];

		// Validate HTML structure
		$this->assertStringContainsString( '<!DOCTYPE html>', $html );
		$this->assertStringContainsString( '<html>', $html );
		$this->assertStringContainsString( '<head>', $html );
		$this->assertStringContainsString( '<body>', $html );
		$this->assertStringContainsString( '<table>', $html );
		$this->assertStringContainsString( '<thead>', $html );
		$this->assertStringContainsString( '<tbody>', $html );

		// Validate CSS classes exist
		$this->assertStringContainsString( 'class="container"', $html );
		$this->assertStringContainsString( 'class="item-cell"', $html );
		$this->assertStringContainsString( 'class="checkbox-cell"', $html );

		// Validate no unescaped user content (security check)
		$this->assertStringNotContainsString( '<script>', $html );
		$this->assertStringNotContainsString( 'javascript:', $html );
	}
}
