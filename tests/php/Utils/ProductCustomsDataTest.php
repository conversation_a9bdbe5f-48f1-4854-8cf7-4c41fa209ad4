<?php
/**
 * Class ProductCustomsDataTest.
 *
 * @package Automattic\WCShipping
 */

namespace Automattic\WCShipping\Tests\php\Utils;

use Automattic\WCShipping\Utils;
use WC_Unit_Test_Case;
use WC_Helper_Product;

/**
 * Class ProductCustomsDataTest.
 *
 * @since 1.1.2
 */
class ProductCustomsDataTest extends WC_Unit_Test_Case {
	/**
	 * Products created during a test.
	 *
	 * @var \WC_Product[]
	 */
	private $products = array();

	/**
	 * {@inheritDoc}
	 */
	public function tear_down(): void {
		foreach ( $this->products as $product ) {
			WC_Helper_Product::delete_product( $product->get_id() );
		}

		$this->products = array();

		parent::tear_down();
	}

	/**
	 * Test successful customs info fetch.
	 *
	 * @since 1.1.2
	 */
	public function test_fetch_correct_customs_data() {
		$product = $this->create_simple_product( array( 'name' => 'My Product Name' ) );

		$product->update_meta_data(
			'wcshipping_customs_info',
			array(
				'description'      => 'Custom Name',
				'hs_tariff_number' => '7777777',
				'origin_country'   => 'DK',
			)
		);
		$product->save();

		$this->assertSame(
			array(
				'description'      => 'Custom Name',
				'hs_tariff_number' => '7777777',
				'origin_country'   => 'DK',
			),
			Utils::get_product_customs_data( $product )
		);
	}

	/**
	 * Test backwards supported customs info field.
	 *
	 * @since 1.1.2
	 */
	public function test_backwards_compatibility() {
		$product = $this->create_simple_product( array( 'name' => 'My Product Name' ) );

		$product->update_meta_data(
			'wc_connect_customs_info',
			array(
				'description'      => 'Custom Name',
				'hs_tariff_number' => '7777777',
				'origin_country'   => 'DK',
			)
		);
		$product->save();

		$this->assertSame(
			array(
				'description'      => 'Custom Name',
				'hs_tariff_number' => '7777777',
				'origin_country'   => 'DK',
			),
			Utils::get_product_customs_data( $product )
		);
	}

	/**
	 * Test that we're prioritising WC Shipping meta data over WCS&T data.
	 *
	 * @since 1.1.2
	 */
	public function test_compatibility_load_order() {
		$product = $this->create_simple_product( array( 'name' => 'My Product Name' ) );

		$product->update_meta_data(
			'wc_connect_customs_info',
			array(
				'description'      => 'Old Description',
				'hs_tariff_number' => '111111',
				'origin_country'   => 'US',
			)
		);
		$product->update_meta_data(
			'wcshipping_customs_info',
			array(
				'description'      => 'New Description',
				'hs_tariff_number' => '222222',
				'origin_country'   => 'CA',
			)
		);
		$product->save();

		$this->assertSame(
			array(
				'description'      => 'New Description',
				'hs_tariff_number' => '222222',
				'origin_country'   => 'CA',
			),
			Utils::get_product_customs_data( $product )
		);
	}

	/**
	 * Test that we get expected default values.
	 *
	 * @since 1.1.2
	 */
	public function test_default_values() {
		$product = $this->create_simple_product( array( 'name' => 'My Product Name' ) );

		// Use the stores base country as the default value as the origin country.
		add_filter(
			'woocommerce_countries_base_country',
			function () {
				return 'CA';
			},
			10,
			0
		);

		$this->assertSame(
			array(
				'description'      => 'My Product Name',
				'hs_tariff_number' => '',
				'origin_country'   => 'CA',
			),
			Utils::get_product_customs_data( $product )
		);

		// Clean-up.
		remove_all_filters( 'woocommerce_countries_base_country' );
	}

	/**
	 * Helper function to create a simple product.
	 *
	 * This also ensure that everything we need to clean up after the test is handled.
	 *
	 * @since 1.1.2
	 *
	 * @return \WC_Product
	 */
	public function create_simple_product( $props = array() ) {
		$product          = WC_Helper_Product::create_simple_product( true, $props );
		$this->products[] = $product;

		return $product;
	}
}
