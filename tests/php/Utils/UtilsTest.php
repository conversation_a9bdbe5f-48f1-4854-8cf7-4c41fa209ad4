<?php
/**
 * Tests for Automattic\WCShipping\Utils class.
 *
 * @package Automattic\WCShipping
 */

namespace Automattic\WCShipping\Tests\php\Utils;

use Automattic\WCShipping\Utils;
use PHPUnit\Framework\TestCase;

/**
 * Class UtilsTest
 */
class UtilsTest extends TestCase {

	/**
	 * Test get_plugin_path method.
	 */
	public function test_get_plugin_path() {
		// Test for WooCommerce plugin path.
		$this->assertEquals( plugin_dir_path( WC_PLUGIN_FILE ), Utils::get_plugin_path( true ) );

		// Test for WCShipping plugin path.
		$this->assertEquals( plugin_dir_path( WCSHIPPING_PLUGIN_FILE ), Utils::get_plugin_path( false ) );
	}

	/**
	 * Test get_relative_plugin_path method.
	 */
	public function test_get_relative_plugin_path() {
		// Test for WooCommerce plugin relative path.
		$expected_woocommerce_relative_path = str_replace( wp_normalize_path( ABSPATH ), '', wp_normalize_path( plugin_dir_path( WC_PLUGIN_FILE ) ) );
		$this->assertEquals( $expected_woocommerce_relative_path, Utils::get_relative_plugin_path( true ) );

		// Test for WCShipping plugin relative path.
		$expected_wcshipping_relative_path = str_replace( wp_normalize_path( ABSPATH ), '', wp_normalize_path( plugin_dir_path( WCSHIPPING_PLUGIN_FILE ) ) );
		$this->assertEquals( $expected_wcshipping_relative_path, Utils::get_relative_plugin_path( false ) );
	}

	/**
	 * Test get_constants_for_js method.
	 */
	public function test_get_constants_for_js() {
		// Expected constants array.
		$expected_constants = array(
			'WCSHIPPING_PLUGIN_FILE'         => WCSHIPPING_PLUGIN_FILE,
			'WCSHIPPING_PLUGIN_DIR'          => plugin_dir_path( WCSHIPPING_PLUGIN_FILE ),
			'WCSHIPPING_RELATIVE_PLUGIN_DIR' => str_replace( wp_normalize_path( ABSPATH ), '', wp_normalize_path( plugin_dir_path( WCSHIPPING_PLUGIN_FILE ) ) ),
			'WC_PLUGIN_RELATIVE_DIR'         => str_replace( wp_normalize_path( ABSPATH ), '', wp_normalize_path( plugin_dir_path( WC_PLUGIN_FILE ) ) ),
		);

		$this->assertEquals( $expected_constants, Utils::get_constants_for_js() );
	}
}
