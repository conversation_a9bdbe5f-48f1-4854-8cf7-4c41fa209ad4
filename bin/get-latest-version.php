<?php
// Add command line argument for the number of versions.
$amount_of_versions = intval($argv[1] ?? 3);

function version_compare_reverse($i, $j) {
	 return version_compare($i, $j) * -1;
}

/**
 * Take input from stdin and adds all valid (ie. x.y.z) versions to an array.
 */
function filter_valid_versions() {
	$versions = [];
	$fp = fopen("php://stdin", 'r');
	$contents = fgets($fp);
	while ($contents !== false) {
		$contents = fgets($fp);
		if (!preg_match('/^\d+\.\d+\.\d+$/', $contents)) {
			continue;
		}
		$versions[] = trim($contents);
	}
	return $versions;
}

/**
 * Take an array of $versions and return the latest minor versions.
 *
 * @param int $amount_of_versions The number of versions to return.
 * @param array $versions The array to be sorted first.
 * @return array Latest minor versions, in the order of oldest to newest.
 */
function get_latest_minor($amount_of_versions, $versions) {
	$last_minor_version = '';
	$results = [];
	foreach ($versions as $version_number) {
		$current_minor_version = substr($version_number, 0, strrpos($version_number, '.'));

		if ( count( $results ) >= $amount_of_versions ) {
			break;
		}

		if ($last_minor_version === $current_minor_version) {
			continue;
		}

		$last_minor_version = $current_minor_version;
		$results[] = $version_number;
	}

	return array_reverse($results);
}

// Output latest minor versions as a JSON array for GitHub action matrix.
$versions = filter_valid_versions();
usort($versions, 'version_compare_reverse');
$results = get_latest_minor($amount_of_versions, $versions);
echo json_encode($results);
