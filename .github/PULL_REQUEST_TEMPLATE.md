### Related Issue(s)
<!-- 
List any issues related to this PR. For example:
- Fixes #123
- Related to #456
-->
- Fixes #

### Description
<!-- A brief description of what this PR does. Outline why these changes are important – what's the problem, and how does this PR address it. -->

### Testing Instructions
<!-- Add all the necessary steps to test this Pull Request. Your steps should allow anyone to test this as a user would. -->
1. Check out this branch on your local environment.
2. ...
3. Please test around this issue

### Screenshots (if applicable):
<!-- If this PR includes visual changes, include screenshots or GIFs. -->

### Types of changes:
<!-- 
What types of changes does your code introduce to the project? Put an `x` in all the boxes that apply: 
-->
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to change)
- [ ] Chore, refactoring or updating dependencies (code change that does not change functionality)

### Checklist:
<!-- 
Go over all the following points, and put an `x` in all the boxes that apply. 
If you're unsure about any of these, don't hesitate to ask. We're here to help! 
-->
- [ ] My code follows the [WordPress Coding Standards](https://developer.wordpress.org/coding-standards/wordpress-coding-standards/) and [Security Best Practices](https://developer.wordpress.org/apis/security/).
- [ ] My change requires an update to our documentation ([public facing](https://woocommerce.com/document/woocommerce-shipping/), [API](https://github.com/woocommerce/woocommerce-shipping/tree/trunk/docs/api), or any other internal documentation).
- [ ] I have updated the documentation accordingly.
- [ ] I have added tests to cover my changes.
- [ ] I have added a `changelog.txt` entry.
- [ ] I have added a changelog entry to the `readme.txt` file.
