version: 2
updates:
  # Enable version updates for pnpm (using npm ecosystem)
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    target-branch: "trunk"
    open-pull-requests-limit: 10
    versioning-strategy: "auto"
    allow:
      - dependency-type: "direct"
    commit-message:
      prefix: "chore"
      include: "scope"
    labels:
      - "dependencies"
      - "javascript"
      - "npm"
    groups:
      dev-dependencies:
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"

  # Enable version updates for Composer
  - package-ecosystem: "composer"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    target-branch: "trunk"
    open-pull-requests-limit: 10
    versioning-strategy: "increase"
    allow:
      - dependency-type: "direct"
    commit-message:
      prefix: "chore"
      include: "scope"
    labels:
      - "dependencies"
      - "php"
      - "composer"
    groups:
      production-dependencies:
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
