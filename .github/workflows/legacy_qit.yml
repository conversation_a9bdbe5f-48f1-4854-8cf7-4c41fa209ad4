name: QIT Tests
# We'd need to revisit this once we've migrated to use the new run-qit.yml workflow.


on:
  workflow_dispatch:
      secrets:
          PARTNER_USER:
              description: 'QIT Partner user account username'
              required: true
          PARTNER_SECRET:
              description: 'QIT Partner account password'
              required: true
  pull_request:

permissions:
  contents: read
  pull-requests: write


jobs:
    build:
      name: Build project
      uses: ./.github/workflows/build_and_upload.yml

    activation:
        runs-on: ubuntu-latest
        env:
            NO_COLOR: 1
            QIT_DISABLE_ONBOARDING: yes
        needs: build
        steps:
            - name: Checkout
              uses: actions/checkout@v3

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: '8.2'
                  tools: composer:v2
                  coverage: none

            - name: Install QIT via composer
              run: composer require woocommerce/qit-cli

            - name: Add partner
              run: ./vendor/bin/qit partner:add --user='${{ secrets.PARTNER_USER }}' --application_password='${{ secrets.PARTNER_SECRET }}'

            - name: Download build
              uses: actions/download-artifact@v4
              with:
                  name: woocommerce-shipping

            - name: Run activation test
              id: run-activation-test
              run: ./vendor/bin/qit run:activation woocommerce-shipping --zip=woocommerce-shipping.zip --wait > activation-result.txt
              # Using the slug of the extension in the marketplace as woocommerce/qit-cli@0.8.1 doesn't work with the ID

            - uses: marocchino/sticky-pull-request-comment@v2
              if: failure()
              with:
                  header: QIT Activation Result
                  recreate: true
                  path: activation-result.txt

    api:
        runs-on: ubuntu-latest
        env:
            NO_COLOR: 1
            QIT_DISABLE_ONBOARDING: yes
        needs: build
        steps:
            - name: Checkout
              uses: actions/checkout@v3

            - name: Download build
              uses: actions/download-artifact@v4
              with:
                  name: woocommerce-shipping

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: '8.2'
                  tools: composer:v2
                  coverage: none

            - name: Install QIT via composer
              run: composer require woocommerce/qit-cli

            - name: Add partner
              run: ./vendor/bin/qit partner:add --user='${{ secrets.PARTNER_USER }}' --application_password='${{ secrets.PARTNER_SECRET }}'

            - name: Run API test
              id: run-api-test
              run: ./vendor/bin/qit run:woo-api 2165910 --zip=woocommerce-shipping.zip --wait > api-result.txt

            - uses: marocchino/sticky-pull-request-comment@v2
              if: failure()
              with:
                  header: QIT API Result
                  recreate: true
                  path: api-result.txt

    end-to-end:
        runs-on: ubuntu-latest
        if: github.ref == 'refs/heads/trunk'
        env:
            NO_COLOR: 1
            QIT_DISABLE_ONBOARDING: yes
        needs: build
        steps:
            - name: Checkout
              uses: actions/checkout@v3

            - name: Download build
              uses: actions/download-artifact@v4
              with:
                  name: woocommerce-shipping

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: '8.2'
                  tools: composer:v2
                  coverage: none

            - name: Install QIT via composer
              run: composer require woocommerce/qit-cli

            - name: Add partner
              run: ./vendor/bin/qit partner:add --user='${{ secrets.PARTNER_USER }}' --application_password='${{ secrets.PARTNER_SECRET }}'

            - name: Run E2E test
              id: run-e2e-test
              run: ./vendor/bin/qit run:woo-e2e 2165910 --zip=woocommerce-shipping.zip --wait > e2e-result.txt

            - uses: marocchino/sticky-pull-request-comment@v2
              if: failure()
              with:
                  header: QIT E2E Result
                  recreate: true
                  path: e2e-result.txt

    # phpstan:
    #   runs-on: ubuntu-latest
    #   env:
    #     NO_COLOR: 1
    #     QIT_DISABLE_ONBOARDING: yes
    #   needs: build
    #   steps:
    #     - name: Checkout
    #       uses: actions/checkout@v3

    #     - name: Download build
    #       uses: actions/download-artifact@v3
    #       with:
    #         name: woocommerce-shipping

    #     - name: Setup PHP
    #       uses: shivammathur/setup-php@v2
    #       with:
    #         php-version: '8.2'
    #         tools: composer:v2
    #         coverage: none

    #     - name: Install QIT via composer
    #       run: composer require woocommerce/qit-cli

    #     - name: Add partner
    #       run: ./vendor/bin/qit partner:add --user='${{ secrets.PARTNER_USER }}' --application_password='${{ secrets.PARTNER_SECRET }}'

    #     - name: Run PHPStan test
    #       id: run-phpstan-test
    #       run: ./vendor/bin/qit run:phpstan 2165910 --zip=woocommerce-shipping.zip --wait > phpstan-result.txt

    #     - uses: marocchino/sticky-pull-request-comment@v2
    #       if: failure()
    #       with:
    #         header: QIT PHPStan Result
    #         recreate: true
    #         path: phpstan-result.txt

    security:
        runs-on: ubuntu-latest
        env:
            NO_COLOR: 1
            QIT_DISABLE_ONBOARDING: yes
        needs: build
        steps:
            - name: Checkout
              uses: actions/checkout@v3

            - name: Download build
              uses: actions/download-artifact@v4
              with:
                  name: woocommerce-shipping

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: '8.2'
                  tools: composer:v2
                  coverage: none

            - name: Install QIT via composer
              run: composer require woocommerce/qit-cli

            - name: Add partner
              run: ./vendor/bin/qit partner:add --user='${{ secrets.PARTNER_USER }}' --application_password='${{ secrets.PARTNER_SECRET }}'

            - name: Run security test
              id: run-security-test
              run: ./vendor/bin/qit run:security 2165910 --zip=woocommerce-shipping.zip --wait > security-result.txt

            - uses: marocchino/sticky-pull-request-comment@v2
              if: failure()
              with:
                  header: QIT Security Result
                  recreate: true
                  path: security-result.txt
