name: PHP Tests

on:
    push:
        paths-ignore:
            - 'docs/**'
    workflow_dispatch:

jobs:
    find_latest_versions:
        name: Get supported WP & WC versions
        runs-on: ubuntu-latest
        outputs:
            wc-versions: ${{ steps.get_wc_versions_for_matrix.outputs.wc-versions }}
            wp-versions: ${{ steps.get_wp_versions_for_matrix.outputs.wp-versions }}
        steps:
            - name: Checkout WooCommerce Shipping
              uses: actions/checkout@v4
            - name: Get current branch
              id: get_branch
              run: echo "branch=$(echo ${GITHUB_REF#refs/heads/})" >> $GITHUB_OUTPUT
            - name: Get WordPress versions
              id: get_wp_versions_for_matrix
              run: |
                  if [ "${{ steps.get_branch.outputs.branch }}" == "trunk" ]; then
                      echo "wp-versions=$(git ls-remote --tags --refs https://github.com/WordPress/WordPress.git | sed 's/.*refs\/tags\///' | php ./bin/get-latest-version.php 3)" >> $GITHUB_OUTPUT
                  else
                      echo "wp-versions=$(git ls-remote --tags --refs https://github.com/WordPress/WordPress.git | sed 's/.*refs\/tags\///' | php ./bin/get-latest-version.php 1)" >> $GITHUB_OUTPUT
                  fi
            - name: Get WooCommerce versions
              id: get_wc_versions_for_matrix
              run: |
                  if [ "${{ steps.get_branch.outputs.branch }}" == "trunk" ]; then
                      echo "wc-versions=$(git ls-remote --tags --refs https://github.com/woocommerce/woocommerce.git | sed 's/.*refs\/tags\///' | php ./bin/get-latest-version.php 3)" >> $GITHUB_OUTPUT
                  else
                      echo "wc-versions=$(git ls-remote --tags --refs https://github.com/woocommerce/woocommerce.git | sed 's/.*refs\/tags\///' | php ./bin/get-latest-version.php 1)" >> $GITHUB_OUTPUT
                  fi
    php_build_and_test:
        name: PHP Tests (WP ${{ matrix.wp-version }} WC ${{ matrix.wc-version }})
        needs: find_latest_versions
        runs-on: ubuntu-latest
        services:
            mariadb:
                image: mariadb:10.9
                ports:
                    - 3306:3306
                env:
                    MYSQL_USER: wp_test
                    MYSQL_PASSWORD: wp_test
                    MYSQL_DATABASE: wordpress_default
                    MYSQL_ROOT_PASSWORD: root
                options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
        strategy:
            matrix:
                wp-version: ${{ fromJSON(needs.find_latest_versions.outputs.wp-versions) }}
                wc-version: ${{ fromJSON(needs.find_latest_versions.outputs.wc-versions) }}
        steps:
            - name: Install SVN if not already installed
              run: |
                  if ! svn --version &> /dev/null
                  then
                    echo "SVN is not installed. Installing SVN..."
                    sudo apt-get update && sudo apt-get install -y subversion
                  else
                    echo "SVN is already installed."
                  fi
            - name: Checkout WooCommerce Shipping
              uses: actions/checkout@v4
            - name: Checkout WooCommerce
              run: git clone --depth=1 --branch=${{ matrix.wc-version }} https://github.com/woocommerce/woocommerce.git /tmp/woocommerce
            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: '8.2'
            - name: Setup PNPM
              uses: pnpm/action-setup@v3
              with:
                  version: 10
            - uses: actions/setup-node@v3
              with:
                  node-version: '22.14.0'
            - name: Build WooCommerce essentials for running tests
              run: |
                  cd /tmp/woocommerce/plugins/woocommerce
                  composer install
                  php bin/generate-feature-config.php
            - name: Setup WordPress
              run: bash /tmp/woocommerce/plugins/woocommerce/tests/bin/install.sh wordpress_test root root 127.0.0.1 ${{ matrix.wp-version }}
            - name: Use PHPUnit v8 for WC < 7.7.0
              if: contains(fromJSON('["7.5.1", "7.6.1"]'), matrix.wc-version)
              run: composer require -W phpunit/phpunit:^8
            - name: Install Composer dependencies
              run: composer install
            - name: Run PHPUnit tests
              run: pnpm run test:php
