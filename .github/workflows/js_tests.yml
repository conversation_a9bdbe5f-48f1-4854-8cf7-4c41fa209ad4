name: JS Tests

on: [pull_request, workflow_dispatch]

jobs:
    js_build_and_test:
        name: JS Eslint and Test
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
            - uses: pnpm/action-setup@v3
              with:
                  version: 10
            - uses: actions/setup-node@v3
              with:
                  node-version: '22.14.0'
            - run: git config --global url.https://github.com/.insteadOf git://github.com/
            - run: pnpm install --frozen-lockfile
            - run: pnpm eslint client/*
            - run: pnpm check-types
            - run: pnpm run test:js
