name: Unit Tests Runner (PHP)

on:
    workflow_call:
        inputs:
            wc_version:
                required: true
                type: string
            wp_version:
                required: true
                type: string

jobs:
    php_build_and_test:
        name: PHP Tests (WP ${{ matrix.wp_version }} WC ${{ matrix.wc_version }})
        runs-on: ubuntu-latest
        services:
            mariadb:
                image: mariadb:10.9
                ports:
                    - 3306:3306
                env:
                    MYSQL_USER: wp_test
                    MYSQL_PASSWORD: wp_test
                    MYSQL_DATABASE: wordpress_default
                    MYSQL_ROOT_PASSWORD: root
                options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
        strategy:
            matrix:
                include:
                    - php: 7.4
                      wp_version: 6.2
                      wc_version: 8.2.3
                      wc_order_datastore: cpt
                    - php: 7.4
                      wp_version: ${{ inputs.wp_version }}
                      wc_version: ${{ inputs.wc_version }}
                      wc_order_datastore: cpt
                    - php: 8.0
                      wp_version: ${{ inputs.wp_version }}
                      wc_version: ${{ inputs.wc_version }}
                      wc_order_datastore: cpt
                    - php: 8.1
                      wp_version: ${{ inputs.wp_version }}
                      wc_version: ${{ inputs.wc_version }}
                      wc_order_datastore: cpt
                    - php: 8.2
                      wp_version: ${{ inputs.wp_version }}
                      wc_version: ${{ inputs.wc_version }}
                      wc_order_datastore: hpos
                    - php: 8.3
                      wp_version: ${{ inputs.wp_version }}
                      wc_version: ${{ inputs.wc_version }}
                      wc_order_datastore: hpos
                    - php: 8.4
                      wp_version: ${{ inputs.wp_version }}
                      wc_version: ${{ inputs.wc_version }}
                      wc_order_datastore: hpos
        env:
            WP_VERSION: ${{ matrix.wp_version }}
            WC_VERSION: ${{ matrix.wc_version }}
            PHP_VERSION: ${{ matrix.php }}
            RUN_UNIT_TESTS: 1
            WC_ORDER_DATASTORE: ${{ matrix.wc_order_datastore }}
        steps:
            - name: Install SVN if not already installed
              run: |
                  if ! svn --version &> /dev/null
                  then
                  echo "SVN is not installed. Installing SVN..."
                  sudo apt-get update && sudo apt-get install -y subversion
                  else
                  echo "SVN is already installed."
                  fi
            - name: Checkout WooCommerce Shipping
              uses: actions/checkout@v4
            - name: Checkout WooCommerce
              run: git clone --depth=1 --branch=${{ env.WC_VERSION }} https://github.com/woocommerce/woocommerce.git /tmp/woocommerce
            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: ${{ env.PHP_VERSION }}
            - name: Setup PNPM
              uses: pnpm/action-setup@v3
              with:
                  version: 10
            - name: Setup Node.js
              uses: actions/setup-node@v3
              with:
                  node-version: '22.14.0'
            - name: Build WooCommerce essentials for running tests
              run: |
                  cd /tmp/woocommerce/plugins/woocommerce
                  composer install
                  php bin/generate-feature-config.php
            - name: Setup WordPress
              run: bash /tmp/woocommerce/plugins/woocommerce/tests/bin/install.sh wordpress_test root root 127.0.0.1 ${{ env.WP_VERSION }}
            - name: Use PHPUnit v8 for WC < 7.7.0
              if: contains(fromJSON('["7.5.1", "7.6.1"]'), matrix.wc_version)
              run: composer require -W phpunit/phpunit:^8
            - name: Install Composer dependencies
              run: composer install
            - name: Run PHPUnit tests
              run: pnpm run test:php
