name: PHP Syntax Check

on: [pull_request, workflow_dispatch]

jobs:
    php_lint:
        name: PHP Syntax Check
        runs-on: ubuntu-latest
        steps:
            - name: Checkout latest WooCommerce Shipping
              uses: actions/checkout@v4
            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: '7.4'
            - name: Lint
              run: find . -type f -name "*.php" -exec php -l {} \;
