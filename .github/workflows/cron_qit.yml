name: <PERSON><PERSON> QIT

on:
    schedule:
        # Run at 02:00 on Sundays.
        - cron: '0 2 * * 0'

jobs:

    build:
        name: Build project
        uses: ./.github/workflows/build_and_upload.yml

    qit-tests:
        name: QIT
        needs: build
        uses: ./.github/workflows/qit_runner.yml
        secrets: inherit
        with:
            extension: 'woocommerce-shipping'
            artifact: 'woocommerce-shipping'
            wp-version: 'stable'
            wc-version: 'nightly'
            test-activation: true
            test-security: true
            test-phpcompatibility: true
            test-validation: true
            test-phpstan: false # TODO: Enable this once we've fixed the PHPStan issues.
            test-woo-api: true
            test-woo-e2e: true
            test-malware: true
            test-plugin-check: true # only run for WPORG

    handle-success:
        if: ${{ success() }}
        needs: qit-tests
        name: Handle success
        runs-on: ubuntu-latest
        env:
            SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        steps:
            - uses: act10ns/slack@v2
              with:
                  status: success
                  message: 'Scheduled workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> passed.'

    handle-error:
        if: ${{ failure() }}
        needs: qit-tests
        name: Handle failure
        runs-on: ubuntu-latest
        env:
            SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        steps:
            - uses: act10ns/slack@v2
              with:
                  status: failure
                  message: 'Scheduled workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> failed. You can find the results in the artifacts section.'
