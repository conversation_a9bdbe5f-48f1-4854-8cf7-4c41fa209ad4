name: Build and Upload zip

on:
    workflow_call:

jobs:
    build:
        runs-on: ubuntu-latest
        steps:
        - name: Checkout WooCommerce Shipping
          uses: actions/checkout@v4

        - name: Setup pnpm
          uses: pnpm/action-setup@v3
          with:
            version: 10

        - name: Setup node
          uses: actions/setup-node@v3
          with:
            node-version: '22.14.0'

        - name: Install dependencies
          run: pnpm install --frozen-lockfile

        - name: Generate ZIP file
          run: WCSHIPPING_SKIP_SOURCEMAP_UPLOAD=1 pnpm run build

        - name: Use the Upload Artifact GitHub Action
          uses: actions/upload-artifact@v4
          with:
            name: woocommerce-shipping
            path: woocommerce-shipping.zip
