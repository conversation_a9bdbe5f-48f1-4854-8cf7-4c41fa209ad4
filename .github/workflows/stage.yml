name: Stage Artifact

on:
    push:
        branches:
            - trunk
            - add/stage-artifacts
jobs:
    build:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout WooCommerce Shipping
              uses: actions/checkout@v4

            - name: Setup pnpm
              uses: pnpm/action-setup@v3
              with:
                  version: 10

            - name: Setup node
              uses: actions/setup-node@v3
              with:
                  node-version: '22.14.0'

            - name: Install dependencies
              run: pnpm install --frozen-lockfile

            - name: Generate ZIP file
              run: WCSHIPPING_SKIP_SOURCEMAP_UPLOAD=1 pnpm run build

            - name: Upload artifact
              uses: actions/upload-artifact@v4
              with:
                  name: woocommerce-shipping
                  path: woocommerce-shipping.zip

    stage:
        name: stage artifact
        runs-on: ubuntu-latest
        needs: build
        steps:
            - uses: actions/download-artifact@v4
              with:
                  name: woocommerce-shipping
                  path: dist-zip
            - name: Display structure of downloaded files
              run: ls -R
            - name: Install dependencies
              run: |
                  sudo apt-get install unzip sshpass
            - name: Unzip artifact
              run: unzip dist-zip/woocommerce-shipping.zip -d dist
            - name: Display structure of unzipped files
              run: ls -R dist
            - name: Add host to known hosts
              run: |
                  mkdir -p ~/.ssh/ && touch ~/.ssh/known_hosts
                  ssh-keyscan -H ${{ secrets.STAGING_SSH_HOST }} >> ~/.ssh/known_hosts
            - name: Upload via rsync
              run: sshpass -p ${{ secrets.STAGING_SSH_PASS }} rsync -avz dist/* ${{ secrets.STAGING_SSH_USER }}@${{ secrets.STAGING_SSH_HOST }}:${{ secrets.STAGING_DESTINATION_PATH }}
