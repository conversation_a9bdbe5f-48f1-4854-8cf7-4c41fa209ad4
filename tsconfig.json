{
  "compilerOptions": {
    "outDir": "./dist/",        // path to output directory
    "sourceMap": true,          // allow sourcemap support
    "strict": true,             // enable strict type checks as a best practice
    "module": "esnext",         // specify module code generation
    "jsx": "react-jsx",         // use typescript to transpile jsx to js
    "target": "es6",            // specify ECMAScript target version
    "allowJs": true,            // allow a partial TypeScript and JavaScript codebase
    "checkJs": false,
    "moduleResolution": "bundler",
    "baseUrl": "./client",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "noImplicitAny": true,
    "lib": ["dom", "esnext", "ScriptHost"],
    "paths": {
      "wcshipping/*": [
        "client/*"
      ],
      "tests/*": [
        "tests/js/*"
      ],
      "types/*": [
        "client/types/*"
      ],
      "react": ["../node_modules/@types/react"],
      "test-helpers": ["./test-helpers"],
      "test-helpers/*": ["./test-helpers/*"]
    },
    "types": ["jest", "@testing-library/jest-dom", "node", "react", "react-dom"],
    "resolveJsonModule": true,
    "skipLibCheck": true // To be removed when the lint errors related to types and multiple resolutions are gone
  },
  "include": [
    "./client/",
    "./tasks/",
    ".eslintrc.js",
    "./tests/js/jest.config.js",
    "webpack.config.js",
    "global.d.ts"
  ],
  "exclude": [ "node_modules", "./assets/javascript/" ]
}
