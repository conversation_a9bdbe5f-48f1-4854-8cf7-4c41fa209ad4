<?xml version="1.0"?>
<ruleset name="WordPress Coding Standards">
	<description>WooCommerce dev PHP_CodeSniffer ruleset.</description>

	<arg value="ps" />
	<arg name="parallel" value="20" />
	<arg name="extensions" value="php" />

	<!-- Exclude paths -->
	<exclude-pattern>dist/*</exclude-pattern>
	<exclude-pattern>release/*</exclude-pattern>
	<exclude-pattern>docker/*</exclude-pattern>
	<exclude-pattern>node_modules/*</exclude-pattern>
	<exclude-pattern>vendor/*</exclude-pattern>
	<exclude-pattern>bin/*</exclude-pattern>
    <exclude-pattern>.direnv/*</exclude-pattern>

	<!-- Configs -->
	<config name="minimum_supported_wp_version" value="6.0" />
	<config name="testVersion" value="7.4-" />

	<!-- Rules -->
	<rule ref="WooCommerce-Core">
		<exclude name="Generic.Commenting.Todo" />
	</rule>

	<rule ref="WordPress.WP.I18n">
		<properties>
			<property name="text_domain" type="array" value="woocommerce-shipping" />
		</properties>
	</rule>

	<rule ref="PHPCompatibility">
		<exclude-pattern>tests/</exclude-pattern>
	</rule>

	<rule ref="WordPress.Files.FileName">
		<exclude-pattern>tests/*</exclude-pattern>
		<exclude-pattern>src</exclude-pattern>
	</rule>

	<rule ref="Generic.Commenting">
		<exclude-pattern>tests/</exclude-pattern>
	</rule>

	<rule ref="Squiz.Commenting.FunctionComment">
		<exclude-pattern>tests/</exclude-pattern>
	</rule>

	<rule ref="Squiz.Commenting.FunctionCommentThrowTag.Missing" />

	<!-- We don't need to do nonce checks in unit test code -->
	<rule ref="WordPress.Security.NonceVerification.Recommended">
		<exclude-pattern>tests/</exclude-pattern>
	</rule>

	<rule ref="PEAR.WhiteSpace.ObjectOperatorIndent" />
</ruleset>
