{"name": "woocommerce-shipping", "title": "WooCommerce Shipping", "version": "1.8.8", "config": {"use_pnpm": true, "translate": true, "use_gh_release_notes": true, "wp_org_slug": "woocommerce-shipping", "assets": {"styles": {"dir": "assets/stylesheets", "css": "assets/stylesheets/*.css"}}}, "engines": {"node": "^22.14.0", "pnpm": "^10.3.0"}, "scripts": {"start": "pnpm run wp-start", "build": "pnpm run build:deps && node tasks/release.mjs", "build:deps": "rm -rf vendor && rm -rf node_modules && composer install --no-dev --optimize-autoloader && pnpm i", "build:client": "pnpm run sass && webpack --mode=production --node-env=production && pnpm run i18n", "build:dev": "pnpm run sass && webpack --mode=development", "release": "node tasks/release.mjs && cp release/woocommerce-shipping.zip ./", "analyze": "ANALYZE_BUNDLES=true npm run build:client", "test:js": "wp-scripts test-unit-js --config tests/js/jest.config.js", "test:js:snapshot": "pnpm test:js --updateSnapshot", "watch": "pnpm run wp-start:watch", "watch:ts": "wp-scripts start --hot", "test:php": "./vendor/bin/phpunit -c phpunit.xml.dist", "test:down": "cd tests && docker compose down -v --remove-orphans", "test:unit:debug": "wp-scripts --inspect-brk test-unit-js --runInBand --no-cache --config tests/js/jest.config.js", "wp-env": "wp-env", "wp-scripts": "wp-scripts", "prepare": "husky", "i18n": "node tasks/i18n.js", "i18n:json": "wp i18n make-json --no-purge languages", "lint:styles": "npx stylelint './client/**/*.scss'", "lint:styles:fix": "pnpm run lint:styles --fix", "lint": "pnpm run lint:styles && pnpm eslint client/*", "lint:fix": "pnpm run lint:styles:fix && pnpm eslint --fix client/*", "presass": "rm -f $npm_package_config_assets_styles_css", "sass": "pnpm run presass && node_modules/.bin/sass $npm_package_config_assets_styles_dir:$npm_package_config_assets_styles_dir --no-source-map --style=compressed", "sass:watch": "pnpm run sass --watch", "wp-start": "wp-scripts start", "wp-start:watch": "wp-scripts start --hot", "check-types": "tsc --noEmit", "qit:authenticate": "./vendor/bin/qit", "qit:get": "node tasks/qit-result.js", "qit:e2e": "pnpm run build && ./vendor/bin/qit run:e2e 2165910 --zip=woocommerce-shipping.zip", "qit:activation": "pnpm run build && ./vendor/bin/qit run:activation 2165910 --zip=woocommerce-shipping.zip", "qit:security": "pnpm run build && ./vendor/bin/qit run:security 2165910 --zip=woocommerce-shipping.zip", "qit:phpstan": "pnpm run build && ./vendor/bin/qit run:phpstan 2165910 --zip=woocommerce-shipping.zip", "qit:api": "pnpm run build && ./vendor/bin/qit run:api 2165910 --zip=woocommerce-shipping.zip", "qit:phpcompatibility": "pnpm run build && ./vendor/bin/qit run:phpcompatibility 2165910 --zip=woocommerce-shipping.zip", "qit:plugin-check": "pnpm run build && ./vendor/bin/qit run:plugin-check 2165910 --zip=woocommerce-shipping.zip", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org a8c --project wc-shipping ./dist && sentry-cli sourcemaps upload --org a8c --project wc-shipping ./dist"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "pnpm run lint:fix", "*.scss": "pnpm run lint:styles:fix", "*.php": ["./bin/phpcbf"]}, "repository": {"type": "git", "url": "git://github.com/woocommerce/woocommerce-shipping"}, "license": "GPL-2.0", "description": "WooCommerce Shipping", "devDependencies": {"@automattic/color-studio": "^4.1.0", "@babel/cli": "^7.26.4", "@babel/core": "^7.26.8", "@babel/eslint-parser": "^7.26.8", "@babel/plugin-transform-optional-chaining": "^7.25.9", "@babel/preset-env": "^7.26.8", "@babel/preset-typescript": "^7.26.0", "@playwright/test": "^1.50.1", "@sentry/cli": "^2.41.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.15", "@types/node": "^22.13.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "@webpack-cli/generators": "^3.0.7", "@woocommerce/components": "12.3.0", "@woocommerce/csv-export": "^1.10.0", "@woocommerce/currency": "^4.3.0", "@woocommerce/data": "^5.0.0", "@woocommerce/date": "^4.3.0", "@woocommerce/dependency-extraction-webpack-plugin": "^3.1.0", "@woocommerce/eslint-plugin": "^2.3.0", "@woocommerce/number": "^2.5.0", "@woocommerce/settings": "^1.0.0", "@wordpress/api-fetch": "^7.18.0", "@wordpress/babel-preset-default": "^8.18.0", "@wordpress/base-styles": "^5.18.0", "@wordpress/components": "^29.4.0", "@wordpress/compose": "^7.18.0", "@wordpress/data": "^10.18.0", "@wordpress/data-controls": "^4.18.0", "@wordpress/dataviews": "^8.0.0", "@wordpress/date": "^5.18.0", "@wordpress/dom-ready": "^4.18.0", "@wordpress/element": "^6.18.0", "@wordpress/env": "^10.18.0", "@wordpress/hooks": "^4.18.0", "@wordpress/html-entities": "^4.18.0", "@wordpress/i18n": "^5.18.0", "@wordpress/icons": "^10.18.0", "@wordpress/jest-preset-default": "^12.18.0", "@wordpress/plugins": "^7.18.0", "@wordpress/postcss-plugins-preset": "^5.18.0", "@wordpress/preferences": "^4.19.0", "@wordpress/prettier-config": "^4.18.0", "@wordpress/scripts": "^26.14.0", "@wordpress/stylelint-config": "^23.10.0", "@wordpress/url": "^4.18.0", "archiver": "^7.0.1", "babel-jest": "^29.7.0", "babel-loader": "^9.2.1", "chalk": "5.4.1", "clsx": "^2.1.1", "css-loader": "^7.1.2", "eslint": "^8.56.0", "eslint-import-resolver-webpack": "^0.13.10", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-jsdoc": "^50.6.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "fork-ts-checker-webpack-plugin": "^9.0.2", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.4.3", "lodash": "^4.17.21", "mini-css-extract-plugin": "^2.9.2", "postcss-loader": "^8.1.1", "prettier": "^3.5.1", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-test-renderer": "^18.3.1", "resize-observer-polyfill": "^1.5.1", "sass": "^1.79.6", "sass-loader": "^16.0.4", "shelljs": "^0.8.5", "style-loader": "^4.0.0", "stylelint": "^16.14.1", "ts-jest": "^29.2.5", "typescript": "^5.7.3", "url-loader": "^4.1.1", "uuid": "^11.0.5", "webpack": "^5.98.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1"}, "dependencies": {"@sentry/react": "^9.1.0"}}