[![CI](https://github.com/woocommerce/woocommerce-shipping/actions/workflows/merge_to_trunk.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipping/actions/workflows/merge_to_trunk.yml)
[![CI](https://github.com/woocommerce/woocommerce-shipping/actions/workflows/cron_qit.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipping/actions/workflows/cron_qit.yml)

# WooCommerce Shipping

- [Product Page](https://woocommerce.com/products/shipping/)
- [Documentation](https://woocommerce.com/document/woocommerce-shipping/)
- [Feature Requests](https://woocommerce.com/feature-requests/shipping/)

## Development Requirements

To develop on this repository you will need the following software installed on your local machine:

- nvm
- pnpm
- docker (only if using wp-env)

## Development Environment

To run the WooCommerce Shipping extension succesfully, you will need a working WordPress site with the following plugins installed:

- Jetpack
- WooCommerce

### WP-ENV

Included in this repository is a working WP-ENV setup that allows you to quickly get up and running with a development environment. To get started *clone this repository* somewhere on your local machine, then run the following:

```bash
composer install
nvm i
pnpm i
pnpm run wp-env start --update
pnpm run watch
pnpm run watch:ts # to watch js/ts files while checking types as well
```

If the commands completed successfully you should now be able to access your development site at http://localhost:8888/ with all the required plugins activated. The username and password for logging into wp-admin is `admin` and `password`

Webpack will be started in dev mode and watch for any changes, the hot module server will host assets on port: 8081. The WP-ENV setup should already be listening for changes on this port.

### Custom Environment

If you already have a local WordPress development environment and want to rather use that, simply clone this repository to your `wp-content/plugins` folder, then while in the folder run the following:

```bash
composer install
nvm i
pnpm i
pnpm run watch
pnpm run watch:ts # to watch js/ts files while checking types as well
```

This will start the webpack in dev mode and watch for changes, the hot module server will host assets on port: 8081. You can then activate the plugin in your WP admin panel.

You will need to add `define( 'WOOCOMMERCE_SHIPPING_DEV_SERVER_URL', 'http://localhost:8081' );` and `define( 'SCRIPT_DEBUG', true );` to wp-config.php to use the dev server.

Please also note you will require WooCommerce and Jetpack to be installed and activated before enabling the plugin in your custom environment.
