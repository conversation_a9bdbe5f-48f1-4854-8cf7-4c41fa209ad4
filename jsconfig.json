{"compilerOptions": {"baseUrl": "./", "paths": {"wcshipping/*": ["client/*"], "components/*": ["client/components/*"], "entrypoints/*": ["client/entrypoints/*"], "data/*": ["client/data/*"], "utils": ["client/utils/index.js"], "utils/*": ["client/utils/*"], "tests/*": ["tests/js/*"], "types/*": ["client/types/*"], "context": ["client/context/index.ts"], "context/*": ["client/context/*"], "effects/*": ["client/effects/*"]}}, "exclude": ["bin", "dist", "docker", "node_modules", "vendor"]}